#!/bin/bash
env=$1
echo "$env"

function deploy() {
    local env=$1
    echo "env: $env"

    npx -y google-artifactregistry-auth --repo-config .npmrc --credential-config ~/.npmrc

    cd client && \
    npm install && \
    echo "finished install" && \
    CUR_ENV=$env npm run builddeploy && \
    cd ../server && \
    npm install && \
    npm run build && \
    if [ $env == 'test' ]; then
        npm run pm2-test
    else
        npm run pm2
    fi
}

# main

if [ -z $env ]; then
    echo "environment variable is required"
else
    if [ $env == 'prod' ] || [ $env == 'test' ]; then
        deploy $env
    else
        echo "environment variable is invalid"
    fi

fi
