{"name": "saas.admin", "version": "1.0.0", "scripts": {"commit": "cz-customizable", "test": "echo \"Error: no test specified\"", "prepare": "husky install", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "sprint": "./script/start_new_sprint.sh"}, "config": {"commitizen": {"path": "cz-customizable"}}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@commitlint/cli": "^19.4.0", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "cz-customizable": "^7.2.1", "husky": "^8.0.0", "prettier": "^3.5.3"}}