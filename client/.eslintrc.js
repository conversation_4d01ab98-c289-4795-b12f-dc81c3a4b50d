/**
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-12 14:29:33
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 15:08:44
 * @Description:
 */
module.exports = {
  extends: require.resolve('@umijs/max/eslint'),
  rules: {
    'no-useless-escape': 'warn',
    '@typescript-eslint/no-unused-vars': 'warn',
    '@typescript-eslint/no-unused-expressions': [
      1,
      {
        allowTernary: true,
        allowShortCircuit: true,
      },
    ],
    'object-curly-newline': 'off',
    '@typescript-eslint/no-unused-expressions': [
      0,
      {
        allowTernary: true,
        allowShortCircuit: true,
      },
    ],
    'no-use-before-define': 'off',
    '@typescript-eslint/no-use-before-define': ['warn', { functions: false }],
  },
};
