/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-03-20 18:28:18
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-05-05 19:25:07
 * @Description:
 */
import { defineConfig } from '@umijs/max';
import routes from './config/routes';
import proxy from './config/proxy';

export default defineConfig({
  hash: true,
  antd: {},
  access: {},
  model: {},
  initialState: {},
  request: {},
  layout: {
    // title: '@umijs/max',
  },
  routes,
  npmClient: 'npm',
  locale: {
    // default zh-CN
    default: 'en-US',
    // title: true,
    // antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: false,
  },
  define: {
    'process.env': {
      NODE_ENV: 'dev',
      UMI_ENV: 'dev',
      CUR_ENV: process.env.CUR_ENV,
    },
  },
  proxy: proxy['dev'],

  chainWebpack: function (config: any) {
    const CompressionPlugin = require('compression-webpack-plugin');
    if (process.env.NODE_ENV === 'production') {
      //gzip压缩
      config.plugin('compression-webpack-plugin').use(CompressionPlugin, [
        {
          test: /\.js$|\.html$|\.css$/,
          threshold: 10240,
          deleteOriginalAssets: false,
        },
      ]);
    }
  },
  tailwindcss: {},
});
