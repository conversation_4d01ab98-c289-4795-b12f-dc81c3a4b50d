/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2022-10-17 10:03:40
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-02-09 18:59:15
 * @Description: 
 */
import React from 'react';

// export const MenuDemandsIcon = () => <Icon component={MenuDemandsSvg} width="5em" fill="red" />;
export const menuDemands: React.FC = () => (
  <svg viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
    <path fillRule="evenodd" clipRule="evenodd" d="M4 3H19.8284L24 7.17157V20.8284L19.8284 25H4V3ZM8 7V21H18.1716L20 19.1716V8.82843L18.1716 7H8Z" />
  </svg>
);
export const menuSupplys: React.FC = () => (
  <svg viewBox="0 0 28 28" xmlns="http://www.w3.org/2000/svg" fill="currentColor">
    <path fillRule="evenodd" clipRule="evenodd" d="M5 4H23V8H9V12H23V24H5V20H19V16H5V4Z" />
  </svg>
);
