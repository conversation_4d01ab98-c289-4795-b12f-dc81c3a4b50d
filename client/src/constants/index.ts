/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-09 10:40:03
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-12 11:47:25
 * @Description: 通用的
 */
import { objectFlip } from '@/utils';
export type TopBarButtonAuth = {
  Search?: boolean;
  Export?: boolean;
  ExportAll?: boolean;
};

export const AdminUser = {
  Super: 1,
  General: 2,
};
export const RolesOptopns = [
  {
    label: 'Super Admin',
    value: 1,
  },
  {
    label: 'Rix Administrator',
    value: 5,
  },
  {
    label: 'Rix Data Analyst',
    value: 6,
  },
];
export const RoleType = {
  SuperAdmin: 1,
  'Rix Administrator': 5,
  'Rix Data Analyst': 6,
};
export const RoleTypeMap: API.StringType = {
  1: 'Super Admin',
  5: 'Rix Administrator',
  6: 'Rix Data Analyst',
};
export const userTypeOptions = [
  {
    label: 'Tenant User',
    value: 1,
  },
  {
    label: 'Rix Administrator',
    value: 4,
  },
  {
    label: 'Rix Data Analyst',
    value: 5,
  },
];
export const UserTypeMap: API.StringType = {
  1: 'Tenant User',
  4: 'Rix Administrator',
  5: 'Rix Data Analyst',
};
export const UserType = {
  TenantUser: 1,
  'Rix Administrator': 4,
  'Rix Data Analyst': 5,
};
export const LoginPath = '/user/login';
export const OpenServicePath = '/open-service';

// 不需要鉴权的页面判断方法
export const isNoAuthPath = (path: string) => {
  return path === LoginPath || path === OpenServicePath;
};

export const ResetPwdType = {
  No: 1,
  Yes: 2,
};
export const StatusMap = {
  Active: 1,
  Paused: 2,
} as const;
export type StatusProps = keyof typeof StatusMap;
export const StatusDesc = objectFlip(StatusMap);

export const StatusOptions = [
  {
    label: 'Active',
    value: 1,
  },
  {
    label: 'Paused',
    value: 2,
  },
];
export const ResetPwdOptions = [
  {
    label: 'No',
    value: 1,
  },
  {
    label: 'Yes',
    value: 2,
  },
];
export const AuthLevel = {
  Supply: 1,
  'Supply App': 2,
  'Supply Placement': 3,
};

export const AppItemColor: API.StringType = {
  // '0': '#FA8C16',
  // '1': '#52C41A',
  // '2': '#13C2C2',
  // '3': '#FA541C',
  // '4': '#722ED1',
  // '5': '#1890FF',
  '0': '#0A5CF4,#25A1FF',
  '1': '#FF9012',
};

export const AppIconColor: API.StringType = {
  android: '#A4C639',
  ios: '#303333',
  roku: '#6C3C97',
};

export const Alphabets = [
  'a',
  'b',
  'c',
  'd',
  'e',
  'f',
  'g',
  'h',
  'i',
  'j',
  'k',
  'l',
  'm',
  'n',
  'o',
  'p',
  'q',
  'r',
  's',
  't',
  'u',
  'v',
  'w',
  'x',
  'y',
  'z',
];
// iconfont 配置(本地跟线上)
export const IconFontUrl: API.StringToStringType = {
  dev: '//at.alicdn.com/t/c/font_3852340_kd04igzymx.js',
  prod: '/js/iconfont.js',
};

export const AdFormatType = {
  Banner: 1,
  Native: 2,
  Video: 3,
  'Reward Video': 4,
};

export const AdFormatToLabel: API.StringToStringType = {
  1: 'Banner',
  2: 'Native',
  3: 'Video',
  4: 'Reward Video',
};

export const AdFormatOptions = [
  { label: 'Banner', value: '1' },
  { label: 'Native', value: '2' },
  { label: 'Video', value: '3' },
  { label: 'Reward Video', value: '4' },
];

export const ProfitModelOptions = [
  { label: 'Net', value: 1 },
  { label: 'Rev Share', value: 2 },
];

export const ProfitModelType = {
  Net: 1,
  'Rev Share': 2,
};

export const ProfitModelTypeToString: API.StringToStringType = {
  1: 'Net',
  2: 'Rev Share',
};

export const UIConfig = {
  SiderWidth: 236, // 侧边菜单宽度
  SiderCollapsedWidth: 50, // 关闭后的宽度
};

export const SpecialSuccessCode = {
  QPS_DUPLICATED_WHEN_PAUSED: 2304,
  UPDATE_PIXALATE: 2605,
};

export const StatusType = {
  Active: 1,
  Paused: 2,
  Delete: 3,
};

export const SendEmailTyleOptions = [
  { label: 'Yes', value: 1 },
  { label: 'No', value: 2 },
];

export const DemandAndSupplyStatusMap = {
  Active: 1,
  Paused: 2,
  Testing: 3,
} as const;
export const DemandAndSupplyStatusOptions = [
  {
    label: 'Active',
    value: 1,
  },
  {
    label: 'Paused',
    value: 2,
  },
  {
    label: 'Testing',
    value: 3,
  },
];
export type DemandAndSupplyStatusProps = keyof typeof DemandAndSupplyStatusMap;
export const DemandAndSupplyStatusDesc = objectFlip(DemandAndSupplyStatusMap);

export const RegionTypeDesc: API.StringToStringType = {
  // 0: 'All Region',
  1: 'USE',
  2: 'APAC',
  3: 'EUW',
};

export const YesNoMap = {
  Yes: 1,
  No: 2,
} as const;
export const YesNoDesc = objectFlip(YesNoMap);
export const YesNoOptions = [
  {
    label: 'Yes',
    value: 1,
  },
  {
    label: 'No',
    value: 2,
  },
];
