/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-19 10:19:43
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-10 10:54:49
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import type { TopBarSearchItem } from '@/components/TopBar';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import StatusTag from '@/components/Tag/StatusTag';
import { StatusDesc, StatusOptions } from '@/constants';
import moment from 'moment';
import { ColumnType } from '@/components/Table/FrontTable';
import EllipsisPopover from '@/components/EllipsisPopover';
import { Country, CountryOptions } from '../country';
export const IvtType = {
  pixalate: 1,
  human: 2,
};
export const IvtTypeOptions = [
  {
    label: 'Pixalate',
    value: 1,
  },
  {
    label: 'Human',
    value: 2,
  },
];
export const IvtTypeDesc: { [key: number]: string } = {
  1: 'Pixalate',
  2: 'Human',
};
export const IvtConfigBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Config',
    icon: 'rix-advanced',
  },
  {
    name: 'IVT Config',
  },
];

export const IvtConfigColumnOptions: ColumnType<ConfigAPI.IvtConfigItem>[] = [
  {
    title: 'ID',
    width: 80,
    dataIndex: 'id',
    sorter: (a, b) => a.id - b.id,
  },
  {
    title: 'Tenant',
    width: 180,
    dataIndex: 'tnt_id',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      const tnt_name = params.tnt_id === 0 ? 'All Tenants' : params.tnt_name;
      return (
        <HoverToolTip title={`${tnt_name}(${params.tnt_id})`}>
          <span>
            {tnt_name}({_})
          </span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Publisher',
    width: 180,
    dataIndex: 'seller_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip
        title={
          params.seller_id ? `${params.seller_name}(${params.seller_id})` : '-'
        }
      >
        <span>
          {params.seller_id
            ? `${params.seller_name}(${params.seller_id})`
            : '-'}
        </span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Advertiser',
    width: 180,
    dataIndex: 'buyer_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip
        title={
          params.buyer_id ? `${params.buyer_name}(${params.buyer_id})` : '-'
        }
      >
        <span>
          {params.buyer_id ? `${params.buyer_name}(${params.buyer_id})` : '-'}
        </span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Type',
    width: 120,
    dataIndex: 'type',
    render: (_, row) => (
      <HoverToolTip
        title={IvtTypeDesc[row['type']] ? IvtTypeDesc[row['type']] : '-'}
      >
        <span>{IvtTypeDesc[row['type']] ? IvtTypeDesc[row['type']] : '-'}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Traffic Ratio',
    width: 120,
    dataIndex: 'ratio',
  },
  {
    title: 'Country',
    width: 160,
    dataIndex: 'country',
    render: (_: string) => {
      const arr =
        (_ &&
          _.split(',')
            .filter((v: string) => v.trim())
            .map((v) => (Country as any)[v])) ||
        [];
      return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
    },
  },
  {
    title: 'Bundle',
    width: 220,
    dataIndex: 'bundle',
    render: (txt, row) => {
      let list = (txt && txt.split(',').filter((v: any) => v)) || [];
      return (
        (Array.isArray(list) && list.length && (
          <EllipsisPopover dataSource={list} />
        )) ||
        '-'
      );
    },
  },
  {
    title: 'Modify By',
    width: 180,
    dataIndex: 'op_name',
  },
  {
    title: 'Updated On',
    width: 160,
    dataIndex: 'update_time',
    render: (_: string) => {
      return moment(_).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: 'Status',
    key: 'status',
    width: 85,
    dataIndex: 'status',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: 'Operation',
    width: 120,
    fixed: 'right',
    dataIndex: 'operate',
    access: 'EditIVTCode',
  },
];

export const IvtConfigSearchOption: TopBarSearchItem[] = [
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Country',
    type: 'select',
    key: 'country',
    value: '',
    mode: 'multiple',
    options: CountryOptions,
  },
  {
    name: 'Type',
    type: 'select',
    key: 'type',
    value: '',
    mode: 'multiple',
    options: IvtTypeOptions,
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    mode: 'multiple',
    options: StatusOptions,
  },
];

export const DefaultFormData = {
  tnt_id: undefined,
  seller_id: [],
  buyer_id: [],
  bundle: [],
  country: [],
  type: 1,
  ratio: 1,
};
