import type { TopBarSearchItem } from '@/components/TopBar';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import StatusTag from '@/components/Tag/StatusTag';
import { StatusOptions } from '@/constants';
import moment from 'moment';
import { ColumnType } from '@/components/Table/FrontTable';

export const CustomColumnOptions: ColumnType<ConfigAPI.CustomPlacementItem>[] =
  [
    {
      title: 'Tenant',
      width: 180,
      dataIndex: 'tnt_id',
      ellipsis: { showTitle: false },
      render: (_, params) => {
        return (
          <HoverToolTip title={`${params.tnt_name}(${params.tnt_id})`}>
            <span>
              {params.tnt_name}({_})
            </span>
          </HoverToolTip>
        );
      },
    },
    {
      title: 'Demand',
      width: 180,
      dataIndex: 'buyer_id',
      ellipsis: { showTitle: false },
      render: (_, params) => (
        <HoverToolTip title={`${params.buyer_name}(${params.buyer_id})`}>
          <span>
            {params.buyer_id ? `${params.buyer_name}(${params.buyer_id})` : '-'}
          </span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Bundle',
      width: 120,
      dataIndex: 'bundle',
    },
    {
      title: 'App ID',
      width: 150,
      dataIndex: 'app_id',
      render: (_) => _ || '-',
    },
    {
      title: 'Banner Tag ID',
      width: 150,
      dataIndex: 'banner_tag_id',
      render: (_) => _ || '-',
    },
    {
      title: 'Native Tag ID',
      width: 150,
      dataIndex: 'native_tag_id',
      render: (_) => _ || '-',
    },
    {
      title: 'Video Tag ID',
      width: 150,
      dataIndex: 'video_tag_id',
      render: (_) => _ || '-',
    },
    {
      title: 'Rewarded Video Tag ID',
      width: 200,
      dataIndex: 'rewarded_video_tag_id',
      render: (_) => _ || '-',
    },
    {
      title: 'Modify By',
      width: 120,
      dataIndex: 'op_name',
    },
    {
      title: 'Updated On',
      width: 180,
      dataIndex: 'update_time',
      render: (_) => moment(_).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: 'Status',
      width: 85,
      dataIndex: 'status',
      render: (_) => <StatusTag value={_} />,
    },
    {
      title: 'Operation',
      width: 120,
      fixed: 'right',
      dataIndex: 'operate',
      access: 'EditCustomPlt',
    },
  ];

export const CustomSearchOption: TopBarSearchItem[] = [
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Demand',
    type: 'selectAll',
    key: 'buyer_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'bundle',
    value: '',
    mode: 'multiple', // 多行的过滤判断
    isNoExact: true, // 模糊匹配
  },
  {
    name: 'App ID',
    type: 'bundle',
    key: 'app_id',
    value: '',
    mode: 'multiple', // 多行的过滤判断
    isNoExact: true, // 模糊匹配
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    mode: 'multiple',
    options: StatusOptions,
  },
];

export const DefaultFormData = {
  tnt_id: undefined,
  buyer_id: undefined,
  bundle: '',
  app_id: '',
  banner_tag_id: '',
  native_tag_id: '',
  video_tag_id: '',
  rewarded_video_tag_id: '',
  status: 1,
};
