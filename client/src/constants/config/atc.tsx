/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 15:28:39
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-15 15:01:17
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import EllipsisPopover from '@/components/EllipsisPopover';
import { ColumnType } from '@/components/Table/FrontTable';
import StatusTag from '@/components/Tag/StatusTag';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import type { TopBarSearchItem } from '@/components/TopBar';
import { AdFormatToLabel, StatusOptions } from '@/constants';
import { Country, CountryOptions } from '@/constants/country';
import { MappingType } from '@/types/common';
import moment from 'moment';

export const AtcBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Config',
    icon: 'rix-advanced',
  },
  {
    name: 'ATC WL',
  },
];

export const ServerRegionOptions = [
  { label: 'USE', value: 'use' },
  { label: 'APSE', value: 'apse' },
  { label: 'EUW', value: 'euw' },
];

export const AdFormatOptions = [
  { label: 'Banner', value: 1 },
  { label: 'Native', value: 2 },
  { label: 'Video', value: 3 },
  { label: 'Reward Video', value: 4 },
];

export const genAtcColumnOptions = (
  extraColumns?: ColumnType<ConfigAPI.AtcItem>[],
  dynamicMaps?: Partial<Record<'adSizeOptions', MappingType[]>>,
): ColumnType<ConfigAPI.AtcItem>[] => {
  const { adSizeOptions = [] } = dynamicMaps || {};

  // adSizeOptions 从 [{ label: value} ...]转成 { value: label }
  const adSizeOptionsMap = adSizeOptions.reduce((acc, curr) => {
    acc[curr.value] = curr.label;
    return acc;
  }, {} as Record<string, string>);

  return [
    {
      title: 'Tenant',
      width: 180,
      dataIndex: 'tnt_id',
      ellipsis: { showTitle: false },
      render: (_, params) => (
        <HoverToolTip title={`${params.tnt_name}(${params.tnt_id})`}>
          <span>
            {params.tnt_name}({_})
          </span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Publisher',
      width: 180,
      dataIndex: 'seller_id',
      ellipsis: { showTitle: false },
      render: (_, params) => (
        <HoverToolTip
          title={
            params.seller_id === 0
              ? 'All Publishers(0)'
              : `${params.seller_name}(${params.seller_id})`
          }
        >
          <span>
            {params.seller_id === 0 ? 'All Publishers' : params.seller_name}({_}
            )
          </span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Advertiser',
      width: 180,
      dataIndex: 'buyer_id',
      ellipsis: { showTitle: false },
      render: (_, params) => (
        <HoverToolTip title={`${params.buyer_name}(${params.buyer_id})`}>
          <span>
            {params.buyer_id ? `${params.buyer_name}(${params.buyer_id})` : '-'}
          </span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Server Region',
      width: 120,
      dataIndex: 'region',
      render: (_: string) => {
        const arr =
          (_ &&
            _.split(',')
              .filter((v: string) => v.trim())
              .map((v) => v.toUpperCase())) ||
          [];
        return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
      },
    },
    {
      title: 'Country',
      width: 160,
      dataIndex: 'country',
      render: (_: string) => {
        const arr =
          (_ &&
            _.split(',')
              .filter((v: string) => v.trim())
              .map((v) => (Country as any)[v])) ||
          [];
        return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
      },
    },
    {
      title: 'Ad Format',
      width: 160,
      dataIndex: 'ad_format',
      render: (_: string) => {
        const arr =
          (_ &&
            _.split(',')
              .filter((v) => v.trim())
              .map((v) => (AdFormatToLabel as any)[v])) ||
          [];
        return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
      },
    },
    {
      title: 'Ad Size',
      width: 160,
      dataIndex: 'ad_size',
      render: (_: string) => {
        const arr =
          (_ &&
            _.split(',')
              .filter((v) => v.trim())
              .map((v) => adSizeOptionsMap[v])) ||
          [];
        return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
      },
    },
    {
      title: 'Bundle',
      width: 160,
      dataIndex: 'bundle',
      render: (_) => {
        const arr = (_ && _.split(',').filter((v: string) => v.trim())) || [];
        return arr.length ? <EllipsisPopover dataSource={arr} /> : '-';
      },
    },
    {
      title: 'Expired',
      width: 160,
      dataIndex: 'expired',
      render: (_: number) => {
        const time = new Date(_ * 1000);
        return moment(time).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: 'Modify By',
      width: 120,
      dataIndex: 'op_name',
    },
    {
      title: 'Updated On',
      width: 160,
      dataIndex: 'update_time',
      render: (_: string) => {
        return moment(_).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: 'Status',
      key: 'status',
      width: 85,
      dataIndex: 'status',
      render: (_) => <StatusTag value={_} />,
    },
    ...(extraColumns || []),
  ];
};

export const genAtcSearchOption = (
  dynamicMaps?: Partial<
    Record<
      | 'tenantOptions'
      | 'publisherOptions'
      | 'advertiserOptions'
      | 'adSizeOptions',
      MappingType[]
    >
  >,
): TopBarSearchItem[] => {
  const {
    tenantOptions = [],
    publisherOptions = [],
    advertiserOptions = [],
    adSizeOptions = [],
  } = dynamicMaps || {};
  return [
    {
      name: 'Tenant',
      type: 'select',
      key: 'tnt_id',
      value: '',
      mode: 'multiple',
      options: tenantOptions,
    },
    {
      name: 'Publisher',
      type: 'selectAll',
      key: 'seller_id',
      value: '',
      mode: 'multiple',
      options: publisherOptions,
    },
    {
      name: 'Advertiser',
      type: 'selectAll',
      key: 'buyer_id',
      value: '',
      mode: 'multiple',
      options: advertiserOptions,
    },
    {
      name: 'Server Region',
      type: 'select',
      key: 'region',
      value: '',
      mode: 'multiple',
      options: ServerRegionOptions,
    },
    {
      name: 'Ad Format',
      type: 'select',
      key: 'ad_format',
      value: '',
      mode: 'multiple',
      options: AdFormatOptions,
    },
    {
      name: 'Ad Size',
      type: 'select',
      key: 'ad_size',
      value: [],
      mode: 'multiple',
      options: adSizeOptions,
    },
    {
      name: 'Country',
      type: 'select',
      key: 'country',
      value: '',
      mode: 'multiple',
      options: CountryOptions,
    },
    {
      name: 'Bundle',
      type: 'bundle',
      key: 'bundle',
      value: '',
      mode: 'multiple', // 多行的过滤判断
      isNoExact: true, // 模糊匹配
    },
    {
      name: 'Status',
      type: 'select',
      key: 'status',
      value: '',
      mode: 'multiple',
      options: StatusOptions,
    },
  ];
};

export const DefaultFormData = {
  seller_id: undefined,
  buyer_id: undefined,
  tnt_id: undefined,
  region: undefined,
  country: undefined,
  bundle: undefined,
  ad_format: undefined,
  ad_size: undefined,
  expired: undefined,
};

export const IsAllMap = {
  YES: 1,
  NO: 0,
  DEFAULT: -1,
};
