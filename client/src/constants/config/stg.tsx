/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-19 10:19:43
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-26 18:26:49
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import type { TopBarSearchItem } from '@/components/TopBar';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import StatusTag from '@/components/Tag/StatusTag';
import { StatusOptions } from '@/constants';
import moment from 'moment';
import { ColumnType } from '@/components/Table/FrontTable';


export const DefaultFormData = {
  tnt_id: undefined,
  bundle: undefined,
  type: 1,
};

export const SupplyChainTypeOptions = [
  {
    label: 'Direct',
    value: 1,
  },
  {
    label: 'Reseller',
    value: 2,
  },
];

export const SupplyChainTypeDesc: API.NumberType = {
  1: 'Direct',
  2: 'Reseller',
};

export const StgTabOptions = [
  { label: 'Bundle', value: '1' },
  { label: 'Domain', value: '2' },
];
export const StgTab = {
  bundle: '1',
  domain: '2',
};

export const StgBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Transparency',
    icon: 'rix-advanced',
  },
  {
    name: 'Supply Chain',
  },
];

export const StgColumnOptions: ColumnType<ConfigAPI.StgItem>[] = [
  {
    title: 'Tenant',
    width: 180,
    dataIndex: 'tnt_id',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      const tnt_name = params.tnt_id === 0 ? 'All Tenants' : params.tnt_name;
      return (
        <HoverToolTip title={`${tnt_name}(${params.tnt_id})`}>
          <span>
            {tnt_name}({_})
          </span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Publisher ID',
    width: 180,
    dataIndex: 'publisher_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={`${params.publisher_id}`}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Type',
    width: 100,
    dataIndex: 'type',
    render: (_) => <span>{SupplyChainTypeDesc[_]}</span>,
  },

  {
    title: 'Bundle',
    width: 160,
    dataIndex: 'bundle',

    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={`${params.bundle}`}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },

  {
    title: 'Modify By',
    width: 120,
    dataIndex: 'op_name',
  },
  {
    title: 'Updated On',
    width: 160,
    dataIndex: 'update_time',
    render: (_: string) => {
      return moment(_).format('YYYY-MM-DD HH:mm:ss');
    },
  },
  {
    title: 'Status',
    key: 'status',
    width: 85,
    dataIndex: 'status',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: 'Operation',
    width: 120,
    fixed: 'right',
    dataIndex: 'operate',
  },
];

export const BundleStgSearchOption: TopBarSearchItem[] = [
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Publisher ID',
    type: 'selectAll',
    key: 'publisher_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Bundle',
    type: 'selectAll',
    key: 'bundle',
    value: '',
    mode: 'multiple', // 多行的过滤判断
    options: [],
    isNoExact: true, // 模糊匹配,
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    mode: 'multiple',
    options: StatusOptions,
  },
  {
    name: 'Type',
    type: 'select',
    key: 'type',
    value: [],
    mode: 'multiple',
    options: SupplyChainTypeOptions,
  },
];

export const DomainStgSearchOption: TopBarSearchItem[] = [
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Publisher ID',
    type: 'selectAll',
    key: 'publisher_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Domain',
    type: 'selectAll',
    key: 'developer_website_domain',
    value: '',
    mode: 'multiple', // 多行的过滤判断
    options: [],
    isNoExact: true, // 模糊匹配,
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    mode: 'multiple',
    options: StatusOptions,
  },
  {
    name: 'Type',
    type: 'select',
    key: 'type',
    value: [],
    mode: 'multiple',
    options: SupplyChainTypeOptions,
  },
];
