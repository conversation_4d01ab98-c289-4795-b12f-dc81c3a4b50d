/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 18:09:22
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 18:14:03
 * @Description:
 */

export const QpsLevelType = {
  supply: 1,
  demand: 2,
  'demand + supply': 3,
  'demand + supply-bundle': 4,
  'demand + supply-unit': 5,
  'demand + supply + ad_format': 6,
  'demand + supply + country': 7,
  'demand + ad_format': 8,
  'demand + country': 9,
  '(unlimit)supply + bundle': 51,
  '(unlimit)supply + country': 52,
  '(unlimit)supply + ad_format': 53,
};

export const QpsLevelTypeToString: API.StringToStringType = {
  1: 'Pub',
  2: 'Adv',
  3: 'Adv + Pub',
  4: 'Adv + Pub + Bundle',
  5: 'Adv + Ad Unit',
  6: 'Adv + Pub + Ad Format',
  7: 'Adv + Pub + Country',
  8: 'Adv + Ad Format',
  9: 'Adv + Country',
  51: 'Pub + Bundle',
  52: 'Pub + Country',
  53: 'Pub + Ad Format',
};

export const RegionListType = {
  'All Region': 0,
  USE: 1,
  APAC: 2,
};

export const RegionLabelMap = {
  // 0: 'All Region',
  USE: 'USE',
  APAC: 'APAC',
};

export const RegionLabel: API.StringToStringType = {
  // 0: 'All Region',
  1: 'USE',
  2: 'APAC',
};

export const QpsAdFormatToLabel: API.StringToStringType = {
  1: 'Banner',
  2: 'Native',
  3: 'Video',
};
