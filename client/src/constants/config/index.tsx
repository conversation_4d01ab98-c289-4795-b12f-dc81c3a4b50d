/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-18 11:55:33
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-20 11:59:41
 * @Description:
 */
import { ColumnProps } from 'antd/es/table';

import type { TopBarSearchItem } from '@/components/TopBar';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import StatusTag from '@/components/Tag/StatusTag';
import { BreadcrumbItem } from '@/components/Breadcrumb';

import { StatusOptions } from '@/constants';
import {
  EcprRegionMap,
  EcprLevelTypeMap,
  EcprRegionOptions,
} from '@/constants/config/ecpr';
export const EcprColumnOptions: ColumnProps<ConfigAPI.EcprListItem>[] = [
  {
    title: 'Tenant',
    width: 120,
    dataIndex: 'tnt_name',
    fixed: 'left',
  },
  {
    title: 'ID',
    key: 'id',
    width: 50,
    dataIndex: 'id',
  },
  {
    title: 'type',
    key: 'type',
    width: 120,
    dataIndex: 'type',
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{EcprLevelTypeMap[_]}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Server Region',
    key: 'server_region',
    width: 120,
    dataIndex: 'server_region',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{EcprRegionMap[_]}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Filter QPS',
    key: 'filter_qps',
    width: 120,
    dataIndex: 'filter_qps',
  },
  {
    title: 'Newer QPS',
    key: 'newer_qps',
    width: 120,
    dataIndex: 'newer_qps',
  },
  {
    title: 'Tier Ecpr',
    key: 'tier_ecpr',
    width: 220,
    dataIndex: 'tier_ecpr',
  },
  {
    title: 'Tier QPS',
    key: 'tier_qps',
    width: 220,
    dataIndex: 'tier_qps',
  },
  {
    title: 'Modify By',
    key: 'op_name',
    width: 120,
    dataIndex: 'op_name',
  },
  {
    title: 'Status',
    key: 'status',
    width: 85,
    dataIndex: 'status',
    render: (_) => <StatusTag value={_} />,
  },
];

export const EcprSearchOption: TopBarSearchItem[] = [
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Server Region',
    type: 'select',
    key: 'server_region',
    value: '',
    mode: 'multiple',
    options: EcprRegionOptions,
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    mode: 'multiple',
    options: StatusOptions,
  },
];

export const EcprBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Config',
    icon: 'rix-advanced',
  },
  {
    name: 'Ecpr',
  },
];
