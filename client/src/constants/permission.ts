/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-16 17:49:04
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-03-27 16:45:12
 * @Description:
 */
import { objectFlip } from '@/utils';

export type Type = {
  [key: string | number]: string;
};

export type NumberType = {
  [key: string | number]: number;
};

// 编辑类型
export const MenuOperationType = {
  Add: '1',
  Delete: '2',
  Edit: '3',
  View: '4',
};

export const OperationTitle: Type = {
  1: 'Add Sub Menu',
  2: 'Delete Menu',
  3: 'Edit Menu',
  4: 'Menu Detail',
};

export const RoleType = {
  'Super Administrator': 1,
  Administrator: 2,
  'General User': 3,
};

export const RoleOptions = [
  {
    label: 'Administrator',
    value: 2,
  },
  {
    label: 'General User',
    value: 3,
  },
];

export const RoleTypeMap = objectFlip(RoleType);

export const PermissionType = {
  Menu: 2,
  Operation: 1,
};

export const OperationType = {
  Write: 1,
  Read: 2,
};

export const OperationTypeMap = objectFlip(OperationType);

export const BlockType = {
  Unblock: 1,
  Block: 2,
};

export const BlockTypeMap = objectFlip(BlockType);

export const StatusType = {
  Active: 1,
  Paused: 0,
};

export const StatusOptions = [
  {
    label: 'Active',
    value: 1,
  },
  {
    label: 'Paused',
    value: 0,
  },
];

export const RoleTypeOptions = [
  {
    label: 'General User',
    value: 3,
  },
  {
    label: 'Administrator',
    value: 2,
  },
];

export const InterfaceTypeOption = [
  {
    label: 'Normal',
    value: 1,
  },
  {
    label: 'NO-Login',
    value: 2,
  },
  {
    label: 'Login-Global',
    value: 3,
  },
];

export const InterfaceOperationTypeOption = [
  {
    label: 'Write',
    value: 1,
  },
  {
    label: 'Read',
    value: 2,
  },
];

export const InterfaceType = {
  1: 'Normal', // 常规接口(权限)
  2: 'NO-Login', // 不需要登录的开放接口
  3: 'Login-Global', // 需要登录的开放接口
};

export const InterfaceOperationType = {
  1: 'Write',
  2: 'Read',
};

export const InterfaceTypeToNumber = {
  Normal: 1,
  'NO-Login': 2,
  'Login-Global': 3,
};

// 右键菜单项
export const MenuItems = [
  { label: '查看', key: MenuOperationType.View },
  { label: '删除', key: MenuOperationType.Delete },
  { label: '新增菜单', key: MenuOperationType.Add },
  { label: '编辑', key: MenuOperationType.Edit },
];

export const MenuDefaultFormData = {
  title: undefined,
  path: undefined,
  is_hide: 1,
  remark: undefined,
  icon: undefined,
  component: undefined,
  interfaces: [],
  menu_render: 1,
  status: 1,
  type: 2,
  node_type: 1
};

export const PermissionDefaultFormData = {
  pms_name: undefined,
  type: PermissionType.Menu, // 1 menu, 2 operation
  menus: [],
  operations: [],
  op_type: 1,
  bl_type: 1,
  remark: undefined,
  status: 1,
};

export const UserDefaultFormData = {
  username: undefined,
  name: undefined,
  roles: undefined,
  permissions: undefined,
  role_type: RoleType['General User'],
  type: undefined,
  status: 1,
};

export const UserType = {
  AM: 1,
  BD: 2,
};

export const UserOptions = [
  { label: 'AM', value: 1 },
  { label: 'BD', value: 2 },
];

export const OperationDefaultFormData = {
  op_name: undefined,
  auth_sign: undefined,
  remark: undefined,
  interfaces: [],
};

export const NodeType = {
  'Node': 1,
  'Link': 2,
  'Node & Link': 3
};
