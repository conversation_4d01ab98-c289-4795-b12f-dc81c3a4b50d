/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-19 17:52:21
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-25 11:19:12
 * @Description:
 */

import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/es/table';
import EllipsisPopover from '@/components/EllipsisPopover';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag, { ColorMap } from '@/components/Tag/NewStatusTag';

export const AppInfoSearchOption: TopBarSearchItem[] = [
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'bundle',
    value: [],
    tooltip: 'Separated by comma or space',
    required: true,
    limit: 50,
    placeholder: 'Input to filter/add,separated by comma/space',
  },
  {
    name: 'Lines',
    type: 'bundle',
    key: 'lines',
    value: [],
    required: true,
    limit: 50,
    splitBy: /\n/g,
    placeholder: 'Input to filter/add',
  },
];

export const StatusDescMap: { [key: number]: string } = {
  1: 'SUCCESS',
  100: 'Bundle No Record',
  101: 'Bundle Market Offline',
  102: 'AAT Not Support',
  103: 'AAT Request Failed',
};

export const StatusColorMap: { [key: number]: string } = {
  1: ColorMap.green,
  100: ColorMap.blue,
  101: ColorMap.blue,
  102: ColorMap.orange,
  103: ColorMap.red,
};

export const StatusTipsMap: { [key: number]: string } = {
  100: 'The bundle does not exist and needs to be recorded',
  101: 'The bundle is not on the GP/App Store',
  102: 'The bundle does not have developer/app-ads.txt information',
  103: 'Querying app-ads.txt information failed',
};

export const AppInfoColumns: ColumnProps<AppAdsAPI.AppInfoItem>[] = [
  {
    title: 'Bundle',
    width: 160,
    dataIndex: 'bundle',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      if (Array.isArray(params.bundle)) {
        return params.bundle.length ? (
          <EllipsisPopover dataSource={params.bundle} />
        ) : (
          '-'
        );
      } else {
        return (
          <HoverToolTip title={params.bundle}>
            <span>{params.bundle}</span>
          </HoverToolTip>
        );
      }
    },
  },
  {
    title: 'Line',
    width: 300,
    dataIndex: 'lines',
    render: (_) => {
      return _.length ? (
        <EllipsisPopover dataSource={_} contentWidth={400} />
      ) : (
        '-'
      );
    },
  },
  {
    title: 'Status',
    width: 85,
    dataIndex: 'status_msg',
    render: (_, row) => (
      <StatusTag
        value={row.status_code}
        statusDescMap={StatusDescMap}
        statusColorMap={StatusColorMap}
        tips={StatusTipsMap[row.status_code] || ''}
      />
    ),
  },
];

export const AppInfoBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Transparency',
    icon: 'rix-apps',
  },
  {
    name: 'App-ads.txt Tool',
  },
];
