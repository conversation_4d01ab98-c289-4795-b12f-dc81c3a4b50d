/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-12-06 16:38:18
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-14 18:55:58
 * @Description:
 */

import { history, Link } from 'umi';
import type { InfoBarTabsProps } from '@/components/InfoBar';
import StatusTag from '@/components/Tag/StatusTag';
import { DemandAndSupplyStatusDesc, StatusDesc, StatusMap } from '..';
import {
  ChannelType,
  DeveloperTrafficMap,
  DeviceType,
  RelationshipType,
  RevTrackTypeDesc,
} from './index';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import RixEngineFont from '@/components/RixEngineFont';
export const SupplyInfoTabs: InfoBarTabsProps<SupplyAPI.SupplyListItem> = [
  {
    title: 'Basic',
    key: 'basic',
    access: 'SupplyPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />,
    },
    columns: [
      {
        title: 'Publisher',
        dataIndex: 'seller_name',
        access: 'SupplyDetailPermission',
        render: (text, row, maxWidth) => {
          return (
            <HoverToolTip title={text} maxWidth={maxWidth}>
              <div
                style={{
                  textOverflow: 'ellipsis',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                }}
              >
                {text}
              </div>
            </HoverToolTip>
          );
        },
      },
      {
        title: 'ID',
        dataIndex: 'seller_id',
      },
      {
        title: 'Integration Type',
        dataIndex: 'integration_type_desc',
      },
      {
        title: 'Channel Type',
        dataIndex: 'channel_type',
        render: (text) => <>{ChannelType[text]}</>,
      },
      {
        title: 'Relationship',
        dataIndex: 'relationship',
        render: (text) => <>{RelationshipType[text]}</>,
      },
      {
        title: 'Device Type',
        dataIndex: 'device_type',
        render: (text) => <>{DeviceType[text]}</>,
      },
      {
        title: 'Custom Placement',
        dataIndex: 'cus_status',
        render: (_) => (
          <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
        ),
      },
      {
        title: 'Developer Traffic',
        dataIndex: 'developer_traffic',
        render: (_) => <>{DeveloperTrafficMap[_] || '-'}</>,
      },
      {
        title: 'Win Rate Profit(%)',
        dataIndex: 'win_rate_profit_ratio',
        render: (text) => <>{text === '-' ? 0 : text}</>,
      },
      {
        title: 'Profit',
        dataIndex: 'profit_status',
        render: (_) => (
          <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
        ),
      },
      {
        title: 'Profit(%)',
        dataIndex: 'profit_ratio',
        render: (text) => <>{text}</>,
      },
      {
        title: 'Profit Model',
        dataIndex: 'profit_model',
        render: (_) => <StatusTag value={_} statusDescMap={{ 1: 'Net' }} />,
      },
      {
        title: 'Revenue Tracking Type',
        dataIndex: 'rev_track_type',
        render: (text) => <>{RevTrackTypeDesc[text]}</>,
      },
      {
        title: 'Banner Multi Size',
        width: 85,
        dataIndex: 'banner_multi_size',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },
      {
        title: 'Crid Filter',
        width: 85,
        dataIndex: 'crid_filter',
        tooltip: () => 'Filter ads with empty crid',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },
      {
        title: 'Status',
        width: 85,
        dataIndex: 'status',
        render: (_) => (
          <StatusTag
            value={_}
            statusDescMap={DemandAndSupplyStatusDesc}
            cusStatusMap={{
              1: '#1CD880',
              2: '#FA5A42',
              3: '#476DF5',
            }}
          />
        ),
      },
      {
        title: 'NURL',
        dataIndex: 'pass_nurl',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },
      {
        title: 'BURL',
        dataIndex: 'pass_burl',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },
      {
        title: 'LURL',
        dataIndex: 'pass_lurl',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },
      {
        title: 'Partner',
        width: 100,
        dataIndex: 'partner_id',
        render: (_, row, maxWidth) => {
          const val = +_ > 0 ? `${row.partner_name}(${_})` : '-';
          return (
            <HoverToolTip title={val} maxWidth={maxWidth}>
              <span>{val}</span>
            </HoverToolTip>
          );
        },
      },
    ],
  },
  {
    title: 'Endpoint',
    key: 'endpoint',
    access: 'SupplyEndpointPermission',
    children: [
      {
        title: 'USE: ',
        type: 'collapse-line',
        showCopy: true,
        copyText: (row) => {
          if (row.pv_domain) {
            return `http://us.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
          }
          return `http://${row.host_prefix}.use.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${row.token}`;
        },
        columns: [
          {
            dataIndex: 'token',
            render: (text, row) => {
              if (row.pv_domain) {
                return `http://us.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${text}`;
              }
              return `http://${row.host_prefix}.use.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${text}`;
            },
          },
        ],
      },
      {
        title: 'APAC: ',
        type: 'collapse-line',
        showCopy: true,
        copyText: (row) => {
          if (row.pv_domain) {
            return `http://ap.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
          }
          return `http://${row.host_prefix}.apse.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${row.token}`;
        },
        columns: [
          {
            dataIndex: 'token',
            render: (text, row) => {
              if (row.pv_domain) {
                return `http://ap.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
              }
              return `http://${row.host_prefix}.apse.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${text}`;
            },
          },
        ],
      },
      {
        title: 'EUW: ',
        type: 'collapse-line',
        showCopy: true,
        copyText: (row) => {
          if (row.pv_domain) {
            return `http://eu.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
          }
          return `http://${row.host_prefix}.euw.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${row.token}`;
        },
        columns: [
          {
            dataIndex: 'token',
            render: (text, row) => {
              if (row.pv_domain) {
                return `http://eu.bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
              }
              return `http://${row.host_prefix}.euw.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${text}`;
            },
          },
        ],
      },
      {
        title: 'Global: ',
        type: 'collapse-line',
        showCopy: true,
        copyText: (row) => {
          if (row.pv_domain) {
            return `http://bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
          }
          return `http://${row.host_prefix}.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${row.token}`;
        },
        columns: [
          {
            dataIndex: 'token',
            render: (text, row) => {
              if (row.pv_domain) {
                return `http://bid.${row.pv_domain}/rtb?sid=${row.seller_id}&token=${row.token}`;
              }
              return `http://${row.host_prefix}.svr.rixengine.com/rtb?sid=${row.seller_id}&token=${text}`;
            },
          },
        ],
      },
    ],
  },
  {
    title: 'Account',
    key: 'account',
    access: 'SupplyAccountPermission',

    children: [
      {
        title: 'Publisher Reporting',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            showCopy: true,
            copyText: (row) => {
              return `${location.origin}`;
            },
            dataIndex: 'reporting_url',
            render: (txt, params) => {
              return `${location.origin}`;
            },
          },
          {
            title: 'User Name',
            showCopy: true,
            dataIndex: 'seller_account_name',
          },

          {
            title: 'Status',
            width: 85,
            dataIndex: 'seller_account_status',
            render: (_) => (
              <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
            ),
          },
        ],
      },
      {
        title: 'Reporting API',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'Sid',
            showCopy: true,
            dataIndex: 'seller_id',
          },
          {
            title: 'Token',
            dataIndex: 'token',
            showCopy: true,
          },
        ],
      },
    ],
  },
  {
    title: 'Authorization',
    key: 'authorization',
    access: 'SupplyDetailPermission',

    type: 'table',
    rowKey: 'auth_buyer_id',
    tableData: [],
    columns: [
      {
        title: 'Advertiser',
        dataIndex: 'auth_buyer_name',
        ellipsis: { showTitle: false },
        width: 150,
        render: (_) => (
          <HoverToolTip title={_}>
            <span>{_}</span>
          </HoverToolTip>
        ),
      },
      {
        title: 'Advertiser ID',
        width: 80,
        dataIndex: 'auth_buyer_id',
        ellipsis: { showTitle: false },
        render: (_) => (
          <HoverToolTip title={_}>
            <span>{_}</span>
          </HoverToolTip>
        ),
      },
    ],
  },
];
