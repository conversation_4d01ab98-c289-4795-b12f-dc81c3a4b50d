/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 10:58:30
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-17 15:41:27
 * @Description:
 */
import { Link } from '@umijs/max';
import { ColumnProps } from 'antd/es/table';
import StatusTag from '@/components/Tag/StatusTag';
import {
  ChannelType,
  DeveloperTrafficMap,
  DeviceType,
  RelationshipType,
  RevTrackTypeDesc,
} from './index';
import { DemandAndSupplyStatusDesc, StatusDesc } from '@/constants';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { ProfitModelTypeToString, ProfitModelType } from '@/constants';

export const SupplyColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    ellipsis: { showTitle: false },
    width: 220,
    render: (text, row) => (
      <HoverToolTip title={text}>
        <span>{text}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Tenant',
    dataIndex: 'tnt_id',
    ellipsis: { showTitle: false },
    width: 220,
    render: (text, row) => (
      <HoverToolTip title={`${row.tnt_name}(${text})`}>
        <span>
          {row.tnt_name}({text})
        </span>
      </HoverToolTip>
    ),
  },
  {
    title: 'ID',
    dataIndex: 'seller_id',
    width: 100,
  },
  {
    title: 'Partner',
    width: 150,
    dataIndex: 'partner_name',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_ || '-'}>
        <span>{_ || '-'}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Integration Type',
    width: 150,
    dataIndex: 'integration_type_desc',
  },
  {
    title: 'Channel Type',
    width: 120,
    dataIndex: 'channel_type',
    render: (text) => <>{ChannelType[text]}</>,
  },
  {
    title: 'Relationship',
    width: 120,
    dataIndex: 'relationship',
    render: (text) => <>{RelationshipType[text]}</>,
  },
  {
    title: 'Device Type',
    width: 120,
    dataIndex: 'device_type',
    render: (text) => <>{DeviceType[text]}</>,
  },

  {
    title: 'Revenue Tracking Type',
    width: 180,
    dataIndex: 'rev_track_type',
    render: (text) => <>{RevTrackTypeDesc[text]}</>,
  },
  {
    title: 'Developer Traffic',
    width: 150,
    dataIndex: 'developer_traffic',
    render: (text) => <>{DeveloperTrafficMap[text] || '-'}</>,
  },
  {
    title: 'Win Rate Profit Ratio(%)',
    width: 200,
    dataIndex: 'win_rate_profit_ratio',
    render: (text) => <>{text}</>,
  },
  {
    title: 'Profit Model',
    width: 130,
    dataIndex: 'profit_model',
    render: (text) => <>{ProfitModelTypeToString[text]}</>,
  },
  {
    title: 'Profit(%)',
    width: 100,
    dataIndex: 'profit_ratio',
    render: (text) => <>{text}</>,
  },
  {
    title: 'Rev Share(%)',
    width: 120,
    dataIndex: 'rev_share_ratio',
    render: (text, row) =>
      text && row.profit_model === ProfitModelType['Rev Share']
        ? `${text}`
        : '-',
  },
  {
    title: 'Banner Multi Size',
    width: 150,
    dataIndex: 'banner_multi_size',
    render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
  },
  {
    title: 'Crid Filter',
    width: 150,
    dataIndex: 'crid_filter',
    render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
  },
  {
    title: 'Status',
    width: 85,
    dataIndex: 'status',
    render: (_) => (
      <StatusTag
        value={_}
        statusDescMap={DemandAndSupplyStatusDesc}
        cusStatusMap={{
          1: '#1CD880',
          2: '#FA5A42',
          3: '#476DF5',
        }}
      />
    ),
  },
];

export const SupplyCardColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
  },
  {
    title: 'Integration Type',
    dataIndex: 'integration_type_desc',
  },

  {
    title: 'Channel Type',
    dataIndex: 'channel_type',
    render: (text: any) => <>{ChannelType[text]}</>,
  },
  {
    title: 'Relationship',
    dataIndex: 'relationship',
    render: (text: any) => <>{RelationshipType[text]}</>,
  },
  {
    title: 'Device Type',
    dataIndex: 'device_type',
    render: (text: any) => <>{DeviceType[text]}</>,
  },
  {
    title: 'Status',
    dataIndex: 'status',
    render: (_) => (
      <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
    ),
  },
];

export const AuthorizationColumns: ColumnProps<SupplyAPI.SellerDemandAuth>[] = [
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'ID',
    dataIndex: 'buyer_id',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
];

export const profileRadioColumn: ColumnProps<SupplyAPI.SupplyListItem> = {
  title: 'Rev Share(%)',
  width: 120,
  dataIndex: 'rev_share_ratio',
  render: (text, row) =>
    text && row.profit_model === ProfitModelType['Rev Share'] ? `${text}` : '-',
};
