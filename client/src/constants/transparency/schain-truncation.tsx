import { BreadcrumbItem } from '@/components/Breadcrumb';
import StatusTag from '@/components/Tag/StatusTag';
import { TopBarSearchItem } from '@/components/TopBar';
import { OptionsToMap } from '@/utils/options-to-map';
import { ColumnProps } from 'antd/es/table';
import moment from 'moment';
import { StatusOptions } from '..';

export const SchainTruncationBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Transparency',
    icon: 'rix-transparency',
  },
  {
    name: 'Schain Truncation',
  },
];

export const LevelOptions = [
  { label: 'Tenant', value: 1 },
  { label: 'Tenant + Demand', value: 2 },
];
export const LevelMapByValueKey = OptionsToMap(LevelOptions, 'value');

export const SchainTruncationTypeOptions = [
  { label: 'Unlimited', value: 1 },
  { label: 'Limited', value: 2 },
];

export const SchainTypeMapByLabelKey = OptionsToMap(
  SchainTruncationTypeOptions,
  'label',
);

export const SchainTruncationSearchOption: TopBarSearchItem[] = [
  {
    name: 'Type',
    type: 'select',
    key: 'level',
    value: [],
    mode: 'multiple',
    options: LevelOptions,
  },
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    mode: 'multiple',
    options: [],
    value: [],
  },
  {
    name: 'Demand',
    type: 'selectAll',
    key: 'buyer_id',
    mode: 'multiple',
    options: [],
    value: [],
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: [],
    mode: 'multiple',
    options: StatusOptions,
  },
];

export const SchainTruncationColumns: ColumnProps<TransparencyAPI.SchainTruncationColumns>[] =
  [
    {
      title: 'type',
      dataIndex: 'level',
      width: 100,
      render: (_, params) => {
        return <span>{LevelMapByValueKey[params.level] || ''}</span>;
      },
    },
    {
      title: 'Tenant',
      dataIndex: 'tnt_id',
      width: 100,
      render: (_, { tnt_name, tnt_id }) => {
        // if tnt_name is not empty, return tnt_name(tnt_id) else return tnt_id
        return tnt_name ? `${tnt_name}(${tnt_id})` : tnt_id;
      },
    },
    {
      title: 'Advertiser',
      dataIndex: 'buyer_id',
      width: 100,
      render: (_, { buyer_name, buyer_id }) => {
        // if buyer_name is not empty, return buyer_name(buyer_id) else return buyer_id
        return buyer_name ? `${buyer_name}(${buyer_id})` : buyer_id || '-';
      },
    },
    {
      title: 'Schain Hops Truncation',
      dataIndex: 'schain_hops',
      width: 100,
      render: (_, params) => {
        return <span>{params.schain_hops || 'Unlimited'}</span>;
      },
    },
    {
      title: 'Modify By',
      width: 120,
      dataIndex: 'op_name',
      render: (_: string) => {
        return <span>{_ || '-'}</span>;
      },
    },
    {
      title: 'Updated On',
      width: 160,
      dataIndex: 'update_time',
      render: (_: string) => {
        return moment(_).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      width: 85,
      render: (_) => <StatusTag value={_} />,
    },
  ];
