import { BreadcrumbItem } from '@/components/Breadcrumb';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { mapOptionsBy } from '@rixfe/rix-tools';
import { ColumnProps } from 'antd/es/table';
import moment from 'moment';

export const PlatformOptions = [
  { label: 'iOS', value: 1 },
  { label: 'Android', value: 2 },
  { label: 'Unknown', value: 0 },
];

export const PlatformOptionsMap = mapOptionsBy(PlatformOptions, 'value');

// '0 unchecked, 1 checked'
export const CheckoutStatus = [
  { label: 'Unchecked', value: 0 },
  { label: 'Checked', value: 1 },
];

export interface AppCrawlerSearchOptionKeys {
  app_bundle_id?: string[];
  platform?: number;
}

export const AppCrawlerSearchOption: TopBarSearchItem[] = [
  {
    name: 'APP Bundle ID',
    type: 'bundle',
    key: 'app_bundle_id',
    value: [],
    tooltip: 'Separated by comma or space',
    placeholder: 'Input to filter/add,separated by comma/space',
  },
  {
    name: 'Platform',
    type: 'select',
    key: 'platform',
    options: PlatformOptions,
    value: undefined,
    placeholder: 'Select platform',
  },
  // status
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    options: CheckoutStatus,
    value: undefined,
  },
];

export const AppCrawlerBaseColumns: ColumnProps<any>[] = [
  {
    title: 'App Bundle ID',
    width: 120,
    dataIndex: 'app_bundle_id',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={params.app_bundle_id}>
          <span>{params.app_bundle_id}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Platform',
    width: 120,
    dataIndex: 'platform',
    ellipsis: { showTitle: false },
    render: (value) => {
      return PlatformOptions.find((item) => item.value === value)?.label || '-';
    },
  },
  {
    title: 'Status',
    width: 120,
    dataIndex: 'status',
    ellipsis: { showTitle: false },
    render: (value) => {
      return CheckoutStatus.find((item) => item.value === value)?.label || '-';
    },
  },
  {
    title: 'Operation User',
    width: 120,
    dataIndex: 'op_user',
    ellipsis: { showTitle: false },
    render: (_, params) => {
      return (
        <HoverToolTip title={params.op_user}>
          <span>{params.op_user ?? '-'}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Create Time',
    width: 120,
    dataIndex: 'create_ts',
    ellipsis: { showTitle: false },
    render: (value) => {
      return value ? moment.unix(value).format('YYYY-MM-DD HH:mm:ss') : '-';
    },
  },
];

export const AppCrawlerBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Transparency',
    icon: 'rix-transparency',
  },
  {
    name: 'App Crawler',
  },
];
