import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Space, Tooltip } from 'antd';
import { ColumnProps } from 'antd/es/table';

// BreadOptions
export const MenuBreadOptions = [
  {
    name: 'Transparency',
    icon: 'rix-transparency',
  },
  {
    name: 'Rix Site Files',
    path: '/transparency/rix-site-files',
  },
];

export const RixFileTypes = {
  SELLERS: 'Sellers.json',
  ADS: 'Ads.txt',
  APP_ADS: 'App-ads.txt',
};

export const RixFiles: { id: string; name: string; url: string }[] = [
  {
    id: RixFileTypes.SELLERS,
    name: 'sellers.json',
    url: 'https://www.rixengine.com/sellers.json',
  },
  {
    id: RixFileTypes.ADS,
    name: 'ads.txt',
    url: 'https://www.rixengine.com/ads.txt',
  },
  {
    id: RixFileTypes.APP_ADS,
    name: 'app-ads.txt',
    url: 'https://www.rixengine.com/app-ads.txt',
  },
];

// columns
export const RixSiteColumns: ColumnProps<any>[] = [
  {
    title: 'File',
    dataIndex: 'name',
    key: 'name',
    width: 200,
  },
  {
    title: 'URL',
    dataIndex: 'url',
    key: 'url',
    render: (url: string) => {
      return url ? (
        <Space align="start">
          <a href={url} target="_blank" rel="noopener noreferrer">
            {url}
          </a>
          <Tooltip title="After the file is uploaded, wait for about a minute and the file content will be updated">
            <ExclamationCircleOutlined
              style={{ color: 'orange', cursor: 'pointer' }}
            />
          </Tooltip>
        </Space>
      ) : (
        <span style={{ color: 'gray' }}>No URL available</span>
      );
    },
  },
];
