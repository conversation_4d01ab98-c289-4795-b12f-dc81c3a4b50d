/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-01-29 11:32:41
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-03 16:20:10
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { TabOption } from '@/hooks/useCurrentTabState';
import { TenantOption } from '@/models/useTenantList';
import { SearchOption } from '@/types/common';
import { Image } from 'antd';
import { ColumnProps } from 'antd/lib/table';

export const PrivateBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Manage',
    icon: 'antd-BorderOuterOutlined',
  },
  {
    name: 'Privatization Management',
  },
];

export const PrivatizationTab = {
  privatization: 'privatization',
  pixalate: 'pixalate',
} as const;

export type PrivatizationTabType =
  (typeof PrivatizationTab)[keyof typeof PrivatizationTab];

export const PrivatizationTabOptions: TabOption<PrivatizationTabType>[] = [
  { label: 'Privatization', value: 'privatization' },
  { label: 'Pixalate', value: 'pixalate' },
];

export type PrivatizationBaseTableProps = {
  defaultTab: PrivatizationTabType;
  tabOptions: TabOption<PrivatizationTabType>[];
  onTabChange: (tab: PrivatizationTabType) => void;
  // tab 组件共享的数据
  tenantOptions: TenantOption[];
};

export const genPrivateSearchOption = (
  tenantOptions: TenantOption[],
  brandOptions: SearchOption<string>[],
): TopBarSearchItem[] => {
  return [
    {
      name: 'Tenant',
      type: 'select',
      key: 'tnt_id',
      value: [],
      mode: 'multiple',
      options: tenantOptions,
    },
    {
      name: 'Brand',
      type: 'select',
      key: 'brand_name',
      value: [],
      mode: 'multiple',
      options: brandOptions,
    },
  ];
};

export const PrivateColumnOptions: ColumnProps<PrivateAPI.BrandList>[] = [
  {
    title: 'Tenant',
    width: 140,
    dataIndex: 'tnt_id',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const name = `${row?.tnt_name}(${row.tnt_id})`;
      return (
        <HoverToolTip title={name}>
          <span>{name}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Brand Name',
    dataIndex: 'brand_name',
    width: 120,
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: "Sellers's Domain",
    dataIndex: 'sj_domain',
    width: 150,
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_ || 'rixengine.com'}>
        <span>{_ || 'rixengine.com'}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Logo Path',
    dataIndex: 'brand_logo_path',
    width: 220,
    ellipsis: { showTitle: false },
    render: (_) =>
      _ ? (
        <Image
          width={248}
          src={`/api/privatization/download?path=${_}`}
          preview={false}
        />
      ) : (
        '-'
      ),
  },
  {
    title: 'Favicon Path',
    dataIndex: 'brand_favicon_path',
    width: 220,
    ellipsis: { showTitle: false },
    render: (_) =>
      _ ? (
        <Image
          width={32}
          src={`/api/privatization/download?path=${_}`}
          preview={false}
        />
      ) : (
        '-'
      ),
  },
];
