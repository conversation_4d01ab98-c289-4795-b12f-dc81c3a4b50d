/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 15:19:49
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-26 17:08:21
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { ColumnProps } from 'antd/lib/table';
import { RoleTypeMap, StatusOptions, UserTypeMap, userTypeOptions } from '..';
import StatusTag from '@/components/Tag/StatusTag';
import { TopBarSearchItem } from '@/components/TopBar';

export const UserBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Manage',
    icon: 'antd-BorderOuterOutlined',
  },
  {
    name: 'User Management',
  },
];

export const TntUserColumnOptions: ColumnProps<TenantAPI.UserListItem>[] = [
  {
    title: 'User Name',
    dataIndex: 'account_name',
    width: 140,
    fixed: 'left',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Tenant',
    width: 140,
    dataIndex: 'tnt_name',
    ellipsis: { showTitle: false },
    render: (tnt_name, row) => {
      const showContent = tnt_name ? `${tnt_name}(${row.tnt_id})` : row.tnt_id;

      return (
        <HoverToolTip title={showContent}>
          <span>{showContent}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'User Type',
    width: 150,
    dataIndex: 'type',
    render: (_) => <span>{UserTypeMap[_]}</span>,
  },
  {
    title: 'Email',
    width: 150,
    dataIndex: 'email',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_ || '-'}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Role',
    width: 150,
    dataIndex: 'role_id',
    render: (_) => <span>{RoleTypeMap[_]}</span>,
  },
  {
    title: 'Status',
    width: 85,
    dataIndex: 'status',
    render: (_) => <StatusTag value={_} />,
  },
];

export const UserSearchOption: TopBarSearchItem[] = [
  {
    name: 'User Name',
    type: 'select',
    key: 'account_name',
    value: '',
    mode: 'multiple',
    options: [],
  },

  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'User Type',
    type: 'select',
    key: 'type',
    value: '',
    mode: 'multiple',
    options: userTypeOptions,
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: '',
    mode: 'multiple',
    options: StatusOptions,
  },
];
