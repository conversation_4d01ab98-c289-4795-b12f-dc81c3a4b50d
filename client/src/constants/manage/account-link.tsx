import { BreadcrumbItem } from '@/components/Breadcrumb';
import EllipsisPopover from '@/components/EllipsisPopover';
import StatusTag from '@/components/Tag/StatusTag';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { ColumnProps } from 'antd/es/table';
import { UserTypeMap } from '..';

export const AccountLinkBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Manage',
    icon: 'antd-BorderOuterOutlined',
  },
  {
    name: 'Account Link',
  },
];

export const AccountLinkColumnOptions: ColumnProps<UserAPI.AccountLinkColumn>[] =
  [
    {
      title: 'User Name',
      dataIndex: 'account_name',
      width: 140,
      ellipsis: { showTitle: false },
      render: (_, { account_name, user_id }) => (
        <HoverToolTip title={account_name}>
          <span>{`${account_name} (${user_id})`}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'User Type',
      width: 150,
      dataIndex: 'type',
      render: (_) => <span>{UserTypeMap[_]}</span>,
    },
    {
      title: 'Linked Users',
      width: 150,
      dataIndex: 'link_users',
      render: (link_users) => {
        if (!link_users || !link_users.length) return '-';
        return (
          <EllipsisPopover
            dataSource={link_users.map(
              (user: UserAPI.AccountLinkColumn) => ({
                value: `${user.account_name} (${user.user_id})`,
                status: user.status,
              }),
            )}
          />
        );
      },
    },
    {
      title: 'Link Status',
      width: 85,
      dataIndex: 'link_status',
      render: (_) => <StatusTag value={_} />,
    },
  ];
