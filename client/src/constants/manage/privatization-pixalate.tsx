import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { TenantOption } from '@/models/useTenantList';
import { ColumnProps } from 'antd/lib/table';

export const RixCommonStatus: Record<number, string> = {
  1: 'True',
  2: 'False',
};

export const RixCommonStatusMap = {
  True: 1,
  False: 2,
} as const;

export const RixCommonStatusOptions = [
  {
    label: 'True',
    value: 1,
  },
  {
    label: 'False',
    value: 2,
  },
];

export const genPixalateSearchOption = (
  tenantOptions: TenantOption[],
): TopBarSearchItem[] => {
  return [
    {
      name: 'Tenant',
      type: 'select',
      key: 'tnt_id',
      value: '',
      mode: 'multiple',
      options: tenantOptions,
    },
    {
      name: 'Use Default',
      type: 'select',
      key: 'use_rix_common',
      value: [],
      mode: 'multiple',
      options: RixCommonStatusOptions,
    },
  ];
};

export const genPixalateColumnOptions = (
  viewDetail: (row: PrivateAPI.PixalateItem) => void,
): ColumnProps<PrivateAPI.PixalateItem>[] => {
  return [
    {
      title: 'Tenant',
      width: 100,
      dataIndex: 'tnt_id',
      ellipsis: { showTitle: false },
      render: (_, row) => {
        const name = `${row?.tnt_name}(${row.tnt_id})`;
        return (
          <HoverToolTip title={name}>
            <span>{name}</span>
          </HoverToolTip>
        );
      },
    },
    {
      title: 'Use Default',
      width: 60,
      dataIndex: 'use_rix_common',
      render: (_, row) => {
        const useRixCommon = row?.pixalate_config?.use_rix_common;
        return useRixCommon ? (
          <span>{RixCommonStatus[useRixCommon]}</span>
        ) : (
          '-'
        );
      },
    },
    {
      title: 'Pixalate Config',
      width: 100,
      dataIndex: 'pixalate_config',
      render: (_, row) => {
        return (
          <span
            onClick={() => viewDetail(row)}
            style={{ color: 'var(--primary-color)', cursor: 'pointer' }}
          >
            view detail
          </span>
        );
      },
    },
    {
      title: 'Operator',
      width: 100,
      dataIndex: 'op_id',
      ellipsis: { showTitle: false },
      render: (_, row) => {
        return <span>{row?.op_name || '-'}</span>;
      },
    },
  ];
};
