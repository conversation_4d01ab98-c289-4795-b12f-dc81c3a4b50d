/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-03-11 17:47:00
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2024-01-12 11:32:00
 * @Description:
 */
import { ColumnProps } from 'antd/lib/table';
export type Type = {
  [key: string | number]: string;
};
// 编辑类型
export const MenuOperationType = {
  Add: '1',
  Delete: '2',
  Edit: '3',
  View: '4',
};

export const OperationTitle: Type = {
  1: 'Add Sub Menu',
  2: 'Delete Menu',
  3: 'Edit Menu',
  4: 'Menu Detail',
};
export const MenuType = {
  Menu: 2,
  Button: 1,
};

export const MenuColumns: ColumnProps<PermissionAPI.InterfaceItem>[] = [
  {
    title: 'Menu Name',
    dataIndex: 'itf_name',
  },
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: 'Route Path',
    dataIndex: 'path',
  },
  {
    title: 'Type',
    dataIndex: 'type_desc',
  },
  {
    title: 'Operation Type',
    dataIndex: 'op_type_desc',
  },
];

export const LinkMenuType = {
  Ordinary: 1,
  Dashboard: 2,
};

export const LinkMenuTypeList = [
  {
    label: 'Ordinary',
    value: LinkMenuType.Ordinary,
  },
  {
    label: 'Dashboard',
    value: LinkMenuType.Dashboard,
  },
];