/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-03-10 15:16:28
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-06-02 17:13:52
 * @Description:
 */

import { Tag } from 'antd';
import { ColumnProps } from 'antd/lib/table';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';
export const RoleType = {
  'Super Administrator': 1,
  Administrator: 2,
  Operator: 3,
  'Data Analyst': 4,
  'Rix Administrator': 5,
  'Rix Data Analyst': 6,
  'Supply User': 7,
  'Demand User': 8,
};

export const AdminUserType = {
  Administrator: 1,
  Ordinary: 2,
};
export const RoleTypeOptions = [
  {
    label: 'Administrator',
    value: 2,
  },
  {
    label: 'General User',
    value: 3,
  },
];
export const RoleSearchOption: TopBarSearchItem[] = [
  {
    name: 'Role',
    type: 'select',
    key: 'role_name',
    value: '',
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Roles',
  },
  {
    name: 'ID',
    type: 'select',
    key: 'id',
    mode: 'multiple',
    value: '',
    options: [],
    placeholder: 'Please Select Role ID',
  },
  {
    name: 'Type',
    type: 'select',
    key: 'type',
    mode: 'multiple',
    value: '',
    options: RoleTypeOptions,
    placeholder: 'Please Select Status',
  },
];

export const RoleBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Permission Config',
    icon: 'rix-advanced',
  },
  {
    name: 'Role',
  },
];
export const RoleColumns: ColumnProps<PermissionAPI.RoleListItem>[] = [
  {
    title: 'Role',
    dataIndex: 'role_name',
  },
  {
    title: 'ID',
    dataIndex: 'id',
  },
  {
    title: 'Type',
    dataIndex: 'type_desc',
  },
  {
    title: 'Permission',
    dataIndex: 'pms_list',
    render: (arr, params) => (
      <>
        {arr.length
          ? arr.map((item: any, index: number) => (
              <Tag color="green" style={{ marginTop: '5px' }} key={index}>
                {item.pms_name}
              </Tag>
            ))
          : params.type === RoleType['Super Administrator']
          ? 'All'
          : '-'}
      </>
    ),
  },
];
