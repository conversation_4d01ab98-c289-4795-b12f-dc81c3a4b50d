/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-10 17:53:35
 * @LastEditors: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 16:08:31
 * @Description:
 */

import { ColumnProps } from 'antd/lib/table';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';
// export const InterfaceType = {
//   1: 'Normal',
//   2: 'NO-Login', // 不需要登录的开放接口
//   3: 'Login-Global' // 需要登录的开放接口
// };
export const InterfaceType = {
  Normal: 1,
  'NO-Login': 2,
  'Login-Global': 3,
};
export const InterfaceTypeOptions = [
  {
    label: 'Normal',
    value: 1,
  },
  {
    label: 'No-Login',
    value: 2,
  },
  {
    label: 'Login-Global',
    value: 3,
  },
];
export const InterfaceOperationTypeOptions = [
  {
    label: 'Write',
    value: 1,
  },
  {
    label: 'Read',
    value: 2,
  },
];
export const InterfaceSearchOption: TopBarSearchItem[] = [
  {
    name: 'Interface',
    type: 'select',
    key: 'id',
    value: '',
    options: [],
    mode: 'multiple',
    placeholder: 'Please Select Interfaces',
  },
  // {
  //   name: 'ID',
  //   type: 'select',
  //   key: 'id',
  //   mode: 'multiple',
  //   value: '',
  //   options: [],
  //   placeholder: 'Please Select Interface ID',
  // },
  {
    name: 'Type',
    type: 'select',
    key: 'type',
    mode: 'multiple',
    value: '',
    options: InterfaceTypeOptions,
    placeholder: 'Please Select Status',
  },
  {
    name: 'Operation Type',
    type: 'select',
    key: 'op_type',
    mode: 'multiple',
    value: '',
    options: InterfaceOperationTypeOptions,
  },
];

export const InterfaceBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Permission Config',
    icon: 'antd-SettingOutlined',
  },
  {
    name: 'Interface',
  },
];
export const InterfaceColumns: ColumnProps<PermissionAPI.InterfaceItem>[] = [
  {
    title: 'Interface',
    dataIndex: 'itf_name',
    width: 200,
    sorter: (a, b) =>
      a.itf_name.slice(0, 1).charCodeAt(0) -
      b.itf_name.slice(0, 1).charCodeAt(0),
  },
  {
    title: 'ID',
    dataIndex: 'id',
    width: 65,
  },
  {
    title: 'Route Path',
    dataIndex: 'path',
  },
  {
    title: 'Type',
    dataIndex: 'type_desc',
    width: 100,
  },
  {
    title: 'Operation Type',
    dataIndex: 'op_type_desc',
    width: 130,
  },
];
