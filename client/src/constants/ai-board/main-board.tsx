/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-12 16:55:06
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-02 11:21:38
 * @Description:
 */

export const OverviewColorMap: API.NumberType = {
  0: 'rgba(18, 107, 240, 0.05)',
  1: 'rgba(28, 175, 52, 0.05)',
  2: 'rgba(255, 144, 18, 0.05)',
  3: 'rgba(108, 44, 235, 0.05)',
};

export const OverviewFillColorMap: API.NumberType = {
  0: '#126bf0',
  1: '#1caf34',
  2: '#ff9012',
  3: '#6c2ceb',
};

type TopCardsItem = {
  title: string;
  number: keyof BoardAPI.OverviewItem;
  percentage: keyof BoardAPI.OverviewItem;
  isDollar?: boolean;
};
export const OverviewOptions: TopCardsItem[] = [
  {
    title: 'Revenue',
    number: 'revenue',
    percentage: 'revenue_increase',
    isDollar: true,
  },
  {
    title: 'Profit',
    number: 'profit',
    percentage: 'profit_increase',
    isDollar: true,
  },
  {
    title: 'Ecpm',
    number: 'ecpm',
    percentage: 'ecpm_increase',
    isDollar: true,
  },

  { title: 'Request', number: 'request', percentage: 'request_increase' },
];
