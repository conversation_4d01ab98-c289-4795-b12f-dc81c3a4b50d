/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-08 14:54:51
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-09 10:20:45
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import type { TopBarSearchItem } from '@/components/TopBar';
import { ColumnType } from '@/components/Table/FrontTable';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import StatusTag from '@/components/Tag/StatusTag';
export const BreadOptions: BreadcrumbItem[] = [
  {
    name: 'Ree AI',
    icon: 'rix-board',
  },
  {
    name: 'Trading Message',
  },
];

export const SearchOption: TopBarSearchItem[] = [
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: '',
    mode: 'multiple',
    options: [],
  },
  {
    name: 'Sent',
    type: 'input',
    key: 'content',
    value: '',
    isNoExact: true,
  },
  {
    name: 'Code',
    type: 'selectAll',
    key: 'mixed_key',
    value: '',
    mode: 'multiple',
    options: [],
  },
];

export const ColumnOptions: ColumnType<BoardAPI.SentMessageItem>[] = [
  {
    title: 'Tenant',
    width: 180,
    dataIndex: 'tnt_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={`${params.tnt_name}(${params.tnt_id})`}>
        <span>
          {params.tnt_name}({_})
        </span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Sent',
    dataIndex: 'content',
    width: 400,
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Code',
    dataIndex: 'mixed_key',
    width: 200,
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Ads.txt',
    dataIndex: 'ext_1',
    width: 70,
    render: (_) => (
      <StatusTag value={_} statusDescMap={{ 1: 'Yes', 2: 'No' }} />
    ),
  },
  {
    title: 'Created At',
    dataIndex: 'create_time',
    width: 160,
  },
  {
    title: 'Operator',
    dataIndex: 'op_name',
    width: 120,
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
];
