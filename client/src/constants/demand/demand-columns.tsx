/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-09 16:58:2.
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-13 15:59:38
 * @Description:
 */
import { ColumnProps } from 'antd/es/table';
import StatusTag from '@/components/Tag/StatusTag';
import {
  ProfitModelType,
  ProfitModelTypeToString,
  DemandAndSupplyStatusMap,
  DemandAndSupplyStatusDesc,
} from '@/constants';
import {
  AuctionTypeToString,
  ImpTrackingTypeToString,
  NativeFormatTypeToString,
} from '@/constants/demand';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

export const BannerTransferFormatOptions: { label: string; value: number }[] = [
  { label: 'Paused', value: 0 },
  { label: 'Banner2Video', value: 1 },
  { label: 'Banner2Native', value: 2 },
];

export const BannerTransferFormatMap: Record<number, string> = {
  0: 'Paused',
  1: 'Banner2Video',
  2: 'Banner2Native',
};

export const BannerTransferFormatConfig = {
  'Paused': 0,
  'Banner2Video': 1,
  'Banner2Native': 2,
};

export const BurlTrackTypeOptions = [
  { label: 'ADM', value: 1 },
  { label: 'Server', value: 2 }
];

export const BurlTrackTypeMap: Record<number, string> = {
  1: 'ADM',
  2: 'Server'
};

export const BurlTrackType = {
  ADM: 1,
  SERVER: 2
};

export const leftTableColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Publisher ID',
    dataIndex: 'seller_id',
  },
  {
    title: 'Integration Type',
    dataIndex: 'integration_type_desc',
  },
];

export const rightTableColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Publisher ID',
    dataIndex: 'seller_id',
  },
  {
    title: 'Integration Type',
    dataIndex: 'integration_type_desc',
  },
];

export const AuthColumns: ColumnProps<SupplyAPI.SupplyListItem>[] = [
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_}>
        <span>{_}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Publisher ID',
    dataIndex: 'seller_id',
  },
  {
    title: 'Integration Type',
    dataIndex: 'integration_type_desc',
  },
];

export const DemandColumns: ColumnProps<DemandAPI.DemandListItem>[] = [
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    width: 220,
    fixed: 'left',
    ellipsis: { showTitle: false },
    render: (_, row) => {
      const isTesting = row.status === DemandAndSupplyStatusMap.Testing;
      return isTesting ? (
        <div>
          <span>{_}</span>
          <Tooltip title={<span>Created By system, only for testing</span>}>
            <QuestionCircleOutlined
              style={{ paddingLeft: '5px', cursor: 'pointer' }}
            />
          </Tooltip>
        </div>
      ) : (
        <HoverToolTip title={_}>
          <span>{_}</span>
        </HoverToolTip>
      );
    },
  },
  {
    title: 'Tenant',
    dataIndex: 'tnt_id',

    width: 220,
    ellipsis: { showTitle: false },
    render: (_, row) => (
      <HoverToolTip title={_}>
        <span>{`${row.tnt_name}(${_})`}</span>
      </HoverToolTip>
    ),
  },

  {
    title: 'ID',
    width: 100,
    dataIndex: 'buyer_id',
  },
  {
    title: 'Partner',
    width: 150,
    dataIndex: 'partner_name',
    ellipsis: { showTitle: false },
    render: (_) => (
      <HoverToolTip title={_ || '-'}>
        <span>{_ || '-'}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Integration Type',
    width: 150,
    dataIndex: 'integration_type_desc',
  },
  {
    title: 'Auction Type',
    width: 150,
    dataIndex: 'auction_type',
    render: (text) => <>{AuctionTypeToString[text]}</>,
  },
  {
    title: 'Profit Model',
    width: 120,
    dataIndex: 'profit_model',
    render: (text) => <>{ProfitModelTypeToString[text]}</>,
  },
  {
    title: 'Profit(%)',
    width: 100,
    dataIndex: 'profit_ratio',
    render: (txt, params) => (
      <span>
        {params.profit_status === DemandAndSupplyStatusMap.Active ? txt : '-'}
      </span>
    ),
  },
  {
    title: 'Max Pixalate IVT(%)',
    width: 180,
    dataIndex: 'max_pxl_ivt_ratio',
    render: (txt, params) => <span>{+txt === -1 ? 'Unlimited' : txt}</span>,
  },
  {
    title: 'Max Human IVT(%)',
    width: 180,
    dataIndex: 'max_hm_ivt_ratio',
    render: (txt, params) => <span>{+txt === -1 ? 'Unlimited' : txt}</span>,
  },
  {
    title: 'Rev Share(%)',
    width: 120,
    dataIndex: 'rev_share_ratio',
    render: (text, row) =>
      text && row.profit_model === ProfitModelType['Rev Share']
        ? `${text}`
        : '-',
  },
  {
    title: 'Impression Track Type',
    width: 200,
    dataIndex: 'imp_track_type',
    render: (text) => <>{ImpTrackingTypeToString[text]}</>,
  },
  {
    title: 'BURL Track Type',
    width: 150,
    dataIndex: 'burl_track_type',
    render: (_: number) => <>{BurlTrackTypeMap[_]}</>,
  },
  {
    title: 'Native Format',
    width: 150,
    dataIndex: 'native_format',
    render: (_, row) => <>{NativeFormatTypeToString[row.native_format]}</>,
  },
  {
    title: 'Native Root Key',
    width: 150,
    dataIndex: 'native_root_key',
    render: (_, row) => <StatusTag value={_} />,
  },
  {
    title: 'Pass Supply Chain',
    width: 150,
    dataIndex: 'schain_required',
    render: (_, row) => <StatusTag value={_} />,
  },
  {
    title: 'Mraid Traffic Filter',
    width: 150,
    dataIndex: 'filter_mraid',
    render: (_, row) => <StatusTag value={_} />,
  },
  {
    title: 'IFA Required',
    width: 150,
    dataIndex: 'idfa_required',
    render: (_, row) => <StatusTag value={_} />,
  },

  {
    title: 'Multi Format',
    width: 150,
    dataIndex: 'multi_format',
    render: (_, row) => <StatusTag value={_} />,
  },
  {
    title: 'Banner Multi Size',
    width: 150,
    dataIndex: 'banner_multi_size',
    render: (_, row) => <StatusTag value={_} />,
  },
  {
    title: 'Banner Transfer Format',
    width: 200,
    dataIndex: 'banner_transfer_format',
    render: (item: number) => BannerTransferFormatMap[item],
  },
  {
    title: 'Pass Display Manager',
    width: 180,
    dataIndex: 'pass_display_manager',
    render: (_, row) => <StatusTag value={_} />,
  },
  {
    title: (
      <div>
        <span>SKOverlay</span>
        <Tooltip title="Whether to pass skoverlay for iOS traffic">
          <QuestionCircleOutlined
            style={{ paddingLeft: '6px', cursor: 'pointer' }}
          />
        </Tooltip>
      </div>
    ),
    width: 120,
    dataIndex: 'skoverlay',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: (
      <div>
        <span>Auto-Store</span>
        <Tooltip title="Whether to pass auto-store for iOS traffic">
          <QuestionCircleOutlined
            style={{ paddingLeft: '6px', cursor: 'pointer' }}
          />
        </Tooltip>
      </div>
    ),
    width: 120,
    dataIndex: 'autostore',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: (
      <div>
        <span>Block non-store apps</span>
        <Tooltip title="Block requests from apps removed from the app store">
          <QuestionCircleOutlined
            style={{ paddingLeft: '6px', cursor: 'pointer' }}
          />
        </Tooltip>
      </div>
    ),
    width: 190,
    dataIndex: 'block_off_store_app',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: (
      <div>
        <span>Additional QPS</span>
        <Tooltip title="Allocate additional QPS to Demand sources with high ECPR when available QPS is constrained">
          <QuestionCircleOutlined
            style={{ paddingLeft: '6px', cursor: 'pointer' }}
          />
        </Tooltip>
      </div>
    ),
    width: 220,
    dataIndex: 'auto_qps',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: 'Status',
    width: 100,
    dataIndex: 'status',
    render: (_, row) => (
      <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
    ),
  },
];
