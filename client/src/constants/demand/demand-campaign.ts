/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-02-27 18:04:57
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-27 18:04:58
 * @Description:
 */
export const demandCampaign = Object.seal({
  Level: {
    '1': 'Country',
    '2': 'Country',
    '3': 'Platform',
    '4': 'Moblie OS',
    '5': 'Mobile Inventory',
    '6': 'Category',
    '7': 'Category',
    '8': 'Ad Platform',
    '9': 'Site',
    '10': 'Moblie APP',
    '11': 'Moblie APP',
    '12': '<PERSON> Size',
    '13': 'Ad Size',
    '14': 'Price',
    '15': 'ServerRegion',
    '16': 'Network',
    '17': 'Network',
  },
  Country: {
    AFG: 'Afghanistan',
    ALB: 'Albania',
    DZA: 'Algeria',
    ASM: 'American Samoa',
    AND: 'Andorra',
    AGO: 'Angola',
    AIA: 'Anguilla',
    ATA: 'Antarctica',
    ATG: 'Antigua and Barbuda',
    ARG: 'Argentina',
    ARM: 'Armenia',
    ABW: 'Aruba',
    AUS: 'Australia',
    AUT: 'Austria',
    AZE: 'Azerbaijan',
    BHS: 'Bahamas',
    BHR: 'Bahrain',
    BGD: 'Bangladesh',
    BRB: 'Barbados',
    BLR: 'Belarus',
    BEL: 'Belgium',
    BLZ: 'Belize',
    BEN: 'Benin',
    BMU: 'Bermuda',
    BTN: 'Bhutan',
    BOL: 'Bolivia',
    BIH: 'Bosnia and Herzegovina',
    BWA: 'Botswana',
    BRA: 'Brazil',
    IOT: 'British Indian Ocean Territory',
    VGB: 'British Virgin Islands',
    BRN: 'Brunei',
    BGR: 'Bulgaria',
    BFA: 'Burkina Faso',
    BDI: 'Burundi',
    KHM: 'Cambodia',
    CMR: 'Cameroon',
    CAN: 'Canada',
    CPV: 'Cape Verde',
    CYM: 'Cayman Islands',
    CAF: 'Central African Republic',
    TCD: 'Chad',
    CHL: 'Chile',
    CHN: 'China',
    CXR: 'Christmas Island',
    CCK: 'Cocos Islands',
    COL: 'Colombia',
    COM: 'Comoros',
    COK: 'Cook Islands',
    CRI: 'Costa Rica',
    HRV: 'Croatia',
    CUB: 'Cuba',
    CUW: 'Curacao',
    CYP: 'Cyprus',
    CZE: 'Czech Republic',
    COD: 'Democratic Republic of the Congo',
    DNK: 'Denmark',
    DJI: 'Djibouti',
    DMA: 'Dominica',
    DOM: 'Dominican Republic',
    TLS: 'East Timor',
    ECU: 'Ecuador',
    EGY: 'Egypt',
    SLV: 'El Salvador',
    GNQ: 'Equatorial Guinea',
    ERI: 'Eritrea',
    EST: 'Estonia',
    ETH: 'Ethiopia',
    FLK: 'Falkland Islands',
    FRO: 'Faroe Islands',
    FJI: 'Fiji',
    FIN: 'Finland',
    FRA: 'France',
    PYF: 'French Polynesia',
    GAB: 'Gabon',
    GMB: 'Gambia',
    GEO: 'Georgia',
    DEU: 'Germany',
    GHA: 'Ghana',
    GIB: 'Gibraltar',
    GRC: 'Greece',
    GRL: 'Greenland',
    GRD: 'Grenada',
    GUM: 'Guam',
    GTM: 'Guatemala',
    GGY: 'Guernsey',
    GIN: 'Guinea',
    GNB: 'Guinea-Bissau',
    GUY: 'Guyana',
    HTI: 'Haiti',
    HND: 'Honduras',
    HKG: 'Hong Kong(China)',
    HUN: 'Hungary',
    ISL: 'Iceland',
    IND: 'India',
    IDN: 'Indonesia',
    IRN: 'Iran',
    IRQ: 'Iraq',
    IRL: 'Ireland',
    IMN: 'Isle of Man',
    ISR: 'Israel',
    ITA: 'Italy',
    CIV: 'Ivory Coast',
    JAM: 'Jamaica',
    JPN: 'Japan',
    JEY: 'Jersey',
    JOR: 'Jordan',
    KAZ: 'Kazakhstan',
    KEN: 'Kenya',
    KIR: 'Kiribati',
    XKX: 'Kosovo',
    KWT: 'Kuwait',
    KGZ: 'Kyrgyzstan',
    LAO: 'Laos',
    LVA: 'Latvia',
    LBN: 'Lebanon',
    LSO: 'Lesotho',
    LBR: 'Liberia',
    LBY: 'Libya',
    LIE: 'Liechtenstein',
    LTU: 'Lithuania',
    LUX: 'Luxembourg',
    MAC: 'Macau',
    MKD: 'Macedonia',
    MDG: 'Madagascar',
    MWI: 'Malawi',
    MYS: 'Malaysia',
    MDV: 'Maldives',
    MLI: 'Mali',
    MLT: 'Malta',
    MHL: 'Marshall Islands',
    MRT: 'Mauritania',
    MUS: 'Mauritius',
    MYT: 'Mayotte',
    MEX: 'Mexico',
    FSM: 'Micronesia',
    MDA: 'Moldova',
    MCO: 'Monaco',
    MNG: 'Mongolia',
    MNE: 'Montenegro',
    MSR: 'Montserrat',
    MAR: 'Morocco',
    MOZ: 'Mozambique',
    MMR: 'Myanmar',
    NAM: 'Namibia',
    NRU: 'Nauru',
    NPL: 'Nepal',
    NLD: 'Netherlands',
    ANT: 'Netherlands Antilles',
    NCL: 'New Caledonia',
    NZL: 'New Zealand',
    NIC: 'Nicaragua',
    NER: 'Niger',
    NGA: 'Nigeria',
    NIU: 'Niue',
    PRK: 'North Korea',
    MNP: 'Northern Mariana Islands',
    NOR: 'Norway',
    OMN: 'Oman',
    PAK: 'Pakistan',
    PLW: 'Palau',
    PSE: 'Palestine',
    PAN: 'Panama',
    PNG: 'Papua New Guinea',
    PRY: 'Paraguay',
    PER: 'Peru',
    PHL: 'Philippines',
    PCN: 'Pitcairn',
    POL: 'Poland',
    PRT: 'Portugal',
    PRI: 'Puerto Rico',
    QAT: 'Qatar',
    COG: 'Republic of the Congo',
    REU: 'Reunion',
    ROU: 'Romania',
    RUS: 'Russia',
    RWA: 'Rwanda',
    BLM: 'Saint Barthelemy',
    SHN: 'Saint Helena',
    KNA: 'Saint Kitts and Nevis',
    LCA: 'Saint Lucia',
    MAF: 'Saint Martin',
    SPM: 'Saint Pierre and Miquelon',
    VCT: 'Saint Vincent and the Grenadines',
    WSM: 'Samoa',
    SMR: 'San Marino',
    STP: 'Sao Tome and Principe',
    SAU: 'Saudi Arabia',
    SEN: 'Senegal',
    SRB: 'Serbia',
    SYC: 'Seychelles',
    SLE: 'Sierra Leone',
    SGP: 'Singapore',
    SXM: 'Sint Maarten',
    SVK: 'Slovakia',
    SVN: 'Slovenia',
    SLB: 'Solomon Islands',
    SOM: 'Somalia',
    ZAF: 'South Africa',
    KOR: 'South Korea',
    SSD: 'South Sudan',
    ESP: 'Spain',
    LKA: 'Sri Lanka',
    SDN: 'Sudan',
    SUR: 'Suriname',
    SJM: 'Svalbard and Jan Mayen',
    SWZ: 'Swaziland',
    SWE: 'Sweden',
    CHE: 'Switzerland',
    SYR: 'Syria',
    TWN: 'Taiwan(China)',
    TJK: 'Tajikistan',
    TZA: 'Tanzania',
    THA: 'Thailand',
    TGO: 'Togo',
    TKL: 'Tokelau',
    TON: 'Tonga',
    TTO: 'Trinidad and Tobago',
    TUN: 'Tunisia',
    TUR: 'Turkey',
    TKM: 'Turkmenistan',
    TCA: 'Turks and Caicos Islands',
    TUV: 'Tuvalu',
    VIR: 'U.S. Virgin Islands',
    UGA: 'Uganda',
    UKR: 'Ukraine',
    ARE: 'United Arab Emirates',
    GBR: 'United Kingdom',
    USA: 'United States',
    URY: 'Uruguay',
    UZB: 'Uzbekistan',
    VUT: 'Vanuatu',
    VAT: 'Vatican',
    VEN: 'Venezuela',
    VNM: 'Vietnam',
    WLF: 'Wallis and Futuna',
    ESH: 'Western Sahara',
    YEM: 'Yemen',
    ZMB: 'Zambia',
    ZWE: 'Zimbabwe',
  },
  Platform: {
    '1': 'Mobile/Tablet',
    '2': 'Personal Computer',
    '3': 'Connected TV',
    '4': 'Phone',
    '5': 'Tablet',
    '6': 'Connected Device',
    '7': 'Set Top Box',
    '0': 'Unknown',
  },
  MoblieOS: {
    '0': 'Undefined',
    '1': 'iOS',
    '2': 'Android',
    '3': 'Other',
    '4': 'Linux',
    '5': 'MacOS',
    '6': 'Windows',
    // OTT/CTV 预留11~29
    '11': 'tvOS',
    '12': 'Roku',
    '13': 'Amazon',
    '14': 'Microsoft',
    '15': 'Samsung Smart TV',
    '16': 'LG Smart TV',
    '17': 'Sony Playstation',
    '18': 'Vizio',
    '19': 'Philips Smart TV',
    '50': 'Tizen',
    '51': 'KaiOS',
  },

  // !这里更改过 之前是1 -> website , 2 -> inapp
  MoblieInventory: {
    '2': 'Website',
    '1': 'Inapp',
  },
  Category: {
    IAB1: 'Arts & Entertainment',
    IAB2: 'Automotive',
    IAB3: 'Business',
    IAB4: 'Careers',
    IAB5: 'Education',
    IAB6: 'Family & Parenting',
    IAB7: 'Health & Fitness',
    IAB8: 'Food & Drink',
    IAB9: 'Hobbies & Interests',
    IAB10: 'Home & Garden',
    IAB11: 'Law,Gov‘t & Politics',
    IAB12: 'News',
    IAB13: 'Personal Finance',
    IAB14: 'Society',
    IAB15: 'Science',
    IAB16: 'Pets',
    IAB17: 'Sports',
    IAB18: 'Style & Fashion',
    IAB19: 'Technology & Computing',
    IAB20: 'Travel',
    IAB21: 'Real Estate',
    IAB22: 'Shopping',
    IAB23: 'Religion & Spirituality',
    IAB24: 'Uncategorized',
    IAB25: 'Non-Standard Content',
    IAB26: 'Illegal Content',
  },
  AdPlatform: {
    // '1': 'Text Ads',
    '2': 'Image Ads',
    '3': 'Native Ads',
    '4': 'Video',
    // '5': 'Instream Midroll Video',
    // '6': 'Instream Postroll Video',
    // '7': 'Outstream Video',
    '8': 'Interstital Video',
    '9': 'Rewarded Video',
  },
  Network: {
    0: 'Unknown',
    1: 'Ethernet',
    2: 'WIFI',
    3: 'Cellular Network – Unknown Generation',
    4: 'Cellular Network – 2G',
    5: 'Cellular Network – 3G',
    6: 'Cellular Network – 4G',
  },
  Adformat: ['', 'Banner', 'Native', 'Video', 'Rewarded Video'],
  Type: {
    '0': 'Open Auction',
  },
  ServerRegion: ['', 'USE', 'APAC'],
  TimeSlot: {
    '0': '00-01',
    '1': '01-02',
    '2': '02-03',
    '3': '03-04',
    '4': '04-05',
    '5': '05-06',
    '6': '06-07',
    '7': '07-08',
    '8': '08-09',
    '9': '09-10',
    '10': '10-11',
    '11': '11-12',
    '12': '12-13',
    '13': '13-14',
    '14': '14-15',
    '15': '15-16',
    '16': '16-17',
    '17': '17-18',
    '18': '18-19',
    '19': '19-20',
    '20': '20-21',
    '21': '21-22',
    '22': '22-23',
    '23': '23-00',
  },
});
