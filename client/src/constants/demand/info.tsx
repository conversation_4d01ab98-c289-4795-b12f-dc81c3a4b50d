/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-12-08 13:58:36
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-14 19:25:19
 * @Description:
 */

import { Tooltip } from 'antd';
import StatusTag from '@/components/Tag/StatusTag';
import {
  DemandAndSupplyStatusDesc,
  DemandAndSupplyStatusMap,
  ProfitModelTypeToString,
  StatusDesc,
} from '..';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import RixEngineFont from '@/components/RixEngineFont';
import { AuctionTypeToString, ImpTrackingTypeToString, NativeFormatTypeToString, ZipMap } from '.';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { InfoBarTabsProps } from '@/components/InfoBar';
import { BannerTransferFormatMap, BurlTrackTypeMap } from './demand-columns';
export const DemandInfoTabs: InfoBarTabsProps<DemandAPI.DemandListItem> = [
  {
    title: 'Basic',
    key: 'basic',
    access: 'DemandPermission',
    titleIcon: {
      icon: <RixEngineFont type="rix-edit-detail" style={{ margin: 0 }} />,
    },
    rowNum: 3, // 每行号展示几列
    columns: [
      {
        title: 'Advertiser',
        dataIndex: 'buyer_name',
        render: (_, row, maxWidth) => {
          const isTesting = row.status === DemandAndSupplyStatusMap.Testing;
          return isTesting ? (
            <div>
              <span>{_}</span>
              <Tooltip title={<span>Created By system, only for testing</span>}>
                <QuestionCircleOutlined
                  style={{ paddingLeft: '5px', cursor: 'pointer' }}
                />
              </Tooltip>
            </div>
          ) : (
            <HoverToolTip title={_} maxWidth={maxWidth}>
              <span>{_}</span>
            </HoverToolTip>
          );
        },
      },
      {
        title: 'ID',
        dataIndex: 'buyer_id',
      },
      {
        title: 'Integration Type',
        dataIndex: 'integration_type_desc',
      },
      {
        title: 'Auction Type',
        dataIndex: 'auction_type',
        render: (text) => <>{AuctionTypeToString[text]}</>,
      },
      {
        title: 'Impression Track Type',
        dataIndex: 'imp_track_type',
        render: (text) => <>{ImpTrackingTypeToString[text]}</>,
      },
      {
        title: 'BURL Track Type',
        dataIndex: 'burl_track_type',
        render: (_) => <>{BurlTrackTypeMap[_]}</>,
      },
      {
        title: 'Native Format',
        dataIndex: 'native_format',
        render: (text) => <>{NativeFormatTypeToString[text]}</>,
      },
      {
        title: 'Native Root Key',
        dataIndex: 'native_root_key',
        render: (text) => <StatusTag value={text} />,
      },
      {
        title: 'Profit',
        dataIndex: 'profit_status',
        render: (_) => (
          <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
        ),
      },
      {
        title: 'Profit(%)',
        width: 100,
        dataIndex: 'profit_ratio',
        render: (txt, params) => (
          <span>
            {params.profit_status === DemandAndSupplyStatusMap.Active
              ? txt
              : '-'}
          </span>
        ),
      },
      {
        title: 'Profit Model',
        width: 120,
        dataIndex: 'profit_model',
        render: (text) => <>{ProfitModelTypeToString[text]}</>,
      },

      {
        title: 'Pass Supply Chain',
        width: 150,
        dataIndex: 'schain_required',
        tooltip: () => 'Whether to send schain to Demand',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },

      {
        title: 'Mraid Traffic Filter',
        width: 150,
        dataIndex: 'filter_mraid',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },
      {
        title: 'Max Pixalate IVT(%)',
        dataIndex: 'max_pxl_ivt_ratio',
        render: (txt) => <span>{+txt === -1 ? 'Unlimited' : txt}</span>,
      },
      {
        title: 'Max Human IVT(%)',
        dataIndex: 'max_hm_ivt_ratio',
        render: (txt) => <span>{+txt === -1 ? 'Unlimited' : txt}</span>,
      },
      {
        title: 'IFA Required',
        dataIndex: 'idfa_required',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },
      {
        title: 'Multi Format',
        dataIndex: 'multi_format',
        tooltip: () => 'Support both a banner ad and a video ad format within a single BidRequest.imp at the same index (e.g. imp[0])',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },
      {
        title: 'Banner Multi Size',
        dataIndex: 'banner_multi_size',
        tooltip: () => 'Add ad size 300*250 for requests with a 320*50 banner',
        render: (_) => <StatusTag value={_} statusDescMap={StatusDesc} />,
      },
      {
        title: 'Banner Transfer Format',
        dataIndex: 'banner_transfer_format',
        render: (_, row) => BannerTransferFormatMap[row.banner_transfer_format],
      },
      {
        title: 'Pass Display Manager',
        dataIndex: 'pass_display_manager',
        tooltip: () => 'Whether to send displaymanager to Demand',
        render: (_) => <StatusTag value={_}/>,
      },
      {
        title: 'SKOverlay',
        dataIndex: 'skoverlay',
        tooltip: () => 'Whether to pass skoverlay for iOS traffic',
        render: (_) => <StatusTag value={_}/>,
      },
      {
        title: 'Auto-Store',
        dataIndex: 'autostore',
        tooltip: () => 'Whether to pass auto-store for iOS traffic',
        render: (_) => <StatusTag value={_}/>,
      },
      {
        title: 'Block non-store apps',
        tooltip: () => 'Block requests from apps removed from the app store',
        dataIndex: 'block_off_store_app',
        render: (_) => <StatusTag value={_} />,
      },
      {
        title: 'Additional QPS',
        tooltip: () => 'Allocate additional QPS to Demand sources with high ECPR when available QPS is constrained',
        dataIndex: 'auto_qps',
        render: (_) => <StatusTag value={_} />,
      },
      {
        title: 'Status',
        dataIndex: 'status',
        render: (_) => (
          <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
        ),
      },
      {
        title: 'Partner',
        dataIndex: 'partner_id',
        render: (_, row, maxWidth) => {
          const val = +_ > 0 ? `${row.partner_name}(${_})` : '-';
          return (
            <HoverToolTip title={val} maxWidth={maxWidth}>
              <span>{val}</span>
            </HoverToolTip>
          );
        },
      },
    ],
  },
  {
    title: 'Endpoint',
    key: 'endpoint',
    access: 'DemandEndpointPermission',

    children: [
      {
        title: 'USE | Banner',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'USE_Banner_url',
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'USE_Banner_socket_timeout',
          },
          {
            title: 'Gzip',
            dataIndex: 'USE_Banner_gzip',
            render: (_) => <StatusTag value={_} statusDescMap={ZipMap} />,
          },
        ],
      },
      {
        title: 'USE | Native',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'USE_Native_url',
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'USE_Native_socket_timeout',
          },
          {
            title: 'Gzip',
            dataIndex: 'USE_Native_gzip',
            render: (_) => <StatusTag value={_} statusDescMap={ZipMap} />,
          },
        ],
      },
      {
        title: 'USE | Video',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'USE_Video_url',
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'USE_Video_socket_timeout',
          },
          {
            title: 'Gzip',
            dataIndex: 'USE_Video_gzip',
            render: (_) => <StatusTag value={_} statusDescMap={ZipMap} />,
          },
        ],
      },
      {
        title: 'APAC | Banner',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'APAC_Banner_url',
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'APAC_Banner_socket_timeout',
          },
          {
            title: 'Gzip',
            dataIndex: 'APAC_Banner_gzip',
            render: (_) => <StatusTag value={_} statusDescMap={ZipMap} />,
          },
        ],
      },
      {
        title: 'APAC | Native',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'APAC_Native_url',
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'APAC_Native_socket_timeout',
          },
          {
            title: 'Gzip',
            dataIndex: 'APAC_Native_gzip',
            render: (_) => <StatusTag value={_} statusDescMap={ZipMap} />,
          },
        ],
      },
      {
        title: 'APAC | Video',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'APAC_Video_url',
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'APAC_Video_socket_timeout',
          },
          {
            title: 'Gzip',
            dataIndex: 'APAC_Video_gzip',
            render: (_) => <StatusTag value={_} statusDescMap={ZipMap} />,
          },
        ],
      },
      {
        title: 'EUW | Banner',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'EUW_Banner_url',
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'EUW_Banner_socket_timeout',
          },
          {
            title: 'Gzip',
            dataIndex: 'EUW_Banner_gzip',
            render: (_) => <StatusTag value={_} statusDescMap={ZipMap} />,
          },
        ],
      },
      {
        title: 'EUW | Native',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'EUW_Native_url',
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'EUW_Native_socket_timeout',
          },
          {
            title: 'Gzip',
            dataIndex: 'EUW_Native_gzip',
            render: (_) => <StatusTag value={_} statusDescMap={ZipMap} />,
          },
        ],
      },
      {
        title: 'EUW | Video',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            dataIndex: 'EUW_Video_url',
          },
          {
            title: 'Socket Timeout(ms)',
            dataIndex: 'EUW_Video_socket_timeout',
          },
          {
            title: 'Gzip',
            dataIndex: 'EUW_Video_gzip',
            render: (_) => <StatusTag value={_} statusDescMap={ZipMap} />,
          },
        ],
      },
    ],
  },
  {
    title: 'Account',
    key: 'account',
    access: 'DemandAccountPermission',

    children: [
      {
        title: 'Advertiser Reporting',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'URL',
            showCopy: true,
            dataIndex: 'reporting_url',
            copyText: (row) => {
              return `${location.origin}`;
            },
            render: (txt, params) => {
              return `${location.origin}`;
            },
          },
          {
            title: 'User Name',
            showCopy: true,
            dataIndex: 'demand_account_name',
          },
          {
            title: 'Status',
            width: 85,
            dataIndex: 'demand_account_status',
            render: (_) => (
              <StatusTag value={_} statusDescMap={DemandAndSupplyStatusDesc} />
            ),
          },
        ],
      },
      {
        title: 'Reporting API',
        type: 'collapse',
        rowNum: 2,
        columns: [
          {
            title: 'Sid',
            showCopy: true,
            dataIndex: 'buyer_id',
          },
          {
            title: 'Token',
            dataIndex: 'token',
            showCopy: true,
          },
        ],
      },
    ],
  },
  {
    title: 'Authorization',
    key: 'authorization',
    access: 'DemandAuthorizationPermission',

    type: 'table',
    rowKey: 'auth_seller_id',
    tableData: [],
    columns: [
      {
        title: 'Publisher',
        dataIndex: 'auth_seller_name',
        ellipsis: { showTitle: false },
        width: 220,
        render: (_) => (
          <HoverToolTip title={_}>
            <span>{_}</span>
          </HoverToolTip>
        ),
      },
      {
        title: 'Publisher ID',
        width: 220,
        dataIndex: 'auth_seller_id',
        ellipsis: { showTitle: false },
        render: (_) => (
          <HoverToolTip title={_}>
            <span>{_}</span>
          </HoverToolTip>
        ),
      },
      {
        title: 'Integration Type',
        width: 150,
        dataIndex: 'integration_type_desc',
      },
    ],
  },
];
