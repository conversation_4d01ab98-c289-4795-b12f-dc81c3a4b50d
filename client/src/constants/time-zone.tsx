/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-02-27 19:07:07
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-27 19:07:08
 * @Description:
 */

export const TimeZoneOptions = [
  { label: '(UTC-12), International Date Line West', value: 'Etc/GMT+12' },
  { label: '(UTC-11), Niue, US minor outlying islands', value: 'Etc/GMT+11' },
  { label: '(UTC-10), Hawaii', value: 'Etc/GMT+10' },
  { label: '(UTC-9), Alaska', value: 'Etc/GMT+9' },
  {
    label: '(UTC-8), Los Angeles, San Francisco, Vancouver, Seattle, Las Vegas',
    value: 'Etc/GMT+8',
  },
  {
    label:
      '(UTC-7), Mountain Time, Arizona, Mazatlan, Phoenix, Salt Lake City, Denver, Edmonton',
    value: 'Etc/GMT+7',
  },
  {
    label:
      '(UTC-6), Mexico City, Monterrey, Chicago, Houston, New Orleans, Memphis',
    value: 'Etc/GMT+6',
  },
  {
    label:
      '(UTC-5), Colombia, Toronto, Ecuador, Peru, Cuba, New York, Hamilton, Santiago',
    value: 'Etc/GMT+5',
  },
  { label: '(UTC-4), Venezuela, Bolivia, Paraguay, Chile', value: 'Etc/GMT+4' },
  {
    label:
      '(UTC-3), Brazil, Santiago, El Salvador, Brasilia, Uruguay, Argentina, Suriname',
    value: 'Etc/GMT+3',
  },
  { label: '(UTC-2), South Georgia, Fernando de Noronha', value: 'Etc/GMT+2' },
  { label: '(UTC-1), Cape Verde, Azores', value: 'Etc/GMT+1' },
  { label: '(UTC+0), London,Iceland,Greenwich Mean Time', value: 'Etc/UTC' },
  {
    label: '(UTC+1), Germany, Ireland, France, Italy, Spain, Morocco',
    value: 'Etc/GMT-1',
  },
  {
    label:
      '(UTC+2), Turkey, Israel, South Africa, Istanbul, Cairo, Athens, Damascus',
    value: 'Etc/GMT-2',
  },
  {
    label: '(UTC+3), Russia, Baghdad, Kuwait, Moscow, St. Petersburg',
    value: 'Etc/GMT-3',
  },
  // { label: '(UTC+3.5),Iran Standard Time, Tehran, Mashhad', value: 'Etc/GMT-4' },
  {
    label: '(UTC+4), Abu Dhabi, Tbilisi, Dubai, Mauritius, Port Louis',
    value: 'Etc/GMT-4',
  },
  {
    label:
      '(UTC+5), Pakistan, Turkmenistan, Maldives, Uzbekistan, Amebia, Tajikistan',
    value: 'Etc/GMT-5',
  },
  // { label: '(UTC+5.5),India, Mumbai, New Delhi, Bangalore, Kolkata', value: 'Etc/GMT-7' },
  {
    label: '(UTC+6), Novosibirsk, Kyrgyzstan, Bangladesh, Bhutan',
    value: 'Etc/GMT-6',
  },
  { label: '(UTC+7), Thailand, Bangkok, Hanoi, Jakarta', value: 'Etc/GMT-7' },
  {
    label: '(UTC+8), China,Malaysia, Philippines, Singapore',
    value: 'Etc/GMT-8',
  },
  {
    label:
      '(UTC+9), Japan, South Korea, Tokyo, Seoul, Sapporo, Osaka, East Timor',
    value: 'Etc/GMT-9',
  },
  {
    label:
      '(UTC+10), Australia, Vladivostok, Guam, Canberra, Melbourne, Sydney, Papua New Guinea',
    value: 'Etc/GMT-10',
  },
  { label: '(UTC+11), Solomon Islands, Sakhalin', value: 'Etc/GMT-11' },
  {
    label:
      '(UTC+12), New Zealand, Auckland, Wellington, Fiji, New Zealand, Nauru',
    value: 'Etc/GMT-12',
  },
];

export const AllTimeZone = TimeZoneOptions.map((v) => v.value);

export const TimeZoneMap: { [key: string]: string } = {
  'Etc/GMT+12': 'UTC-12',
  'Etc/GMT+11': 'UTC-11',
  'Etc/GMT+10': 'UTC-10',
  'Etc/GMT+9': 'UTC-9',
  'Etc/GMT+8': 'UTC-8',
  'Etc/GMT+7': 'UTC-7',
  'Etc/GMT+6': 'UTC-6',
  'Etc/GMT+5': 'UTC-5',
  'Etc/GMT+4': 'UTC-4',
  'Etc/GMT+3': 'UTC-3',
  'Etc/GMT+2': 'UTC-2',
  'Etc/GMT+1': 'UTC-1',
  'Etc/UTC': 'UTC+0',
  'Etc/GMT-12': 'UTC+12',
  'Etc/GMT-11': 'UTC+11',
  'Etc/GMT-10': 'UTC+10',
  'Etc/GMT-9': 'UTC+9',
  'Etc/GMT-8': 'UTC+8',
  'Etc/GMT-7': 'UTC+7',
  'Etc/GMT-6': 'UTC+6',
  'Etc/GMT-5': 'UTC+5',
  'Etc/GMT-4': 'UTC+4',
  'Etc/GMT-3': 'UTC+3',
  'Etc/GMT-2': 'UTC+2',
  'Etc/GMT-1': 'UTC+1',
};

export const TimeZoneMapLabel: any = {
  'Etc/GMT+12': '(UTC-12), International Date Line West',
  'Etc/GMT+11': '(UTC-11), Niue, US minor outlying islands',
  'Etc/GMT+10': '(UTC-10), Hawaii',
  'Etc/GMT+9': '(UTC-9), Alaska',
  'Etc/GMT+8':
    '(UTC-8), Los Angeles, San Francisco, Vancouver, Seattle, Las Vegas',
  'Etc/GMT+7':
    '(UTC-7), Mountain Time, Arizona, Mazatlan, Phoenix, Salt Lake City, Denver, Edmonton',
  'Etc/GMT+6':
    '(UTC-6), Mexico City, Monterrey, Chicago, Houston, New Orleans, Memphis',
  'Etc/GMT+5':
    '(UTC-5), Colombia, Toronto, Ecuador, Peru, Cuba, New York, Hamilton, Santiago',
  'Etc/GMT+4': '(UTC-4), Venezuela, Bolivia, Paraguay, Chile',
  'Etc/GMT+3':
    '(UTC-3), Brazil, Santiago, El Salvador, Brasilia, Uruguay, Argentina, Suriname',
  'Etc/GMT+2': '(UTC-2), South Georgia, Fernando de Noronha',
  'Etc/GMT+1': '(UTC-1), Cape Verde, Azores',
  'Etc/UTC': '(UTC+0) London,Iceland, Greenwich Mean Time',
  'Etc/GMT-12':
    '(UTC+12), New Zealand, Auckland, Wellington, Fiji, New Zealand, Nauru',
  'Etc/GMT-11': '(UTC+11), Solomon Islands, Sakhalins',
  'Etc/GMT-10':
    '(UTC+10), Australia, Vladivostok, Guam, Canberra, Melbourne, Sydney, Papua New Guinea',
  'Etc/GMT-9':
    '(UTC+9), Japan, South Korea, Tokyo, Seoul, Sapporo, Osaka, East Timor',
  'Etc/GMT-8': '(UTC+8), China,Malaysia, Philippines, Singapore',
  'Etc/GMT-7': '(UTC+7), Thailand, Bangkok, Hanoi, Jakarta',
  'Etc/GMT-6': '(UTC+6), Novosibirsk, Kyrgyzstan, Bangladesh, Bhutan',
  'Etc/GMT-5':
    '(UTC+5), Pakistan, Turkmenistan, Maldives, Uzbekistan, Amebia, Tajikistan',
  'Etc/GMT-4': '(UTC+4), Abu Dhabi, Tbilisi, Dubai, Mauritius, Port Louis',
  'Etc/GMT-3': '(UTC+3), Russia, Baghdad, Kuwait, Moscow, St. Petersburg',
  'Etc/GMT-2':
    '(UTC+2), Turkey, Israel, South Africa, Istanbul, Cairo, Athens, Damascus',
  'Etc/GMT-1': '(UTC+1), Germany, Ireland, France, Italy, Spain, Morocco',
};
