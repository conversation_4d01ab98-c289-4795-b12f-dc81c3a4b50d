/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-28 10:57:12
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-29 12:11:57
 * @Description:
 */
// ?types
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';

// ?utils
import StatusTag from '@/components/Tag/StatusTag';
import { objectFlip } from '@/utils';
import moment from 'moment-timezone';
export const ExportStatusMap = {
  Creating: 1,
  Created: 2,
  // Expired: 3,
  Failed: 4,
};
export const ExportStatusDesc = objectFlip(ExportStatusMap);
export const ExportStatusOptions = [
  {
    label: 'Creating',
    value: 1,
  },
  {
    label: 'Created',
    value: 2,
  },
  // {
  //   label: 'Expired',
  //   value: 3
  // },
  {
    label: 'Failed',
    value: 4,
  },
];

export const ExportBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report',
  },
  {
    name: 'Export Log',
  },
];

export const ExportType = {
  1: 'Full Reporting',
  2: 'Advertiser Billing Reporting',
  3: 'Publisher Billing Reporting',
  // 4: 'Advertiser Reporting',
  // 5: 'Publisher Reporting',
  6: 'Publisher Block Reporting',
  7: 'Advertiser Block Reporting',
};
export const ExportTypeOptions = [
  { label: 'Full Reporting', value: 1 },
  { label: 'Advertiser Billing Reporting', value: 2 },
  { label: 'Publisher Billing Reporting', value: 3 },
  // { label: 'Advertiser Reporting', value: 4 },
  // { label: 'Publisher Reporting', value: 5 },
  { label: 'Publisher Block Reporting', value: 6 },
  { label: 'Advertiser Block Reporting', value: 7 },
];
export const ExportTypeDesc = objectFlip(ExportType);
export const ExportSearchOptions: TopBarSearchItem[] = [
  {
    name: 'Export From',
    type: 'select',
    key: 'type',
    mode: 'multiple',
    value: [],
    options: ExportTypeOptions,
  },
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: [],
    options: ExportStatusOptions,
    placeholder: 'Please Select Status',
  },
];
const ExportLogStatusMap: { [key: number]: string } = {
  1: '#FFB114',
  2: '#1CD880',
  // 3: ColorMap.red,,
  4: '#FA5A42',
};
export const ExportColumns: ColumnProps<FullReportingAPI.ExportedReportItem>[] =
  [
    {
      title: 'Name',
      dataIndex: 'name',
      width: 220,
    },
    {
      title: 'Export From',
      dataIndex: 'type_desc',
      width: 120,
    },
    {
      title: 'Status',
      width: 80,
      dataIndex: 'status',
      render: (_, row) => {
        const diffTime = moment().diff(moment(row.create_time), 'minutes');
        let status = row.status;
        let err_msg = row.err_msg;
        let type = row.status_desc.toLowerCase();
        // 15分钟后，如果还是creating状态，视为任务failed
        if (diffTime > 15 && row.status === ExportStatusMap.Creating) {
          status = ExportStatusMap.Failed;
          type = 'failed';
          err_msg = 'Task timeout, please try again later';
        }
        return (
          <div>
            <StatusTag
              value={_}
              statusDescMap={ExportStatusDesc}
              cusStatusMap={ExportLogStatusMap}
              tips={status === 4 ? err_msg : ''}
            />
          </div>
        );
      },
    },
    {
      title: 'Created At',
      dataIndex: 'create_time',
      width: 120,
    },
  ];
