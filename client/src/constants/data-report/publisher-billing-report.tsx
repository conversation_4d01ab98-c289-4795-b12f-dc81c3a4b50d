/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-27 21:59:36
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 16:45:37
 * @Description:
 */

// ?types
import { BreadcrumbItem } from '@/components/Breadcrumb';
import {
  TopBarSearchItem,
  CheckboxUniqueKeyOptionsType,
} from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';

// ?components
import HoverToolTip from '@/components/Tooltip/HoverTooltip';

// ?utils
import { formatMoney } from '@/utils';
import { Country, CountryOptions } from '../country';
import { AdFormatOptions } from '../config/atc';
import { AdFormatToLabel } from '@/constants';

// export const CalculateMetrics = [
//   'profit',
//   'profit_rate',
//   'total_request',
//   'request',
// ];
export const BreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report',
  },
  {
    name: 'Billing Report',
  },
  {
    name: 'Publisher',
  },
];

const DimensionsOptions = [
  { label: 'Date', value: 'day' },
  { label: 'Month', value: 'month' },
  { label: 'Tenant', value: 'tnt_id' },
  { label: 'Publisher', value: 'seller_id' },
  { label: 'Advertiser', value: 'buyer_id' },
  { label: 'Pub Partner', value: 'partner_id' },
  { label: 'Bundle', value: 'app_bundle_id' },
  { label: 'Country', value: 'country' },
  { label: 'Ad Format', value: 'ad_format' },
  { label: 'Ad Size', value: 'ad_size' },
];
export const SearchOptions: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],
    timeLimit: 366,
  },
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: [],
    options: [],
    mode: 'multiple',
    // rules: [{ required: true, message: 'Please Select Tenant' }],
    // limitSelectCount: 5,
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    mode: 'multiple',
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple',
  },
  {
    name: 'Pub Partner',
    key: 'partner_id',
    type: 'selectAll',
    mode: 'multiple',
    value: [],
    options: [],
    placeholder: 'Please select Pub Partners',
  },
  {
    name: 'Bundle',
    key: 'app_bundle_id',
    type: 'bundle',
    value: '',
    placeholder: 'Support multiple Bundles',
    tooltip: 'Separated by comma or space'
  },
  {
    name: 'Country',
    key: 'country',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: CountryOptions
  },
  {
    name: 'Ad Format',
    key: 'ad_format',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: AdFormatOptions
  },
  {
    name: 'Ad Size',
    key: 'ad_size',
    type: 'select',
    mode: 'multiple',
    value: [],
    options: [],
  },
  {
    name: 'Dimensions',
    key: 'columns',
    value: [],
    type: 'checkboxFold',
    options: DimensionsOptions,
  },
];

export const DefaultDimension = ['day', 'seller_id', 'tnt_id'];
export const CheckboxUniqueKeyOptions: CheckboxUniqueKeyOptionsType[] = [
  {
    key: 'columns',
    value: ['day', 'month'], // 这两个值二选一
  },
];

export const AllColumns: ColumnProps<FullReportingAPI.BillingListItem>[] = [
  {
    title: 'Date',
    dataIndex: 'date',
    width: 130,
    key: 'day',
    fixed: 'left',
    sorter: true,
  },
  {
    title: 'Month',
    key: 'month',
    dataIndex: 'date',
    width: 130,
    sorter: true,
  },
  {
    title: 'Tenant',
    dataIndex: 'tnt_id',
    width: 150,
    key: 'tnt_id',
    sorter: true,
    ellipsis: { showTitle: false },
    render: (txt, params) => (
      <HoverToolTip title={params.tnt_id}>
        <span>{params.tnt_id}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Publisher',
    width: 180,
    dataIndex: 'seller_id',
    key: 'seller_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.seller}>
        <span>{params.seller}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Advertiser',
    width: 180,
    dataIndex: 'buyer_id',
    key: 'buyer_id',
    ellipsis: { showTitle: false },
    render: (_, params) => (
      <HoverToolTip title={params.buyer}>
        <span>{params.buyer}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Pub Partner',
    dataIndex: 'partner_id',
    width: 180,
    ellipsis: { showTitle: false },
    render: (_, params) => {
      const str = params.partner_name ? `${params.partner_name || '-'}(${_})` : _ || '-';
      return (
        <HoverToolTip title={str}>
          <span>{str}</span>
        </HoverToolTip>
      );
    }
  },
  // {
  //   title: 'Publisher Gross Revenue',
  //   width: 220,
  //   dataIndex: 'seller_gross_revenue',
  //   sorter: true,
  //   render: (txt: string) => <>{formatMoney(+txt)}</>
  // },
  {
    title: 'Publisher Net Revenue',
    width: 220,
    dataIndex: 'seller_net_revenue',
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Impression(ADM)',
    width: 150,
    dataIndex: 'impression',
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },

  {
    title: 'Impression(Pay)',
    width: 180,
    dataIndex: 'seller_payment_impression',
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Bundle',
    dataIndex: 'app_bundle_id',
    width: 150,
    key: 'app_bundle_id'
  },
  {
    title: 'Country',
    dataIndex: 'country',
    width: 150,
    key: 'country',
    render: (txt: string) => <>{Country[txt as keyof typeof Country]}</>
  },
  {
    title: 'Ad Format',
    dataIndex: 'ad_format',
    width: 150,
    key: 'ad_format',
    render: (txt: string) => <>{AdFormatToLabel[txt]}</>
  },
  {
    title: 'Ad Size',
    dataIndex: 'ad_size',
    width: 150,
    key: 'ad_size'
  },
  {
    title: 'Total Request',
    width: 160,
    dataIndex: 'total_request',
    sorter: (a, b) => +a.total_request - +b.total_request,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Request',
    width: 160,
    dataIndex: 'request',
    sorter: (a, b) => +a.request - +b.request,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Response',
    width: 160,
    dataIndex: 'response',
    sorter: (a, b) => +a.response - +b.response,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Profit',
    width: 120,
    dataIndex: 'profit',
    sorter: (a, b) => +a.profit - +b.profit,
    render: (txt: number) => <>{txt}</>,
  },
  {
    title: 'Profit Rate(%)',
    width: 150,
    dataIndex: 'profit_rate',
    sorter: (a, b) => +a.profit_rate - +b.profit_rate,
  },
];
export const DefaultColumnKeys = [
  // 'seller_gross_revenue',
  'seller_net_revenue',
  'impression',
  'seller_payment_impression',
  'total_request',
  'request',
  'response',
  'profit',
  'profit_rate',
  // 'date',
  // 'tnt_id',
  // 'seller_id',
];
