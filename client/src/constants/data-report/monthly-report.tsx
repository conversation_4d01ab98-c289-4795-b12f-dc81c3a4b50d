/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-29 15:41:36
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 16:18:40
 * @Description:
 */

// ?types
import { BreadcrumbItem } from '@/components/Breadcrumb';
import {
  TopBarSearchItem,
  CheckboxUniqueKeyOptionsType,
} from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
import StatusTag from '@/components/Tag/StatusTag';

// ?components
import HoverToolTip from '@/components/Tooltip/HoverTooltip';

// ?utils
import { formatMoney } from '@/utils';
import { StatusOptions } from '..';
import { FormatExportValueMapType } from '@/utils/export-file';

export const BreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'antd-LineChartOutlined',
  },
  {
    name: 'Billing Report',
  },
  {
    name: 'Monthly',
  },
];

// const DimensionsOptions = [
//   { label: 'Date', value: 'day' },
//   { label: 'Month', value: 'month' },
//   { label: 'Tenant', value: 'tnt_id' },
// ];
export const SearchOptions: TopBarSearchItem[] = [
  {
    name: 'Month',
    type: 'select',
    key: 'date',
    value: [],
    mode: 'single',
    allowClear: false,
  },
  {
    name: 'Tenant Status',
    type: 'select',
    key: 'tnt_status',
    value: [],
    mode: 'single',
    options: StatusOptions,
  },
  // {
  //   name: 'Tenant',
  //   type: 'selectAll',
  //   key: 'tnt_id',
  //   value: [],
  //   options: [],
  //   mode: 'multiple',
  // },

  // {
  //   name: 'Dimensions',
  //   key: 'columns',
  //   value: [],
  //   type: 'checkboxFold',
  //   options: DimensionsOptions,
  //   disabled: true,
  // },
];

export const DefaultDimension = ['month', 'tnt_id'];
export const CheckboxUniqueKeyOptions: CheckboxUniqueKeyOptionsType[] = [
  {
    key: 'columns',
    value: ['month'], // 这两个值二选一
  },
];

export const AllColumns: ColumnProps<FullReportingAPI.MonthlyReportItem>[] = [
  {
    title: 'Month',
    key: 'month',
    dataIndex: 'month',
    width: 130,
  },
  {
    title: 'Tenant',
    dataIndex: 'tnt_id',
    width: 150,
    key: 'tnt_id',
    sorter: true,
    ellipsis: { showTitle: false },
    render: (txt, params) => (
      <HoverToolTip title={params.tnt_id}>
        <span>{params.tnt_id}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Tenant Status',
    width: 120,
    dataIndex: 'tnt_status',
    render: (_) => <StatusTag value={_} />,
  },
  {
    title: 'Advertiser Net Revenue',
    width: 220,
    sorter: true,
    dataIndex: 'buyer_net_revenue',
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Publisher Net Revenue',
    width: 220,
    sorter: true,
    dataIndex: 'seller_net_revenue',
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Advertiser Request',
    width: 220,
    sorter: true,
    dataIndex: 'buyer_request',
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Publisher Total Request',
    width: 220,
    sorter: true,
    dataIndex: 'seller_total_request',
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Publisher Request',
    width: 220,
    sorter: true,
    dataIndex: 'seller_request',
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Profit',
    key: 'profit',
    dataIndex: 'profit',
    width: 150,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Profit Rate(%)',
    key: 'profit_rate',
    dataIndex: 'profit_rate',
    width: 150,
  },
];
export const DefaultColumnKeys = [
  'month',
  'tnt_id',
  'tnt_status',
  'buyer_net_revenue',
  'seller_net_revenue',
  'seller_total_request',
  'seller_request',
  'buyer_request',
  'profit',
  'profit_rate',
];

const StatusMap: Record<number, string> = {
  1: 'Active',
  2: 'Paused',
};

// 处理本地导出的数据映射问题
export const FormatExportValueMap: FormatExportValueMapType = {
  tnt_status: (tnt_status: any) => StatusMap[tnt_status],
};
