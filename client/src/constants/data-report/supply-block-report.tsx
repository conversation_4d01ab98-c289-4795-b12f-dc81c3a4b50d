/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 18:03:45
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-09 15:39:36
 * @Description:
 */
import { BreadcrumbItem } from '@/components/Breadcrumb';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { formatMoney } from '@/utils';
import { FormatExportValueMapType } from '@/utils/export-file';
import { ColumnProps } from 'antd/lib/table';
import {
  BlockMapOptions,
  BlockMapRender,
  SellerTagMap,
  SupplyBlockStatusMap,
} from './block-report';

// const MetricsOptions = [{ label: 'Block Request', value: 'block_request' }];

export const DimensionsOptions = [
  { label: 'Date', value: 'day' },
  { label: 'Hour', value: 'day_hour' },
  { label: 'Tenant', value: 'tnt' },
  { label: 'Publisher', value: 'seller_id' },
  { label: 'Status', value: 'status' },
  { label: 'Pub Tag', value: 'seller_tag' },
  { label: 'Source Tenant', value: 'source_tenant' },
  { label: 'Source Deep', value: 'source_deep' },
];

export const DashboardSearchOption: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],
    timeLimit: 31,
  },
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: [],
    options: [],
    mode: 'multiple',
    rules: [{ required: true, message: 'Please Select Tenant' }],
    limitSelectCount: 1,
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    mode: 'multiple',
  },
  // status, mapping 映射待后续确定
  {
    name: 'Status',
    type: 'select',
    key: 'status',
    value: [],
    options: BlockMapOptions(SupplyBlockStatusMap),
    mode: 'multiple',
  },
  {
    name: 'Pub Tag',
    type: 'select',
    key: 'seller_tag',
    value: [],
    options: BlockMapOptions(SellerTagMap),
    mode: 'multiple',
  },
  {
    name: 'Dimensions',
    key: 'columns',
    value: [],
    type: 'checkboxFold',
    options: DimensionsOptions,
  },
];

export const DashboardBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'rix-data-report',
  },
  {
    name: 'Publisher Block Reporting',
  },
];

export const DashboardAllColumns: ColumnProps<FullReportingAPI.FullReportListItem>[] =
  Object.seal([
    {
      title: 'Date',
      key: 'day',
      fixed: 'left',
      width: 160,
      dataIndex: 'date',
      render: (_, params) => params.date,
      sorter: true,
    },
    {
      title: 'Hour',
      dataIndex: 'date',
      fixed: 'left',
      width: 160,
      key: 'day_hour',
      render: (txt, params) => <>{params.date}</>,
      sorter: true,
    },
    {
      title: 'Tenant',
      dataIndex: 'tnt',
      width: 150,
      key: 'tnt',
      ellipsis: { showTitle: false },
      render: (txt, params) => (
        <HoverToolTip title={params.tnt}>
          <span>{params.tnt ?? '-'}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Publisher',
      dataIndex: 'seller_id',
      width: 180,
      key: 'seller_id',
      ellipsis: { showTitle: false },
      render: (_, params) => (
        <HoverToolTip title={params.seller}>
          <span>{params.seller ?? '-'}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Status',
      key: 'status',
      dataIndex: 'status',
      width: 250,
      render: (status: number) => BlockMapRender(status, SupplyBlockStatusMap),
    },
    {
      title: 'Pub Tag',
      key: 'seller_tag',
      dataIndex: 'seller_tag',
      width: 150,
      render: (tag: number) => BlockMapRender(tag, SellerTagMap),
    },
    {
      title: 'Source Tenant',
      key: 'source_tenant',
      dataIndex: 'source_tenant',
      width: 150,
      ellipsis: { showTitle: false },
      render: (txt: number) => (
        <HoverToolTip title={txt}>
          <span>{txt}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Source Deep',
      key: 'source_deep',
      dataIndex: 'source_deep',
      width: 150,
    },
    {
      title: 'Block Request',
      key: 'block_request',
      dataIndex: 'block_request',
      width: 150,
      sorter: true,
      render: (block_request: number) => formatMoney(block_request),
    },
  ]);

// 指标固定字段：Block Request
export const HardCodeMetrics = ['block_request'];

export const DashBoardDefaultMetrics = [...HardCodeMetrics];

export const DashboardDefaultDimension = ['day', 'tnt', 'status', 'seller_id'];

// 用于展示的默认列，需要和 默认选中的筛选条件一致
export const DashboardDefaultColumnKeys = [
  ...DashboardDefaultDimension,
  ...DashBoardDefaultMetrics,
];

// 处理本地导出的数据映射问题
export const FormatExportValueMap: FormatExportValueMapType = {
  buyer_id: (_, row: any) => row.buyer,
  seller_id: (_, row: any) => row.seller,
  status: (_, row: any) => SupplyBlockStatusMap[row.status]?.code || row.status,
  seller_tag: (_, row: any) =>
    SellerTagMap[row.seller_tag]?.code || row.seller_tag,
};
