/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 18:08:45
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 18:11:42
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-10-11 11:26:18
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-10-11 11:35:52
 * @Description:
 */
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';

import { ColumnProps } from 'antd/lib/table';

import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import {
  QpsAdFormatToLabel,
  QpsLevelType,
  QpsLevelTypeToString,
  RegionLabel,
} from '../config/strategy';
import { Country } from '../country';
import EllipsisPopover from '@/components/EllipsisPopover';
import { qpsForamt } from '@/utils/qps';

export const ConfigQpsColumns: ColumnProps<StrategyAPI.QpsListItem>[] = [
  {
    title: 'Advertiser',
    dataIndex: 'buyer_name',
    width: 150,
    fixed: 'left',
    ellipsis: { showTitle: false },
    render: (txt, params) => (
      <HoverToolTip title={(txt && `${txt}(${params.buyer_id})`) || '-'}>
        <span>{(txt && `${txt}(${params.buyer_id})`) || '-'}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'Publisher',
    dataIndex: 'seller_name',
    width: 150,
    ellipsis: { showTitle: false },

    render: (txt, params) => (
      <HoverToolTip title={(txt && `${txt}(${params.seller_id})`) || '-'}>
        <span>{(txt && `${txt}(${params.seller_id})`) || '-'}</span>
      </HoverToolTip>
    ),
  },
  {
    title: (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <span>Type</span>
        <Tooltip
          title={
            <span>
              Adv = Advertiser
              <br />
              Pub = Publisher
            </span>
          }
        >
          <QuestionCircleOutlined
            style={{ paddingLeft: '5px', cursor: 'pointer' }}
          />
        </Tooltip>
      </div>
    ),
    dataIndex: 'level',
    width: 180,
    render: (txt) => <>{QpsLevelTypeToString[txt]}</>,
  },
  {
    title: 'Content',
    dataIndex: 'ots_id',
    width: 300,
    ellipsis: { showTitle: false },
    render: (txt, row) => {
      let list = (txt && txt.split(',').filter((v: any) => v)) || [];
      if (
        [
          QpsLevelType['demand + supply + ad_format'],
          QpsLevelType['(unlimit)supply + ad_format'],
          QpsLevelType['demand + ad_format'],
        ].includes(row.level)
      ) {
        list = list.map((val: string) => QpsAdFormatToLabel[val] || val);
      }
      if (
        [
          QpsLevelType['demand + supply + country'],
          QpsLevelType['(unlimit)supply + country'],
          QpsLevelType['demand + country'],
        ].includes(row.level)
      ) {
        list = list.map((val: number) => (Country as any)[val] || val);
      }
      return (
        (Array.isArray(list) && list.length && (
          <EllipsisPopover dataSource={list} />
        )) ||
        '-'
      );
    },
  },

  {
    title: 'Server Region',
    dataIndex: 'region',
    width: 120,
    render: (txt) => (
      <HoverToolTip title={txt}>
        <span>{RegionLabel[txt] || '-'}</span>
      </HoverToolTip>
    ),
  },
  {
    title: 'QPS',
    width: 90,
    dataIndex: 'qps',
    render: (_, row) => {
      if (
        [
          QpsLevelType['(unlimit)supply + ad_format'],
          QpsLevelType['(unlimit)supply + country'],
          QpsLevelType['(unlimit)supply + bundle'],
        ].includes(row.level)
      ) {
        return 'Unlimit';
      }
      return qpsForamt(_) || '-';
    },
  },
];
