/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-27 17:01:50
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-18 15:38:39
 * @Description:
 */

import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { Tooltip } from 'antd';
import { CheckboxUniqueKeyOptionsType, TopBarSearchItem } from '@/components/TopBar';
import { formatMoney } from '@/utils';
import { ColumnProps } from 'antd/es/table';
import { InfoCircleOutlined } from '@ant-design/icons';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { Country } from '@/constants/country';

const fraudTypeMap: any = {
  UN: 'UN',
  appSpoofing:
    'Traffic, where the app identifier (e.g. bundleId) reported to the exchange, does not match the characteristics of the app detected by Pixalate.',
  autoreloader:
    'Impressions with very periodic patterns cannot be generated by a human.',
  blankUserAgent: 'The impression has an empty User Agent field.',
  clickFarm:
    'An impression originating from a user who has been flagged as being associated with human click farm activity.',
  cookieStuffing:
    'Activity from a cookie that has connected to the internet via a statistically significant inflated number of different IP Addresses.',
  datacenter:
    'The User’s IP has a match in the Pixalate known Datacenter list.',
  datacenterProxy:
    'The impression is from an intermediary proxy device, running in a datacenter, that exists to manipulate traffic counts, pass non-human or invalid traffic or fails to comply with protocol',
  defasedApp:
    'Traffic sourced from mobile apps that have been delisted from their respective app stores.',
  deviceIdStuffing:
    'Activity from a device that has connected to the internet via a statistically significant inflated number of different IP Addresses.',
  displayClickFraud:
    'Clicks that are generated from the same browser or device at a statistically significant inflated rate.',
  displayImpressionFraud:
    'Impressions that are generated from the same browser or device at a statistically significant inflated rate.',
  doorwaySite:
    'A user which has been flagged for accessing a given page or domain via multiple spoofed page referrers.',
  duplicateClicks:
    'High volumes of duplicate clicks may indicate an integration issue.',
  duplicateImpressions:
    'High volumes of duplicate impressions may indicate an integration issue.',
  fastClicker:
    'Activity originating from users which generate clicks less than one second apart from their respective impression.',
  highCTRTraffic:
    'Traffic associated with domains or apps demonstrating high-risk CTR behavior.',
  highRiskApp:
    'The impression is from an app that has been flagged for a high risk of invalid traffic.',
  highRiskDeviceId:
    'The impression is from a device ID that has been flagged for a high risk of invalid traffic.',
  highRiskDomain:
    'The impression is from a domain that has been flagged for a high risk of invalid traffic.',
  highRiskIP:
    'The impression is from an IP Address that has been flagged for a high risk of invalid traffic.',
  hijackedSession:
    'Activity originating from a device or browser has a statistically significant inflated number of user sessions.',
  IABcrawler:
    'Activity originating from bots that use a User Agent string which matches a User Agent on the list of IAB known crawlers.',
  IABdummyBot:
    'Activity originating from bots that use a User Agent string that does not match any existing browser.',
  idioBots:
    'Bots (or users) that change their User Agent string (spoofing), while keeping the same cookie.',
  IPObfuscation:
    'An IP that has been spoofed such that the impression is rendered to a different IP address than the one originally offered',
  locationObfuscation:
    'Activity originating from an IP where multiple impressions deviate from the geographic location that is reported in the advertising transaction.',
  malware: 'The impression is from domains or pages known to host malware.',
  maskedIP:
    'The IP of a user does not match the IP and the associated ISP reported in the advertising transaction.',
  phishing:
    'The impression is from domains or pages associated with phishing tactics.',
  privateIP:
    'The IP Address associated with the ad impression is from the private network space.',
  proxy:
    'The impression is from an intermediary proxy device that exists to manipulate traffic counts, pass non­human or invalid traffic or fails to comply with the protocol.',
  publisherFraud:
    'Publishers operating domains or apps which violate standard ad serving practices including, for example, stacked ads, high ad density and inflated impression counts.',
  smartbot:
    'Bots (or users) that change their browser agent string (spoofing) and cookies very often under the same IP, creating low volume traffic or high-volume traffic under configuration that looks like a busy enterprise network.',
  TOR: 'Traffic originating from a TOR network node.',
  videoClickFraud:
    'Video ad clicks that are generated from the same browser or device at a statistically significant inflated rate.',
  videoImpressionFraud:
    'Video ad impressions that are generated from the same browser or device at a statistically significant inflated rate.',
};

export const PixalateCheckboxUniqueKeyOptions: CheckboxUniqueKeyOptionsType[] = [
  {
    key: 'dimension',
    value: ['day', 'month'],
  },
];

export const PixalateSearchOption: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],
    tooltip:
      '(UTC+0), London,Iceland,Greenwich Mean Time; Time zone change is not supported',
    timeLimit: 93,
  },
  {
    name: 'Tenant',
    type: 'select',
    key: 'tnt_id',
    value: [],
    options: [],
    mode: 'multiple',
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple',
  },
  {
    name: 'Publisher',
    type: 'selectAll',
    key: 'seller_id',
    value: [],
    options: [],
    mode: 'multiple',
  },
  {
    name: 'Sub Publisher ID',
    type: 'bundle',
    key: 'publisher_id',
    value: '',
    placeholder: 'Support multiple Sub Publisher ID',
    tooltip: 'Separated by comma or space',
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'app_bundle_id',
    value: '',
    placeholder: 'Support multiple Bundles',
    tooltip: 'Separated by comma or space',
  },
  {
    name: 'Fraud Type',
    type: 'select',
    key: 'fraud_type',
    value: [],
    options: Object.keys(fraudTypeMap).map((v) => ({ label: v, value: v })),
    mode: 'multiple',
  },
  {
    name: 'Country',
    key: 'country',
    type: 'select',
    mode: 'multiple',
    value: '',
    options: Object.keys(Country).map((item) => {
      return {
        value: item,
        label: (Country as any)[item],
      };
    }),
  },
  {
    name: 'Dimension',
    key: 'dimension',
    type: 'checkboxFold',
    value: [],
    options: [
      { label: 'Date', value: 'day' },
      { label: 'Month', value: 'month' },
      { label: 'Tenant', value: 'tnt_id' },
      { label: 'Fraud Type', value: 'fraud_type' },
      { label: 'Advertiser', value: 'buyer_id' },
      { label: 'Publisher', value: 'seller_id' },
      { label: 'Sub Publisher ID', value: 'publisher_id' },
      { label: 'Bundle', value: 'app_bundle_id' },
      { label: 'Country', value: 'country' },
    ],
  },
];

export const PixalateChangeableColumns: ColumnProps<FullReportingAPI.PixalateReportItem>[] =
  [
    {
      title: 'PX Tracked Ads',
      dataIndex: 'gross_tracked_ads',
      width: 160,
      sorter: (a, b) => +a.gross_tracked_ads - +b.gross_tracked_ads,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Ivt(%)',
      dataIndex: 'ivt_rate',
      width: 100,
      sorter: true,
    },

    {
      title: 'Sivt(%)',
      dataIndex: 'sivt_imp_rate',
      width: 100,
      sorter: (a, b) => +a.sivt_imp_rate - +b.sivt_imp_rate,
    },
    {
      title: 'Givt(%)',
      dataIndex: 'givt_imp_rate',
      width: 100,
      sorter: (a, b) => +a.givt_imp_rate - +b.givt_imp_rate,
    },
    {
      title: 'Sivt Impressions',
      dataIndex: 'sivt_imp',
      width: 150,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Givt Impressions',
      dataIndex: 'givt_imp',
      width: 150,
      render: (txt: string) => <>{formatMoney(+txt)}</>,
    },
    {
      title: 'Measured Impressions',
      dataIndex: 'measured_imp',
      width: 180,
      render: (txt: number) => <>{formatMoney(txt)}</>,
    },
    {
      title: 'Views',
      dataIndex: 'views',
      width: 100,
      render: (txt: number) => <>{formatMoney(txt)}</>,
    },
    // {
    //   title: 'Share Of Ivt Ad Counts',
    //   dataIndex: 'share_of_givt_sivt_impressions',
    //   width: 220,
    //   render: (txt: number) => <>{txt}</>,
    // },
    {
      title: 'Net Viewability (%)',
      dataIndex: 'viewability',
      width: 180,
      render: (txt: number) => <>{txt}</>,
    },
  ];

export const PixalateAllColumns: ColumnProps<FullReportingAPI.PixalateReportItem>[] =
  [
    {
      title: 'Date',
      dataIndex: 'day',
      width: 130,
      sorter: (a, b) => {
        // a.day_hour - b.day_hour,
        const a1 = new Date(a.day).getTime();
        const b1 = new Date(b.day).getTime();
        return a1 - b1;
      },
    },
    {
      title: 'Month',
      dataIndex: 'month',
      width: 130,
      sorter: (a, b) => {
        const a1 = new Date(a.month).getTime();
        const b1 = new Date(b.month).getTime();
        return a1 - b1;
      },
    },
    {
      title: 'Tenant',
      dataIndex: 'tnt_id',
      width: 160,
      sorter: true,
      render: (_, params) => (
        <HoverToolTip title={params.tnt_id}>
          <span>{params.tnt_id}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Fraud Type',
      width: 200,
      dataIndex: 'fraud_type',
      render: (_) => {
        const tips = _ !== 'UN' ? fraudTypeMap[_] : '';
        return tips ? (
          <>
            <span>{_}</span>
            <Tooltip title={tips}>
              <InfoCircleOutlined
                style={{
                  color: '#0DB4BE',
                  fontSize: 16,
                  paddingLeft: 6,
                  cursor: 'pointer',
                }}
              />
            </Tooltip>
          </>
        ) : (
          _
        );
      },
    },
    {
      title: 'Advertiser',
      width: 200,
      dataIndex: 'buyer_id',
      sorter: true,
      ellipsis: { showTitle: false },
      render: (_, params) => (
        <HoverToolTip title={params.buyer_id}>
          <span>{params.buyer_id}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Publisher',
      width: 200,
      dataIndex: 'seller_id',
      ellipsis: { showTitle: false },
      sorter: true,
      render: (_, params) => (
        <HoverToolTip title={params.seller_id}>
          <span>{params.seller_id}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Sub Publisher ID',
      width: 200,
      dataIndex: 'publisher_id',
      ellipsis: { showTitle: false },
      sorter: true,
      render: (_, params) => (
        <HoverToolTip title={params.publisher_id}>
          <span>{params.publisher_id}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Bundle',
      width: 200,
      dataIndex: 'app_bundle_id',
      ellipsis: { showTitle: false },
      render: (_, params) => (
        <HoverToolTip title={params.app_bundle_id}>
          <span>{params.app_bundle_id}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Country',
      width: 200,
      dataIndex: 'country',
      ellipsis: { showTitle: false },
      render: (_) => (
        <HoverToolTip title={(Country as any)[_]}>
          <span>{(Country as any)[_] || _}</span>
        </HoverToolTip>
      ),
    },
    ...PixalateChangeableColumns,
  ];

export const DefaultDimension = ['day'];

export const DefaultColumnKeys = ['day'];

export const DefaultMetrics = [
  'gross_tracked_ads',
  'sivt_imp_rate',
  'givt_imp_rate',
  'sivt_imp',
  'givt_imp',
  'ivt_rate',
  'measured_imp',
  'views',
  // 'share_of_givt_sivt_impressions',
  'viewability',
];

export const PixalateBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'antd-LineChartOutlined',
  },
  {
    name: 'Pixalate Reporting',
  },
];
