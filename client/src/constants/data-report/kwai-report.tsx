/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-10-20 14:37:37
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-14 16:42:28
 * @Description:
 */

import HoverToolTip from '@/components/Tooltip/HoverTooltip';
import { TopBarSearchItem } from '@/components/TopBar';
import { formatMoney } from '@/utils';
import { ColumnProps } from 'antd/es/table';

import { BreadcrumbItem } from '@/components/Breadcrumb';

export const KwaiSearchOption: TopBarSearchItem[] = [
  {
    name: 'Time Period',
    type: 'date',
    key: 'date',
    value: [],
    tooltip:
      '(UTC+0), London,Iceland,Greenwich Mean Time; Time zone change is not supported',
    timeLimit: 7,
  },
  {
    name: 'Tenant ID',
    type: 'select',
    key: 'tnt_id',
    value: [],
    options: [],
    mode: 'multiple',
  },
  {
    name: 'Advertiser',
    type: 'selectAll',
    key: 'buyer_id',
    value: [],
    options: [],
    mode: 'multiple',
  },

  {
    name: 'APP ID',
    type: 'bundle',
    key: 'app_id',
    value: '',
    placeholder: 'Support multiple Bundles',
    tooltip: 'Separated by comma or space',
  },
  {
    name: 'Bundle',
    type: 'bundle',
    key: 'bundle',
    value: '',
    placeholder: 'Support multiple Bundles',
    tooltip: 'Separated by comma or space',
  },
  {
    name: 'Dimension',
    key: 'dimension',
    type: 'checkboxFold',
    value: [],
    options: [
      { label: 'Date', value: 'day' },
      { label: 'Advertiser', value: 'buyer_id' },
      { label: 'Tenant ID', value: 'tnt_id' },
      { label: 'APP ID', value: 'app_id' },
      { label: 'Bundle', value: 'bundle' },
      { label: 'Country', value: 'country' },
    ],
  },
  {
    name: 'Metrics',
    key: 'metrics',
    value: [],
    type: 'checkboxFold',
    options: [
      { label: 'Request', value: 'request' },
      { label: 'Response', value: 'response' },
      { label: 'Click', value: 'click' },
      { label: 'Impression', value: 'impression' },
      { label: 'Revenue', value: 'revenue' },
      {
        label: 'Fill Rate',
        value: 'fill_rate',
      },
      {
        label: 'Render Rate',
        value: 'impression_rate',
      },
      {
        label: 'Click Rate',
        value: 'click_rate',
      },
      {
        label: 'eCpm',
        value: 'ecpm',
      },
      {
        label: 'eCPR',
        value: 'ecpr',
      },
    ],
  },
];

export const KwaiChangeableColumns: ColumnProps<FullReportingAPI.KwaiReportItem>[] =
  [
    {
      title: 'Tenant ID',
      dataIndex: 'tnt_id',
      width: 160,
      render: (_, params) => (
        <HoverToolTip title={params.tenant}>
          <span>{params.tenant}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Advertiser',
      dataIndex: 'buyer_id',
      width: 160,
      render: (_, params) => (
        <HoverToolTip title={params.buyer}>
          <span>{params.buyer}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'APP ID',
      dataIndex: 'app_id',
      width: 100,
      // sorter: (a, b) => +a.givt_imp_rate - +b.givt_imp_rate,
    },
    {
      title: 'Bundle',
      width: 200,
      dataIndex: 'bundle',
      ellipsis: { showTitle: false },
      render: (_, params) => (
        <HoverToolTip title={params.bundle}>
          <span>{params.bundle}</span>
        </HoverToolTip>
      ),
    },
    {
      title: 'Country',
      dataIndex: 'country',
      width: 150,
      key: 'country',
    },
  ];

export const KwaiAllColumns: ColumnProps<FullReportingAPI.KwaiReportItem>[] = [
  {
    title: 'Date',
    dataIndex: 'day',
    width: 130,
    sorter: (a, b) => {
      // a.day_hour - b.day_hour,
      const a1 = new Date(a.day).getTime();
      const b1 = new Date(b.day).getTime();
      return a1 - b1;
    },
  },
  ...KwaiChangeableColumns,
  {
    title: 'Request',
    key: 'request',
    dataIndex: 'request',
    width: 150,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Response',
    key: 'response',
    dataIndex: 'response',
    width: 150,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Click',
    key: 'click',
    dataIndex: 'click',
    width: 150,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Impression',
    key: 'impression',
    dataIndex: 'impression',
    width: 150,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+txt)}</>,
  },
  {
    title: 'Revenue',
    key: 'revenue',
    dataIndex: 'revenue',
    width: 150,
    sorter: true,
    render: (txt: string) => <>{formatMoney(+(+txt).toFixed(2))}</>,
  },
  {
    title: 'Fill Rate(%)',
    key: 'fill_rate',
    dataIndex: 'fill_rate',
    sorter: true,
    width: 150,
  },
  {
    title: 'Render Rate(%)',
    key: 'impression_rate',
    dataIndex: 'impression_rate',
    sorter: true,
    width: 150,
  },
  {
    title: 'Click Rate(%)',
    key: 'click_rate',
    dataIndex: 'click_rate',
    sorter: true,
    width: 150,
  },

  {
    title: 'eCPM',
    key: 'ecpm',
    dataIndex: 'ecpm',
    sorter: true,
    width: 200,
  },
  {
    title: 'eCPR',
    key: 'ecpr',
    dataIndex: 'ecpr',
    sorter: true,
    width: 200,
  },
];

export const DefaultDimension = ['day'];

export const DefaultColumnKeys = ['day'];

export const DefaultMetrics = [
  'request',
  'response',
  'click',
  'impression',
  'revenue',
  'fill_rate',
  'impression_rate',
  'click_rate',
  'ecpm',
  'ecpr',
];

export const KwaiBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Date Report',
    icon: 'antd-LineChartOutlined',
  },
  {
    name: 'Kwai Reporting',
  },
];
