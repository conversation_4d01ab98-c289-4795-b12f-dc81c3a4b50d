/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-20 18:31:47
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-20 19:35:39
 * @Description:
 */

import { BreadcrumbItem } from '@/components/Breadcrumb';
import RixEngineFont from '@/components/RixEngineFont';

export const MenuBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Admin Setting',
    icon: 'rix-advanced'
   },
   {
     name: 'Menu'
   }
];

export const MenuDefaultFormData = {
  title: undefined,
  remark: undefined,
  status: 1,
  type: 2
};

export const MenuOperationType = {
  'Menu': 2,
  'Operation': 1
};
