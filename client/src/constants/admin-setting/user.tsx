/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-28 17:27:00
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2024-01-16 11:10:16
 * @Description:
 */
import { ButtonType, ColumnsType } from '@/components/Table/BackTable';
import StatusTag from '@/components/Tag/StatusTag';
import { TopBarSearchItem } from '@/components/TopBar';
import { StatusOptions } from '@/constants';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import { PlusOutlined } from '@ant-design/icons';
import HoverTooltip from '@/components/Tooltip/HoverTooltip';

const LoginStatusMap: { [key: number]: string } = {
  1: '在线',
  2: '离线',
};

export const UserColumns: ColumnsType<UserAPI.UserListItem>[] = [
  {
    title: '账号',
    dataIndex: 'account_name',
    fixed: 'left',
    width: 150,
    ellipsis: { showTitle: false },
    render: (_, row) => {
      return (
        <HoverTooltip title={`${_}(${row.admin_id})`}>
          <span>{`${_}(${row.admin_id})`}</span>
        </HoverTooltip>
      );
    },
  },
  {
    title: '昵称',
    dataIndex: 'display_name',
    width: 150,
    ellipsis: { showTitle: false },
    render: (_) => {
      return (
        <HoverTooltip title={_}>
          <span>{_ || '-'}</span>
        </HoverTooltip>
      );
    },
  },
  {
    title: '角色',
    dataIndex: 'role_name',
    width: 150,
    ellipsis: { showTitle: false },
    render: (_) => {
      return (
        <HoverTooltip title={_}>
          <span>{_ || '-'}</span>
        </HoverTooltip>
      );
    },
  },
  {
    title: '备注',
    width: 120,
    dataIndex: 'remark',
    ellipsis: { showTitle: false },
    render: (_) => {
      return (
        <HoverTooltip title={_}>
          <span>{_ || '-'}</span>
        </HoverTooltip>
      );
    },
  },
  {
    title: '登录状态',
    width: 100,
    dataIndex: 'is_login',
    render: (_: number) => (
      <StatusTag value={_} statusDescMap={LoginStatusMap} />
    ),
  },
  {
    title: '更新时间',
    dataIndex: 'update_time',
    width: 180,
  },
  {
    title: '状态',
    width: 100,
    dataIndex: 'status',
    render: (_: number) => <StatusTag value={_} />,
  },
  {
    title: '操作',
    width: 170,
    fixed: 'right',
    dataIndex: 'operate',
    access: ['EditAdminUser', 'ResetAdminPassword'],
  },
];

export const UserSearchOption: TopBarSearchItem[] = [
  {
    type: 'select',
    key: 'admin_id',
    mode: 'multiple',
    value: '',
    placeholder: '请选择账户',
    name: '账号',
    options: [],
  },
  {
    type: 'select',
    key: 'role_id',
    mode: 'multiple',
    value: '',
    placeholder: '请选择角色',
    name: '角色',
    options: [],
  },
  {
    type: 'select',
    key: 'status',
    mode: 'multiple',
    value: '',
    options: StatusOptions,
    placeholder: '请选择状态',
    name: '状态',
  },
];

export const UserBreadOptions: BreadcrumbItem[] = [
  {
    name: 'Admin Setting',
    icon: 'rix-advanced',
  },
  {
    name: 'User',
  },
];

export const UserBtnOptions: ButtonType[] = [
  {
    label: 'Add User',
    type: 'primary',
    size: 'small',
    icon: <PlusOutlined />,
    key: 'create',
    disabled: false,
    access: 'AddAdminUser',
  },
];
