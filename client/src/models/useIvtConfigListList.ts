/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 17:53:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-27 15:42:13
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getIvtConfigList } from '@/services/api';

interface IvtProps {
  reload: () => Promise<any>;
  loading: boolean;
  dataSource: ConfigAPI.IvtConfigItem[];
}

export default function useIvtList(): IvtProps {
  const { data, run, loading, error } = useCustomRequest(getIvtConfigList, {
    cacheKey: 'ivt-config-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading,
  };
}
