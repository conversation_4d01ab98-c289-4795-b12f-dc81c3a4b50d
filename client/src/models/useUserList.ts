/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-02-12 03:24:23
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-03-27 16:52:08
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getUserList } from '@/services/api';
export default function useUserList() {
  const { data, run, loading, error } = useCustomRequest(getUserList, {
    cacheKey: 'user-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading,
  };
}
