/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-04-21 15:18:53
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 19:02:18
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getTenantList } from '@/services/api';

export type TenantOption = {
  label: string;
  value: string;
};

interface TntListProps {
  tenantList: TenantAPI.TenantListItem[];
  reload: () => Promise<any>;
  loading: boolean;
  options: TenantOption[];
}
export default function useTenantList(): TntListProps {
  const { data, run, loading, error } = useCustomRequest(getTenantList, {
    cacheKey: 'tnt-all-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    tenantList: data,
    options: data?.map((item: TenantAPI.TenantListItem) => ({
      label: `${item.tnt_name}(${item.tnt_id})`,
      value: item.tnt_id,
    })) || [],
    reload: run,
    loading,
  };
}
