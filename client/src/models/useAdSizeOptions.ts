import useCustomRequest from '@/hooks/useCustomRequest';
import { getDict } from '@/services/api';
import { useCallback } from 'react';

export interface AdSizeOptions {
  label: string;
  value: number;
}

export default function useAdSizeOptions() {
  const {
    data: options,
    loading,
    run,
  } = useCustomRequest(getDict, {
    cacheKey: 'ad_size',
    // cache time 1min
    cacheTime: 60 * 1000,
  });

  const fetchAdSize = useCallback(() => {
    if (options === undefined || options.length === 0) {
      run({ dict_type: 'ad_size' });
    }
  }, [run, options]);

  return {
    adSizeOptions: (options || []) as AdSizeOptions[],
    loading,
    fetchAdSize,
  };
}

export const fetchAdSize = () => getDict({ dict_type: 'ad_size' });
