import useCustomRequest from '@/hooks/useCustomRequest';
import { getDict } from '@/services/api';
import { useCallback, useMemo } from 'react';

interface SupplyEPProps {
  options: {
    label: string;
    value: string;
  }[];
  loading: boolean;
  fetchSupplyEP: () => Promise<any>;
}

export default function useSupplyEP(): SupplyEPProps {
  const { data, run, loading } = useCustomRequest(getDict, {
    cacheKey: 'supply_tag',
  });

  const options = useMemo(() => {
    return (
      data?.map((item: any) => ({
        label: item.supply_tag,
        value: item.supply_tag,
      })) || []
    );
  }, [data]);

  const fetchSupplyEP = useCallback(() => {
    return run({ dict_type: 'supply_tag' });
  }, [run]);

  return {
    options,
    loading,
    fetchSupplyEP,
  };
}
