/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-01-30 16:45:27
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-30 16:46:36
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getTenantPrivatizationList } from '@/services/api';
export default function useBrandList() {
  const { data, run, loading, error } = useCustomRequest(
    getTenantPrivatizationList,
    {
      cacheKey: 'brand-list',
    },
  );
  if (error) {
    console.error(error);
  }
  return {
    brandList: Object.freeze(data),
    brandOptions: Object.freeze(
      data?.map((item: PrivateAPI.BrandList) => ({
        label: item.brand_name,
        value: item.brand_name,
      })) || [],
    ),
    reload: run,
    loading,
  };
}
