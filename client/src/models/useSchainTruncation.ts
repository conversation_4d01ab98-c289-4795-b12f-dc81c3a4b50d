import useCustomRequest from '@/hooks/useCustomRequest';
import { getSchainTruncationList } from '@/services/transparency';

export default () => {
  const { data, run, loading, error } = useCustomRequest(
    getSchainTruncationList,
    {
      cacheKey: 'schain-truncation-list',
    },
  );
  if (error) {
    console.error(error);
  }
  return {
    schainTruncationList: data,
    reload: run,
    loading,
  };
};
