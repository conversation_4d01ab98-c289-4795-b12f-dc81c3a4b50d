import useCustomRequest from '@/hooks/useCustomRequest';
import { getCustomPlacementList } from '@/services/api';

interface CustomPlacementListProps {
  reload: () => Promise<any>;
  loading: boolean;
  dataSource: ConfigAPI.CustomPlacementItem[];
}

export default function useCustomList(): CustomPlacementListProps {
  const { data, run, loading, error } = useCustomRequest(
    getCustomPlacementList,
    {
      cacheKey: 'config-custom-placement-list',
    },
  );
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading,
  };
}
