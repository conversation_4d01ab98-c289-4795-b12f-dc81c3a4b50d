/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:15:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-07 15:56:50
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getDemandListWithTesting } from '@/services/demand';

export default function useDemandList() {
  const { data, run, loading, error } = useCustomRequest(getDemandListWithTesting, {
    cacheKey: 'demand-with-testing-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    demandList: Object.freeze(data),
    reload: run,
    loading
  };
}
