/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 17:53:03
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 17:54:06
 * @Description: 
 */


import useCustomRequest from '@/hooks/useCustomRequest';
import { getActWlList } from '@/services/api';

interface AtcWlListProps {
  reload: () => Promise<any>,
  loading: boolean,
  dataSource: ConfigAPI.AtcItem[]
}

export default function useActWlList(): AtcWlListProps {
  const { data, run, loading, error } = useCustomRequest(getActWlList, {
    cacheKey: 'config-atc-wl-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading,
  };
}