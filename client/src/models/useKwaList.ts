/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-15 16:15:05
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-15 16:16:09
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getKwaPlacementList } from '@/services/api';

interface KwaPlacementListProps {
  reload: () => Promise<any>;
  loading: boolean;
  dataSource: ConfigAPI.KwaiPlacementItem[];
}

export default function useKwaList(): KwaPlacementListProps {
  const { data, run, loading, error } = useCustomRequest(getKwaPlacementList, {
    cacheKey: 'config-kwa-placement-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading,
  };
}
