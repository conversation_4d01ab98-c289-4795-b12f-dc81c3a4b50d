/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-09 10:28:39
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-09 10:28:39
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getDemandList } from '@/services/api';

interface DemandListProps {
  allDemandList: ConfigAPI.DemandListItem[];
  reload: () => Promise<any>;
  loading: boolean;
}

// 只返回buyer_id,buyer_name,tnt_id
export default function useAllDemandList(): DemandListProps {
  const { data, run, loading, error } = useCustomRequest(getDemandList, {
    cacheKey: 'demand-all-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    allDemandList: data,
    reload: run,
    loading,
  };
}
