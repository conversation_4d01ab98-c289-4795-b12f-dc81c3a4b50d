/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-09-19 10:49:11
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-09-19 14:28:15
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getStgChainList } from '@/services/api';

interface StgChainListProps {
  reload: () => Promise<any>;
  loading: boolean;
  dataSource: ConfigAPI.StgItem[];
}

export default function useStgChainList(): StgChainListProps {
  const { data, run, loading, error } = useCustomRequest(getStgChainList, {
    cacheKey: 'config-stg-chain-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading,
  };
}
