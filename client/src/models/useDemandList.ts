import useCustomRequest from '@/hooks/useCustomRequest';
import { getDemandDetailList } from '@/services/api';

interface DemandListProps {
  demandList: DemandAPI.DemandListItem[];
  reload: () => Promise<any>;
  loading: boolean;
}

// 返回demand list详情
export default function useDemandList(): DemandListProps {
  const { data, run, loading, error } = useCustomRequest(getDemandDetailList, {
    cacheKey: 'demand-detail-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    demandList: data,
    reload: run,
    loading,
  };
}
