/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-10-20 15:47:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-20 15:47:39
 * @Description:
 */
/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-21 17:05:52
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-03-27 16:51:32
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getKwaiReportList } from '@/services/api';

export default function useInterfaceList() {
  const { data, run, loading, error } = useCustomRequest(getKwaiReportList, {
    cacheKey: 'interface-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading,
  };
}
