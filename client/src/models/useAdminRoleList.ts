/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-21 16:55:41
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 16:55:43
 * @Description: 
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllAdminRole } from '@/services/api';

interface OperationListProps {
  dataSource: AdminSettingAPI.RoleItem[],
  reload: () => Promise<any>,
  loading: boolean
}

export default function useAdminRoleList(): OperationListProps {
  const { data, run, loading, error } = useCustomRequest(getAllAdminRole, {
    cacheKey: 'admin-role-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading
  };
}
