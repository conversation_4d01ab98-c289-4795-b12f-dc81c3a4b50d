/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-21 16:56:13
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 16:56:15
 * @Description: 
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllAdminUserList } from '@/services/api';

interface UserListProps {
  allUserList: UserAPI.UserListItem[],
  reload: () => Promise<any>,
  loading: boolean
}
export default function useAdminUserList(): UserListProps {
  const { data, run, loading, error } = useCustomRequest(getAllAdminUserList, {
    cacheKey: 'user-all-list'
  });
  if (error) {
    console.error(error);
  }
  const dataSource = Array.isArray(data) ? data : [];
  return {
    allUserList: dataSource,
    reload: run,
    loading
  };
}
