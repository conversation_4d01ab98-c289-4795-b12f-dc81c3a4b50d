/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-09 10:27:21
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-09 10:28:29
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getSupplyList } from '@/services/api';

interface SupplyListProps {
  allSupplyList: ConfigAPI.SupplyListItem[];
  reload: () => Promise<any>;
  loading: boolean;
}

// 只返回seller_id,seller_name,tnt_id
export default function useAllSupplyList(): SupplyListProps {
  const { data, run, loading, error } = useCustomRequest(getSupplyList, {
    cacheKey: 'supply-all-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    allSupplyList: data,
    reload: run,
    loading,
  };
}
