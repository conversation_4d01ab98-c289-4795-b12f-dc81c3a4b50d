/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-02-27 18:57:54
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-27 19:03:30
 * @Description:
 */

import { PartnerType } from '@/constants/partner';
import useCustomRequest from '@/hooks/useCustomRequest';
import { getPartnerList } from '@/services/api';

interface PartnerListProps {
  dataSource: PartnerAPI.PartnerListItem[];
  reload: () => void;
  loading: boolean;
  buyerPartnerList: PartnerAPI.PartnerListItem[];
  sellerPartnerList: PartnerAPI.PartnerListItem[];
}

export default function usePartnerList(): PartnerListProps {
  const { data, run, loading, error } = useCustomRequest(getPartnerList, {
    cacheKey: 'partner-all-list',
  });
  if (error) {
    console.error(error);
  }
  const tmp = Array.isArray(data) ? data : [];
  const seller = tmp.filter((v) => v.type !== PartnerType.Advertiser);
  const buyer = tmp.filter((v) => v.type !== PartnerType.Publisher);
  return {
    dataSource: data || [],
    reload: run,
    loading,
    buyerPartnerList: buyer,
    sellerPartnerList: seller,
  };
}
