/*
 * @Author: chen<PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:15:35
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-17 18:56:42
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getSupplyDetailList } from '@/services/api';

export default function useSupplyList() {
  const { data, run, loading, error } = useCustomRequest(getSupplyDetailList, {
    cacheKey: 'supply-detail-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading,
  };
}
