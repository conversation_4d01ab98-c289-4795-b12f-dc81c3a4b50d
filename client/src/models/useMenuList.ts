/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-21 17:05:30
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-03-27 16:51:53
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllMenu } from '@/services/api';

export default function useMenuList() {
  const { data, run, loading, error } = useCustomRequest(getAllMenu, {
    cacheKey: 'menu-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading,
  };
}
