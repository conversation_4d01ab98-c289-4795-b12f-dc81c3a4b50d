/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-27 17:04:36
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-03-27 17:04:37
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllUserList } from '@/services/api';

export default function useAllUserList() {
  const { data, run, loading, error } = useCustomRequest(getAllUserList, {
    cacheKey: 'user-all-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    userList: data,
    reload: run,
    loading,
  };
}
