/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-28 11:14:01
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-28 11:15:18
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-11-20 18:53:59
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-28 17:35:33
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getExportedReportList } from '@/services/api';

type Props = {
  dataSource: FullReportingAPI.ExportedReportItem[];
  reload: () => Promise<any>;
  loading: boolean;
};

export default function useExportReportList(): Props {
  const { data, run, loading, error } = useCustomRequest(
    getExportedReportList,
    {
      cacheKey: 'exported-report-list',
    },
  );
  if (error) {
    console.error(error);
  }
  return {
    dataSource: Object.freeze(data),
    reload: run,
    loading,
  };
}
