/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-21 16:56:31
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 16:56:34
 * @Description: 
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllAdminMenu } from '@/services/api';

interface OperationListProps {
  dataSource: AdminSettingAPI.MenuItem[],
  reload: () => Promise<any>,
  loading: boolean
}

export default function useAdminMenuList(): OperationListProps {
  const { data, run, loading, error } = useCustomRequest(getAllAdminMenu, {
    cacheKey: 'admin-menu-list'
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading
  };
}
