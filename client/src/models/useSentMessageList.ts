/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-01-08 15:03:18
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-08 15:13:13
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getAllSentMessage } from '@/services/api';

interface Props {
  sentMessageList: BoardAPI.SentMessageItem[];
  reload: () => Promise<any>;
  loading: boolean;
}
export default function useSentMessageList(): Props {
  const { data, run, loading, error } = useCustomRequest(getAllSentMessage, {
    cacheKey: 'all-ent-message-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    sentMessageList: data,
    reload: run,
    loading,
  };
}
