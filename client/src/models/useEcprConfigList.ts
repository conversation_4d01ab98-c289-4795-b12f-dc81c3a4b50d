/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-05-19 09:39:02
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-05-19 09:39:03
 * @Description:
 */
import useCustomRequest from '@/hooks/useCustomRequest';
import { getEcprConfigList } from '@/services/api';

export default function useAllUserList() {
  const { data, run, loading, error } = useCustomRequest(getEcprConfigList, {
    cacheKey: 'ecpr-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    data,
    reload: run,
    loading,
  };
}
