import useCustomRequest from '@/hooks/useCustomRequest';
import { getTenantPixalateList } from '@/services/api';

export default function useTenantPixalate() {
  const { data, run, loading, error } = useCustomRequest(
    getTenantPixalateList,
    {
      cacheKey: 'tenant-pixalate-list',
    },
  );
  if (error) {
    console.error(error);
  }
  return {
    data,
    reload: run,
    loading,
  };
}
