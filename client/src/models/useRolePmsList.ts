/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-05-16 15:51:26
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-05-16 15:52:36
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getPlatformRole } from '@/services/api';

export default function useRolePmsList() {
  const { data, run, loading, error } = useCustomRequest(getPlatformRole, {
    cacheKey: 'role-pms-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data,
    reload: run,
    loading,
  };
}
