/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-04-03 17:42:31
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-03 17:45:42
 * @Description:
 */

import useCustomRequest from '@/hooks/useCustomRequest';
import { getPixlPrebidList } from '@/services/api';

interface PartnerListProps {
  dataSource: ConfigAPI.PixlItem[];
  reload: () => void;
  loading: boolean;
}

export default function usePixlPrebidList(): PartnerListProps {
  const { data, run, loading, error } = useCustomRequest(getPixlPrebidList, {
    cacheKey: 'pixalate-prebid-list',
  });
  if (error) {
    console.error(error);
  }
  return {
    dataSource: data || [],
    reload: run,
    loading,
  };
}
