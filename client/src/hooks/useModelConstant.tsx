/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:46:01
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-02-01 14:31:00
 * @Description:
 */
import React, { useState, useEffect } from 'react';
import { useModel } from 'umi';
import type { models } from '@@/plugin-model/model';

type GetNamespaces<M> = {
  [K in keyof M]: M[K] extends { namespace: string }
    ? M[K]['namespace']
    : never;
}[keyof M];

type Namespaces = GetNamespaces<typeof models>;

type PropsType<T, K> = {
  modelName: Namespaces,
  key: K;
}

function useModelConstant<T extends object>({ modelName, key }: PropsType<T, keyof T>) {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRow, setSelectedRow] = useState<T | undefined>(undefined);
  const { dataSource, reload, loading } = useModel(modelName) as any;

  const reset = () => {
    setSelectedRowKeys([]);
    setSelectedRow(undefined);
  };
  // 路由改变刷新页面
  useEffect(() => {
    reload();
  }, []);

  useEffect(() => {
    reset();
  }, [loading]);

  const handleRowClick = (record: T) => {
    const curSelectedId: any = record[key];
    if (!selectedRowKeys.length || !selectedRowKeys.includes(curSelectedId)) {
      setSelectedRowKeys([curSelectedId]);
      setSelectedRow(record);
    }
  };

  const handleRowChange = (curSelectedRowKeys: any) => {
    setSelectedRowKeys(curSelectedRowKeys);
    if (!curSelectedRowKeys.length) {
      setSelectedRow(undefined);
    } else if (curSelectedRowKeys.length === 1) {
      const record = dataSource.find((el: T) => el[key] === curSelectedRowKeys[0]);
      setSelectedRow(record);
    } else {
      setSelectedRow(undefined);
    }
  };

  return {
    selectedRowKeys,
    setSelectedRowKeys,
    selectedRow,
    setSelectedRow,
    dataSource,
    reload,
    loading,
    reset,
    handleRowClick,
    handleRowChange
  };
}

export default useModelConstant;
