@import '~antd/es/style/variable.less';

@font-face {
  font-family: 'Montserrat';
  src: url('~@/assets/fonts/Montserrat.otf');
}

@font-face {
  font-family: 'Satoshi Variable';
  font-weight: 400;
  src: url('~@/assets/fonts/Satoshi-Bold.ttf');
}

:root {
  --primary-color: #1568d4;
}

#root {
  --primary-color: #1568d4;
}

html,
body,
#root {
  height: 100%;
  width: 100%;
}

body,
body div,
#root {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-family: 'Montserrat', 'Helvetica Neue', -apple-system, BlinkMacSystemFont,
    'Segoe UI', Roboto, Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji',
    'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji', 'Satoshi Variable';
}

.colorWeak {
  filter: invert(80%) !important;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset !important;
  box-shadow: none !important;
}

.ant-pro-sider-light .ant-pro-sider-collapsed-button {
  border: none !important;
}

.ant-menu-inline .ant-menu-item::after {
  display: none !important;
}
canvas {
  display: block;
}

ul,
ol {
  list-style: none;
}

.ant-menu-submenu-popup {
  .ant-menu {
    border-radius: 6px !important;
  }
}

.ant-menu-vertical .ant-menu-item,
.ant-menu-vertical .ant-menu-submenu-title {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.ant-menu-inline-collapsed {
  .ant-menu-submenu-selected {
    background: @primary-1 !important;
  }
}

.ant-layout {
  min-height: 100vh !important;
  .ant-pro-global-header {
    box-shadow: none !important;
    border-bottom: 1px solid #e2eaeb !important;
  }
}

div .ant-picker {
  border-radius: 6px;
}

.ant-input-number-wrapper.ant-input-number-group
  > .ant-input-number:first-child,
.ant-input-number-wrapper .ant-input-number-group-addon:first-child {
  border-top-left-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
}

.ant-checkbox {
  .ant-checkbox-inner {
    border-radius: 4px;
  }
  &.ant-checkbox-checked::after {
    border-radius: 4px;
  }
  &.ant-checkbox-checked .ant-checkbox-inner {
    background-color: #1568d4;
    border-color: #1568d4;
  }
}

.ant-form-vertical {
  .ant-form-item {
    margin-bottom: 12px;
    .ant-form-item-explain-error {
      margin-bottom: 12px;
    }
  }
}

body {
  .ant-input:focus,
  .ant-input-focused {
    border: 1px solid #1568d4;
    box-shadow: 0px 0px 4px #58c1d0;
  }
  .ant-select-dropdown {
    border-radius: 6px;
    .ant-select-item-option-active {
      background: #e6e8eb;
    }
    .ant-select-item-option-selected {
      background: #e1ecfa !important;
      &:hover {
        background: #e6e8eb;
      }
    }
    .ant-select-item-option {
      color: #252829;
    }
  }
  .ant-checkbox-group {
    .ant-checkbox-wrapper {
      color: #252829;
    }
  }
  .ant-input-affix-wrapper > input.ant-input:focus {
    border: none;
  }
  .ant-btn-default {
    color: #5e6466;
    background: #f3f5f5;
    border-radius: 6px;
    border: none;
    &:hover {
      color: #5e6466;
      background: #d7dadb !important;
      border: none;
    }
  }
  .ant-btn-primary {
    border: none;
    background: #1568d4;
    &:hover {
      background: #4692f5;
      border: none;
    }
    &:active {
      background: #0a56b8;
      border: none;
    }
  }
  .ant-btn,
  .ant-input {
    border-radius: 6px;
  }
  .ant-input-affix-wrapper {
    border-radius: 6px;
  }
  .ant-layout-sider {
    .ant-layout-sider-children {
      background-color: #f3f5f5;

      .ant-menu.ant-pro-sider-menu {
        box-shadow: none;
        border: none;
        background-color: #f3f5f5;

        .ant-menu-item {
          margin-top: 0 !important;
        }
      }

      .ant-menu {
        color: #252829;
        &.ant-menu-root > .ant-menu-submenu {
          margin: 0px 8px;
        }
        &.ant-menu-root > .ant-menu-item-active {
          .ant-menu-title-content {
            background-color: #e6e8eb;
          }
        }
        &.ant-menu-root > .ant-menu-item-selected {
          background-color: #f3f5f5;
          .ant-menu-title-content {
            background-color: #e1ecfa;
          }
        }
        &.ant-menu-root > .ant-menu-item-only-child {
          padding: 0 !important;
          .ant-menu-title-content {
            border-radius: 6px;
            margin: 0 8px;
            padding: 0 16px;
            border-radius: 6px;
          }
        }
        &.ant-menu-root > .ant-pro-sider-collapsed-button {
          &.ant-menu-item-active {
            .ant-menu-title-content {
              background: none;
            }
          }
        }
        > .ant-menu-submenu {
          // margin: 0px 8px;
          border-radius: 6px;

          .ant-menu-submenu-arrow {
            color: #252829;
          }

          .ant-menu-title-content .ant-pro-menu-item {
            display: flex;
            align-items: center;
            padding-left: 0 !important;
            span {
              svg {
                width: 20px;
              }
            }
          }
        }

        > .ant-menu-submenu-open {
          .ant-menu-submenu-arrow {
            color: #252829;
          }

          .ant-menu-sub {
            .ant-menu-item-only-child {
              border-radius: 6px;

              .ant-pro-menu-item-title {
                font-weight: 400;
                color: #5e6466;
              }

              &:not(.ant-menu-item-selected):hover {
                background: #e6e8eb;

                .ant-pro-menu-item-title {
                  color: #5e6466;
                  // font-weight: 700;
                  text-shadow: 0 0 0.85px #5e6466;
                }
              }

              &.ant-menu-item-selected {
                background: #e1ecfa;

                .ant-pro-menu-item-title {
                  // color: #1568D4;
                  color: #1568d4;
                  font-weight: 700;
                }

                &:hover {
                  .ant-pro-menu-item-title {
                    // color: #1568D4;
                    color: #1568d4;
                  }
                }
              }
            }

            .ant-menu-item-selected {
              border-radius: 6px;

              &:hover {
                background: #e1ecfa;
                border-radius: 6px;

                .ant-pro-menu-item-title {
                  // color: #1568D4;
                  color: #1568d4;
                }
              }
            }
          }
        }

        .ant-menu-submenu-selected > div[role='menuitem'] {
          .ant-menu-submenu-arrow {
            // color: #1568D4;
            color: #1568d4;
          }
        }
      }

      .ant-menu-inline-collapsed {
        & > .ant-menu-item[role='menuitem'] {
          .ant-menu-title-content {
            margin: 0;
          }
        }
        .ant-menu-submenu {
          margin: 4px 4px;

          .ant-menu-submenu-title {
            padding: 2px 9px;
          }
        }
        &.ant-menu-root > .ant-menu-item-only-child {
          margin: 0;
        }
      }

      .ant-menu-root {
        > .ant-menu-submenu {
          .ant-menu-submenu-title {
            &:hover {
              background: #e6e8eb;
              border-radius: 6px;

              span {
                color: #252829;
              }
            }
          }

          &.ant-menu-submenu-selected {
            .ant-menu-submenu-title {
              &:hover {
                background: #e6e8eb;
                border-radius: 6px;

                span {
                  // color: #1568D4;
                  color: #1568d4;
                }
              }
            }
          }
        }
      }
    }
  }
  .ant-form .ant-form-item {
    .ant-form-item-label > label {
      color: #5e6466;
    }
    .ant-form-item-control {
      padding-left: 6px;
    }
  }
}

// 全局使用的溢出隐藏
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.ant-spin-nested-loading .ant-space-align-center {
  position: absolute !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.ant-spin.ant-spin-lg.ant-spin-spinning {
  position: absolute !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
