import React from 'react';
import { HighlightText, Paragraph, SubsectionTitle } from './common';
import styles from '../index.less';
import List from './common/List';

const PaymentInfo: React.FC = () => {
  return (
    <>
      <SubsectionTitle>4.1 服务费计算方式</SubsectionTitle>
      <Paragraph>
        服务费按月结算，甲方按照百万广告请求费用和流水抽佣向乙方支付服务费用，该费用对应的银行转账手续费由甲乙双方各自承担。
      </Paragraph>

      <Paragraph>
        月度结算标准具体如下，其中广告请求是指出口请求量（广告平台后台可见），
        <HighlightText>
          乙方佣金比例为月度流水(甲方从DSP获取的广告收入)的4%
          ，同时收取0.22美元/百万广告请求的费用。
        </HighlightText>
      </Paragraph>
      <Paragraph>
        <HighlightText>
          即：服务费 = 月收入 * 4% + 月度出口广告请求数/100万 *
          0.22（单位：美元）。
        </HighlightText>
      </Paragraph>

      <SubsectionTitle>4.2 支付方式</SubsectionTitle>
      <Paragraph>
        每个中国自然月的前 5 天，由乙方以本协议文首所载邮箱向甲方文首所载邮箱发起对上个月的结算申请以及具体对账单，甲方应在收到结算申请后的 10 个工作日内确认结算或提出异议。双方确认结算单据后，
        <HighlightText>
          甲方在收到乙方符合相关法律要求的合法、等额税务票证后，应在45个自然日内向乙方进行支付。
        </HighlightText>
      </Paragraph>
      <List
        type="letter"
        items={[
          <Paragraph>
            如双方对该对账单产生争议，双方以第三方广告需求方数据为准，数据从第三方广告需求方的后台获取。除非另有说明，所有付款均应以美元支付。
          </Paragraph>,
          <Paragraph>
            如本协议文首所载邮箱发生变更，任何一方需第一时间向对方通知变更后的对接人邮箱。如一方未及时通知，另一方再发送邮件后对方未按照本协议约定按时回复，需要及时通过电话等方式与对方联系了解对接人变动情况。
          </Paragraph>,
          <Paragraph>
            本协议下所产生的所有费用、税费，包括但不限于海关、关税、增值税，均由甲乙双方自行承担。
          </Paragraph>,
        ]}
      />
      <SubsectionTitle>4.3 本协议收付款账户及开票信息</SubsectionTitle>
      <Paragraph>乙方账户信息为：</Paragraph>
      <div className={styles.paragraph}>
        <div className={styles.infoRow}>
          <div className={styles.label}>户 名：</div>
          <div className={styles.content}>AI-RIXENGINE LIMITED</div>
        </div>
        <div className={styles.infoRow}>
          <div className={styles.label}>开户行：</div>
          <div className={styles.content}>
            Citibank, N.A., Hong Kong Branch（花旗银行香港分行）
          </div>
        </div>
        <div className={styles.infoRow}>
          <div className={styles.label}>银行账号：</div>
          <div className={styles.content}>**********</div>
        </div>
        <div className={styles.infoRow}>
          <div className={styles.label}>Bank code：</div>
          <div className={styles.content}>006</div>
        </div>
        <div className={styles.infoRow}>
          <div className={styles.label}>SWIFT code：</div>
          <div className={styles.content}>CITIHKHX</div>
        </div>
      </div>

      <SubsectionTitle>4.4 修改银行账号</SubsectionTitle>
      <Paragraph>若乙方欲变更收款信息，则</Paragraph>
      <List
        type="number"
        items={[
          <Paragraph>
            通过如下联系方式通知甲方并与甲方沟通签署补充协议的事宜
          </Paragraph>,
          <Paragraph>与甲方就此变更签署补充协议</Paragraph>,
        ]}
      />
      <Paragraph>
        只有同时满足上述2项条件，此变更才对双方发生效力。否则此变更对双方不发生效力，由此产生的任何损失（例如付款方付错账户）由甲方承担，与乙方无关：
      </Paragraph>

      <div className={styles.infoRow}>
        <div className={styles.label}>双方邮箱地址：</div>
        <div className={styles.content}><EMAIL></div>
      </div>
      <div className={styles.infoRow}>
        <div className={styles.label}>双方联系电话：</div>
        <div className={styles.content}>18118733793</div>
      </div>
      <div className={styles.infoRow}>
        <div className={styles.label}>双方其他联系方式：</div>
        <div className={styles.content}></div>
      </div>
    </>
  );
};

export default PaymentInfo;
