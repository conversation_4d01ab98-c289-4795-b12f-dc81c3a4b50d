import React from 'react';
import styles from '../index.less';
import { ContractHeaderProps } from '../types/contract';

const ContractHeader: React.FC<ContractHeaderProps> = ({ partyA, partyB }) => {
  return (
    <>
      {/* <h1 className={styles.contractTitle}>广告服务平台合作协议</h1> */}
      <div className={styles.contractHeader}>
        <div className={styles.partyInfo}>
          <div className={styles.infoRow}>
            <span className={styles.label}>甲方：</span>
            <span className={styles.content}>{partyA.name}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.label}>联系人：</span>
            <span className={styles.content}>{partyA.contact}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.label}>联系地址：</span>
            <span className={styles.content}>{partyA.address}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.label}>联系电话：</span>
            <span className={styles.content}>{partyA.phone}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.label}>电子邮箱：</span>
            <span className={styles.content}>{partyA.email}</span>
          </div>
        </div>

        <div className={styles.partyInfo}>
          <div className={styles.infoRow}>
            <span className={styles.label}>乙方：</span>
            <span className={styles.content}>{partyB.name}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.label}>联系人：</span>
            <span className={styles.content}>{partyB.contact}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.label}>联系地址：</span>
            <span className={styles.content}>{partyB.address}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.label}>联系电话：</span>
            <span className={styles.content}>{partyB.phone}</span>
          </div>
          <div className={styles.infoRow}>
            <span className={styles.label}>电子邮箱：</span>
            <span className={styles.content}>{partyB.email}</span>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContractHeader;
