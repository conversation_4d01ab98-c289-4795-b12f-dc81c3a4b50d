import React from 'react';
import styles from '../index.less';
import { ServiceCardProps } from '../types/contract';
import { SectionTitle } from './common';

const ServiceCard: React.FC<ServiceCardProps> = ({
  content,
  className,
  loading,
  error,
  title,
}) => {
  if (loading) {
    return (
      <div className={`${styles.serviceCard} ${className || ''}`}>
        <div className={styles.loading}>Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`${styles.serviceCard} ${className || ''}`}>
        <div className={styles.error}>Error: {error}</div>
      </div>
    );
  }

  return (
    <div className={`${styles.serviceCard} ${className || ''}`}>
      {title && <SectionTitle>{title}</SectionTitle>}
      {content}
    </div>
  );
};

export default ServiceCard;
