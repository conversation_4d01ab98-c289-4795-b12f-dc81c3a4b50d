import React from 'react';
import { Paragraph } from './common';

const Liability: React.FC = () => {
  return (
    <>
      <Paragraph>
        9.1
        如一方违反本合同中所规定的义务，违约方在收到守约方中要求纠正其违约行为的书面通知之日，应立即停止其违约行为，并在七(7)日内赔偿守约方因此受到的所有损失。如违约方继续进行违约行为或不履行其义务，守约方除就其所有损失而获得违约方赔偿外，亦有权提前终止本协议。
      </Paragraph>

      <Paragraph>
        9.2
        本合同期间，如出现下列情况之一时，守约方可书面通知违约方而终止本协议：违约方违反本合同其他约定，且自守约方书面通知其纠正之日起七(7)日内仍未纠正的。
      </Paragraph>

      <Paragraph>
        9.3
        本协议任一方不得随意终止本协议内容。甲乙任何一方如需提前终止合同，需要至少提前90天向另一方发出书面通知。
      </Paragraph>
    </>
  );
};

export default Liability;
