import React from 'react';
import { Paragraph } from './common';

const NotificationChanges: React.FC = () => {
  return (
    <>
      <Paragraph>
        11.1
        本合同双方之间的任何通知均必须以书面形式写成，且均应发往本合同所载之通讯/联系地址。通知可以电子邮件、专人派送、特快专递或挂号邮件形式发送。
      </Paragraph>

      <Paragraph>
        11.2
        通知及函件若为电子邮件形式，则以邮件进入收件方指定之电子邮件系统之日为收件方收到日期；若为专人派送（包括特快专递），则送达时间应按收件一方签收之日期为准；若以挂号邮件发送，则送达时间应以邮局之发送单据为凭，自发送之日起七日为准。
      </Paragraph>

      <Paragraph>
        11.3 本合同的任何变更均应以书面制成，经双方签署后方生效。
      </Paragraph>
    </>
  );
};

export default NotificationChanges;
