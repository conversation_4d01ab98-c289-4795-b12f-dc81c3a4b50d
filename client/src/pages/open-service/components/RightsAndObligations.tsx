import React from 'react';
import { Paragraph, SubsectionTitle } from './common';
import List from './common/List';

const RightsAndObligations: React.FC = () => {
  return (
    <>
      <SubsectionTitle>3.1 甲方的责任及义务</SubsectionTitle>
      <List
        type="letter"
        items={[
          <Paragraph>
            甲方有责任完成上游广告需求方及下游流量提供方的合同签署、对账、收付款、开票等工作。
          </Paragraph>,
          <Paragraph>
            甲方有义务向乙方同步上下游的合同签署进展、收付款等相关信息，以便乙方调整运营策略。
          </Paragraph>,
          <Paragraph>
            甲方有义务依照本协议按时足额支付乙方相关服务费用。
          </Paragraph>,
          <Paragraph>
            甲方有义务将乙方平台提供的app-ads.txt记录添加到甲方自有产品以及直接开发者下游流量提供方的开发者网站的app-ads.txt文本文件中，并标识为DIRECT帐户关系。乙方有权利定期更新app-ads.txt记录。
          </Paragraph>,
          <Paragraph>
            甲方有义务让乙方平台作为上游广告需求方参与甲方通过ADX平台对接的所有下游流量的竞价。
          </Paragraph>,
        ]}
      />
      <SubsectionTitle>3.2 乙方的责任与服务</SubsectionTitle>
      <List
        type="letter"
        items={[
          <Paragraph>
            采买及技术对接第三方云服务，或租赁物理服务器，包括不限于全球3个节点的服务器配置、带宽配置、云存储付费算法功能等。
          </Paragraph>,
          <Paragraph>
            采买及技术对接第三方监测服务，包括不限于Pixalate等平台。购买发生的费用后续经过沟通协商决定，以邮件确认和补充协议为准。
          </Paragraph>,
          <Paragraph>
            乙方负责ADX平台开发，乙方所开发的ADX平台基于OpenRTB
            2.5，用于对接同样遵循相关标准的上下游渠道。
          </Paragraph>,
          <Paragraph>
            乙方负责ADX平台的技术运营及维护，包括不限于对接上下游渠道对接等工作。
          </Paragraph>,
          <Paragraph>
            乙方有义务根据业务的发展与甲方沟通服务器及带宽相关硬件费用的免费扩容事项，避免因硬件不足造成业务损失。
          </Paragraph>,
          <Paragraph>
            乙方有定期更新、升级ADX平台的责任，乙方更新(包括一般错误修复和小的增强)和升级(包括增强和主要功能更改)，无需向甲方进一步收费。
          </Paragraph>,
          <Paragraph>
            自ADX正式启动日起至整个协议合作期间，乙方应保持ADX系统稳定运转。
          </Paragraph>,
        ]}
      />
    </>
  );
};

export default RightsAndObligations;
