import React from 'react';
import { HighlightText, Paragraph, SubsectionTitle } from './common';
import List from './common/List';

const ContractDefinitions: React.FC = () => {
  return (
    <>
      <SubsectionTitle>
        1.1 除非协议上下文另有约定，下列术语在本协议中使用时应具有如下含义：
      </SubsectionTitle>
      <List
        type="letter"
        items={[
          <Paragraph>
            ADX(广告交换)是一种技术平台，联系着DSP（买方平台）和SSP（卖方平台），是实现精准营销的交易场所。
          </Paragraph>,
          <Paragraph>
            DSP是一个数字广告服务的需求方平台，促进广告主、代理商从多个广告网络购买媒体广告库存。
          </Paragraph>,
          <Paragraph>
            SSP是一个供应端平台，也就是媒体侧提供流量的平台。
          </Paragraph>,
          <Paragraph>流水收入：甲方从DSP获取的广告收入。</Paragraph>,
          <Paragraph>流量获取成本：甲方支付给SSP的流量成本。</Paragraph>,
          <Paragraph>
            <HighlightText>
              IDC服务器成本：甲方使用乙方服务器产生的费用，由乙方承担。
            </HighlightText>
          </Paragraph>,
          <Paragraph>
            <HighlightText>
              服务费：按照本协议约定的百万广告请求费用和流水抽佣比例计算后，乙方应得的收入。
            </HighlightText>
          </Paragraph>,
          <Paragraph>
            开户费： 广告平台服务首次部署配置的费用，只收取一次。
          </Paragraph>,
          <Paragraph>
            API是指开发者、SSP、ADX、DSP交互的应用程序编程接口及其相关工具和信息。
          </Paragraph>,
          <Paragraph>
            “维护服务”是指乙方为保持交易所按照其功能规格运行而提供的维护服务。
          </Paragraph>,
          <Paragraph>
            第三方工具购买成本：主要是甲方和乙方为保证业务发展，双方共同同意采买的第三方广告工具，包括但不限于反作弊扫描工具、素材监控工具等等。购买费用由甲方承担。由甲方和乙方共同商定一方作为支付方支付，如果由乙方先垫付，则甲方需要在月度结算时支付给乙方。
            第三方工具费用的提供方式：根据业务需要，乙方会为甲方提供第三方工具单独的账号，根据第三方工具的采买次数、采买功能进行收费，乙方提供使用费用清单给甲方（比如扫描次数、工具服务内容等）。
          </Paragraph>,
          <Paragraph>入口请求量：指ADX接收到SSP的总请求数量。</Paragraph>,
          <Paragraph>出口请求量：指ADX分发给DSP的总请求数量。</Paragraph>,
        ]}
      />
      <SubsectionTitle>
        1.2 本协议中的标题仅为方便阅读而设，不应影响本协议任何条款的解释。
      </SubsectionTitle>
    </>
  );
};

export default ContractDefinitions;
