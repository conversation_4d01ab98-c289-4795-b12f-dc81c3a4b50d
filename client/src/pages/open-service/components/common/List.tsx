import React from 'react';
import styles from '../../index.less';

interface ListProps {
  items: React.ReactNode[];
  type?: 'number' | 'letter';
}

const List: React.FC<ListProps> = ({ items, type = 'number' }) => {
  const listClass = type === 'number' ? styles.numberList : styles.letterList;

  return (
    <ul className={listClass}>
      {items.map((item, index) => (
        <li key={index} className={styles.item}>
          {item}
        </li>
      ))}
    </ul>
  );
};

export default List;
