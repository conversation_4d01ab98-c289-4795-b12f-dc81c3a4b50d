import React, { useState, useEffect } from 'react';
import styles from './ReadingProgress.module.less';

const ReadingProgress: React.FC = () => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    const throttle = (func: () => void, delay: number) => {
      let timeoutId: NodeJS.Timeout;
      let lastExecTime = 0;
      return () => {
        const currentTime = Date.now();
        if (currentTime - lastExecTime > delay) {
          func();
          lastExecTime = currentTime;
        } else {
          clearTimeout(timeoutId);
          timeoutId = setTimeout(() => {
            func();
            lastExecTime = Date.now();
          }, delay - (currentTime - lastExecTime));
        }
      };
    };

    const handleScroll = throttle(() => {
      const scrolled = window.scrollY;
      const maxHeight = document.documentElement.scrollHeight - window.innerHeight;
      const progress = maxHeight > 0 ? (scrolled / maxHeight) * 100 : 0;
      setProgress(Math.min(progress, 100));
    }, 100);

    window.addEventListener('scroll', handleScroll);
    // 初始计算一次
    handleScroll();
    
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className={styles.readingProgress}>
      <div 
        className={styles.progressBar} 
        style={{ width: `${progress}%` }} 
      />
    </div>
  );
};

export default ReadingProgress; 
