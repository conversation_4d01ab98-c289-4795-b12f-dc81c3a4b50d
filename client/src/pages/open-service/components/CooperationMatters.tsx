import React from 'react';
import { HighlightText, Paragraph, UnderlineText } from './common';

const CooperationMatters: React.FC = () => {
  return (
    <>
      <Paragraph>
        2.1 双方合作期限：本协议的初始期限将从
        <HighlightText>2025年【 】月【 】日</HighlightText>
        起生效，最初为期一(1)年(“初始期限”)。初始期限结束后，本协议将连续自动续签，每次续签十二(12)个月
        (每个续签期限称为“续签期限”，与初始期限合称为“期限”)，除非任何一方在初始期限或当时的续签期限结束前至少提前九十(90)天向另一方发出书面终止通知。
      </Paragraph>

      <Paragraph>
        2.2 甲方采购乙方的ADX技术服务能力，对外开展程序化广告平台（即“
        <UnderlineText>ADX平台</UnderlineText>
        ”）服务。乙方负责将甲方商务拓展进来的DSP和SSP平台具体接入到该ADX平台，运营该ADX平台的底层服务器成本由乙方自行承担。
      </Paragraph>

      <Paragraph>
        2.3 乙方为甲方提供基于<UnderlineText>ADX平台</UnderlineText>
        的底层技术支持、第三方服务器支持、上游广告需求方及下游流量提供方的技术支持服务。乙方有权收取相应服务费用。
      </Paragraph>
    </>
  );
};

export default CooperationMatters;
