import React from 'react';
import { Paragraph } from './common';

const ForceMAjeure: React.FC = () => {
  return (
    <>
      <Paragraph>
        8.1
        “不可抗力”是指合同双方不能合理控制、不可预见或即使预见亦无法避免的事件，该事件妨碍、影响或延误任何一方根据合同履行其全部或部分义务。该事件包括但不限于政府行为、自然灾害、战争、电脑病毒、黑客攻击、网络故障、带宽或其他网络设备或技术提供商的服务延迟、服务故障或任何其它类似事件。
      </Paragraph>

      <Paragraph>
        8.2
        出现不可抗力事件时，知情方应及时、充分地向对方以书面形式发通知，并告知对方该类事件对本合同可能产生的影响，并应当在合理期限内提供相关证明。
      </Paragraph>

      <Paragraph>
        8.3
        由于以上所述不可抗力事件致使合同的部分或全部不能履行或延迟履行，则双方于彼此间不承担任何违约责任。
      </Paragraph>
    </>
  );
};

export default ForceMAjeure;
