import React from 'react';
import { Paragraph } from './common';

const Confidentiality: React.FC = () => {
  return (
    <>
      <Paragraph>
        7.1
        未经披露方的书面许可，任何一方不得向第三方（有关法律、法规、政府部门、证券交易所或其他监管机构要求和双方的法律、会计、商业及其他顾问、雇员除外）泄露本合同的条款的任何内容以及本合同的签订及履行情况，以及通过签订和履行本合同而获知的对方及对方关联公司的任何信息。
      </Paragraph>

      <Paragraph>
        7.2 本合同有效期内及终止后，本保密条款仍具有法律效力。
      </Paragraph>
    </>
  );
};

export default Confidentiality;
