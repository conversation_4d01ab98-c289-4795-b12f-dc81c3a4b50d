import { Button, message } from 'antd';
import classnames from 'classnames';
import { useState } from 'react';
import Confidentiality from './components/Confidentiality';
import ContractDefinitions from './components/ContractDefinitions';
import ContractHeader from './components/ContractHeader';
import ContractTransfer from './components/ContractTransfer';
import CooperationMatters from './components/CooperationMatters';
import DisputeResolution from './components/DisputeResolution';
import ReadingProgress from './components/enhanced/ReadingProgress';
import ForceMAjeure from './components/ForceMAjeure';
import IntellectualProperty from './components/IntellectualProperty';
import Liability from './components/Liability';
import NotificationChanges from './components/NotificationChanges';
import OtherTerms from './components/OtherTerms';
import PaymentInfo from './components/PaymentInfo';
import RightsAndObligations from './components/RightsAndObligations';
import ServiceCard from './components/ServiceCard';
import { useContractData } from './hooks/useContractData';
import styles from './index.less';

const Page = () => {
  const [loading, setLoading] = useState(false);
  const {
    data: contractData,
    loading: dataLoading,
    error: dataError,
  } = useContractData();

  const handleActivate = () => {
    setLoading(true);
    if (window.self !== window.top) {
      window.parent.postMessage(
        {
          type: 'agree',
          data: {},
        },
        '*',
      );
    } else {
      message.error('Please use the iframe to activate the service');
      setLoading(false);
    }
  };

  return (
    <div className={styles.openServicePage}>
      <ReadingProgress />
      {/* 协议内容 */}
      <div className={styles.serviceContent}>
        <ServiceCard
          content={
            contractData ? (
              <ContractHeader
                partyA={contractData.partyA}
                partyB={contractData.partyB}
              />
            ) : null
          }
          loading={dataLoading}
          error={dataError || undefined}
        />
        <ServiceCard title="1. 定义" content={<ContractDefinitions />} />
        <ServiceCard title="2. 合作事项" content={<CooperationMatters />} />
        <ServiceCard title="3. 双方权利与义务" content={<RightsAndObligations />} />
        <ServiceCard title="4. 支付与结算" content={<PaymentInfo />} />
        <ServiceCard title="5. 合同的转让" content={<ContractTransfer />} />
        <ServiceCard title="6. 知识产权及数据隐私" content={<IntellectualProperty />} />
        <ServiceCard title="7. 保密" content={<Confidentiality />} />
        <ServiceCard title="8. 不可抗力" content={<ForceMAjeure />} />
        <ServiceCard title="9. 违约责任" content={<Liability />} />
        <ServiceCard title="10. 争议的解决及适用法律" content={<DisputeResolution />} />
        <ServiceCard title="11. 通知及变更" content={<NotificationChanges />} />
        <ServiceCard title="12. 其他条款" content={<OtherTerms />} />
      </div>
      {/* 开通后操作栏；固定在底部 */}
      <div className={styles.fixedBar}>
        <div className={styles.operationBox}>
          <Button
            type="primary"
            className={classnames(styles.button, styles.activateButton)}
            onClick={handleActivate}
            loading={loading}
          >
            同意开通协议
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Page;
