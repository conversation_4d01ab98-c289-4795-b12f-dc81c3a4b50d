@import './styles/tokens.less';
@import './styles/components.less';
@import './styles/responsive.less';

.openServicePage {
  min-height: 100dvh;
  background-color: var(--color-background);
}

.fixedBar {
  height: var(--fixed-bar-height);
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--color-card-background);
  border-top: 1px solid var(--color-border);

  .operationBox {
    width: min(var(--container-max-width), 100%);
    height: 100%;
    margin: 0 auto;

    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);

    .button {
      height: var(--spacing-xl);
      border-radius: 6px;
      padding: 5px var(--spacing-md);

      font-weight: 600;
      font-size: var(--font-size-body);
      line-height: 22px;
    }

    .activateButton {
      background-color: var(--color-primary);
      border-color: var(--color-primary);
    }
  }
}

.serviceContent {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 20px 0 100px 0; // 底部增加空间避免被固定按钮遮挡

  display: flex;
  flex-direction: column;
  gap: 20px;
}

.serviceCard {
  width: 100%;
  border-radius: 6px;
  background-color: var(--color-card-background);
  padding: var(--card-padding-desktop);

  font-weight: 400;
  font-size: var(--font-size-body);
  color: var(--color-text-primary);

  .contractTitle {
    font-size: var(--font-size-h1);
    text-align: center;
    font-weight: 600;
    line-height: var(--line-height-h1);
    margin-bottom: var(--spacing-md);
  }

  // 二级标题
  .sectionTitle {
    font-size: var(--font-size-h2);
    font-weight: 600;
    line-height: var(--line-height-h2);
    margin-bottom: var(--spacing-md);
  }

  // 三级标题
  .subsectionTitle {
    font-size: var(--font-size-h3);
    color: var(--color-text-primary);
    line-height: var(--line-height-h3);
    margin-bottom: var(--spacing-sm);
    margin-top: var(--spacing-md);
    font-weight: 600;

    &:first-child {
      margin-top: 0;
    }
  }

  // 正文
  .paragraph {
    font-size: var(--font-size-body);
    color: var(--color-text-primary);
    line-height: var(--line-height-body);
    margin-bottom: var(--spacing-sm);
  }

  // 高亮：重点信息
  .highlight {
    font-size: var(--font-size-body);
    line-height: var(--line-height-body);
    color: var(--color-primary);
    font-weight: 600;
  }

  // 下划线
  .underline {
    text-decoration: underline;
  }

  // 加载状态
  .loading {
    text-align: center;
    color: var(--color-text-secondary);
    padding: var(--spacing-lg);
  }

  // 错误状态
  .error {
    text-align: center;
    color: #ff4d4f;
    padding: var(--spacing-lg);
  }

  // 卡片标题
  .cardTitle {
    font-size: var(--font-size-h2);
    font-weight: 600;
    line-height: var(--line-height-h2);
    margin-bottom: var(--spacing-md);
    color: var(--color-text-primary);
  }

  // 字母列表： a) b) c) d) e) f) g) h) i) j) k) l) m) n) o) p) q) r) s) t) u) v) w) x) y) z)
  .letterList {
    list-style-type: none;
    padding-left: 0;
    counter-reset: letter;

    li {
      position: relative;
      padding-left: 22px;
    }
    li::before {
      content: counter(letter, lower-alpha) ')';
      counter-increment: letter;
      position: absolute;
      left: 0;
      top: 0;
      font-weight: 600;
    }
  }

  // 数字列表 1) 2) 3) 4) 5) 6) 7) 8) 9) 10)
  .numberList {
    list-style-type: none; // 去除默认数字样式
    counter-reset: number; // 初始化计数器
    padding-left: 0;

    li {
      position: relative;
      padding-left: 22px; // 给自定义序号留空间
    }
    li::before {
      content: counter(number, decimal) ')'; // 1) 2) 3) ...
      counter-increment: number; // 每个li递增
      position: absolute;
      left: 0;
      top: 0;
      font-weight: 600;
    }
  }
}

// 合同头部样式
.contractHeader {
  .partyInfo {
    margin-bottom: var(--spacing-lg);

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 响应式布局
@media (max-width: @tablet) {
  .serviceCard {
    padding: var(--card-padding-mobile);
  }

  .serviceContent {
    padding: var(--spacing-md) var(--spacing-md) 100px var(--spacing-md);
  }

  .contractTerms,
  .paymentInfo,
  .contractHeader {
    .sectionTitle {
      font-size: 18px;
      line-height: 26px;
    }
  }
}
