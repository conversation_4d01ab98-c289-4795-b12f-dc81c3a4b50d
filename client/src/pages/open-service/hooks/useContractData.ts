import { useState, useEffect } from 'react';
import { ContractData, PartyInfo } from '../types/contract';

export const useContractData = () => {
  const [data, setData] = useState<ContractData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // 模拟从现有组件中提取数据，组织成结构化格式
    const loadData = async () => {
      try {
        setLoading(true);
        
        // 从现有的 ContractHeader 组件中提取的数据
        const partyA: PartyInfo = {
          name: '', // 甲方名称为空，待填写
          contact: '',
          address: '',
          phone: '',
          email: ''
        };

        const partyB: PartyInfo = {
          name: 'AI-RIXENGINE LIMITED',
          contact: 'Rix_AM',
          address: 'FLAT/RM A 12/F ZJ 300, 300LOCKHART RD WAN CHAI HONG KONG',
          phone: '18118733793',
          email: '<EMAIL>'
        };

        const contractData: ContractData = {
          partyA,
          partyB,
          sections: [] // 暂时为空，后续可以动态配置合同条款
        };

        setData(contractData);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err.message : '加载数据失败');
        setData(null);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  return { data, loading, error };
}; 
