// 基础类型定义
export interface PartyInfo {
  name: string;
  contact: string;
  address: string;
  phone: string;
  email: string;
}

export interface ContractSection {
  id: string;
  title: string;
  component: React.ComponentType;
  order: number;
}

export interface ContractData {
  partyA: PartyInfo;
  partyB: PartyInfo;
  sections: ContractSection[];
}

// 组件 Props 类型
export interface ServiceCardProps {
  content: React.ReactNode;
  className?: string;
  loading?: boolean;
  error?: string;
  title?: string;
}

export interface ContractHeaderProps {
  partyA: PartyInfo;
  partyB: PartyInfo;
} 
