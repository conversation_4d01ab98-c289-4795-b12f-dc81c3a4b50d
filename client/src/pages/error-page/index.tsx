/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-24 15:53:00
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-20 18:56:41
 * @Description:
 */

import React, { PureComponent } from 'react';

export default class ErrorBound extends PureComponent<any, any> {
  componentDidCatch(error: any, errorInfo: any) {
    const str = (error && String(error).toLowerCase()) || '';
    // 两种错误
    if (
      str.indexOf('loading chunk') !== -1 ||
      str.indexOf('loading css chunk') !== -1
    ) {
      this.handleRefresh();
    }
  }

  handleRefresh() {
    window.location.reload();
  }

  render() {
    return this.props.children;
  }
}
