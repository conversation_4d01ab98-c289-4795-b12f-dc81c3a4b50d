/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-19 18:57:37
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-25 11:39:53
 * @Description:
 */
import PageContainer from '@/components/RightPageContainer';
import FrontTable from '@/components/Table/FrontTable';
import type { SearchResultItem } from '@/components/TopBar';
import {
  AppInfoBreadOptions,
  AppInfoColumns,
  AppInfoSearchOption,
  StatusDescMap,
} from '@/constants/app-ads/app-info';
import { getAppInfo } from '@/services/api';
import { downloadCsv, fetchData } from '@/utils';
import { message } from 'antd';
import React, { useState } from 'react';

const Page: React.FC = () => {
  const [dataSource, setDataSource] = useState<AppAdsAPI.AppInfoItem[]>([]);
  const [loading, setLoading] = useState(false);

  const handleSearchChange = (val: SearchResultItem[]) => {
    // 将 val 转换为 { bundle: string[], lines: string[] } 格式
    const params = val.reduce((acc, cur) => {
      acc[cur.key] = cur.value || [];
      return acc;
    }, {} as Record<string, string[]>);

    if (!params.bundle.length) {
      message.error('Bundle is required');
      return;
    }

    if (!params.lines.length) {
      message.error('Lines is required');
      return;
    }

    getTableData(params);
  };

  const onSuccess = (result: AppAdsAPI.AppInfoResult) => {
    const { data } = result;
    setDataSource(data);
  };

  const getTableData = (params: any) => {
    fetchData({ request: getAppInfo, params, onSuccess, setLoading });
  };

  const handleExport = (tableData: AppAdsAPI.AppInfoItem[]) => {
    // 数据需要全部展开，按照 一个 bundle 一个 line 一个 status_desc 导出 一条数据
    let tmp: any[] = [];
    // 后端逻辑已经排序，这里不需要再排序
    const dataSourceLen = tableData.length;
    for (let i = 0; i < dataSourceLen; i++) {
      const item = tableData[i];
      const linesLen = item.lines.length;
      for (let j = 0; j < linesLen; j++) {
        tmp.push({
          bundle: item.bundle,
          lines: item.lines[j],
          status_msg: StatusDescMap[item.status_code],
        });
      }
    }

    const fields: any = AppInfoColumns.map((item) => {
      return {
        label: item.title,
        value: item.dataIndex,
      };
    });

    const fileName = `app_info_${new Date().getTime()}`;
    downloadCsv(fileName, tmp, { fields });
  };
  return (
    <PageContainer options={AppInfoBreadOptions}>
      <FrontTable<AppAdsAPI.AppInfoItem>
        pageTitle="App-ads.txt Tool"
        searchOptions={AppInfoSearchOption}
        columns={AppInfoColumns}
        rowKey={'bundle'}
        isFold
        isExport
        defaultFold
        scroll={{ y: 'auto' }} // 非动态， 需要自己指定
        labelWidth={110}
        handleSearch={handleSearchChange}
        isBackSearch
        dataSource={dataSource}
        loading={loading}
        handleExport={handleExport}
      />
    </PageContainer>
  );
};

export default Page;
