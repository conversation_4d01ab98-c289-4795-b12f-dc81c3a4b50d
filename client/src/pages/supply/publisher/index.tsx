/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:18:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-18 12:51:54
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useState } from 'react';
import { useModel } from 'umi';
import FrontTable from '@/components/Table/FrontTable';
import { SupplyColumns } from '@/constants/supply/supply-columns';
import { SupplySearchOption, SupplyBreadOptions } from '@/constants/supply';
import PageContainer from '@/components/RightPageContainer';
import { ColumnProps } from 'antd/lib/table';
import AddSupplyModel from '../components/AddSupplyDrawer';
import { TopBarSearchItem } from '@/components/TopBar';
import RixEngineFont from '@/components/RixEngineFont';
import OperateRender from '@/components/OperateRender';
import type { OperateRenderItem } from '@/components/OperateRender';
import { DemandAndSupplyStatusMap } from '@/constants';
import InfoBar from '@/components/InfoBar';
import { SupplyInfoTabs } from '@/constants/supply/info';
import { fetchData } from '@/utils';
import { getSupplyAuth } from '@/services/supply';

const Page: React.FC = () => {
  const { dataSource: supplyList, reload, loading } = useModel('useSupplyList');
  const { tenantList, reload: reloadTenant } = useModel('useTenantList');
  const {
    sellerPartnerList: partnerList,
    reload: reloadPartner,
    loading: partnerLoading,
  } = useModel('usePartnerList');
  const [currentRow, setCurrentRow] = useState<
    SupplyAPI.SupplyListItem | undefined
  >(undefined);
  const [activeKey, setActiveKey] = useState('basic');
  const [supplyInfoTabs, setSupplyInfoTabs] = useState(SupplyInfoTabs);
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [supply, setCurrentSupply] = useState<
    SupplyAPI.SupplyListItem | undefined
  >(undefined);
  const { dataSource: integrationTypeList, reload: reloadIntegrationType } =
    useModel('useSellerIntegrationTypeList');
  const [searchOptions, setSearchOptions] = useState(SupplySearchOption);
  const [dataSource, setDataSource] = useState<SupplyAPI.SupplyListItem[]>([]);
  const [infoLoading, setInfoLoading] = useState(false);
  const handleEditSupply = (params: SupplyAPI.SupplyListItem) => {
    setCurrentSupply(params);
    setIsEdit(true);
    setVisible(true);
  };

  const handleClose = () => {
    setVisible(false);
  };

  const getSupplyAuthList = () => {
    fetchData({
      setLoading: setInfoLoading,
      request: getSupplyAuth,
      params: { seller_id: currentRow?.seller_id, tnt_id: currentRow?.tnt_id },
      onSuccess: (data: SupplyAPI.SellerDemandAuth[]) => {
        if (data) {
          const index = supplyInfoTabs.findIndex(
            (item) => item.key === 'authorization',
          );
          const tabs = [...supplyInfoTabs];
          if (index !== -1) {
            tabs[index].tableData = data.map((item) => {
              return {
                auth_buyer_id: item.buyer_id,
                auth_buyer_name: item.buyer_name,
              };
            });
          }
          setSupplyInfoTabs(tabs);
        }
      },
    });
  };

  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Edit',
      onClick: handleEditSupply,
      icon: <RixEngineFont type="edit" />,
    },
  ];
  const tmpColumns2: ColumnProps<SupplyAPI.SupplyListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 150,
      fixed: 'right',
      onCell: () => {
        return {
          onClick: (e) => e.stopPropagation(),
        };
      },
      render: (txt, params) => (
        <OperateRender btnOptions={OperateOptions} params={params} />
      ),
    },
  ];
  const columns = [...SupplyColumns, ...tmpColumns2];
  useEffect(() => {
    if (supplyList) {
      reload();
    }
    if (!integrationTypeList) {
      reloadIntegrationType();
    }
    if (!partnerList?.length) {
      reloadPartner();
    }
    if (!tenantList?.length) {
      reloadTenant();
    }
  }, []);

  useEffect(() => {
    setDataSource(supplyList);
  }, [supplyList]);

  useEffect(() => {
    if (currentRow) {
      const tabs = [...SupplyInfoTabs];
      const baseIndex = tabs.findIndex(
        (item) => item.key === 'basic' && item.titleIcon,
      );
      if (baseIndex !== -1) {
        tabs[baseIndex].titleIcon!.onClick = (row) => {
          setCurrentRow(undefined);
          handleEditSupply(row!);
        };
      }
      setSupplyInfoTabs(tabs);
      if (activeKey === 'authorization') {
        getSupplyAuthList();
      }
    }
  }, [currentRow]);

  useEffect(() => {
    if (!tenantList || !integrationTypeList || !supplyList) return;
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    if (integrationTypeList && integrationTypeList.length) {
      const tmp = integrationTypeList.map((item) => {
        return {
          label: item.itg_name,
          value: item.id,
        };
      });
      const index = options.findIndex(
        (item) => item.key === 'integration_type',
      );
      if (index !== -1) {
        options[index].options = tmp;
      }
    }
    if (supplyList && tenantList) {
      const sOptions = supplyList.map((item: SupplyAPI.SupplyListItem) => {
        return {
          label: `${item.seller_name}(${item.seller_id})`,
          value: item.seller_id,
        };
      });
      const tOptions = tenantList.map((item: TenantAPI.TenantListItem) => {
        return {
          label: `${item.tnt_name}(${item.tnt_id})`,
          value: item.tnt_id,
        };
      });

      const sIndex = options.findIndex((item) => item.key === 'seller_id');
      const tIndex = options.findIndex((item) => item.key === 'tnt_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
      if (tIndex !== -1) {
        options[tIndex].options = tOptions;
      }
    }
    setSearchOptions(options);
  }, [integrationTypeList, supplyList, tenantList]);
  const normalEmptyRender = () => <span> </span>;

  const handleClickRow = (record: SupplyAPI.SupplyListItem) => {
    if (record.seller_id === currentRow?.seller_id) {
      setCurrentRow(undefined);
    } else {
      setCurrentRow(record);
    }
  };

  const handleInfoTabChange = (activeKey: string) => {
    setActiveKey(activeKey);
    if (activeKey === 'authorization') {
      const index = supplyInfoTabs.findIndex(
        (item) => item.key === 'authorization',
      );
      if (index !== -1) {
        getSupplyAuthList();
      }
    }
  };

  const handleSearchValueChange = (changeValue: any) => {
    if (changeValue.tnt_id && Array.isArray(supplyList)) {
      const dOptions: any[] = supplyList
        .filter(
          (v: ConfigAPI.SupplyListItem) =>
            !changeValue.tnt_id.length || changeValue.tnt_id.includes(v.tnt_id),
        )
        .map((item: ConfigAPI.SupplyListItem) => {
          return {
            label: `${item.seller_name}(${item.seller_id})`,
            value: item.seller_id,
          };
        });
      const dIndex = searchOptions.findIndex(
        (item) => item.key === 'seller_id',
      );
      if (dIndex !== -1) {
        searchOptions[dIndex].options = dOptions;
        setSearchOptions([...searchOptions]);
      }
    }
  };
  return (
    <PageContainer flexDirection="column" options={SupplyBreadOptions}>
      <FrontTable<SupplyAPI.SupplyListItem>
        pageTitle="Publisher List"
        searchOptions={searchOptions}
        isFold={true}
        loading={loading || partnerLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'seller_id'}
        request={reload}
        scroll={{ x: 1000, y: 'calc(100vh - 220px)' }}
        labelWidth={125}
        emptyRender={
          supplyList && supplyList.length ? normalEmptyRender : undefined
        }
        initialValues={{
          status: [
            DemandAndSupplyStatusMap.Active,
            DemandAndSupplyStatusMap.Testing,
          ],
        }}
        onRow={(record) => {
          return {
            onClick: () => handleClickRow(record),
          };
        }}
        rowClassName={(record) => {
          return record.seller_id === currentRow?.seller_id
            ? styles['row-selected']
            : '';
        }}
        allowSaveSearch={true}
        handleSearchValueChange={handleSearchValueChange}
      />
      <AddSupplyModel
        visible={visible}
        isEdit={isEdit}
        handleClose={handleClose}
        reloadSupply={reload}
        supply={supply}
        partnerList={partnerList}
      />
      <InfoBar<SupplyAPI.SupplyListItem>
        loading={infoLoading}
        dataSource={currentRow}
        title={currentRow?.seller_name || ''}
        tabs={supplyInfoTabs}
        width={600}
        handleClose={() => setCurrentRow(undefined)}
        defaultActiveKey={activeKey}
        handleTabChange={handleInfoTabChange}
      />
    </PageContainer>
  );
};

export default Page;
