import { useEffect, useState } from 'react';
import { Form, Radio, message } from 'antd';
import { useModel } from 'umi';

import { updateSupply } from '@/services/api';
import { fetchData } from '@/utils';

import NormalRadio from '@/components/Radio/NormalRadio';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import InputNumber from '@/components/Input/InputNumber';
import Input from '@/components/Input/NormalInput';

import {
  StatusOptions,
  StatusMap,
  ProfitModelOptions,
  ProfitModelType,
  DemandAndSupplyStatusMap,
  DemandAndSupplyStatusOptions,
} from '@/constants';
// import { RatioRule, RevShareRatioRule } from '@/constants/strategy';
import {
  ChannelOptions,
  RelationshipOptions,
  DeviceOptions,
  PassUrlStatusOptions,
  PassUrlStatus,
  RevTrackTypeOptions,
  RevTrackType,
  DeveloperTrafficOptions,
} from '@/constants/supply';
import { IntegrationType } from '@/constants/developer/app';
import NormalSelect from '@/components/Select/NormalSelect';
// import { useRequest } from 'ahooks';

type AddSupplyModelProps = {
  seller_id?: number;
  supply?: SupplyAPI.SupplyListItem;
  visible: boolean;
  isEdit: boolean;
  partnerList: PartnerAPI.PartnerListItem[];
  handleClose: () => void;
  reloadSupply: () => void;
};

const DefaultFormData = {
  seller_name: '',
  integration_type: undefined,
  channel_type: 1,
  device_type: 1,
  relationship: 2,
  profit_model: ProfitModelType.Net,
  profit_ratio: 30,
  rev_share_ratio: 100,
  profit_status: StatusMap.Active,
  cus_status: StatusMap.Paused,
  pass_burl: PassUrlStatus.Paused,
  pass_nurl: PassUrlStatus.Paused,
  pass_lurl: PassUrlStatus.Paused,
  rev_track_type: RevTrackType.ADM,
  status: DemandAndSupplyStatusMap.Active,
  banner_multi_size: StatusMap.Paused,
};

const AddSupplyModel: React.FC<AddSupplyModelProps> = ({
  supply,
  handleClose,
  visible,
  isEdit,
  reloadSupply,
  partnerList,
}) => {
  // const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [profitModel, setProfitModel] = useState(ProfitModelType.Net);
  const [type, setType] = useState(0);
  const [cusDisabled, setCusDisabled] = useState(false);
  const [showTagID, setShowTagID] = useState(false);
  const [siteDisabled, setSiteDisabled] = useState(false);
  // const [profitRatioChange, setProfitRatioChange] = useState(false);
  const { dataSource: integrationTypeList, reload } = useModel(
    'useSellerIntegrationTypeList',
  );
  const [itgTypeOptions, setItgTypeOptions] = useState<any[]>([]);
  // const { runAsync: checkedName } = useRequest(isAccountNameExists, {
  //   debounceWait: 300,
  //   manual: true
  // });
  // if (initialState?.currentUser?.tnt_id === 1059) {
  //   ProfitModelOptions.push({ label: 'Rev Share', value: 2 });
  // }

  useEffect(() => {
    if (integrationTypeList && integrationTypeList.length) {
      setItgTypeOptions(
        integrationTypeList
          .filter((item) => item.id !== IntegrationType['Perbid'])
          .map((item, index) => {
            return {
              label: item.itg_name,
              value: item.id,
              disabled: isEdit,
            };
          }),
      );
    }
    if (visible && !isEdit) {
      handleDefaultType();
    }
    if (!integrationTypeList && visible) {
      reload();
    }
  }, [integrationTypeList, visible]);

  useEffect(() => {
    if (supply && isEdit && visible) {
      form.setFieldsValue({
        ...supply,
        sp_id: supply.sp_id > 0 ? supply.sp_id : undefined,
      });
      setType(supply.integration_type);
      setProfitModel(supply.profit_model);
      setShowTagID(supply.cus_status === 2);
    } else if (!isEdit && visible) {
      handleDefaultType();
    } else {
      form.resetFields();
      setProfitModel(ProfitModelType.Net);
    }
  }, [supply, isEdit, visible]);

  useEffect(() => {
    const item =
      integrationTypeList && integrationTypeList.find((v) => v.id === type);
    if (item) {
      if (
        ['iOS SDK', 'android SDK', 'Max-JSTag', 'MW-JSTag', 'VastTag'].includes(
          item.itg_name,
        )
      ) {
        if (item.itg_name === 'MW-JSTag') {
          form.setFieldsValue({ channel_type: 2, cus_status: 1 });
        } else {
          form.setFieldsValue({ channel_type: 1, cus_status: 1 });
        }
        setSiteDisabled(true);
        setCusDisabled(true);
      } else {
        if (item.itg_name === 'RTB') {
          setCusDisabled(true);
        }
        form.setFieldValue('cus_status', supply?.cus_status || 2);
        setShowTagID(form.getFieldValue('cus_status') === 2);
        setSiteDisabled(false);
      }
    }
  }, [type]);

  const handleDefaultType = () => {
    if (integrationTypeList && integrationTypeList.length) {
      form.setFieldValue('integration_type', integrationTypeList[0].id);
      form.setFieldValue('tagid_status', 2);
      setShowTagID(true);
      setType(integrationTypeList[0].id);
    }
  };
  const handleConfirm = () => {
    form.submit();
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    handleClose();
    reloadSupply();
  };

  const handleEditSupply = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateSupply, params });
  };
  const handleFinish = (values: any) => {
    if (isEdit) {
      const filterValues = Object.fromEntries([
        ['banner_multi_size', values.banner_multi_size],
        ['crid_filter', values.crid_filter],
        ['developer_traffic', values.developer_traffic],
        ['win_rate_profit_ratio', values.win_rate_profit_ratio],
      ].filter(([_, value]) => value !== undefined && value !== null));

      handleEditSupply({
        ...filterValues,
        tnt_id: supply?.tnt_id || 0,
        seller_id: supply?.seller_id || 0,
      });
    }
  };

  const onCancel = () => {
    form.resetFields();
    handleClose();
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.profit_model) {
      setProfitModel(changeValue.profit_model);
    }
    if (changeValue.integration_type) {
      setType(changeValue.integration_type);
    }
    if (changeValue.cus_status) {
      setShowTagID(changeValue.cus_status === 2);
    }
    // if (changeValue.profit_ratio) {
    //   setProfitRatioChange(true);
    // }
    if (changeValue.seller_name && !isEdit) {
      form.setFieldsValue({ seller_account_name: changeValue.seller_name });
      form.validateFields(['seller_account_name']);
    }
  };
  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Publisher` : `Create Publisher`}
      grayName={isEdit ? `${supply?.seller_name}` : ''}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={false}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
      >
        <Form.Item
          name="seller_name"
          label="Publisher Name:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Enter Publisher Name',
            },
          ]}
        >
          <Input placeholder="Please Enter Publisher Name" disabled />
        </Form.Item>
        <Form.Item
          name="seller_account_name"
          label="Publisher Account Name:"
          rules={[
            {
              required: true,
              message: 'Please Enter Publisher Account Name',
            },
            // {
            //   validator: (_, value, callback) => {
            //     if (value) {
            //       checkedName({ account_name: `Pub_${value?.trim()}` }).then(
            //         (res: any) => {
            //           if (res.code === 0) {
            //             callback();
            //           } else {
            //             callback(`${res.message}.Please Edit!`);
            //           }
            //         },
            //       );
            //     }
            //   },
            // },
          ]}
        >
          <Input
            placeholder="Please Enter Publisher Account Name"
            addonBefore={isEdit ? undefined : 'Pub_'}
            disabled={isEdit}
            allowClear
          />
        </Form.Item>
        <Form.Item
          name="sp_id"
          label="Partner:"
          rules={[
            {
              required: false,
              message: 'Please Select Partner',
            },
          ]}
        >
          <NormalSelect
            style={{ width: 360 }}
            options={partnerList.map((v) => ({
              label: `${v.partner_name}(${v.partner_id})`,
              value: v.sp_id,
            }))}
            allowClear
            disabled
          />
        </Form.Item>
        <Form.Item
          name="integration_type"
          label="Integration Type:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Integration Type',
            },
          ]}
        >
          <NormalRadio options={itgTypeOptions} disabled></NormalRadio>
        </Form.Item>

        <Form.Item
          name="channel_type"
          label="Channel Type:"
          rules={[
            {
              required: true,
              message: 'Please Select Channel Type',
            },
          ]}
        >
          <NormalRadio disabled={isEdit}>
            {ChannelOptions.map((item, index) => (
              <Radio
                key={index}
                value={item.value}
                disabled={
                  isEdit || siteDisabled || type === IntegrationType['MW-JSTag']
                }
              >
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        <Form.Item
          name="relationship"
          label="Relationship:"
          rules={[
            {
              required: true,
              message: 'Please Select Relationship',
            },
          ]}
        >
          <NormalRadio>
            {RelationshipOptions.map((item, index) => (
              <Radio key={index} value={item.value} disabled>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        <Form.Item
          name="device_type"
          label="Device Type:"
          rules={[
            {
              required: true,
              message: 'Please Select Device Type',
            },
          ]}
        >
          <NormalRadio disabled={isEdit}>
            {DeviceOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>

        <Form.Item
          name="cus_status"
          label="Custom Placement:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Custom Placement',
            },
          ]}
          tooltip="Whether to support custom ad configuration"
        >
          <NormalRadio disabled={isEdit || cusDisabled}>
            {StatusOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        <Form.Item
          name="developer_traffic"
          label="Developer Traffic:"
          rules={[
            {
              required: true,
              message: 'Please Select Developer Traffic',
            },
          ]}
        >
          <NormalRadio options={DeveloperTrafficOptions} />
        </Form.Item>
        <Form.Item
          name="win_rate_profit_ratio"
          label="Win Rate Profit(%):"
          rules={[
            {
              required: true,
              message: 'Please Input Win Rate Profit Ratio',
            },
          ]}
        >
          <InputNumber addonAfter="%" style={{ width: 120 }} min={0} max={50} />
        </Form.Item>

        {!cusDisabled && showTagID && (
          <Form.Item
            name="tagid_status"
            label="Publisher TagID:"
            rules={[
              {
                required: true,
                message: 'Please Select Publisher TagID',
              },
            ]}
            tooltip="Whether to generate the publisher tagid in the unit ID field in the report"
          >
            <NormalRadio disabled>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value} disabled>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}

        {/* 不能编辑 */}
        <Form.Item
          name="profit_status"
          label="Profit:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Profit Status',
            },
          ]}
        >
          <NormalRadio disabled>
            {StatusOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        {/* 必填 */}
        <Form.Item
          name="profit_ratio"
          label="Profit Ratio:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Input Profit Ratio',
            },
            // RatioRule,
          ]}
        >
          <InputNumber
            addonAfter="%"
            style={{ width: 120 }}
            min={0}
            max={60}
            disabled
          />
        </Form.Item>
        <Form.Item
          name="profit_model"
          label="Profit Model:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Profit Model',
            },
          ]}
        >
          <NormalRadio disabled>
            {ProfitModelOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        {profitModel === ProfitModelType['Rev Share'] && (
          <Form.Item
            name="rev_share_ratio"
            label="Rev Share Ratio:"
            validateTrigger={['onChange', 'onBlur']}
            rules={[
              {
                required: true,
                message: 'Please Input Rev Share Ratio',
              },
              // RevShareRatioRule,
            ]}
          >
            <InputNumber
              addonAfter="%"
              style={{ width: 120 }}
              min={1}
              max={100}
              disabled
            />
          </Form.Item>
        )}
        <Form.Item
          name="rev_track_type"
          label="Revenue Tracking Type:"
          rules={[
            {
              required: true,
              message: 'Please Select Revenue Tracking Type',
            },
          ]}
        >
          <NormalRadio disabled>
            {RevTrackTypeOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        <Form.Item
          name="banner_multi_size"
          label="Banner Multi Size:"
          rules={[
            {
              required: true,
              message: 'Please Select Multi Format',
            },
          ]}
        >
          <NormalRadio options={StatusOptions} />
        </Form.Item>
        <Form.Item
          name="crid_filter"
          label="Crid Filter:"
          rules={[
            {
              required: true,
              message: 'Please Select Crid',
            },
          ]}
          tooltip="Filter ads with empty crid"
        >
          <NormalRadio options={StatusOptions} />
        </Form.Item>
        <Form.Item
          name="status"
          label="Status:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Status',
            },
          ]}
        >
          <NormalRadio disabled>
            {DemandAndSupplyStatusOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>

        <Form.Item
          name="pass_nurl"
          label="NURL:"
          rules={[
            {
              required: true,
              message: 'Please Select NURL Status',
            },
          ]}
        >
          <NormalRadio disabled>
            {PassUrlStatusOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        <Form.Item
          name="pass_burl"
          label="BURL:"
          rules={[
            {
              required: true,
              message: 'Please Select BURL Status',
            },
          ]}
        >
          <NormalRadio disabled>
            {PassUrlStatusOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        <Form.Item
          name="pass_lurl"
          label="LURL:"
          rules={[
            {
              required: true,
              message: 'Please Select LURL Status',
            },
          ]}
        >
          <NormalRadio disabled>
            {PassUrlStatusOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
      </Form>
    </NormalDrawer>
  );
};

export default AddSupplyModel;
