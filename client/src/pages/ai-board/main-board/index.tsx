/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-12 16:18:32
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-07 14:37:43
 * @Description:
 */
import React from 'react';
import styles from './index.less';
import { G2 } from '@ant-design/charts';
import Overview from './components/Overview';
import TopCountry from './components/TopCountry';
import SevenDaysCountry from './components/SevenDaysCountry';
import TopAdFormat from './components/TopAdFormat';
import AllTenantRevenue from './components/AllTenantRevenue';
import EcpmAndEcpr from './components/EcpmAndEcpr';

const Page: React.FC = () => {
  const { registerTheme } = G2;
  registerTheme('custom-theme', {
    colors10: [
      '#126BF0',
      '#1CAF34',
      '#6C2CEB',
      '#FF9012',
      '#4692F5',
      '#C1CBCC',
    ],
  });

  return (
    <div className={styles['main-board-container']}>
      <Overview />
      <TopCountry />
      <div className={styles['top-country-and-adformat-container']}>
        <SevenDaysCountry />
        <TopAdFormat />
      </div>
      <AllTenantRevenue />
      <EcpmAndEcpr />
    </div>
  );
};
export default Page;
