/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-12-14 17:07:50
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-08 14:39:33
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useRef, useState } from 'react';
import { Bar, Plot, Line, G2 } from '@ant-design/plots';

import type { BarConfig, LineConfig } from '@ant-design/charts';
import Card from '../Card';

import { BarOptions, LineOptions } from '@antv/g2plot';
import { fetchData } from '@/utils';
import { getTopCountryEcpmAndEcpr } from '@/services/ai-board';

interface EcprAndEcpmProps {
  totalData?: {
    barData?: BoardAPI.TopCountryEcpmAndEcprItem[];
    lineData?: { [key: string]: BoardAPI.TopCountryEcpmAndEcprItem[] };
    ctvBarData?: BoardAPI.TopCountryEcpmAndEcprItem[];
    ctvLineData?: BoardAPI.TopCountryEcpmAndEcprItem[];
  };
  loading?: boolean;
}

const EcpmAndEcpr: React.FC = () => {
  G2.registerInteraction('hover-cursor', {
    showEnable: [
      {
        trigger: 'element:mouseenter',
        action: 'cursor:pointer',
      },
      {
        trigger: 'element:mouseleave',
        action: 'cursor:default',
      },
    ],
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [totalData, setTotalData] = useState<EcprAndEcpmProps['totalData']>();
  const dataRef = useRef<EcprAndEcpmProps['totalData']>(undefined);
  const barRef = useRef<any>(undefined);
  const ecprLineRef = useRef<any>(undefined);
  const ecpmLineRef = useRef<any>(undefined);
  const curAdFormat = useRef('Banner');
  const [adFormat, setAdFormat] = useState<string>('Banner');
  const [currentTab, setCurrentTab] = useState('ecpm');
  const config: BarConfig = {
    theme: 'custom-theme',
    data: [],
    isGroup: true,
    xField: currentTab,
    yField: 'ad_format',
    seriesField: 'country',
    marginRatio: 0,
    label: {
      position: 'right',
      layout: [
        // 柱形图数据标签位置自动调整
        {
          type: 'interval-adjust-position',
        },
        // 数据标签防遮挡
        {
          type: 'interval-hide-overlap',
        },
        // 数据标签文颜色自动调整
        {
          type: 'adjust-color',
        },
      ],
    },
    interactions: [
      {
        type: 'hover-cursor',
      },
      {
        type: 'element-highlight-by-x',
      },
      {
        type: 'element-single-selected',
      },
    ],
    legend: {
      position: 'bottom',
    },
    state: {
      active: {
        style: {
          stroke: 'rgba(0,0,0,0)',
          lineWidth: 2,
        },
      },
    },
  };
  const ecprLineConfig: LineConfig = {
    theme: 'custom-theme',
    data: [],
    appendPadding: 16,
    xField: 'date',
    yField: 'ecpr',
    seriesField: 'country',
    isStack: false,
    connectNulls: true,
    legend: {
      position: 'bottom',
    },
  };
  const ecpmLineConfig: LineConfig = {
    theme: 'custom-theme',
    data: [],
    appendPadding: 16,
    xField: 'date',
    yField: 'ecpm',
    seriesField: 'country',
    isStack: false,
    connectNulls: true,
    legend: {
      position: 'bottom',
    },
  };
  const baronReady = (plot: Plot<BarOptions>) => {
    plot.on('element:click', (evt: any) => {
      const totalData = dataRef.current;
      const { data } = evt.data;
      setAdFormat(data.ad_format);
      curAdFormat.current = data.ad_format;
      const ecprLinePlot: Plot<LineOptions> = ecprLineRef.current?.getChart();
      const ecpmLinePlot: Plot<LineOptions> = ecpmLineRef.current?.getChart();
      const ecprData = totalData?.lineData?.[data.ad_format].map((item) => {
        return {
          date: item.date,
          ecpr: item.ecpr,
          country: item.country,
        };
      });
      const ecpmData = totalData?.lineData?.[data.ad_format].map((item) => {
        return {
          date: item.date,
          ecpm: item.ecpm,
          country: item.country,
        };
      });
      ecprLinePlot.changeData(ecprData);
      ecpmLinePlot.changeData(ecpmData);
    });
  };
  useEffect(() => {
    if (!totalData) {
      fetchData({
        setLoading,
        request: getTopCountryEcpmAndEcpr,
        onSuccess: (res) => {
          setTotalData(res);
        },
      });
    }
  }, []);
  useEffect(() => {
    if (totalData) {
      dataRef.current = totalData;
    }
    if (totalData && totalData.barData?.length) {
      const barPlot: Plot<BarOptions> = barRef.current?.getChart();
      const data = totalData.barData.map((item) => {
        return {
          country: item.country,
          ecpr: item.ecpr,
          ecpm: item.ecpm,
          ad_format: item.ad_format,
        };
      });
      if (totalData.ctvBarData) {
        data.push(...totalData.ctvBarData);
      }
      barPlot.changeData(data || []);
    }
    if (totalData && totalData.lineData) {
      const ecprLinePlot: Plot<LineOptions> = ecprLineRef.current?.getChart();
      const ecpmLinePlot: Plot<LineOptions> = ecpmLineRef.current?.getChart();

      const ecprData = totalData.lineData[curAdFormat.current].map((item) => {
        return {
          date: item.date,
          ecpr: item.ecpr,
          country: item.country,
        };
      });

      const ecpmData = totalData.lineData[curAdFormat.current].map((item) => {
        return {
          date: item.date,
          ecpm: item.ecpm,
          country: item.country,
        };
      });
      ecprLinePlot.changeData(ecprData);
      ecpmLinePlot.changeData(ecpmData);
    }
  }, [totalData, currentTab, adFormat]);
  return (
    <Card
      title="The left side represents eCPM, and the right side represents eCPR."
      loading={loading}
      options={[
        {
          label: 'eCPM',
          value: 'ecpm',
        },
        {
          label: 'eCPR',
          value: 'ecpr',
        },
      ]}
      handleChange={(value) => setCurrentTab(value)}
      width="96px"
    >
      <div className={styles['ecpr-ecpm-top-container']}>
        <Bar {...config} ref={barRef} onReady={baronReady} />
      </div>
      <div className={styles['ecpr-ecpm-bottom-container']}>
        <Card title={`eCPM-${adFormat}`} className={styles['ecpm']}>
          <div className={styles['ecpm-container']}>
            <Line {...ecpmLineConfig} ref={ecpmLineRef} />
          </div>
        </Card>
        <Card title={`eCPR-${adFormat}`} className={styles['ecpr']}>
          <div className="ecpr-container">
            <Line {...ecprLineConfig} ref={ecprLineRef} />
          </div>
        </Card>
      </div>
    </Card>
  );
};

export default EcpmAndEcpr;
