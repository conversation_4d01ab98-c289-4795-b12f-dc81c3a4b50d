/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-14 17:07:52
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-08 11:37:31
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useRef, useState } from 'react';
import { Pie, Plot, Line } from '@ant-design/plots';

import type { PieConfig, LineConfig } from '@ant-design/charts';
import Card from '../Card';

import { PieOptions, LineOptions } from '@antv/g2plot';
import { fetchData } from '@/utils';
import { getTopAdFormat } from '@/services/ai-board';

interface TopAdFormatProps {
  totalData?: {
    adFormatData: BoardAPI.TopAdFormatItem[];
    ctvData: BoardAPI.CTVData[];
  };
  loading?: boolean;
}

const TopAdFormat: React.FC = () => {
  const roseRef = useRef<any>(undefined);
  const lineRef = useRef<any>(undefined);
  const [currentTab, setCurrentTab] = useState('ad_format');
  const [loading, setLoading] = useState<boolean>(false);
  const [totalData, setTotalData] = useState<TopAdFormatProps['totalData']>();
  const config: PieConfig = {
    theme: 'custom-theme',
    appendPadding: 10,
    data: [],
    angleField: 'revenue',
    colorField: 'ad_format',
    radius: 0.65,
    label: {
      // type: 'spider',
      autoRotate: true,
      labelHeight: 30,
      content: '{name}-{percentage}\n{value}',
      layout: {
        type: 'fixed-overlap',
      },
    },
    tooltip: {
      customContent: (title, data: any) => {
        const adSizes = data[0]?.data?.top_ad_sizes;
        const adSizeHtml = adSizes
          ?.map((item: any) => {
            return `<div class="${styles['ad-size-item']}">
              <span>${item.ad_size}</span>
              <span class="${styles['rev']}">${item.revenue}</span>
            </div>`;
          })
          .join('');
        return `<div class="${styles['tooltip-container']}">
          <div class="${styles['tooltip-title']}">${title}</div>
          <div class="${styles['ad-size-container']}">${adSizeHtml}</div>
        </div>`;
      },
    },
    interactions: [
      {
        type: 'element-selected',
      },
      {
        type: 'element-active',
      },
      {
        type: 'element-highlight',
      },
    ],
    legend: {
      position: 'bottom',
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: 'rgba(0,0,0,0)',
        },
      },
    },
  };
  const lineConfig: LineConfig = {
    theme: 'custom-theme',
    data: [],
    padding: 'auto',
    xField: 'date',
    yField: 'revenue',
    label: {},
    point: {
      size: 5,
      shape: 'diamond',
      style: {
        fill: 'white',
        stroke: '#5B8FF9',
        lineWidth: 2,
      },
    },
    tooltip: {
      showMarkers: false,
    },

    interactions: [
      {
        type: 'marker-active',
      },
    ],
    xAxis: {
      tickCount: 7,
    },
  };
  useEffect(() => {
    fetchData({
      setLoading,
      request: getTopAdFormat,
      onSuccess: (res) => {
        setTotalData(res);
      },
    });
  }, []);
  useEffect(() => {
    if (totalData && totalData.adFormatData.length) {
      if (currentTab === 'ad_format') {
        const rosePlot: Plot<PieOptions> = roseRef.current?.getChart();
        const data = totalData.adFormatData;
        rosePlot.changeData(data);
      } else {
        const linePlot: Plot<LineOptions> = lineRef.current?.getChart();

        linePlot.changeData(totalData.ctvData);
      }
    }
  }, [totalData, currentTab]);

  return (
    <Card
      loading={loading}
      options={[
        {
          label: 'Ad Format',
          value: 'ad_format',
        },
        {
          label: 'CTV',
          value: 'ctv',
        },
      ]}
      handleChange={(value) => {
        setCurrentTab(value);
      }}
      width="196px"
      className={styles['top-adformat-card']}
    >
      <div className={styles['top-adformat-container']}>
        {currentTab === 'ad_format' ? (
          <Pie {...config} ref={roseRef} />
        ) : (
          <Line {...lineConfig} ref={lineRef} />
        )}
      </div>
    </Card>
  );
};

export default TopAdFormat;
