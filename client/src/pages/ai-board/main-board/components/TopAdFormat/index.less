.top-adformat-card {
  width: 39%;
  .top-adformat-container {
    height: 366px;
  }
}
.tooltip-container {
  padding: 12px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.98);
  min-width: 220px;
  min-height: 88px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;
  .tooltip-title {
    font-size: 14px;
    font-weight: 500;
    color: #8d9799;
  }
  .ad-size-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    width: 100%;
    .ad-size-item {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .rev {
        color: #252829;
        margin-left: auto;
      }
    }
  }
}

@media screen and (max-width: 828px) {
  .top-adformat-card {
    width: 100%;
  }
}
