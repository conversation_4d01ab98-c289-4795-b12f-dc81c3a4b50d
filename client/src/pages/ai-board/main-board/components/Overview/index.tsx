/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-12 17:20:07
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-29 17:48:52
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useState } from 'react';
import { Area } from '@ant-design/plots';
import type { AreaConfig } from '@ant-design/charts';
import Card from '../Card';
import RixEngineFont from '@/components/RixEngineFont';
import { fetchData, formatNumberToUnit } from '@/utils';
import { getOverview } from '@/services/ai-board';
import {
  OverviewColorMap,
  OverviewFillColorMap,
  OverviewOptions,
} from '@/constants/ai-board/main-board';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';

interface OverviewProps {
  data?: BoardAPI.OverviewItem[];
}

const Overview: React.FC<OverviewProps> = () => {
  const [loading, setLoading] = useState<boolean>(false);
  const [currentData, setCurrentData] = useState();
  useEffect(() => {
    if (!currentData) {
      fetchData({
        setLoading,
        request: getOverview,
        onSuccess: (res) => {
          if (res) {
            setCurrentData(res[0]);
          }
        },
      });
    }
  }, []);

  // 注意index, 0: revenue, 1: profit, 2: ecpm, 3: request
  const getConfig = (
    index: number,
    data?: BoardAPI.OverviewItem,
  ): AreaConfig => {
    const hoursData = data?.hours_data;
    const currentData = hoursData?.map((item) => {
      let value = item[
        OverviewOptions[index]
          .number as keyof BoardAPI.OverviewItem['hours_data']
      ] as number | string;

      return {
        date: item.date,
        value,
      };
    });
    const config: AreaConfig = {
      autoFit: true,
      data: currentData || [],
      xField: 'date',
      yField: 'value',
      xAxis: false,
      yAxis: false,
      smooth: true,
      areaStyle: {
        fillOpacity: 1,
        fill: OverviewColorMap[index] as string,
      },
      line: {
        color: OverviewFillColorMap[index] as string,
        style: {
          lineWidth: 3,
        },
      },
      tooltip: {
        position: 'top',
        customContent: (title, data) => {
          let value = data?.[0]?.data.value;
          if (typeof value === 'number' && index === 3) {
            value = formatNumberToUnit(value);
          } else {
            value = +(+value).toFixed(2);
          }
          return `<div >
            <div style="margin-bottom: 8px">${title}</div>
            <div>${value}</div>
          </div>`;
        },
        domStyles: {
          'g2-tooltip': {
            backgroundColor: '#fff',
            boxShadow: '0px 0px 10px rgba(0, 0, 0, 0.1)',
            borderRadius: '4px',
            padding: '4px 8px',
          },
          'g2-tooltip-marker': {
            display: 'block',
          },
        },
      },

      padding: 4,
    };
    return config;
  };

  return (
    <Card
      title={`Growth Rate of Today  (Hour Level) Update at ${
        currentData?.['update_time'] || ''
      }`}
      loading={loading}
    >
      <div className={styles['container']}>
        {OverviewOptions.map((item, index) => {
          const config = getConfig(index, currentData);
          return (
            <div
              key={index}
              className={styles['item-container']}
              style={{ backgroundColor: `${OverviewColorMap[index]}` }}
            >
              {currentData && (
                <>
                  <div className={styles['left']}>
                    <div className={styles['title']}>{item.title}</div>
                    <div className={styles['number']}>
                      <span>{`${item.isDollar ? '$' : ''}`}</span>
                      <HoverToolTip
                        title={
                          typeof currentData[item.number] === 'string' &&
                          currentData[item.number]
                            ? currentData[item.number]
                            : (currentData[item.number] as number).toFixed(2)
                        }
                        placement="top"
                        maxWidth={120}
                      >
                        <span>
                          {typeof currentData[item.number] === 'string' &&
                          currentData[item.number]
                            ? currentData[item.number]
                            : (currentData[item.number] as number).toFixed(2)}
                        </span>
                      </HoverToolTip>
                    </div>
                    <div
                      className={styles['percentage']}
                      style={{
                        color:
                          +currentData[item.percentage] < 0
                            ? '#1CAF34'
                            : '#F54B31',
                      }}
                    >
                      <RixEngineFont
                        type={
                          +currentData[item.percentage] < 0
                            ? 'rix-decline'
                            : 'rix-increase'
                        }
                        style={{
                          fontSize: 12,
                          color:
                            +currentData[item.percentage] < 0
                              ? '#1CAF34'
                              : '#F54B31',
                          marginRight: 4,
                        }}
                      ></RixEngineFont>
                      {currentData[item.percentage]}%
                    </div>
                  </div>
                  <div className={styles['right']}>
                    <Area {...config} />
                  </div>
                </>
              )}
            </div>
          );
        })}
      </div>
    </Card>
  );
};

export default Overview;
