.container {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  width: 100%;
  gap: 12px;
  min-height: 146px;
  .item-container {
    max-width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // width: 276px;
    flex: 1;
    padding: 16px 12px;
    border-radius: 4px;
    .left {
      .title {
        color: #5e6466;
      }
      .number {
        max-width: 100%;
        span:first-child {
          margin-right: 4px;
        }
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: 'Satoshi Variable';
        font-size: 32px;
        color: #252829;
        margin: 12px 0 8px 0;
      }
      .percentage {
        font-family: 'Satoshi Variable';
        line-height: normal;
        display: flex;
        align-items: center;
      }
    }

    .right {
      width: 108px;
      height: 62px;
    }
  }
}
