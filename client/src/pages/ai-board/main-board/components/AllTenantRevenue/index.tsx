/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-14 17:07:45
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-12-28 11:07:43
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useRef, useState } from 'react';
import { Column, Plot } from '@ant-design/plots';

import type { ColumnConfig } from '@ant-design/charts';
import Card from '../Card';

import { ColumnOptions } from '@antv/g2plot';
import { getAllTenantRevenue } from '@/services/ai-board';
import { fetchData } from '@/utils';

const AllTenantRevenue: React.FC = () => {
  const tntRef = useRef<any>(undefined);
  const [loading, setLoading] = useState<boolean>(false);
  const [totalData, setTotalData] = useState<BoardAPI.TenantRevenueItem[]>();
  const config: ColumnConfig = {
    appendPadding: 24,
    data: [],
    xField: 'tnt_id',
    yField: 'revenue',
    label: {
      position: 'top',
      // 'top', 'bottom', 'middle',
    },
    xAxis: {
      label: {
        autoHide: true,
        autoRotate: false,
      },
    },
    slider: {
      start: 0,
      end: 1,
    },
    color: '#126BF0',
  };
  useEffect(() => {
    fetchData({
      setLoading,
      request: getAllTenantRevenue,
      onSuccess: (res) => {
        setTotalData(res);
      },
    });
  }, []);
  useEffect(() => {
    if (totalData?.length) {
      const tntPlot: Plot<ColumnOptions> = tntRef.current?.getChart();

      tntPlot.changeData(totalData);
    }
  }, [totalData]);
  return (
    <Card headline="All Statistics - Tenant" loading={loading}>
      <div className={styles['all-tnt-container']}>
        <Column {...config} ref={tntRef} />
      </div>
    </Card>
  );
};
export default AllTenantRevenue;
