/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-12 18:57:46
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-09 11:30:57
 * @Description:
 */
import React, { useEffect } from 'react';
import styles from './index.less';
import { Spin } from 'antd';
import CusLoadingIcon from '@/components/LoadingIcon';
import NormalSelect from '@/components/Select/NormalSelect';

interface CardProps {
  loading?: boolean;
  title?: React.ReactNode;
  headline?: string;
  children?: React.ReactNode;
  className?: string;
  options?: { label: string; value: any }[];
  handleChange?: (value: any) => void;
  width?: string;
}
const Card: React.FC<CardProps> = ({
  headline,
  title,
  children,
  loading = false,
  className = '',
  options,
  handleChange,
  width,
}) => {
  const [value, setValue] = React.useState(options?.[0].value);

  const onChange = (value: any) => {
    setValue(value);
    handleChange?.(value);
  };
  return (
    <div className={`${styles['card-container']} ${className}`}>
      {(title || headline || options?.length) && (
        <div className={styles['title-container']}>
          {options?.length && (
            <div className={styles['select-con']}>
              <NormalSelect
                options={options}
                onChange={onChange}
                value={value}
                bordered={false}
                style={{
                  fontSize: '16px',
                  fontWeight: 'bold',
                  width: width,
                }}
              ></NormalSelect>
            </div>
          )}
          {headline && <div className={styles['headline']}>{headline}</div>}
          {title && (
            <div className={styles['subtitle']}>
              <div className={styles['block']}></div>
              <div>{title}</div>
            </div>
          )}
        </div>
      )}

      <Spin indicator={<CusLoadingIcon />} spinning={loading}>
        {children}
      </Spin>
    </div>
  );
};

export default Card;
