/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-12-14 17:07:54
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-02 16:30:17
 * @Description:
 */

import styles from './index.less';
import React, { useEffect, useRef, useState } from 'react';
import { G2, Pie, Plot } from '@ant-design/plots';

import type { Datum, PieConfig } from '@ant-design/charts';
import Card from '../Card';

import { PieOptions } from '@antv/g2plot';
import { getTopCountry } from '@/services/ai-board';
import { fetchData } from '@/utils';

const TopCountry: React.FC = () => {
  const tntRef = useRef<any>(null);
  const countryRef = useRef<any>(null);
  const dataRef = useRef<BoardAPI.TopCountryItem[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [totalData, setTotalData] = useState<BoardAPI.TopCountryItem[]>();
  const [currentCountry, setCurrentCountry] = useState<string>('');
  const [profit, setProfit] = useState<string>('0');
  const [topTntInAllCountry, setTopTntInAllCountry] = useState<
    BoardAPI.TopCountryItem[]
  >([]);
  const TopCountryConfig: PieConfig = {
    theme: 'custom-theme',
    appendPadding: [48, 24, 24, 24],
    data: [],
    angleField: 'revenue',
    colorField: 'country',
    radius: 0.75,
    innerRadius: 0.55,
    autoFit: true,
    tooltip: {
      formatter: (datum: Datum) => {
        return {
          name: datum.country,
          value: `${datum.revenue.toFixed(2)}`,
        };
      },
    },
    label: {
      autoRotate: false,
      layout: {
        type: 'fixed-overlap',
      },
      offset: '50%',
      style: {
        textAlign: 'center',
      },
      offsetY: -4,
      content: '{name}-{percentage}',
    },
    statistic: {
      title: false,
      content: false,
    },
    annotations: [
      {
        top: true,
        type: 'html',
        alignX: 'middle',
        alignY: 'middle',
        html: (container, view) => {
          const data = view.getOptions()?.data;
          const tooltipHtml = `<div class=${styles['tooltip']}>
            <span class="${styles['tooltip-text']}">Total Revenue($)</span>
            <span class="${styles['tooltip-text']}">Profit($)</span>
          </div>
          `;
          const html = `<div class="${
            styles['country-statistic-container']
          }" id='country-statistic-container'>
            <div class="${styles['country-total']}" >
              <span>${
                data?.reduce((r, d) => r + d.revenue, 0).toFixed(2) || 0
              }</span>
            </div>
            <div class="${styles['country-profit']}" id='country-profit'>
              <span style={fontSize:'1.12em'}>${profit}</span>
            </div>
            ${tooltipHtml}
          </div>`;
          container.innerHTML = html;
        },
        position: ['50%', '50%'],
      },
    ],
    interactions: [
      {
        type: 'element-single-selected',
      },
      {
        type: 'element-highlight-by-color',
      },
    ],
    legend: {
      position: 'right-bottom',
      layout: 'vertical',
      maxWidthRatio: 0.3,
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: 'rgba(0,0,0,0)',
          cursor: 'pointer',
        },
      },
    },
  };
  const TopTenantConfig: PieConfig = {
    theme: 'custom-theme',
    appendPadding: [48, 24, 24, 24],
    data: [],
    angleField: 'revenue',
    colorField: 'tnt',
    radius: 0.75,
    innerRadius: 0.55,
    tooltip: {
      formatter: (datum: Datum) => {
        return {
          name: datum.tnt,
          value: `${datum.revenue.toFixed(2)}`,
        };
      },
    },
    autoFit: true,
    label: {
      formatter: (datum: Datum) => {
        return `${+datum.revenue.toFixed(2)}`;
      },
      autoRotate: false,
      layout: {
        type: 'fixed-overlap',
      },
      offset: '50%',
      style: {
        textAlign: 'center',
      },
      offsetY: -4,
    },
    annotations: [
      {
        top: true,
        type: 'html',
        alignX: 'middle',
        alignY: 'middle',
        html: (container, view) => {
          let data = view.getOptions()?.data;

          const tooltipHtml = `<div class=${styles['tooltip']}>
            <span class="${styles['tooltip-text']}">Total Revenue($)</span>
            <span class="${styles['tooltip-text']}">Profit($)</span>
          </div>
          `;
          let curProfit = '0';
          if (currentCountry) {
            const item = totalData?.find(
              (item) => item.country === currentCountry,
            );
            if (item) {
              // curProfit = (item?.revenue - item.sl_revenue).toFixed(2);
              item.top_tnts?.forEach((tnt) => {
                curProfit = (
                  +curProfit + +(tnt.revenue - tnt.sl_revenue)
                ).toFixed(2);
              });
            }
          }
          const html = currentCountry
            ? `<div class="${styles['country-statistic-container']}">
            <div class="${styles['country-total']}">
              <span>${
                data?.reduce((r, d) => r + d.revenue, 0).toFixed(2) || 0
              }</span>
            </div>
            <div class="${styles['country-profit']}">
              <span style={fontSize:'1.12em'}>${curProfit}</span>
            </div>
            ${tooltipHtml}
          </div>`
            : '';
          container.innerHTML = html;
        },
        position: ['50%', '50%'],
      },
    ],
    statistic: {
      title: false,
      content: false,
    },
    interactions: [
      {
        type: 'element-highlight',
      },
    ],
    legend: {
      layout: 'vertical',
      position: 'right-bottom',
      marker: {
        symbol: 'square',
      },
      maxItemWidth: 100,
    },
    state: {
      active: {
        style: {
          shadowBlur: 4,
          stroke: 'rgba(0,0,0,0)',
          cursor: 'pointer',
        },
      },
    },
  };
  useEffect(() => {
    if (!totalData) {
      fetchData({
        setLoading,
        request: getTopCountry,
        onSuccess: (res) => {
          setTotalData(res.data);
          setProfit((+res.profit).toFixed(2));
          setTopTntInAllCountry(res.topTntInAllCountry);
        },
      });
    }
  }, []);
  useEffect(() => {
    if (totalData?.length) {
      dataRef.current = totalData;
      const topCountryData = totalData.map((item) => {
        return {
          country: item.country,
          revenue: item.revenue,
        };
      });
      const config = {
        ...TopCountryConfig,
        data: topCountryData,
      };
      const countryPlot: Plot<PieConfig> = countryRef?.current?.getChart();
      countryPlot.update({
        ...config,
        animation: {
          appear: false,
        },
      });
      countryPlot.setState('selected', (data: Datum) => {
        return data.country === currentCountry;
      });
      const tntPlot: Plot<PieConfig> = tntRef?.current?.getChart();
      if (!currentCountry) {
        tntPlot.update({
          ...TopTenantConfig,
          data: topTntInAllCountry,
        });
      } else {
        const tntData = totalData.find((item, index) => {
          return item.country === currentCountry;
        })?.top_tnts;
        tntPlot.update({ ...TopTenantConfig, data: tntData });
      }
    }
  }, [totalData, currentCountry]);

  const topCountryOnReady = (plot: Plot<PieOptions>) => {
    plot.on('element:click', (evt: any) => {
      const states = plot.getStates();
      const isSelectedCty = states.some(
        (item: any) => item.state === 'selected',
      );
      if (isSelectedCty) {
        const country = evt.data?.data.country;
        setCurrentCountry(country);
      } else {
        setCurrentCountry('');
      }
    });
  };
  return (
    <div>
      <div className={styles['headline']}>All Statistics</div>
      <div className={styles['container']}>
        <Card
          title="The table below shows yesterday statistics"
          loading={loading}
          className={styles['country-top-country-card']}
        >
          <div className={styles['top-country-container']}>
            <Pie
              {...TopCountryConfig}
              ref={countryRef}
              onReady={topCountryOnReady}
            />
          </div>
        </Card>

        <Card
          title={
            <>
              Top Tenants:{' '}
              <span style={{ color: '#4692F5', fontWeight: 700 }}>
                ({currentCountry || 'All Country'})
              </span>
            </>
          }
          loading={loading}
          className={styles['country-top-tnt-card']}
        >
          <div className={styles['country-top-tnt-container']}>
            <Pie {...TopTenantConfig} ref={tntRef} />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default TopCountry;
