/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-12-14 17:07:51
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-08 11:36:00
 * @Description:
 */
import styles from './index.less';
import React, { useEffect, useRef, useState } from 'react';
import { Line, Plot } from '@ant-design/plots';

import type { Datum, LineConfig } from '@ant-design/charts';
import Card from '../Card';

import { LineOptions } from '@antv/g2plot';
import moment from 'moment';
import { fetchData } from '@/utils';
import { getSevenDaysCountry } from '@/services/ai-board';

const SevenDaysCountry: React.FC = ({}) => {
  const lineRef = useRef<any>(undefined);
  const [loading, setLoading] = useState<boolean>(false);
  const [totalData, setTotalData] = useState<BoardAPI.SevenDaysCountryItem>();
  const lineConfig: LineConfig = {
    theme: 'custom-theme',
    autoFit: true,
    data: [],
    xField: 'date',
    yField: 'revenue',
    seriesField: 'country',

    yAxis: {
      label: {
        // 数值格式化为千分位
        formatter: (v) =>
          `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`),
      },
    },
    legend: {
      position: 'bottom',
      offsetY: 10,
    },
    tooltip: {
      formatter: (datum: Datum) => {
        return {
          name: datum.country,
          value: `${datum.revenue}`,
        };
      },
    },
  };
  useEffect(() => {
    fetchData({
      setLoading,
      request: getSevenDaysCountry,
      onSuccess: (res) => {
        setTotalData(res);
      },
    });
  }, []);
  useEffect(() => {
    if (totalData) {
      const linePlot: Plot<LineOptions> = lineRef.current?.getChart();
      linePlot.changeData(totalData.data);
    }
  }, [totalData]);
  return (
    <Card
      headline="Country Statistics"
      title={`${moment(totalData?.start_date).format('YYYY/MM/DD')}~${moment(
        totalData?.end_date,
      ).format('YYYY/MM/DD')}  Updated at ${
        totalData?.update_time || ''
      }(UTC+0)`}
      loading={loading}
      className={styles['seven-days-country-card']}
    >
      <div className={styles['seven-days-country-container']}>
        <Line {...lineConfig} ref={lineRef}></Line>
      </div>
    </Card>
  );
};

export default SevenDaysCountry;
