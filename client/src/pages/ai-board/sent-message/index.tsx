/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-05 16:05:47
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-09 10:20:56
 * @Description:
 */
import { useModel } from '@umijs/max';
import React, { useEffect, useState } from 'react';
import PageContainer from '@/components/RightPageContainer';
import type { TopBarSearchItem } from '@/components/TopBar';
import FrontTable from '@/components/Table/FrontTable';
import {
  BreadOptions,
  SearchOption,
  ColumnOptions,
} from '@/constants/ai-board/sent-message';

const Page: React.FC = () => {
  const { tenantList, reload } = useModel('useTenantList');
  const [searchOptions, setSearchOptions] =
    useState<TopBarSearchItem[]>(SearchOption);
  const {
    sentMessageList,
    reload: reloadList,
    loading,
  } = useModel('useSentMessageList');
  const [dataSource, setDataSource] = useState<BoardAPI.SentMessageItem[]>([]);
  useEffect(() => {
    reload();
    reloadList();
  }, []);
  useEffect(() => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    if (Array.isArray(tenantList)) {
      const tmpTenantOptions = tenantList.map((item) => ({
        label: `${item.tnt_name}(${item.tnt_id})`,
        value: item.tnt_id,
      }));
      const sIndex = options.findIndex((item) => item.key === 'tnt_id');
      if (sIndex !== -1) {
        options[sIndex].options = tmpTenantOptions;
      }
    }
    if (Array.isArray(sentMessageList)) {
      const tmpCodeOptions = sentMessageList.map((item) => ({
        label: `${item.mixed_key}`,
        value: item.mixed_key,
      }));
      const sIndex = options.findIndex((item) => item.key === 'mixed_key');
      if (sIndex !== -1) {
        options[sIndex].options = tmpCodeOptions;
      }
    }
    setSearchOptions(options);
  }, [tenantList, sentMessageList]);
  useEffect(() => {
    setDataSource(sentMessageList);
  }, [sentMessageList]);
  return (
    <PageContainer options={BreadOptions}>
      <FrontTable<BoardAPI.SentMessageItem>
        pageTitle="Trading Message"
        searchOptions={searchOptions}
        loading={loading}
        columns={ColumnOptions}
        dataSource={dataSource}
        rowKey={'id'}
        request={reloadList}
        labelWidth={120}
        scroll={{ x: 1000, y: 'auto' }}
        isFold
        // initialValues={defaultParams}
      />
    </PageContainer>
  );
};
export default Page;
