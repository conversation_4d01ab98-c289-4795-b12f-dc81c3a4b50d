/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-11-08 16:39:15
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 17:12:09
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import { Button, Form, Input, message } from 'antd';
import { fetchData } from '@/utils';
import { updateCompanyUser, getCurrentUser } from '@/services/api';
import { history } from '@umijs/max';

type BasicProps = {
  user?: UserAPI.UserListItem;
  initialState?: API.InitialStateType;
  setInitialState: (params: API.InitialStateType) => void;
}

const Page: React.FC<BasicProps> = ({ user, initialState, setInitialState }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (user) {
      form.setFieldsValue({ ...user });
    }
  }, [user]);

  const onGetUserSuccess = (data: any) => {
    // const
    setInitialState({
      ...initialState,
      isCollapsed: initialState?.isCollapsed || false,
      currentUser: {
        ...data
      }
    });
  };

  const fetchCurrentData = () => {
    fetchData({ setLoading, request: getCurrentUser, params: {}, onSuccess: onGetUserSuccess });
  };

  const onUserSuccess = () => {
    message.success('更新成功');
    fetchCurrentData();
  };

  const handleFinish = (values: any) => {
    const params = {
      display_name: values.display_name
    };
    fetchData({ setLoading, request: updateCompanyUser, params, onSuccess: () => onUserSuccess() });
  };

  const handleGoBack = () => {
    history.push('/welcome');
  };
  return (
    <div>
      <h3>基本信息</h3>
      <Form
        form={form}
        layout="vertical"
        labelCol={{ style: { width: 180 }}}
        wrapperCol={{ span: 24 }}
        onFinish={handleFinish}
      >
        <Form.Item
          name="account_name"
          label="账号"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true
            }
          ]}
        >
          <Input style={{ maxWidth: 418 }} disabled />
        </Form.Item>
        <Form.Item
          name="display_name"
          label="昵称"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true
            }
          ]}
        >
          <Input style={{ maxWidth: 418 }} placeholder="请输入昵称"/>
        </Form.Item>
        <Form.Item>
          <div style={{ textAlign: 'right', width: 418 }}>
            <Button onClick={handleGoBack} disabled={loading} style={{ marginRight: 16 }}>
              取消
            </Button>
            <Button type="primary" onClick={() => { form.submit(); }} loading={loading}>
              提交
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};

export default Page;
