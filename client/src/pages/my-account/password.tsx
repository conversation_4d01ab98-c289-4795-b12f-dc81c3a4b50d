/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-11-08 16:39:22
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 17:12:16
 * @Description:
 */
import React, { useState } from 'react';
import { Button, Form, Input } from 'antd';
import { fetchData } from '@/utils';
import { history } from '@umijs/max';
import { useRequest } from 'ahooks';
import { resetPassword, validPassword, logOut } from '@/services/api';
import NormalModal from '@/components/Modal/NormalModal';
import { LoginPath } from '@/constants';

type BasicProps = {
  user?: UserAPI.UserListItem
}

const Page: React.FC<BasicProps> = ({ user }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const { runAsync: checkedPwd } = useRequest(validPassword, {
    debounceWait: 300,
    manual: true
  });

  const onSuccess = async() => {
    NormalModal.success({
      title: '密码重置成功',
      content: '您的密码已重置成功，请重新登录',
      onOk: () => {
        logOut();
        history.push(LoginPath);
      }
    });
  };
  const onError = (data: any) => {
    form.setFields([{ name: 'old_password', errors: [data] }]);
  };

  const handleFinish = (values: any) => {
    const params = {
      user_id: user?.user_id || 0,
      new_password: values.new_password,
      old_password: values.old_password
    };
    fetchData({ setLoading, request: resetPassword, params, onSuccess, onError });
  };

  const handleGoBack = () => {
    history.push('/welcome');
  };
  return (
    <div>
      <h3>重置密码</h3>
      <Form
        form={form}
        layout="vertical"
        labelCol={{ style: { width: 180 }}}
        wrapperCol={{ span: 24 }}
        onFinish={handleFinish}
      >
        <Form.Item
          name="old_password"
          label="旧密码"
          validateTrigger={['onBlur']}
          rules={[
            {
              required: true,
              message: '请输入就密码'
            },
            {
              type: 'string',
              min: 6,
              max: 25,
              message: '请输入6-25个字符'
            },
            {
              validateTrigger: ['onBlur'],
              validator: (_, value, callback) => {
                value &&
                        checkedPwd({ password: value }).then((res: any) => {
                          if (res.code === 0) {
                            callback();
                          } else {
                            callback('当前密码错误');
                          }
                        });
              }
            }
          ]}
          hasFeedback
        >
          <Input.Password style={{ maxWidth: 418 }} allowClear placeholder="请输入当前密码"/>
        </Form.Item>
        <Form.Item
          name="new_password"
          label="新密码"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: '请输入新密码'
            },
            {
              type: 'string',
              min: 6,
              max: 25,
              message: '请输入6-25个字符'
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                const chineseReg = /[\u4e00-\u9fa5]/;
                if (
                  !value ||
                        (reg.test(value) && getFieldValue('old_password') !== value && !chineseReg.test(value))
                ) {
                  return Promise.resolve();
                }
                if (!reg.test(value)) {
                  return Promise.reject(new Error('密码必须包含字母数字'));
                }
                return Promise.reject(new Error('密码不能包含中文'));
              }
            })
          ]}
          hasFeedback
        >
          <Input.Password style={{ maxWidth: 418 }} allowClear placeholder="请输入新密码"/>
        </Form.Item>
        <Form.Item
          name="confirm_password"
          label="确认密码"
          validateTrigger={['onChange', 'onBlur']}
          dependencies={['new_password']}
          rules={[
            {
              required: true,
              message: '请再次确认密码'
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                if (!value || (getFieldValue('new_password') === value && reg.test(value))) {
                  return Promise.resolve();
                }
                if (!reg.test(value)) {
                  return Promise.reject(new Error('密码必须包含字母数字'));
                }
                return Promise.reject(new Error('密码不匹配'));
              }
            })
          ]}
          hasFeedback
        >
          <Input.Password style={{ maxWidth: 418 }} allowClear placeholder="请再次确认密码"/>
        </Form.Item>
        <Form.Item>
          <div style={{ textAlign: 'right', width: 418 }}>
            <Button onClick={handleGoBack} disabled={loading} style={{ marginRight: 16 }}>
              取消
            </Button>
            <Button type="primary" onClick={() => { form.submit(); }} loading={loading}>
              提交
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};

export default Page;
