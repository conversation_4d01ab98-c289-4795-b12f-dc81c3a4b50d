/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-10 19:48:58
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-26 17:16:04
 * @Description:
 */
import React from 'react';
import PageContainer from '@/components/RightPageContainer';
import styles from './index.less';
import { Tabs } from 'antd';
import { useModel } from '@umijs/max';
import BasicPage from './basic';
import PasswordPage from './password';
import { AccountBreadOptions } from '@/constants/my-account/my-account';

const Page: React.FC = () => {
  const { initialState, setInitialState } = useModel('@@initialState');

  return (
    <PageContainer hideMenu={true} isAuto={false} options={AccountBreadOptions} isBack backUrl="/welcome">
      <div className={styles['container']}>
        <Tabs
          defaultActiveKey="1"
          tabPosition="left"
          style={{ height: 220 }}
          items={[
            {
              label: '基本信息',
              key: '1',
              children: <BasicPage user={initialState?.currentUser} initialState={initialState} setInitialState={setInitialState}/>
            },
            {
              label: '重置密码',
              key: '2',
              children: <PasswordPage user={initialState?.currentUser}/>
            }
          ]}
        />
      </div>
    </PageContainer>
  );
};

export default Page;
