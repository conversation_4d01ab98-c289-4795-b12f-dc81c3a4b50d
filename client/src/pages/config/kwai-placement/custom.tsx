import React, { useEffect, useState } from 'react';
import FrontTable from '@/components/Table/FrontTable';
import {
  CustomColumnOptions,
  CustomSearchOption,
} from '@/constants/config/custom';
import RixEngineFont from '@/components/RixEngineFont';
import { useModel } from '@umijs/max';
import OperateRender from '@/components/OperateRender';
import { CommonPlacementProps } from '.';
import AddCustomModel from '../components/AddCustomModel';

const CustomPlacementPage: React.FC<CommonPlacementProps> = ({
  tabOptions,
  onTabChange,
  tntLoading,
  tenantList,
  allDemandList,
}) => {
  const { dataSource, loading, reload } = useModel('useCustomList');
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<ConfigAPI.CustomPlacementItem>();
  const [columns, setColumns] = useState(CustomColumnOptions);
  const [searchOptions, setSearchOptions] = useState(CustomSearchOption);
  const [defaultParams] = useState({ status: 1 });

  const onOperateRender = (_: number, row: ConfigAPI.CustomPlacementItem) => {
    return (
      <OperateRender
        params={row}
        btnOptions={[
          {
            label: 'Edit',
            icon: <RixEngineFont type="edit" />,
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            onClick: () => handleEdit(row),
          },
        ]}
      />
    );
  };

  useEffect(() => {
    const columns = CustomColumnOptions.map((v) => ({ ...v }));
    const index = columns.findIndex((v) => v.dataIndex === 'operate');
    if (index !== -1) {
      columns[index].render = onOperateRender;
    }
    setColumns(columns);
  }, []);

  // 为搜索项添加下拉选项
  useEffect(() => {
    const options = CustomSearchOption.map((v) => ({ ...v }));
    let tenantOptions = Array.isArray(tenantList)
      ? tenantList.map((v) => ({
          label: `${v.tnt_name}(${v.tnt_id})`,
          value: v.tnt_id,
        }))
      : [];
    let demandOptions = Array.isArray(allDemandList)
      ? allDemandList.map((v) => ({
          label: `${v.buyer_name}(${v.buyer_id})`,
          value: v.buyer_id,
        }))
      : [];
    const tIndex = options.findIndex((v) => v.key === 'tnt_id');
    const dIndex = options.findIndex((v) => v.key === 'buyer_id');
    if (tIndex !== -1) {
      options[tIndex].options = tenantOptions;
    }
    if (dIndex !== -1) {
      options[dIndex].options = demandOptions;
    }
    setSearchOptions(options);
  }, [tenantList, allDemandList]);

  const handleEdit = (row: ConfigAPI.CustomPlacementItem) => {
    setEditItem(row);
    setIsEdit(true);
    setVisible(true);
  };
  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    reload();
  };
  return (
    <>
      <FrontTable<ConfigAPI.CustomPlacementItem>
        searchOptions={searchOptions}
        loading={loading || tntLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add Custom Placement',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            access: 'AddCustomPlt',
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={120}
        scroll={{ x: 1000, y: 'auto' }}
        isFold
        defaultTab={'custom'}
        tabOptions={tabOptions}
        onTabChange={onTabChange}
        initialValues={defaultParams}
      />
      <AddCustomModel
        visible={visible}
        isEdit={isEdit}
        item={editItem}
        onClose={handleClose}
        onSave={handleSave}
        tenantList={tenantList || []}
        allDemandList={allDemandList || []}
      />
    </>
  );
};

export default CustomPlacementPage;
