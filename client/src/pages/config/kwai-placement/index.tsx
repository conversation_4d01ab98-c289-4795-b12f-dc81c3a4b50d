import { useSearchParams } from '@umijs/max';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import PageContainer from '@/components/RightPageContainer';
import { BreadcrumbItem } from '@/components/Breadcrumb';
import KwaiPlacement from './kwai';
import CustomPlacement from './custom';
import { useModel } from '@umijs/max';

type Tab = 'kwai' | 'custom' | '' | null;

const BreadOptions: BreadcrumbItem[] = [
  {
    name: 'Config',
    icon: 'rix-advanced',
    key: 'config',
  },
  {
    name: 'Kwai Placement',
    key: 'kwai',
  },
  {
    name: 'Custom Placement',
    key: 'custom',
  },
];

const TabOptions = [
  {
    label: 'Kwai Placement',
    value: 'kwai',
  },
  {
    label: 'Custom Placement',
    value: 'custom',
  },
];

export interface CommonPlacementProps {
  tabOptions: { label: string; value: string | number }[];
  onTabChange: (value: string | number) => void;
  tenantList: TenantAPI.TenantListItem[];
  allDemandList: ConfigAPI.DemandListItem[];
  tntLoading: boolean;
}

const Page: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [tab, setTab] = useState<Tab>('');
  // 面包屑
  const [breadOptions, setBreadOptions] = useState<BreadcrumbItem[]>([]);
  const {
    tenantList,
    reload: reloadTnt,
    loading: tntLoading,
  } = useModel('useTenantList');
  const { allDemandList, reload: reloadDemand } = useModel('useAllDemandList');

  useEffect(() => {
    const tab = searchParams.get('tab') as Tab;
    setTab(tab === 'custom' ? 'custom' : 'kwai');

    reloadTnt();
    reloadDemand();
  }, []);

  useEffect(() => {
    const isCustom = tab === 'custom';
    const tabName = isCustom ? 'custom' : 'kwai';

    setBreadOptions(
      BreadOptions.filter((item) =>
        [tabName, 'config'].includes(item.key ?? ''),
      ),
    );
    if (tab) {
      setSearchParams({ tab }, { replace: true });
    }
  }, [tab]);

  const handleTabChange = useCallback((value: string | number) => {
    setTab(value as Tab);
  }, []);

  const RenderTabComponent = useMemo(() => {
    if (!tab) {
      return null;
    }
    const isCustom = tab === 'custom';

    const Component: React.FC<CommonPlacementProps> = isCustom
      ? CustomPlacement
      : KwaiPlacement;

    return (
      <Component
        tabOptions={TabOptions}
        onTabChange={handleTabChange}
        tenantList={tenantList}
        allDemandList={allDemandList}
        tntLoading={tntLoading}
      />
    );
  }, [tab, tenantList, allDemandList, tntLoading]);

  return (
    <PageContainer options={breadOptions}>{RenderTabComponent}</PageContainer>
  );
};

export default Page;
