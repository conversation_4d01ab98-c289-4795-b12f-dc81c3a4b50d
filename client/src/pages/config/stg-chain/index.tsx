/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-18 18:10:24
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-06 19:41:12
 * @Description:
 */

import React, { useEffect, useRef, useState } from 'react';
import PageContainer from '@/components/RightPageContainer';
import FrontTable from '@/components/Table/FrontTable';
import {
  StgBreadOptions,
  StgColumnOptions,
  BundleStgSearchOption,
  DomainStgSearchOption,
  StgTab,
  StgTabOptions,
} from '@/constants/config/stg';
import RixEngineFont from '@/components/RixEngineFont';
import { useModel } from '@umijs/max';
import AddStgChain from '../components/AddStgChain';
import AddStgChainV2 from '../components/AddStgChainV2';

import OperateRender from '@/components/OperateRender';
import NormalModal from '@/components/Modal/NormalModal';
import { Checkbox, Col, Form, FormInstance, Row } from 'antd';
import { downloadCsv } from '@/utils';
import HoverToolTip from '@/components/Tooltip/HoverTooltip';

const Page: React.FC = () => {
  const {
    dataSource: bundleData,
    loading: loadingBundle,
    reload: reloadBundle,
  } = useModel('useStgChainList');
  const {
    dataSource: domainData,
    loading: loadingDomain,
    reload: reloadDomain,
  } = useModel('useStgChainListV2');
  const {
    tenantList,
    reload: reloadTnt,
    loading: tntLoading,
  } = useModel('useTenantList');

  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<ConfigAPI.StgItem>();
  const [columns, setColumns] = useState(StgColumnOptions);
  const [searchOptions, setSearchOptions] = useState(BundleStgSearchOption);
  const [defaultParams] = useState({ status: 1 });
  const [defaultExportColumns] = useState(['publisher_id', 'type', 'bundle']);
  const [currentTab, setCurrentTab] = useState(StgTab.bundle);
  const [form] = Form.useForm();
  const searchFormRef = useRef<FormInstance<any>>(null);
  const onOperateRender = (_: number, row: ConfigAPI.StgItem) => {
    return (
      <OperateRender
        params={row}
        btnOptions={[
          {
            label: 'Edit',
            icon: <RixEngineFont type="edit" />,
            // eslint-disable-next-line @typescript-eslint/no-use-before-define
            onClick: () => handleEdit(row),
          },
        ]}
      />
    );
  };

  const handleColumns = (currentTab?: string) => {
    const columns = StgColumnOptions.map((v) => ({ ...v }));
    const index = columns.findIndex((v) => v.dataIndex === 'operate');
    if (index !== -1) {
      columns[index].render = onOperateRender;
    }
    const bIndex = columns.findIndex((v) => v.dataIndex === 'bundle');
    if (bIndex !== -1 && currentTab === StgTab.domain) {
      columns[bIndex].dataIndex = 'developer_website_domain';
      columns[bIndex].title = 'Domain';
      columns[bIndex].render = (_, params) => (
        <HoverToolTip title={`${params.developer_website_domain}`}>
          <span>{_}</span>
        </HoverToolTip>
      );
    }
    setColumns(columns);
  };
  useEffect(() => {
    reloadTnt();
    handleColumns();
  }, []);

  useEffect(() => {
    let options =
      currentTab === StgTab.bundle
        ? BundleStgSearchOption
        : DomainStgSearchOption;
    //
    options = options.map((v) => ({ ...v }));
    let dataSource = currentTab === StgTab.bundle ? bundleData : domainData;
    let tenantOptions = Array.isArray(tenantList)
      ? tenantList.map((v) => ({
          label: `${v.tnt_name}(${v.tnt_id})`,
          value: v.tnt_id,
        }))
      : [];
    tenantOptions.unshift({ label: 'All Tenants', value: 0 });
    const tIndex = options.findIndex((v) => v.key === 'tnt_id');
    const pIndex = options.findIndex((v) => v.key === 'publisher_id');
    const dIndex = options.findIndex((v) => v.key === 'bundle');
    const dmIndex = options.findIndex(
      (v) => v.key === 'developer_website_domain',
    );
    if (tIndex !== -1) {
      options[tIndex].options = tenantOptions;
    }
    if (pIndex !== -1) {
      const arr = [
        ...new Set(
          dataSource?.map((v) => v.publisher_id).filter((v) => v) || [],
        ),
      ];
      options[pIndex].options = arr.map((v, index) => ({
        label: v,
        value: v,
        key: index,
      }));
    }
    if (dIndex !== -1) {
      const arr = [
        ...new Set(
          dataSource?.map((v) => v.bundle).filter((v) => v && v.trim()) || [],
        ),
      ];
      options[dIndex].options = arr.map((v, index) => ({
        label: v,
        value: v,
        key: index,
      }));
    }
    if (dmIndex !== -1) {
      const arr = [
        ...new Set(
          dataSource
            ?.map((v) => v.developer_website_domain)
            .filter((v) => v && v.trim()) || [],
        ),
      ];
      options[dmIndex].options = arr.map((v, index) => ({
        label: v,
        value: v,
        key: index,
      }));
    }

    setSearchOptions(options);
  }, [tenantList, bundleData, domainData, currentTab]);

  useEffect(() => {
    if (currentTab) {
      currentTab === StgTab.bundle ? reloadBundle() : reloadDomain();
      handleColumns(currentTab);
    }
  }, [currentTab]);

  const handleEdit = (row: ConfigAPI.StgItem) => {
    setEditItem(row);
    setIsEdit(true);
    setVisible(true);
  };

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    currentTab === StgTab.bundle ? reloadBundle() : reloadDomain();
  };

  // tableData
  const tableDataRef = useRef<ConfigAPI.StgItem[]>([]);
  const handleFinish = (values: any) => {
    const { export_columns } = values;
    const fileName = `report_${new Date().getTime()}`;
    const fields: { label: string; value: string }[] = [];
    columns.forEach((item) => {
      if (export_columns.includes(item.dataIndex as string)) {
        fields.push({
          label: item.title as string,
          value: item.dataIndex as string,
        });
      }
    });
    downloadCsv(fileName, tableDataRef.current, { fields });
    form.resetFields();
    tableDataRef.current = [];
  };
  const handleExport = (tableData: ConfigAPI.StgItem[]) => {
    const checkBoxOptions = columns
      .map((v) => ({
        label: v.title as string,
        value: v.dataIndex as string,
      }))
      .filter((v) => v.value !== 'operate');

    NormalModal.confirm({
      title: 'Export',
      content: (
        <Form onFinish={handleFinish} form={form}>
          <Form.Item
            noStyle
            name="export_columns"
            initialValue={defaultExportColumns}
          >
            <Checkbox.Group>
              <Row>
                {checkBoxOptions.map((v, index) => (
                  <Col key={index} span={12}>
                    <Checkbox value={v.value}>{v.label}</Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      ),
      onOk: () => {
        tableDataRef.current = tableData;
        form.submit();
      },
      onCancel: () => {
        form.resetFields();
        tableDataRef.current = [];
      },
    });
  };
  const handleTabChange = (tab: string | number) => {
    setCurrentTab(tab as string);
  };

  return (
    <PageContainer options={StgBreadOptions}>
      <FrontTable<ConfigAPI.StgItem>
        searchFormRef={searchFormRef}
        searchOptions={searchOptions}
        loading={loadingBundle || loadingDomain || tntLoading}
        columns={columns}
        dataSource={currentTab === StgTab.bundle ? bundleData : domainData}
        rowKey={'id'}
        request={currentTab === StgTab.bundle ? reloadBundle : reloadDomain}
        btnOptions={[
          {
            label: 'Add Supply Chain',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={120}
        scroll={{ x: 1000, y: 'auto' }}
        isFold
        initialValues={defaultParams}
        isExport
        handleExport={handleExport}
        tabOptions={StgTabOptions}
        onTabChange={handleTabChange}
        defaultTab={currentTab}
      />
      {currentTab === StgTab.bundle ? (
        <AddStgChain
          visible={visible}
          isEdit={isEdit}
          item={editItem}
          onClose={handleClose}
          onSave={handleSave}
          tenantList={tenantList || []}
        />
      ) : (
        <AddStgChainV2
          visible={visible}
          isEdit={isEdit}
          item={editItem}
          onClose={handleClose}
          onSave={handleSave}
          tenantList={tenantList || []}
        />
      )}
    </PageContainer>
  );
};

export default Page;
