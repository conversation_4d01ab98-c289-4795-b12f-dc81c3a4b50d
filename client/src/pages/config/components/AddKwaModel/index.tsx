/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-15 14:27:36
 * @LastEditors: chen<PERSON>dan <EMAIL>
 * @LastEditTime: 2023-08-15 17:55:12
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import { Form, Radio, message, Input } from 'antd';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { fetchData } from '@/utils';
import { DefaultFormData } from '@/constants/config/kwai';
import { StatusOptions } from '@/constants';
import { addKwaPlacement, updateKwaPlacement } from '@/services/api';

type AddKwaProps = {
  isEdit: boolean;
  visible: boolean;
  item?: ConfigAPI.KwaiPlacementItem;
  onClose: () => void;
  onSave: () => void;
  tenantList: TenantAPI.TenantListItem[];
  allDemandList: ConfigAPI.DemandListItem[];
};

const AddKwaModel: React.FC<AddKwaProps> = ({
  visible,
  isEdit,
  onClose,
  tenantList,
  allDemandList,
  onSave,
  item,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [demandList, setDemandList] = useState<
    { buyer_id: number; buyer_name: string }[]
  >([]);
  const [nativePid, setNativePid] = useState('');
  const [intersPid, setIntersPid] = useState('');
  const [rewardPid, setRewardPid] = useState('');

  useEffect(() => {
    form.resetFields();
    setNativePid('');
    setIntersPid('');
    setRewardPid('');
    if (visible && isEdit && item) {
      setNativePid(item.native_pid || '');
      setIntersPid(item.inters_pid || '');
      setRewardPid(item.reward_pid || '');
      form.setFieldsValue(item);
      handleTntChange(item.tnt_id);
    }
  }, [item, visible, isEdit]);

  const handleTntChange = (tnt_id: number) => {
    const dOptions = allDemandList.filter((v) => v.tnt_id === tnt_id);
    setDemandList(dOptions);
  };

  const onConfirm = () => {
    form.submit();
  };

  const onSuccess = () => {
    message.success(isEdit ? 'Edit Success' : 'Add Success');
    onSave();
  };

  const handleAddKwa = (params: any) => {
    fetchData({ request: addKwaPlacement, params, onSuccess, setLoading });
  };

  const handleUpdateKwa = (params: any) => {
    fetchData({ request: updateKwaPlacement, params, onSuccess, setLoading });
  };

  const handleFinish = (values: any) => {
    if (isEdit) {
      const params = {
        ...values,
        id: item?.id,
        ori_data: item,
      };
      handleUpdateKwa(params);
    } else {
      handleAddKwa(values);
    }
  };

  const handleValueChange = (changeValue: any, allValue: any) => {
    setNativePid(allValue.native_pid || '');
    setIntersPid(allValue.inters_pid || '');
    setRewardPid(allValue.reward_pid || '');
    if (changeValue.tnt_id) {
      handleTntChange(+changeValue.tnt_id);
      const params = {
        ...allValue,
        buyer_id: undefined,
      };
      // 重置数据
      form.setFieldsValue(params);
    }
  };

  return (
    <NormalDrawer
      open={visible}
      blackName={isEdit ? 'Edit Kwai Placement' : 'Add Kwai Placement'}
      onClose={onClose}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
        onFinish={handleFinish}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        initialValues={DefaultFormData}
      >
        <Form.Item
          label="Tenant"
          name="tnt_id"
          rules={[{ required: true, message: 'Please Select Tenant' }]}
        >
          <NormalSelect
            options={tenantList.map((v) => ({
              label: `${v.tnt_name}(${v.tnt_id})`,
              value: v.tnt_id,
            }))}
            placeholder="Please Select Tenant"
            allowClear
            disabled={isEdit}
            showSearch
          />
        </Form.Item>
        <Form.Item
          label="Demand"
          name="buyer_id"
          rules={[{ required: true, message: 'Please Select Demand' }]}
        >
          <NormalSelect
            options={demandList.map((v) => ({
              label: `${v.buyer_name}(${v.buyer_id})`,
              value: v.buyer_id,
            }))}
            placeholder="Please Select Demand"
            allowClear
            disabled={isEdit}
            showSearch
          />
        </Form.Item>
        <Form.Item
          label="Bundle"
          name="bundle"
          rules={[{ required: true, message: 'Please Input Bundle' }]}
        >
          <Input placeholder="please input bundle" disabled={isEdit} />
        </Form.Item>
        <Form.Item
          label="App ID"
          name="app_id"
          rules={[{ required: true, message: 'Please Input App ID' }]}
        >
          <Input placeholder="please input App ID" />
        </Form.Item>
        <Form.Item
          label="Native Unit ID"
          name="native_pid"
          rules={[
            { required: !intersPid && !rewardPid, message: 'Native Unit ID' },
          ]}
        >
          <Input placeholder="please input Native Unit ID" />
        </Form.Item>
        <Form.Item
          label="Interstitial Video Unit ID"
          name="inters_pid"
          rules={[
            {
              required: !nativePid && !rewardPid,
              message: 'please Input Interstitial Video Unit ID',
            },
          ]}
        >
          <Input placeholder="please input Interstitial Unit ID" />
        </Form.Item>
        <Form.Item
          label="Reward Video Unit ID"
          name="reward_pid"
          rules={[
            {
              required: !nativePid && !intersPid,
              message: 'Please Input Reward Video Unit ID',
            },
          ]}
        >
          <Input placeholder="please input Reward Video placement ID" />
        </Form.Item>
        <Form.Item
          label="Token"
          name="token"
          rules={[{ required: true, message: 'Please Input Token' }]}
        >
          <Input placeholder="please input token" />
        </Form.Item>
        <Form.Item
          label="Mixed Status"
          name="mixed_status"
          rules={[{ required: true }]}
        >
          <NormalRadio>
            {StatusOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        </Form.Item>
        {isEdit && (
          <Form.Item label="Status" name="status" rules={[{ required: true }]}>
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddKwaModel;
