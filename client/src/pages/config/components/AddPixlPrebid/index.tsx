/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-04-03 16:39:56
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-03 19:03:23
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import { Form, Radio, message } from 'antd';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { fetchData } from '@/utils';
import { addPixlPrebid, updatePixlPrebid } from '@/services/api';
import { DefaultFormData } from '@/constants/config/pixalate-prebid';
import { StatusOptions } from '@/constants';
import { IsAllMap } from '@/constants/config/atc';

type AddPixlPrebidProps = {
  isEdit: boolean;
  visible: boolean;
  item?: ConfigAPI.PixlItem;
  onClose: () => void;
  onSave: () => void;
  tenantList: TenantAPI.TenantListItem[];
  supplyList: ConfigAPI.SupplyListItem[];
  demandList: ConfigAPI.DemandListItem[];
};
const AddPixlPrebidModel: React.FC<AddPixlPrebidProps> = ({
  visible,
  isEdit,
  item,
  onClose,
  onSave,
  tenantList,
  supplyList,
  demandList,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isAll, setIsAll] = useState(IsAllMap.DEFAULT);
  const tenantOptions = tenantList?.map((v) => ({
    label: `${v.tnt_name}(${v.tnt_id})`,
    value: v.tnt_id,
  }));
  const [supplyOptions, setSupplyOptions] = useState<
    { label: string; value: number; disabled?: boolean }[]
  >([]);
  const [demandOptions, setDemandOptions] =
    useState<{ label: string; value: number }[]>();

  useEffect(() => {
    if (visible && isEdit && item) {
      const sOptions =
        supplyList?.map((v) => ({
          label: `${v.seller_name}(${v.seller_id})`,
          value: v.seller_id,
        })) || [];

      const dOptions =
        demandList?.map((v) => ({
          label: `${v.buyer_name}(${v.buyer_id})`,
          value: v.buyer_id,
        })) || [];
      setDemandOptions(dOptions);
      setSupplyOptions(sOptions);
      form.setFieldsValue(item);
    } else {
      form.resetFields();
    }
  }, [item, visible, isEdit]);

  const onConfirm = () => {
    form.submit();
  };

  const handleValueChange = (changeValue: any, allValue: any) => {
    if (changeValue.tnt_id) {
      const sOptions = supplyList
        .filter((v) => v.tnt_id === changeValue.tnt_id)
        .map((v) => ({
          label: `${v.seller_name}(${v.seller_id})`,
          value: v.seller_id,
          disabled: false,
        }));
      const dOptions = demandList
        .filter((v) => v.tnt_id === changeValue.tnt_id)
        .map((v) => ({
          label: `${v.buyer_name}(${v.buyer_id})`,
          value: v.buyer_id,
        }));
      setDemandOptions(dOptions);
      setSupplyOptions(sOptions);
      const params = {
        ...allValue,
        buyer_id: undefined,
        seller_id: undefined
      };
      // 重置数据
      form.setFieldsValue(params);
    }
    if (!changeValue.seller_id?.length) setIsAll(IsAllMap.DEFAULT);
    if (allValue.seller_id?.length) {
      const isAll = allValue.seller_id.includes(0);
      setIsAll(isAll ? IsAllMap.YES : IsAllMap.NO);
    }
  };

  const onSuccess = () => {
    message.success(isEdit ? 'Edit Success' : 'Add Success');
    setIsAll(IsAllMap.DEFAULT);
    setDemandOptions([]);
    setSupplyOptions([]);
    onSave();
  };

  const handleAddPixlPrebid = (params: ConfigAPI.PixlItem) => {
    fetchData({ request: addPixlPrebid, params, onSuccess, setLoading });
  };

  const handleUpdatePixlPrebid = (params: ConfigAPI.PixlItem) => {
    fetchData({ request: updatePixlPrebid, params, onSuccess, setLoading });
  };

  const handleFinish = (values: ConfigAPI.PixlItem) => {
    if (isEdit) {
      const params = {
        ...values,
        id: item!.id,
        ori_data: item,
      };
      handleUpdatePixlPrebid(params);
    } else {
      handleAddPixlPrebid({ ...values });
    }
  };

  const handleClose = () => {
    setIsAll(IsAllMap.DEFAULT);
    setDemandOptions([]);
    setSupplyOptions([]);
    onClose?.();
  };
  return (
    <NormalDrawer
      open={visible}
      blackName={isEdit ? 'Edit Pixalate Prebid' : 'Add Pixalate Prebid'}
      onClose={handleClose}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
        onFinish={handleFinish}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        initialValues={{ ...DefaultFormData }}
      >
        <Form.Item
          label="Tenant"
          name="tnt_id"
          rules={[{ required: true, message: 'Please Select Tenant' }]}
        >
          <NormalSelect
            options={tenantOptions}
            placeholder="Please Select Tenant"
            allowClear
            disabled={isEdit}
            showSearch
          />
        </Form.Item>
        <Form.Item
          label="Advertiser"
          name="buyer_id"
          rules={[{ required: true, message: 'Please Select Advertiser' }]}
        >
          <NormalSelect
            options={demandOptions}
            placeholder="Please Select Advertiser"
            allowClear
            disabled={isEdit}
            showSearch
            mode="multiple"
          />
        </Form.Item>
        <Form.Item label="Publisher" name="seller_id">
          <NormalSelect
            placeholder="Please Select Publisher"
            allowClear
            disabled={isEdit}
            showSearch
            mode="multiple"
            optionFilterProp="children"
            filterOption={(input, option: any) => {
              return option?.children?.toLowerCase().indexOf(input?.toLowerCase()) >= 0
            }}
          >
            <NormalSelect.Option
              value={0}
              key={-1}
              disabled={isAll === IsAllMap.NO}
            >
              All Publishers
            </NormalSelect.Option>
            {supplyOptions &&
              supplyOptions.map((item, index: number) => {
                return (
                  <NormalSelect.Option
                    value={item.value}
                    key={index}
                    disabled={isAll === IsAllMap.YES}
                  >
                    {item.label}
                  </NormalSelect.Option>
                );
              })}
          </NormalSelect>
        </Form.Item>
        {isEdit && (
          <Form.Item label="Status" name="status" rules={[{ required: true }]}>
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddPixlPrebidModel;
