import React, { useEffect, useState } from 'react';
import { Form, Radio, message } from 'antd';

import NormalInput from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import EditTag from '@/components/EditTag';

import { StatusOptions } from '@/constants';
import {
  EcprLevelTypeOptions,
  EcprRegionOptions,
  EcprLevelType,
} from '@/constants/config/ecpr';

import { addEcprConfig, editEcprConfig } from '@/services/api';

import { fetchData } from '@/utils';
import { useModel } from '@umijs/max';

type AddEcprProps = {
  isEdit: boolean;
  visible: boolean;
  setVisible: (params: boolean) => void;
  rowEcprInfo?: ConfigAPI.EcprListItem;
  reloadEcprList: () => void;
  tenantOptions: CommonAPI.OptionsType[];
};
const DefalutValue = {
  type: EcprLevelType['Seller-Buyer'],
};
const AddEcprConfig: React.FC<AddEcprProps> = ({
  isEdit,
  visible,
  setVisible,
  rowEcprInfo,
  reloadEcprList,
  tenantOptions,
}) => {
  const [form] = Form.useForm();
  const [tierEcpr, setTierEcpr] = useState<string[]>([]);
  const [tierQps, setTierQps] = useState<string[]>([]);
  const [isChange, setIsChange] = useState<boolean>(false);
  const { initialState } = useModel('@@initialState');
  useEffect(() => {
    if (isEdit && visible) {
      form.setFieldsValue({
        ...rowEcprInfo,
      });
      const { tier_ecpr, tier_qps } = rowEcprInfo || {};
      form.setFieldsValue({
        tier_ecpr: { tags: tier_ecpr?.split(',') || [] },
        tier_qps: { tags: tier_qps?.split(',') || [] },
      });
      setTierEcpr(tier_ecpr?.split(',') || []);
      setTierQps(tier_qps?.split(',') || []);
    } else {
      form.resetFields();
      form.setFieldsValue(DefalutValue);
    }
  }, [isEdit, visible]);

  useEffect(() => {
    if (!tierEcpr?.length) {
      setTierQps([]);
    } else {
      const tmpQps = new Array(tierEcpr.length + 1).fill('');
      const newQps = tierQps.concat(tmpQps).slice(0, tmpQps.length);
      setTierQps(newQps);
    }
  }, [tierEcpr]);
  useEffect(() => {
    form.setFieldValue('tier_qps', { tags: tierQps });
  }, [tierQps]);
  const handleValueChange = (changedValues: any) => {
    if (changedValues?.tnt_id) {
      setIsChange(changedValues?.tnt_id !== rowEcprInfo?.tnt_id);
    }
    if (changedValues.server_region) {
      setIsChange(changedValues.server_region !== rowEcprInfo?.server_region);
    }
  };
  const handleConfirm = () => {
    form.submit();
  };
  const onSuccess = (data: any) => {
    message.success('Success');
    reloadEcprList();
    setVisible(false);
    setIsChange(false);
    form.resetFields();
  };
  const handleFinish = (values: any) => {
    const params = {
      ...values,
      op_id: initialState?.currentUser?.admin_id,
      ori_data: rowEcprInfo,
    };

    isEdit
      ? fetchData({
          request: editEcprConfig,
          params: { ...params, uniKeyChange: isChange, id: rowEcprInfo?.id },
          onSuccess,
        })
      : fetchData({
          request: addEcprConfig,
          params: params,
          onSuccess,
        });
  };

  const handleCancel = () => {
    setVisible(false);
    setIsChange(false);

    setTierEcpr([]);
    setTierQps([]);
  };
  const checkTags = (_: any, value: { tags: string[] }) => {
    if (!value || !value.tags.length) {
      return Promise.reject('Please Input Tier Ecpr');
    }

    return Promise.resolve();
  };
  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Ecpr Config` : `Add Ecpr Config`}
      loading={false}
      onConfirm={handleConfirm}
      open={visible}
      onClose={handleCancel}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
        onFinish={handleFinish}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
      >
        <Form.Item
          label="Tenant Name"
          name="tnt_id"
          rules={[{ required: true, message: 'Please Input Tenant Name!' }]}
        >
          <NormalSelect
            options={tenantOptions}
            placeholder="Please Input Tenant Name"
          />
        </Form.Item>

        <Form.Item
          label="Type"
          name="type"
          rules={[{ required: true, message: 'Please Select Type!' }]}
        >
          <NormalSelect
            options={EcprLevelTypeOptions}
            allowClear={false}
            disabled={true}
          />
        </Form.Item>
        <Form.Item
          name="server_region"
          label="Server Region:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: 'Please Select Server Region',
            },
          ]}
        >
          <NormalSelect
            placeholder="Please Select Server Region"
            showSearch
            optionFilterProp="children"
            options={EcprRegionOptions}
          ></NormalSelect>
        </Form.Item>
        <Form.Item
          name="filter_qps"
          label="Filter QPS:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              validator: (rule, value) => {
                let vaildValue = Number(value);
                if (isNaN(vaildValue)) {
                  return Promise.reject('Please Input Number');
                }
                if (!vaildValue) {
                  return Promise.reject('Please Input QPS');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <NormalInput></NormalInput>
        </Form.Item>
        <Form.Item
          name="newer_qps"
          label="Newer QPS:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              validator: (rule, value) => {
                let vaildValue = Number(value);
                if (isNaN(vaildValue)) {
                  return Promise.reject('Please Input Number');
                }
                if (!vaildValue) {
                  return Promise.reject('Please Select or Input QPS');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <NormalInput></NormalInput>
        </Form.Item>
        <Form.Item
          name="tier_ecpr"
          label="Tier Ecpr:"
          tooltip="Please DoubleClick The tag to Edit"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              validator: checkTags,
            },
          ]}
        >
          <EditTag
            tags={tierEcpr}
            setTags={setTierEcpr}
            showAdd={true}
            colseable={true}
          ></EditTag>
        </Form.Item>
        <Form.Item
          name="tier_qps"
          label="Tier QPS:"
          rules={[
            {
              required: true,
              validator: checkTags,
            },
          ]}
        >
          <EditTag
            tags={tierQps}
            setTags={setTierQps}
            showAdd={false}
            colseable={false}
          ></EditTag>
        </Form.Item>
        {isEdit && (
          <>
            <Form.Item
              label="Status"
              name="status"
              rules={[{ required: true }]}
            >
              <NormalRadio>
                {StatusOptions.map((item, index) => (
                  <Radio key={index} value={item.value}>
                    {item.label}
                  </Radio>
                ))}
              </NormalRadio>
            </Form.Item>
          </>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddEcprConfig;
