/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-10-16 17:28:14
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-12 14:32:51
 * @Description:
 */

import BundleTableEdit from '@/components/BundleTableEdit';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import AllSelectSearch from '@/components/Input/AllSelectSearch';
import InputNumberNormal from '@/components/Input/InputNumber';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import { StatusOptions } from '@/constants';
import { DefaultFormData, IvtTypeOptions } from '@/constants/config/ivt';
import { CountryOptions } from '@/constants/country';
import { addIvtConfig, updateIvtConfig } from '@/services/api';
import { fetchData, removeZWSpace } from '@/utils';
import { Form, Input, message } from 'antd';
import React, { useEffect, useState } from 'react';

type Options = { label: string; value: number; key?: number; tnt_id?: number };

type AddIvtConfigProps = {
  isEdit: boolean;
  visible: boolean;
  item?: ConfigAPI.IvtConfigItem;
  onClose: () => void;
  onSave: () => void;
  tenantOptions: Options[];
  demandOptions: Options[];
  supplyOptions: Options[];
};
const AddIvtConfigModel: React.FC<AddIvtConfigProps> = ({
  visible,
  isEdit,
  item,
  onClose,
  onSave,
  tenantOptions,
  demandOptions,
  supplyOptions,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [bundleList, setBundleList] = useState<string[]>([]);

  useEffect(() => {
    form.resetFields();
    setBundleList([]);

    if (visible && isEdit && item) {
      const { seller_id, buyer_id, bundle = '', country = '', ...rest } = item;
      // split 后时存在空字符串 需要过滤掉
      const bundleList = bundle.split(',').filter((v) => v);
      const countryList = country.split(',').filter((v) => v);
      const tmp = {
        ...rest,
        // 编辑时单选
        seller_id: seller_id || undefined,
        buyer_id: buyer_id || undefined,
        bundle: bundleList,
        country: countryList,
      };
      setBundleList(bundleList);
      form.setFieldsValue(tmp);
    }
  }, [item, visible, isEdit]);

  const handleValueChange = (changeValue: any, allValue: any) => {
    if (changeValue.tnt_id) {
      const params = {
        ...allValue,
        buyer_id: [],
        seller_id: [],
      };
      // 重置数据
      form.setFieldsValue(params);
    }
  };

  const onSuccess = () => {
    message.success(isEdit ? 'Edit Success' : 'Add Success');
    onSave();
  };

  const handleAddIvtConfig = (params: ConfigAPI.AddIvtParams) => {
    fetchData({ request: addIvtConfig, params, onSuccess, setLoading });
  };

  const handleUpdateIvtConfig = (params: ConfigAPI.UpdateIvtParams) => {
    // params.isChange = isChange;
    const { seller_id, buyer_id } = params;
    if (seller_id?.length && seller_id[0] === 0) {
      delete params.seller_id;
    }
    if (buyer_id?.length && buyer_id[0] === 0) {
      delete params.buyer_id;
    }
    fetchData({ request: updateIvtConfig, params, onSuccess, setLoading });
  };

  const onConfirm = async () => {
    const { seller_id, buyer_id, bundle, ...rest } =
      await form.validateFields();

    const bundleStr = removeZWSpace(bundle.join(','));
    const sellerIdList = Array.isArray(seller_id)
      ? seller_id.filter((v) => v)
      : [seller_id || 0];
    const buyerIdList = Array.isArray(buyer_id)
      ? buyer_id.filter((v) => v)
      : [buyer_id || 0];

    const params = {
      ...rest,
      seller_id: sellerIdList,
      buyer_id: buyerIdList,
      bundle: bundleStr,
      ori_data: item,
    };

    // console.log(JSON.stringify(params));

    if (isEdit) {
      handleUpdateIvtConfig(params);
    } else {
      handleAddIvtConfig(params);
    }
  };

  const handleBundleChange = (val: string[]) => {
    setBundleList(val);
    form.setFieldValue('bundle', val);
  };

  return (
    <NormalDrawer
      open={visible}
      blackName={isEdit ? 'Edit IVT Config' : 'Add IVT Config'}
      onClose={onClose}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        initialValues={{ ...DefaultFormData }}
      >
        {/* id hidden */}
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>
        <Form.Item
          label="Tenant"
          name="tnt_id"
          rules={[{ required: true, message: 'Please Select Tenant' }]}
        >
          <NormalSelect
            options={tenantOptions}
            placeholder="Please Select Tenant"
            allowClear
            disabled={isEdit}
            showSearch
          />
        </Form.Item>
        {/* seller_id */}
        <Form.Item noStyle dependencies={['tnt_id']}>
          {({ getFieldValue }) => {
            const tntId = getFieldValue('tnt_id');
            return (
              <Form.Item label="Publisher" name="seller_id">
                <NormalSelect
                  placeholder="Please Select Publisher"
                  allowClear
                  mode={isEdit ? undefined : 'multiple'}
                  options={supplyOptions.filter((v) => v.tnt_id === tntId)}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        {/* buyer_id */}
        <Form.Item noStyle dependencies={['tnt_id']}>
          {({ getFieldValue }) => {
            const tntId = getFieldValue('tnt_id');
            return (
              <Form.Item label="Advertiser" name="buyer_id">
                {isEdit ? (
                  <NormalSelect
                    placeholder="Please Select Advertiser"
                    options={demandOptions.filter((v) => v.tnt_id === tntId)}
                    allowClear
                    showSearch
                  />
                ) : (
                  <AllSelectSearch
                    placeholder="Please Select Advertiser"
                    options={demandOptions.filter((v) => v.tnt_id === tntId)}
                    allowClear
                    mode={isEdit ? undefined : 'multiple'}
                  />
                )}
              </Form.Item>
            );
          }}
        </Form.Item>
        <Form.Item label="Country" name="country">
          <NormalSelect
            placeholder="Please Select Country"
            options={CountryOptions}
            mode="multiple"
            allowClear
          />
        </Form.Item>
        <Form.Item
          name="ratio"
          label="Traffic Ratio:"
          validateTrigger={['onChange', 'onBlur']}
          validateFirst
          rules={[{ required: true, message: 'Please Input Traffic Ratio' }]}
        >
          <InputNumberNormal style={{ width: '150px' }} min={1} max={100} />
        </Form.Item>
        <Form.Item label="Bundle" name="bundle">
          <BundleTableEdit
            editTitle={'Edit Bundle List'}
            editTips={`Enter the bundle(one per line)`}
            deleteAllTitle="Delete All"
            deleteAllTips={`Are you sure to delete all bundle?`}
            onValueChange={handleBundleChange}
            defaultList={bundleList}
            contentMaxHeight={
              isEdit ? 'calc(100vh - 500px)' : 'calc(100vh - 430px)'
            }
            editSingle={true}
          />
        </Form.Item>
        <Form.Item label="Type" name="type" rules={[{ required: true }]}>
          <NormalRadio options={IvtTypeOptions} />
        </Form.Item>
        {isEdit && (
          <Form.Item label="Status" name="status" rules={[{ required: true }]}>
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddIvtConfigModel;
