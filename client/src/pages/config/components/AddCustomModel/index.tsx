import React, { useEffect, useMemo, useState } from 'react';
import { Form, message, Input } from 'antd';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { fetchData } from '@/utils';
import { DefaultFormData } from '@/constants/config/custom';
import { StatusOptions } from '@/constants';
import { addCustomPlacement, updateCustomPlacement } from '@/services/config';

type AddCustomProps = {
  isEdit: boolean;
  visible: boolean;
  item?: ConfigAPI.CustomPlacementItem;
  onClose: () => void;
  onSave: () => void;
  tenantList: TenantAPI.TenantListItem[];
  allDemandList: ConfigAPI.DemandListItem[];
};

const AddCustomModel: React.FC<AddCustomProps> = ({
  visible,
  isEdit,
  onClose,
  tenantList,
  allDemandList,
  onSave,
  item,
}) => {
  const [form] = Form.useForm<Partial<ConfigAPI.CustomPlacementItem>>();
  const [loading, setLoading] = useState(false);

  // 编辑状态下，初始化表单数据
  useEffect(() => {
    form.resetFields();
    if (visible && isEdit && item) {
      form.setFieldsValue(item);
    }
  }, [item, visible, isEdit]);

  // 监听表单值变化
  const tntId = Form.useWatch('tnt_id', form);

  const demandListOptions = useMemo(() => {
    return allDemandList
      .filter((v) => v.tnt_id === tntId)
      .map((v) => ({
        label: `${v.buyer_name}(${v.buyer_id})`,
        value: v.buyer_id,
      }));
  }, [allDemandList, form, tntId]);

  const onSuccess = () => {
    message.success(isEdit ? 'Edit Success' : 'Add Success');
    onSave();
  };

  // 表单提交
  const onConfirm = async () => {
    const formData = await form.validateFields();

    // 格式化参数
    const params = Object.entries(formData).reduce((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = value;
      }
      return acc;
    }, {} as any);

    if (
      !params.banner_tag_id &&
      !params.native_tag_id &&
      !params.video_tag_id &&
      !params.rewarded_video_tag_id
    ) {
      message.error('At least one tag id');
      return;
    }

    const request = isEdit ? updateCustomPlacement : addCustomPlacement;

    fetchData({
      request,
      params: { ...params, ori_data: item },
      setLoading,
      onSuccess,
    });
  };

  const handleValueChange = (changedValues: any) => {
    if (changedValues.tnt_id) {
      // 值变化时，清空买家选项
      form.setFieldsValue({ buyer_id: undefined });
    }
  };

  return (
    <NormalDrawer
      open={visible}
      blackName={isEdit ? 'Edit Custom Placement' : 'Add Custom Placement'}
      onClose={onClose}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        initialValues={DefaultFormData}
      >
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>
        <Form.Item
          label="Tenant"
          name="tnt_id"
          rules={[{ required: true, message: 'Please Select Tenant' }]}
        >
          <NormalSelect
            options={tenantList.map((v) => ({
              label: `${v.tnt_name}(${v.tnt_id})`,
              value: v.tnt_id,
            }))}
            placeholder="Please Select Tenant"
            allowClear
            disabled={isEdit}
            showSearch
          />
        </Form.Item>
        <Form.Item
          label="Demand"
          name="buyer_id"
          rules={[{ required: true, message: 'Please Select Demand' }]}
        >
          <NormalSelect
            options={demandListOptions}
            placeholder="Please Select Demand"
            allowClear
            disabled={isEdit}
            showSearch
          />
        </Form.Item>
        <Form.Item label="Bundle" name="bundle">
          <Input placeholder="please input bundle" disabled={isEdit} />
        </Form.Item>
        <Form.Item label="App ID" name="app_id">
          <Input placeholder="please input App ID" />
        </Form.Item>
        <Form.Item label="Banner Tag ID" name="banner_tag_id">
          <Input placeholder="please input Banner Tag ID" />
        </Form.Item>
        <Form.Item label="Native Tag ID" name="native_tag_id">
          <Input placeholder="please input Native Tag ID" />
        </Form.Item>
        <Form.Item label="Video Tag ID" name="video_tag_id">
          <Input placeholder="please input Video Tag ID" />
        </Form.Item>
        <Form.Item label="Rewarded Video Tag ID" name="rewarded_video_tag_id">
          <Input placeholder="please input Rewarded Video Tag ID" />
        </Form.Item>
        {isEdit && (
          <Form.Item label="Status" name="status" rules={[{ required: true }]}>
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddCustomModel;
