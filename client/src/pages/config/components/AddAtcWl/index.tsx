import BundleTableEdit from '@/components/BundleTableEdit';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import NormalRadio from '@/components/Radio/NormalRadio';
import RixEngineFont from '@/components/RixEngineFont';
import NormalSelect from '@/components/Select/NormalSelect';
import { StatusOptions } from '@/constants';
import {
  AdFormatOptions,
  DefaultFormData,
  IsAllMap,
  ServerRegionOptions,
} from '@/constants/config/atc';
import { CountryOptions } from '@/constants/country';
import { AdSizeOptions } from '@/models/useAdSizeOptions';
import { addAtcWL, updateAtcWl } from '@/services/api';
import { fetchData } from '@/utils';
import { DatePicker, Form, Radio, message } from 'antd';
import moment, { Moment } from 'moment';
import React, { useEffect, useState } from 'react';

type AddAtcWlProps = {
  isEdit: boolean;
  visible: boolean;
  item?: ConfigAPI.AtcItem;
  onClose: () => void;
  onSave: () => void;
  tenantList: TenantAPI.TenantListItem[];
  allSupplyList: ConfigAPI.SupplyListItem[];
  allDemandList: ConfigAPI.DemandListItem[];
  adSizeOptions: AdSizeOptions[];
};
const AddAtcWlModel: React.FC<AddAtcWlProps> = ({
  visible,
  isEdit,
  item,
  onClose,
  onSave,
  tenantList,
  allSupplyList,
  allDemandList,
  adSizeOptions,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [bundleList, setBundleList] = useState<string[]>([]);
  const [supplyList, setSupplyList] = useState<
    { seller_id: number; seller_name: string }[]
  >([]);
  const [demandList, setDemandList] = useState<
    { buyer_id: number; buyer_name: string }[]
  >([]);

  const [isAll, setIsAll] = useState(IsAllMap.DEFAULT);

  // Add custom validator function
  const validateAtLeastOneField = () => ({
    validator(_: any, value: any) {
      const formValues = form.getFieldsValue();
      // seller_id 类型 为 number 或者 number[]
      const isAllPublishers = Array.isArray(formValues.seller_id)
        ? formValues.seller_id.includes(0)
        : formValues.seller_id === 0;
      if (!isAllPublishers) {
        return Promise.resolve();
      }

      const hasCountry = formValues.country?.length > 0;
      const hasAdFormat = formValues.ad_format?.length > 0;
      const hasAdSize = formValues.ad_size?.length > 0;
      const hasBundle = bundleList.length > 0;

      if (hasCountry || hasAdFormat || hasAdSize || hasBundle) {
        return Promise.resolve();
      }
      return Promise.reject(
        new Error(
          'When selecting All Publishers, at least one of Country, Ad Format, Ad Size, or Bundle must be selected',
        ),
      );
    },
  });

  // 根据tnt_id过滤供应和需求
  const handleTntChange = (tnt_id: number) => {
    const sOptions = allSupplyList.filter((v) => v.tnt_id === tnt_id);
    const dOptions = allDemandList.filter((v) => v.tnt_id === tnt_id);
    setIsAll(IsAllMap.DEFAULT);
    setSupplyList(sOptions);
    setDemandList(dOptions);
  };

  useEffect(() => {
    form.resetFields();
    // 清空数据
    setBundleList([]);
    setSupplyList([]);
    setDemandList([]);
    setIsAll(IsAllMap.DEFAULT);
    if (visible && isEdit && item) {
      const {
        ad_format,
        ad_size,
        country,
        bundle,
        expired,
        buyer_id,
        tnt_id,
        seller_id,
      } = item;
      handleTntChange(+tnt_id);
      const bundleArr = bundle.split(',').filter((v) => v.trim());
      setBundleList(bundleArr);
      const time = new Date(expired * 1000);
      const cur_time = new Date().getTime();
      const time_str = moment(time).format('YYYY-MM-DD');
      const cur_str = moment().format('YYYY-MM-DD');
      const params: any = {
        ...item,
        ad_format: ad_format
          .split(',')
          .filter((v) => v.trim())
          .map((v) => +v),
        ad_size: ad_size
          .split(',')
          .filter((v) => v.trim())
          .map((v) => +v),
        country: country.split(',').filter((v) => v.trim()),
        bundle: bundle.split(',').filter((v) => v.trim()),
        expired: moment(time),
        // 注意：item.seller_id 类型为 number，需要转换成 number[]
        seller_id: Array.isArray(seller_id)
          ? seller_id
          : [seller_id].filter((v) => v !== undefined),
        buyer_id: +buyer_id ? buyer_id : undefined,
      };
      if (cur_time >= time.getTime() || cur_str === time_str) {
        params.expired = moment().add(7, 'day');
      }
      form.setFieldsValue(params);
    }
  }, [item, visible, isEdit]);

  const onConfirm = () => {
    form.submit();
  };

  const handleBundleChange = (val: string[]) => {
    setBundleList(val);
    form.setFieldValue('bundle', val.join(','));
  };

  const handleSupplyChange = (seller_id: number[]) => {
    if (!seller_id?.length) setIsAll(IsAllMap.DEFAULT);
    if (seller_id?.length) {
      const isAll = seller_id.includes(0);
      setIsAll(isAll ? IsAllMap.YES : IsAllMap.NO);
    }
  };

  const handleValueChange = (changeValue: any, allValue: any) => {
    console.log(changeValue);
    if (changeValue.tnt_id) {
      handleTntChange(+changeValue.tnt_id);
      const params = {
        ...allValue,
        seller_id: undefined,
        buyer_id: undefined,
      };
      // 重置数据
      form.setFieldsValue(params);
    }

    if (Array.isArray(changeValue.seller_id)) {
      handleSupplyChange(changeValue.seller_id);
    }
  };

  const onSuccess = () => {
    message.success(isEdit ? 'Edit Success' : 'Add Success');
    onSave();
  };

  const handleAddAtc = (params: any) => {
    fetchData({ request: addAtcWL, params, onSuccess, setLoading });
  };

  const handleUpdateAtc = (params: any) => {
    fetchData({ request: updateAtcWl, params, onSuccess, setLoading });
  };

  const handleFinish = (values: any) => {
    const date = moment(values.expired).format('YYYY-MM-DD');
    const bundle = bundleList.filter((v) => v.trim());
    if (isEdit) {
      const params = {
        ...values,
        id: item?.id,
        expired: date,
        bundle,
        ori_data: item,
      };
      handleUpdateAtc(params);
    } else {
      handleAddAtc({ ...values, expired: date, bundle });
    }
  };

  const handleDisabledDate = (currentDate: Moment) => {
    return (
      currentDate.endOf('day') > moment().add(7, 'day').endOf('day') ||
      currentDate <= moment().endOf('day')
    );
  };

  return (
    <NormalDrawer
      open={visible}
      blackName={isEdit ? 'Edit ATC WL' : 'Add ATC WL'}
      onClose={onClose}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
        onFinish={handleFinish}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        initialValues={{ ...DefaultFormData, expired: moment().add(7, 'day') }}
      >
        <Form.Item
          label="Server Region"
          name="region"
          rules={[{ required: true, message: 'Please Select Server Region' }]}
        >
          <NormalSelect
            options={ServerRegionOptions}
            placeholder="Please Select Server Region"
            allowClear
            disabled={isEdit}
          />
        </Form.Item>
        <Form.Item
          label="Tenant"
          name="tnt_id"
          rules={[{ required: true, message: 'Please Select Tenant' }]}
        >
          <NormalSelect
            options={tenantList.map((v) => ({
              label: `${v.tnt_name}(${v.tnt_id})`,
              value: v.tnt_id,
            }))}
            placeholder="Please Select Tenant"
            allowClear
            disabled={isEdit}
            showSearch
          />
        </Form.Item>
        <Form.Item
          label="Publisher"
          name="seller_id"
          rules={[{ required: true, message: 'Please Select Publisher' }]}
        >
          <NormalSelect
            mode="multiple"
            placeholder="Please Select Publisher"
            allowClear
            disabled={isEdit}
            showSearch
            filterOption
          >
            <NormalSelect.Option
              value={0}
              key={-1}
              disabled={isAll === IsAllMap.NO}
            >
              All Publishers
            </NormalSelect.Option>
            {supplyList &&
              supplyList.map((item: any, index: number) => {
                return (
                  <NormalSelect.Option
                    value={item.seller_id}
                    key={index}
                    disabled={isAll === IsAllMap.YES}
                  >
                    {item.seller_name}({item.seller_id})
                  </NormalSelect.Option>
                );
              })}
          </NormalSelect>
        </Form.Item>
        <Form.Item label="Advertiser" name="buyer_id">
          <NormalSelect
            options={demandList.map((v) => ({
              label: `${v.buyer_name}(${v.buyer_id})`,
              value: v.buyer_id,
            }))}
            placeholder="Please Select Advertiser"
            allowClear
            disabled={isEdit}
            showSearch
          />
        </Form.Item>
        <Form.Item
          label="Country"
          name="country"
          rules={[validateAtLeastOneField]}
        >
          <NormalSelect
            options={CountryOptions}
            placeholder="Please Select Country"
            mode="multiple"
            allowClear
            showSearch
          />
        </Form.Item>
        <Form.Item
          label="Ad Format"
          name="ad_format"
          rules={[validateAtLeastOneField]}
        >
          <NormalSelect
            options={AdFormatOptions}
            placeholder="Please Select Ad Format"
            mode="multiple"
            allowClear
          />
        </Form.Item>
        <Form.Item
          label="Ad Size"
          name="ad_size"
          rules={[validateAtLeastOneField]}
        >
          <NormalSelect
            options={adSizeOptions}
            placeholder="Please Select Ad Size"
            mode="multiple"
            allowClear
            showSearch
          />
        </Form.Item>
        <Form.Item
          label="Bundle"
          name="bundle"
          rules={[validateAtLeastOneField]}
        >
          <BundleTableEdit
            editTitle={'Edit Bundle List'}
            editTips={`Enter the bundle(one per line)`}
            deleteAllTitle="Delete All"
            deleteAllTips={`Are you sure to delete all bundle?`}
            onValueChange={handleBundleChange}
            defaultList={bundleList}
            contentMaxHeight={
              isEdit ? 'calc(100vh - 500px)' : 'calc(100vh - 430px)'
            }
            editSingle={true}
          />
        </Form.Item>
        <Form.Item
          label="Expired"
          name="expired"
          rules={[{ required: true, message: 'Please Select Expired' }]}
        >
          <DatePicker
            suffixIcon={<RixEngineFont type="rix-calender" />}
            allowClear
            disabledDate={handleDisabledDate}
            style={{ width: '60%' }}
          />
        </Form.Item>
        {isEdit && (
          <Form.Item label="Status" name="status" rules={[{ required: true }]}>
            <NormalRadio>
              {StatusOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddAtcWlModel;
