/*
 * @Author: ch<PERSON><PERSON><PERSON> chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-08-08 15:27:36
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-12-28 11:24:08
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-26 19:06:15
 * @Description:
 */
import OperateRender from '@/components/OperateRender';
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable, { ColumnType } from '@/components/Table/FrontTable';
import {
  AtcBreadOptions,
  genAtcColumnOptions,
  genAtcSearchOption,
} from '@/constants/config/atc';
import { useModel } from '@umijs/max';
import React, { useEffect, useMemo, useState } from 'react';
import AddAtcWl from '../components/AddAtcWl';

const Page: React.FC = () => {
  const { dataSource, loading, reload } = useModel('useAtcWlList');
  const {
    tenantList,
    reload: reloadTnt,
    loading: tntLoading,
  } = useModel('useTenantList');
  const { allSupplyList, reload: reloadSupply } = useModel('useAllSupplyList');
  const { allDemandList, reload: reloadDemand } = useModel('useAllDemandList');
  const { adSizeOptions, fetchAdSize } = useModel('useAdSizeOptions');
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<ConfigAPI.AtcItem>();
  const [columns, setColumns] = useState(genAtcColumnOptions());
  const [searchOptions, setSearchOptions] = useState(genAtcSearchOption());
  const [defaultParams] = useState({ status: 1 });

  const extraColumns: ColumnType<ConfigAPI.AtcItem>[] = useMemo(() => {
    return [
      {
        title: 'Operation',
        width: 120,
        fixed: 'right',
        dataIndex: 'operate',
        access: 'EditConfigAtc',
        render: (_: number, row: ConfigAPI.AtcItem) => {
          return (
            <OperateRender
              params={row}
              btnOptions={[
                {
                  label: 'Edit',
                  icon: <RixEngineFont type="edit" />,
                  // eslint-disable-next-line @typescript-eslint/no-use-before-define
                  onClick: () => handleEdit(row),
                },
              ]}
            />
          );
        },
      },
    ];
  }, []);

  useEffect(() => {
    reload();
    reloadTnt();
    reloadSupply();
    reloadDemand();
    fetchAdSize();

    setColumns(genAtcColumnOptions(extraColumns, { adSizeOptions }));
  }, []);

  useEffect(() => {
    let tenantOptions = Array.isArray(tenantList)
      ? tenantList.map((v) => ({
          label: `${v.tnt_name}(${v.tnt_id})`,
          value: v.tnt_id,
        }))
      : [];
    let supplyOptions = Array.isArray(allSupplyList)
      ? allSupplyList.map((v) => ({
          label: `${v.seller_name}(${v.seller_id})`,
          value: v.seller_id,
        }))
      : [];
    let demandOptions = Array.isArray(allDemandList)
      ? allDemandList.map((v) => ({
          label: `${v.buyer_name}(${v.buyer_id})`,
          value: v.buyer_id,
        }))
      : [];

    setSearchOptions(
      genAtcSearchOption({
        tenantOptions,
        publisherOptions: supplyOptions,
        advertiserOptions: demandOptions,
        adSizeOptions,
      }),
    );
    setColumns(
      genAtcColumnOptions(extraColumns, {
        adSizeOptions,
      }),
    );
  }, [tenantList, allSupplyList, allDemandList, adSizeOptions]);

  const handleEdit = (row: ConfigAPI.AtcItem) => {
    setEditItem(row);
    setIsEdit(true);
    setVisible(true);
  };

  const handleAdd = () => {
    setIsEdit(false);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
  };
  const handleSave = () => {
    setVisible(false);
    reload();
  };
  return (
    <PageContainer options={AtcBreadOptions}>
      <FrontTable<ConfigAPI.AtcItem>
        pageTitle="ATC WL"
        searchOptions={searchOptions}
        loading={loading || tntLoading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add ATC WL',
            type: 'primary',
            size: 'small',
            onClick: handleAdd,
            access: 'AddAtcWlCode',
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={120}
        scroll={{ x: 1000, y: 'auto' }}
        isFold
        initialValues={defaultParams}
      />
      <AddAtcWl
        visible={visible}
        isEdit={isEdit}
        item={editItem}
        onClose={handleClose}
        onSave={handleSave}
        tenantList={tenantList || []}
        allSupplyList={allSupplyList || []}
        allDemandList={allDemandList || []}
        adSizeOptions={adSizeOptions}
      />
    </PageContainer>
  );
};

export default Page;
