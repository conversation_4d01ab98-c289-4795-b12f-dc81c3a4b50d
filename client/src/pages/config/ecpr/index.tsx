import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { useModel } from '@umijs/max';
import { Form, message } from 'antd';
import type { TopBarSearchItem } from '@/components/TopBar';
import PageContainer from '@/components/RightPageContainer';
import AddEcprConfig from '../components/AddEcprConfig';
import NormalModal from '@/components/Modal/NormalModal';
import RixEngineFont from '@/components/RixEngineFont';
import OperateRender from '@/components/OperateRender';
import FrontTable, { ColumnType } from '@/components/Table/FrontTable';

import {
  EcprColumnOptions,
  EcprSearchOption,
  EcprBreadOptions,
} from '@/constants/config';

import type { OperateRenderItem } from '@/components/OperateRender';
import { ColumnProps } from 'antd/es/table';
import { fetchData } from '@/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { deleteEcprConfig } from '@/services/api';

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [currentRow, setCurrentRow] = useState<ConfigAPI.EcprListItem>();
  const [dataSource, setDataSource] = useState<ConfigAPI.EcprListItem[]>([]);
  const [searchOptions, setSearchOptions] = useState<any[]>(EcprSearchOption);
  const [tenantOptions, setTenantOptions] = useState<any[]>([]);
  const { tenantList, reload, loading } = useModel('useTenantList');
  const {
    data: ecprConfigList,
    reload: reloadEcprList,
    loading: ecprLoading,
  } = useModel('useEcprConfigList');
  useEffect(() => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    if (Array.isArray(tenantList)) {
      const tmpTenantOptions = tenantList.map((item) => ({
        label: item.tnt_name,
        value: item.tnt_id,
      }));
      setTenantOptions(tmpTenantOptions);
      const sIndex = options.findIndex((item) => item.key === 'tnt_id');
      if (sIndex !== -1) {
        options[sIndex].options = tmpTenantOptions;
      }
    }
    setSearchOptions(options);
  }, [tenantList]);
  const attentionModel = (params: any) => {
    NormalModal.confirm({
      title: 'Tips',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Are you sure? This Ecpr config will be permanently deleted and no
            longer accessible through API.
          </div>
          <div>Do you want to delete this ecpr config?</div>
        </>
      ),
      okText: 'Delete',
      cancelText: 'Cancel',
      onOk: () => {
        const deleteSuccess = () => {
          message.success('Delete success');
          reloadEcprList();
        };
        fetchData({
          request: deleteEcprConfig,
          params: params,
          onSuccess: deleteSuccess,
        });
      },
      okButtonProps: {
        danger: true,
      },
    });
  };
  const handleEditEcpr = (params: ConfigAPI.EcprListItem) => {
    setVisible(true);
    setIsEdit(true);
    setCurrentRow(params);
  };
  const handleEcprDelete = (params: ConfigAPI.EcprListItem) => {
    attentionModel(params);
  };
  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Edit',
      onClick: handleEditEcpr,
      icon: <RixEngineFont type="edit" />,
    },
    {
      label: 'Delete',
      onClick: handleEcprDelete,
      icon: <RixEngineFont type="rix-trash" className={styles['delete-btn']} />,
      isDelete: true,
    },
  ];
  const tmpColumns: ColumnType<ConfigAPI.EcprListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 170,
      fixed: 'right',
      access: 'EditEcprCode',
      render: (txt, params) => (
        <OperateRender btnOptions={OperateOptions} params={params} />
      ),
    },
  ];

  const column = [...EcprColumnOptions, ...tmpColumns];
  useEffect(() => {
    if (initialState?.currentUser) {
      form.setFieldsValue(initialState.currentUser);
    }
  }, [initialState?.currentUser]);

  useEffect(() => {
    if (!tenantList) reload();
    if (!ecprConfigList) reloadEcprList();
  }, []);
  useEffect(() => {
    if (tenantList?.length && ecprConfigList?.length) {
      const tmpList = ecprConfigList.map((item: ConfigAPI.EcprListItem) => {
        const tmptanant = tenantList.find(
          (tnt: TenantAPI.TenantListItem) => tnt.tnt_id === item.tnt_id,
        );
        return {
          tnt_name: tmptanant?.tnt_name,
          ...item,
        };
      });
      setDataSource(tmpList);
    }
  }, [tenantList, ecprConfigList]);

  const handleTenant = () => {
    setVisible(true);
    setIsEdit(false);
  };
  return (
    <PageContainer options={EcprBreadOptions}>
      <FrontTable<ConfigAPI.EcprListItem>
        pageTitle="Ecpr Config List"
        searchOptions={searchOptions}
        loading={ecprLoading}
        columns={column}
        dataSource={dataSource}
        rowKey={'id'}
        request={reloadEcprList}
        btnOptions={[
          {
            label: 'Add Ecpr Config',
            type: 'primary',
            size: 'small',
            access: 'AddEcprCode',
            onClick: handleTenant,
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={100}
        scroll={{ x: 1000, y: 'auto' }}
      />
      <AddEcprConfig
        visible={visible}
        isEdit={isEdit}
        setVisible={setVisible}
        rowEcprInfo={currentRow}
        reloadEcprList={reloadEcprList}
        tenantOptions={tenantOptions}
      />
    </PageContainer>
  );
};

export default Page;
