/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-10 10:59:11
 * @Description:
 */
import { useEffect, useState } from 'react';
import { Form, message } from 'antd';
import { updateAppInfo } from '@/services/api';
import { fetchData } from '@/utils';

import NormalModal from '@/components/Modal/NormalModal';
import NormalInput from '@/components/Input/NormalInput';

type EditInfoModelProps = {
  item?: TransparencyAPI.AppInfoItem;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadInfo: () => void;
};

const DefaultFormData = {
  aat_domain: '',
};

const AddCapModel: React.FC<EditInfoModelProps> = ({
  item,
  handleClose,
  visible,
  isEdit,
  reloadInfo,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (item && isEdit && visible) {
      form.setFieldsValue(item);
    } else {
      form.resetFields();
    }
  }, [item, isEdit, visible]);
  const handleConfirm = () => {
    form.submit();
  };
  const handleFinish = (values: any) => {
    if (isEdit) {
      handleEditInfo({
        // ...item,
        ...values,
        ori_data: item,
      });
    }
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    handleClose();
    reloadInfo();
  };

  const onCancel = () => {
    form.resetFields();
    handleClose();
  };

  const handleEditInfo = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateAppInfo, params });
  };
  const vaildDomain = (rule: any, value: any) => {
    const reg = /^([a-zA-Z0-9]+(-[a-zA-Z0-9]+)*\.)+[a-zA-Z]{2,}$/;
    if (!value) {
      return Promise.resolve();
    } else if (!reg.test(value)) {
      return Promise.reject(
        new Error('Please enter the correct domain format'),
      );
    }
    return Promise.resolve();
  };
  return (
    <NormalModal
      title={'Edit App Info'}
      onOk={handleConfirm}
      open={visible}
      onCancel={onCancel}
      confirmLoading={loading}
      maskClosable={true}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
      >
        {/* hidden developer_id */}
        <Form.Item name="developer_id" hidden>
          <NormalInput />
        </Form.Item>
        {/* Mutil AAt Domain */}
        <Form.Item
          name="multi_aat_domain"
          label="Manual AAT Domain:"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              validator: vaildDomain,
            },
          ]}
        >
          <NormalInput
            placeholder="Please enter the AAT domain"
            style={{ width: '300px' }}
          ></NormalInput>
        </Form.Item>
      </Form>
    </NormalModal>
  );
};

export default AddCapModel;
