import NormalDrawer from '@/components/Drawer/NormalDrawer';
import InputNumberNormal from '@/components/Input/InputNumber';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import { StatusOptions } from '@/constants';
import { LevelOptions } from '@/constants/transparency/schain-truncation';
import { updateSchainTruncationConfig } from '@/services/transparency';
import { fetchData } from '@/utils';
import { Form, Input, message } from 'antd';
import { useWatch } from 'antd/lib/form/Form';
import { useCallback, useEffect, useMemo, useState } from 'react';

interface SchainTruncationDrawerProps {
  visible: boolean;
  currentRow: TransparencyAPI.SchainTruncationColumns | null;
  handleClose: (params?: TransparencyAPI.SchainTruncationColumns) => void;
  tenantOptions: any[];
  demandOptions: any[];
  refresh: () => void;
}

const SchainTruncationDrawer = ({
  visible,
  handleClose,
  currentRow,
  tenantOptions,
  demandOptions,
  refresh,
}: SchainTruncationDrawerProps) => {
  const isEdit = currentRow !== null;
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const tnt_id = useWatch('tnt_id', form);

  // demandOptions need to be filtered by current tenant value
  const filteredDemandOptions = useMemo(() => {
    return demandOptions.filter((item) => item.tnt_id === tnt_id);
  }, [demandOptions, tnt_id]);

  // Initialize form fields when drawer is visible
  useEffect(() => {
    if (visible) {
      const { id, level, tnt_id, buyer_id, schain_hops, status } =
        currentRow || {};
      form.setFieldsValue({
        level: 1,
        status: 1,
        ...(currentRow && {
          id,
          level,
          tnt_id,
          buyer_id,
          schain_hops: schain_hops === 0 ? undefined : schain_hops,
          status,
        }),
      });
    } else {
      form.resetFields();
    }
  }, [visible, currentRow, form, isEdit]);

  // Form submit handler
  const handleConfirm = useCallback(async () => {
    try {
      const values = await form.validateFields();

      fetchData({
        request: updateSchainTruncationConfig,
        params: values,
        setLoading,
        onSuccess: () => {
          message.success(`${isEdit ? 'Update' : 'Add'} successfully`);
          refresh();
          handleClose();
        },
        onError: () => {
          message.error(`${isEdit ? 'Update' : 'Add'} failed`);
        },
      });
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  }, [form, isEdit]);

  // Handle drawer close
  const handleDrawerClose = useCallback(() => {
    handleClose();
    form.resetFields();
  }, [handleClose, form]);

  const handleValuesChange = useCallback(
    (changedFields: any, allFields: any) => {
      if (changedFields.tnt_id) {
        form.setFieldValue('buyer_id', null);
      }
    },
    [form],
  );

  return (
    <NormalDrawer
      blackName={
        isEdit
          ? 'Edit Schain Truncation Config'
          : 'Add Schain Truncation Config'
      }
      loading={loading}
      onConfirm={handleConfirm}
      open={visible}
      onClose={handleDrawerClose}
      maskClosable={false}
      width={'min(424px, 100%)'}
    >
      <Form layout="vertical" form={form} onValuesChange={handleValuesChange}>
        {/* id hidden */}
        <Form.Item name="id" hidden>
          <Input type="hidden" />
        </Form.Item>
        {/* level */}
        <Form.Item
          label="Type:"
          name="level"
          rules={[{ required: true, message: 'Please select a type' }]}
        >
          <NormalSelect options={LevelOptions} disabled={isEdit} />
        </Form.Item>
        {/* tnt_id */}
        <Form.Item
          label="Tenant:"
          name="tnt_id"
          rules={[{ required: true, message: 'Please select a tenant' }]}
        >
          <NormalSelect
            options={tenantOptions}
            allowClear
            showSearch
            placeholder="Please select a tenant"
            disabled={isEdit}
          />
        </Form.Item>
        {/* buyer_id */}
        <Form.Item noStyle dependencies={['level']}>
          {({ getFieldValue }) => {
            const level = getFieldValue('level');
            if (level === 1) {
              return null;
            }
            return (
              <Form.Item
                label="Advertiser:"
                name="buyer_id"
                rules={[
                  { required: true, message: 'Please select a advertiser' },
                ]}
              >
                <NormalSelect
                  options={filteredDemandOptions}
                  allowClear
                  showSearch
                  placeholder="Please select a advertiser"
                  disabled={isEdit}
                />
              </Form.Item>
            );
          }}
        </Form.Item>
        {/* schain_hops */}
        <Form.Item
          label="Schain Hops Truncation:"
          name="schain_hops"
          tooltip="Filter requests sent to Demand with more than N schain-hops, N is between 1 and 10"
          rules={[
            {
              required: true,
              message: 'Please input schain hops truncation',
            },
          ]}
        >
          <InputNumberNormal min={1} max={10} width="100%" />
        </Form.Item>
        {/* status */}
        {isEdit && (
          <Form.Item label="Status:" name="status" rules={[{ required: true }]}>
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default SchainTruncationDrawer;
