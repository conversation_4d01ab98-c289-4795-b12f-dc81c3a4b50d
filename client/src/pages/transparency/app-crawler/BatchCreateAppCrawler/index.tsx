import LongContentMore from '@/components/LongContentMore';
import RixEngineFont from '@/components/RixEngineFont';
import HoverTooltip from '@/components/Tooltip/HoverTooltip';
import { PlatformOptionsMap } from '@/constants/transparency/app-crawler';
import classnames from 'classnames';
import BatchCreateAppInput from './BatchCreateAppInput';
import { BundleItem } from './types';
import { useBundleList } from './useBundleList';
import { useBundleModal } from './useBundleModal';
import { useVisibleBundles } from './useVisibleBundles';

type BundleTableEditProps = {
  value?: BundleItem[];
  onChange?: (val: BundleItem[]) => void;
};

const BatchCreateAppCrawler = ({
  value = [],
  onChange = () => {},
}: BundleTableEditProps) => {
  // 处理 弹窗的逻辑
  const {
    bundleVisible,
    currentEditBundle,
    openNewBundleModal,
    openEditBundleModal,
    closeBundleModal,
  } = useBundleModal();

  // 处理 虚拟滚动的数据
  const { dataSource, showList, handleShowDataChange } =
    useVisibleBundles(value);

  // 处理 编辑的逻辑
  const {
    handleSaveBundle,
    handleClearBundle,
    handleDeleteOneBundle,
    handleSaveEditBundle,
  } = useBundleList({
    value,
    onChange,
    currentEditBundle,
    closeBundleModal,
  });

  return (
    <div className="tailwind">
      <div className="mb-3 flex items-center gap-2">
        <RixEngineFont
          type="rix-edit"
          handleClick={openNewBundleModal}
          className="cursor-pointer text-lg hover:text-[#4692f5]"
        />
        <RixEngineFont
          type="rix-trash"
          handleClick={handleClearBundle}
          className={classnames({
            'cursor-pointer text-xl hover:text-[#ff4d4f]': value.length > 0,
            'pointer-events-none cursor-not-allowed text-2xl text-[#d7dadb]':
              value.length === 0,
          })}
        />
      </div>
      {value.length > 0 ? (
        <LongContentMore
          contentMaxHeight={'calc(100vh - 430px)'}
          open={true}
          onShowDataChange={handleShowDataChange}
          dataSource={dataSource}
          className="w-full"
        >
          {showList.map((item, index) => (
            <div
              className="flex h-7 items-center border-b border-[#d9d9d9] leading-7 text-[#252829] last:border-b-0"
              key={index}
            >
              <div className="w-8 shrink-0 border-r border-[#d9d9d9] text-center">
                <span>{index + 1}</span>
              </div>
              <div className="w-0 flex-1 overflow-hidden text-ellipsis whitespace-nowrap border-r border-[#d9d9d9] px-2">
                <HoverTooltip
                  title={item.app_bundle_id}
                  maxWidth={142}
                  placement="topLeft"
                >
                  <span className="cursor-pointer">{item.app_bundle_id}</span>
                </HoverTooltip>
              </div>
              <div className="w-[88px] shrink-0 border-r border-[#d9d9d9] px-2 text-center">
                {PlatformOptionsMap[item.platform]}
              </div>
              <div className="flex shrink-0 items-center gap-2 px-2">
                <RixEngineFont
                  type="rix-edit"
                  handleClick={() => openEditBundleModal(item)}
                  className="cursor-pointer text-lg hover:text-[#4692f5]"
                />
                <RixEngineFont
                  type="rix-trash"
                  handleClick={() => handleDeleteOneBundle(item)}
                  className="cursor-pointer text-xl hover:text-[#ff4d4f]"
                />
              </div>
            </div>
          ))}
        </LongContentMore>
      ) : (
        <div className="flex flex-col items-center justify-center rounded-md border border-[#d9d9d9] py-3 text-[#8a8a8a]">
          <RixEngineFont type="rix-empty" className="text-5xl" />
          <span>No Data</span>
        </div>
      )}
      <BatchCreateAppInput
        visible={bundleVisible}
        handleCancel={closeBundleModal}
        handleOk={handleSaveBundle}
        defaultList={value.map((item) => item.app_bundle_id)}
        validator={(val) => !!val}
        isReverse={true}
        isEdit={!!currentEditBundle}
        currentEditBundle={currentEditBundle?.app_bundle_id}
        onSaveEdit={handleSaveEditBundle}
        currentEditBundleItem={currentEditBundle}
      />
    </div>
  );
};

export default BatchCreateAppCrawler;
