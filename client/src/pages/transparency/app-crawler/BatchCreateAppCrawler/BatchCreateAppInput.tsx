import NormalInput from '@/components/Input/NormalInput';
import NormalModal from '@/components/Modal/NormalModal';
import NormalSelect from '@/components/Select/NormalSelect';
import { PlatformOptions } from '@/constants/transparency/app-crawler';
import { Button, Form } from 'antd';
import { memo, useEffect } from 'react';
import { BundleItem } from './types';
import { useBundleActions } from './useBundleActions';

type BundleProps = {
  visible: boolean;
  handleOk: (val: any) => void;
  handleCancel: () => void;
  defaultList: string[];
  validator?: (val: string) => boolean;
  maskClosable?: boolean;
  keyboard?: boolean;
  isReverse?: boolean;
  isEdit?: boolean;
  currentEditBundle?: string;
  onSaveEdit?: (editedBundle: BundleItem) => void;
  currentEditBundleItem?: BundleItem | null;
};

const BundleInput = memo(function BundleInput({
  defaultList,
  visible,
  handleCancel,
  handleOk,
  validator,
  maskClosable = false,
  keyboard = false,
  isReverse = false,
  isEdit = false,
  currentEditBundle,
  onSaveEdit,
  currentEditBundleItem,
}: BundleProps): JSX.Element {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      if (isEdit) {
        form.setFieldsValue({
          app_bundle_id: currentEditBundle || '',
          platform: currentEditBundleItem?.platform || 1,
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, isEdit, currentEditBundle, currentEditBundleItem, form]);

  const { handleAdd, handleDelete, handleOverWrite, handleSaveSingle } =
    useBundleActions({
      form,
      defaultList,
      isReverse,
      validator,
      handleOk,
      handleCancel,
      isEdit,
      onSaveEdit,
      currentEditBundle,
    });

  return (
    <NormalModal
      title={'Edit App Crawler Bundle List'}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={maskClosable}
      keyboard={keyboard}
      footer={
        <>
          {!isEdit && (
            <>
              <Button type="primary" onClick={handleAdd}>
                Add
              </Button>
              <Button type="primary" onClick={handleDelete}>
                Delete
              </Button>
              <Button type="primary" onClick={handleOverWrite}>
                OverWrite
              </Button>
            </>
          )}
          {isEdit && (
            <Button type="primary" onClick={handleSaveSingle}>
              Save
            </Button>
          )}
        </>
      }
    >
      <Form form={form} layout="vertical">
        {!isEdit && (
          <Form.Item
            name="bundles"
            rules={[{ required: true, message: 'Please input bundle id!' }]}
          >
            <NormalInput.TextArea
              rows={4}
              placeholder={'Enter the app crawler bundle(one per line)'}
            />
          </Form.Item>
        )}
        {isEdit && (
          <>
            <Form.Item
              label="App Bundle ID"
              name="app_bundle_id"
              rules={[{ required: true, message: 'Please input bundle id!' }]}
            >
              <NormalInput />
            </Form.Item>
            <Form.Item
              label="Platform"
              name="platform"
              rules={[{ required: true, message: 'Please select a platform!' }]}
            >
              <NormalSelect
                options={PlatformOptions}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </>
        )}
      </Form>
    </NormalModal>
  );
});

export default BundleInput;
