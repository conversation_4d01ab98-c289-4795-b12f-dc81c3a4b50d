import { useCallback, useState } from 'react';
import { BundleItem } from './types';

export const useBundleModal = () => {
  const [bundleVisible, setBundleVisible] = useState(false);
  const [currentEditBundle, setCurrentEditBundle] = useState<BundleItem | null>(
    null,
  );

  const openNewBundleModal = useCallback(() => {
    setCurrentEditBundle(null);
    setBundleVisible(true);
  }, []);

  const openEditBundleModal = useCallback((item: BundleItem) => {
    setCurrentEditBundle(item);
    setBundleVisible(true);
  }, []);

  const closeBundleModal = useCallback(() => {
    setBundleVisible(false);
    setCurrentEditBundle(null);
  }, []);

  return {
    bundleVisible,
    currentEditBundle,
    openNewBundleModal,
    openEditBundleModal,
    closeBundleModal,
  };
};
