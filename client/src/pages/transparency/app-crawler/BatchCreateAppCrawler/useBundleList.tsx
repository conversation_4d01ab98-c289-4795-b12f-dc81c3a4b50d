import NormalModal from '@/components/Modal/NormalModal';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { useCallback } from 'react';
import { inferPlatform } from './helper';
import { BundleItem } from './types';

type UseBundleListProps = {
  value: BundleItem[];
  onChange: (val: BundleItem[]) => void;
  currentEditBundle: BundleItem | null;
  closeBundleModal: () => void;
};

export const useBundleList = ({
  value,
  onChange,
  currentEditBundle,
  closeBundleModal,
}: UseBundleListProps) => {
  const handleSaveBundle = useCallback(
    (inputValues: string[]) => {
      // 将输入的字符串转换为 BundleItem 对象
      const bundleItems: BundleItem[] = [];

      for (const item of inputValues) {
        const trimItem = item.trim();
        if (trimItem !== '') {
          bundleItems.push({
            app_bundle_id: trimItem,
            platform: inferPlatform(trimItem),
          });
        }
      }
      onChange(bundleItems);
    },
    [onChange],
  );

  const handleClearBundle = useCallback(() => {
    if (value.length) {
      NormalModal.confirm({
        title: 'Delete All',
        content: `Are you sure to delete all app crawler bundle?`,
        okText: 'Delete',
        icon: <ExclamationCircleOutlined />,
        onOk: () => {
          // 清空数据
          onChange([]);
        },
        okButtonProps: {
          danger: true,
        },
      });
    }
  }, [onChange, value]);

  const handleDeleteOneBundle = useCallback(
    (item: BundleItem) => {
      NormalModal.confirm({
        title: 'Tips',
        content: `Are you sure to delete "${item.app_bundle_id}" ?`,
        okText: 'Delete',
        icon: <ExclamationCircleOutlined />,
        onOk: () => {
          const arr = value.filter(
            (val) =>
              val.app_bundle_id !== item.app_bundle_id ||
              val.platform !== item.platform,
          );
          // 删除数据
          onChange(arr);
        },
        okButtonProps: {
          danger: true,
        },
      });
    },
    [onChange, value],
  );

  const handleSaveEditBundle = useCallback(
    (editedBundle: BundleItem) => {
      if (!currentEditBundle) return;

      const updatedList = value.map((bundle) =>
        bundle.app_bundle_id === currentEditBundle.app_bundle_id &&
        bundle.platform === currentEditBundle.platform
          ? editedBundle
          : bundle,
      );

      onChange(updatedList);
      closeBundleModal();
    },
    [currentEditBundle, onChange, closeBundleModal, value],
  );

  return {
    handleSaveBundle,
    handleClearBundle,
    handleDeleteOneBundle,
    handleSaveEditBundle,
  };
};
