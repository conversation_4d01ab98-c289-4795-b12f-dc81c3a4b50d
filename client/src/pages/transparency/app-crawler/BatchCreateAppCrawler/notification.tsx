import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { notification } from 'antd';
import React from 'react';
import styles from './index.less';

const renderItems = (
  items: string[],
  title: string,
  icon: React.ReactNode,
  contentClassName: string,
) => {
  if (items.length === 0) return null;
  return (
    <div>
      {icon}
      <span>{title}</span>
      <div className={contentClassName}>
        {items.map((v, index) => (
          <p key={index} style={{ marginBottom: 0 }}>
            {v}
          </p>
        ))}
      </div>
    </div>
  );
};

export const showBundleOperationNotification = (
  title: string,
  {
    succeeded = [],
    repeated = [],
    invalid = [],
    notFound = [],
  }: {
    succeeded?: string[];
    repeated?: string[];
    invalid?: string[];
    notFound?: string[];
  },
) => {
  if (
    succeeded.length === 0 &&
    repeated.length === 0 &&
    invalid.length === 0 &&
    notFound.length === 0
  ) {
    return;
  }

  const description = (
    <>
      {renderItems(
        succeeded,
        `succeed: `,
        <CheckCircleOutlined
          style={{
            color: '#0DB4BE',
            fontSize: '16px',
            paddingRight: '5px',
          }}
        />,
        styles['tips-content'],
      )}
      {renderItems(
        repeated,
        `repeated: `,
        <CloseCircleOutlined
          style={{
            color: '#F75941',
            fontSize: '16px',
            paddingRight: '5px',
          }}
        />,
        styles['tips-content'],
      )}
      {renderItems(
        invalid,
        `invalid: `,
        <CloseCircleOutlined
          style={{
            color: '#F75941',
            fontSize: '16px',
            paddingRight: '5px',
          }}
        />,
        styles['tips-content'],
      )}
      {renderItems(
        notFound,
        `not found failure: `,
        <CloseCircleOutlined
          style={{
            color: '#F75941',
            fontSize: '16px',
            paddingRight: '5px',
          }}
        />,
        styles['tips-content'],
      )}
    </>
  );

  notification.open({
    message: title,
    description,
    className: 'tips-billing-contract',
    duration: 5,
  });
}; 
