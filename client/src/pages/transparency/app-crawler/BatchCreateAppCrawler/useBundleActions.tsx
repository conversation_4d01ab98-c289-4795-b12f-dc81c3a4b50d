import { isValidBundle } from '@/utils';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { FormInstance, notification } from 'antd';
import React, { useCallback } from 'react';
import styles from './index.less';
import { BundleItem } from './types';

// Merged notification logic
const renderItems = (
  items: string[],
  title: string,
  icon: React.ReactNode,
  contentClassName: string,
) => {
  if (items.length === 0) return null;
  return (
    <div>
      {icon}
      <span>{title}</span>
      <div className={contentClassName}>
        {items.map((v, index) => (
          <p key={index} style={{ marginBottom: 0 }}>
            {v}
          </p>
        ))}
      </div>
    </div>
  );
};

const showBundleOperationNotification = (
  title: string,
  {
    succeeded = [],
    repeated = [],
    invalid = [],
    notFound = [],
  }: {
    succeeded?: string[];
    repeated?: string[];
    invalid?: string[];
    notFound?: string[];
  },
) => {
  if (
    succeeded.length === 0 &&
    repeated.length === 0 &&
    invalid.length === 0 &&
    notFound.length === 0
  ) {
    return;
  }

  const description = (
    <>
      {renderItems(
        succeeded,
        `succeed: `,
        <CheckCircleOutlined
          style={{
            color: '#0DB4BE',
            fontSize: '16px',
            paddingRight: '5px',
          }}
        />,
        styles['tips-content'],
      )}
      {renderItems(
        repeated,
        `repeated: `,
        <CloseCircleOutlined
          style={{
            color: '#F75941',
            fontSize: '16px',
            paddingRight: '5px',
          }}
        />,
        styles['tips-content'],
      )}
      {renderItems(
        invalid,
        `invalid: `,
        <CloseCircleOutlined
          style={{
            color: '#F75941',
            fontSize: '16px',
            paddingRight: '5px',
          }}
        />,
        styles['tips-content'],
      )}
      {renderItems(
        notFound,
        `not found failure: `,
        <CloseCircleOutlined
          style={{
            color: '#F75941',
            fontSize: '16px',
            paddingRight: '5px',
          }}
        />,
        styles['tips-content'],
      )}
    </>
  );

  notification.open({
    message: title,
    description,
    className: 'tips-billing-contract',
    duration: 5,
  });
};

type UseBundleActionsProps = {
  form: FormInstance;
  defaultList: string[];
  isReverse: boolean;
  validator?: (val: string) => boolean;
  handleOk: (val: any) => void;
  handleCancel: () => void;
  isEdit: boolean;
  onSaveEdit?: (editedBundle: BundleItem) => void;
  currentEditBundle?: string;
};

export const useBundleActions = ({
  form,
  defaultList,
  isReverse,
  validator,
  handleOk,
  handleCancel,
  isEdit,
  onSaveEdit,
  currentEditBundle,
}: UseBundleActionsProps) => {
  const processBundles = (text: string): string[] => {
    return text
      .split('\n')
      .map((bundle) => bundle.trim().replaceAll(',', ''))
      .filter(Boolean);
  };

  const handleAdd = useCallback(async () => {
    const { bundles = '' } = await form.validateFields();
    const editBundleList = processBundles(bundles);
    const data = isReverse
      ? [...editBundleList, ...defaultList]
      : [...defaultList, ...editBundleList];
    const newBundleList = [...new Set(data)];
    const repeat = defaultList.filter((bundle) =>
      [...new Set(editBundleList)].includes(bundle),
    );
    const validBundle: string[] = [];
    const invalidBundle: string[] = [];

    newBundleList.forEach((bundle) => {
      if ((validator && validator(bundle)) || isValidBundle(bundle)) {
        validBundle.push(bundle);
      } else {
        invalidBundle.push(bundle);
      }
    });

    const diff = validBundle.filter((bundle) => !defaultList.includes(bundle));
    handleOk(validBundle);
    handleCancel();
    showBundleOperationNotification('Add', {
      succeeded: diff,
      repeated: repeat,
      invalid: invalidBundle,
    });
  }, [form, defaultList, isReverse, validator, handleOk, handleCancel]);

  const handleDelete = useCallback(async () => {
    const { bundles = '' } = await form.validateFields();
    const editBundleList = processBundles(bundles);
    let newBundleList = [...new Set(defaultList)];
    const notFound = editBundleList.filter(
      (bundle) => !newBundleList.includes(bundle),
    );
    newBundleList = newBundleList.filter(
      (bundle) => !editBundleList.includes(bundle),
    );
    const diff = defaultList.filter(
      (bundle) => !newBundleList.includes(bundle),
    );
    handleOk(newBundleList);
    handleCancel();
    showBundleOperationNotification('Delete', {
      succeeded: diff,
      notFound: notFound,
    });
  }, [form, defaultList, handleOk, handleCancel]);

  const handleOverWrite = useCallback(async () => {
    const { bundles = '' } = await form.validateFields();
    const editBundleList = processBundles(bundles);
    const newBundleList = [...new Set(editBundleList)];
    const validBundle: string[] = [];
    const invalidBundle: string[] = [];
    newBundleList.forEach((bundle) => {
      if ((validator && validator(bundle)) || isValidBundle(bundle)) {
        validBundle.push(bundle);
      } else {
        invalidBundle.push(bundle);
      }
    });
    handleOk(validBundle);
    handleCancel();
    showBundleOperationNotification('Overwrite', {
      succeeded: validBundle,
      invalid: invalidBundle,
    });
  }, [form, validator, handleOk, handleCancel]);

  const handleSaveSingle = useCallback(async () => {
    const values = await form.validateFields();
    if (isEdit && onSaveEdit) {
      onSaveEdit({
        app_bundle_id: values.app_bundle_id,
        platform: values.platform,
      });
    } else {
      const newBundleList = [...new Set(defaultList)].filter((v) => v);
      const findIndex = newBundleList.findIndex(
        (bundle) => bundle === currentEditBundle,
      );
      if (findIndex > -1) {
        newBundleList[findIndex] = values.app_bundle_id;
      }
      handleOk(newBundleList);
    }
    handleCancel();
  }, [
    form,
    isEdit,
    onSaveEdit,
    defaultList,
    currentEditBundle,
    handleOk,
    handleCancel,
  ]);

  return { handleAdd, handleDelete, handleOverWrite, handleSaveSingle };
};
