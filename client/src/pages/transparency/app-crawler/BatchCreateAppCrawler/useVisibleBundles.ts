import { DataSourceType } from '@/components/EllipsisPopover';
import { useCallback, useMemo, useState } from 'react';
import { BundleItem } from './types';

export const useVisibleBundles = (value: BundleItem[]) => {
  const dataSource = useMemo(() => value.map((_, index) => index), [value]);

  const [visibleIndices, setVisibleIndices] = useState<number[]>([]);

  const handleShowDataChange = useCallback((val: DataSourceType | number[]) => {
    setVisibleIndices(val as number[]);
  }, []);

  const showList = useMemo(() => {
    const list: BundleItem[] = [];

    for (const index of visibleIndices) {
      const item = value[index];
      if (!item) continue;
      list.push(item);
    }

    return list;
  }, [visibleIndices, value]);

  return {
    dataSource,
    showList,
    handleShowDataChange,
  };
};
