.permission-page {
  :global {
    .ant-layout-sider {
      margin-left: 32px;
    }
    .ant-layout-content {
      background-color: #fff;
    }
    .ant-menu-title-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0;
      span {
        margin-right: 10px;
      }
    }
    .ant-menu {
      border: none;
      border-bottom: 1px solid #e2eaeb;
      min-height: calc(100vh - 244px);
      max-height: calc(100vh - 244px);
      overflow: auto;
      // padding-right: 12px;
      .ant-menu-item {
        height: 38px;
        font-size: 14px;
        margin-top: 12px !important;
        border-radius: 6px;
        padding: 0 12px;
      }
    }
    .ant-spin-nested-loading {
      background-color: #fff;
    }
  }
  background-color: #fff;
  padding: 32px 32px 0 0;
  max-height: 100%;
  .add-role-btn {
    width: 100%;
    height: 36px;
  }
  .menu-btn {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    span {
      color: #8d9799;
      font-size: 16px;
    }
    .edit-btn {
      margin: 0;
      &:hover {
        color: #00a1ba;
      }
    }
    .delete-btn {
      margin: 0 0 0 8px;
      &:hover {
        color: red;
      }
    }
  }
  .role-item-name {
    font-size: 14px;
    color: #4692f5;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.permission-container {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  padding-left: 40px;
  button {
    align-self: flex-end;
    margin: 12px 0;
  }
  :global {
    .ant-radio-group {
      background-color: #fff;
    }
  }
  .permission-top {
    display: flex;
    justify-content: flex-start;
    color: #252829;
    font-weight: 700;
    font-size: 16px;
    margin-bottom: 12px;
  }
}
