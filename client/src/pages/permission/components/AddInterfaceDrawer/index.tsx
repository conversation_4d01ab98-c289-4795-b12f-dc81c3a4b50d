/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-12 14:44:36
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-03-01 17:50:50
 * @Description:
 */

import { Form, message, Input } from 'antd';
import { fetchData } from '@/utils';
import Select from '@/components/Select/NormalSelect';

import NormalDrawer from '@/components/Drawer/NormalDrawer';

import React, { useEffect, useState } from 'react';

import {
  InterfaceTypeOptions,
  InterfaceOperationTypeOptions,
} from '@/constants/permission/interface';
import { addInterface, updateInterface } from '@/services/api';

const DefaultFormData = {
  Interface_name: undefined,
  type: undefined,
  permissions: undefined,
};
type AddInterfaceDrawerProps = {
  Interface?: PermissionAPI.InterfaceItem;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadInterface: () => void;
};
const AddInterfaceDrawer: React.FC<AddInterfaceDrawerProps> = ({
  Interface,
  handleClose,
  visible,
  isEdit,
  reloadInterface,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [type, setType] = useState<number | undefined>();

  useEffect(() => {
    if (Interface && isEdit && visible) {
      form.setFieldsValue(Interface);
    } else {
      form.resetFields();
    }
  }, [Interface, isEdit, visible]);

  const handleValueChange = (changeValue: any) => {
    if (changeValue.type) {
      setType(changeValue.type);
    }
  };
  const handleConfirm = () => {
    form.submit();
  };
  const onSuccess = () => {
    message.success('success');
    handleClose();
    reloadInterface();
  };
  const handleFinish = (values: any) => {
    values.id = Interface?.id;
    values.path = values.path.trim();
    isEdit
      ? fetchData({
          request: updateInterface,
          params: { ...values, ori_data: Interface },
          onSuccess,
        })
      : fetchData({ request: addInterface, params: values, onSuccess });
  };
  const onCancel = () => {
    form.resetFields();
    handleClose();
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Interface` : `Create Interface`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
    >
      <Form
        initialValues={DefaultFormData}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        form={form}
        onValuesChange={handleValueChange}
        layout="vertical"
        onFinish={handleFinish}
      >
        <Form.Item
          label="Interface Name"
          name="itf_name"
          rules={[{ required: true, message: 'Please Input Interface Name!' }]}
        >
          <Input placeholder="Please Input Interface Name" />
        </Form.Item>
        <Form.Item
          label="API Path"
          name="path"
          rules={[{ required: true, message: 'Please Input API Path!' }]}
        >
          <Input placeholder="Please Input API Path" />
        </Form.Item>
        <Form.Item
          label="Type"
          name="type"
          rules={[{ required: true, message: 'Please Select Type!' }]}
        >
          <Select placeholder="Please Select Type">
            {InterfaceTypeOptions.map((item) => {
              return (
                <Select.Option key={item.value} value={item.value}>
                  {item.label}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>

        <Form.Item
          label="Operation Type"
          name="op_type"
          rules={[{ required: true, message: 'Please Select Operation Type!' }]}
        >
          <Select
            options={InterfaceOperationTypeOptions}
            placeholder="Please Select Operation Type"
          />
        </Form.Item>
      </Form>
    </NormalDrawer>
  );
};

export default AddInterfaceDrawer;
