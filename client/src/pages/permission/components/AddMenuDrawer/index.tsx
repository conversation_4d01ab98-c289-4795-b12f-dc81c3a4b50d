/*
 * @Author: <PERSON>
 * @Date: 2023-03-16 21:59:13
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-12 14:48:28
 * @Description:
 */

import { Input, Form, Radio, Select, message } from 'antd';
import { MenuDefaultFormData } from '@/constants/permission';
import { useEffect, useState } from 'react';
import { fetchData, isArrSame, isObjectValueEqual } from '@/utils';
import { addMenu, updateMenu } from '@/services/permission';

import NormalDrawer from '@/components/Drawer/NormalDrawer';

type MenuRightProps = {
  currentMenu?: PermissionAPI.MenuItem | undefined;
  interfaceList: PermissionAPI.InterfaceItem[];
  menuList: PermissionAPI.MenuItem[];
  isEdit: boolean;
  handleSuccess: () => void;
  visible: boolean;
  handleClose: () => void;
};
const Page: React.FC<MenuRightProps> = ({
  currentMenu,
  interfaceList,
  menuList,
  isEdit,
  handleSuccess,
  visible,
  handleClose,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [path, setPath] = useState('');

  useEffect(() => {
    if (currentMenu && isEdit) {
      setPath(currentMenu.path);
      form.setFieldsValue({
        ...currentMenu,
        // icon: currentMenu.icon_tmp,
        pid: currentMenu.pid || '',
      });
    } else {
      form.resetFields();
    }
  }, [currentMenu, isEdit]);

  const handleSave = () => {
    form.submit();
  };

  const getMenuKeyValue = (value: any) => {
    return {
      path: value.path || '',
      title: value.title,
      is_hide: value.is_hide,
      icon: value.icon || '',
      component: value.component || '',
      menu_render: value.menu_render,
      status: value.status,
    };
  };

  const onFinish = (value: any) => {
    if (isEdit && currentMenu) {
      const isSame = isArrSame(value.interfaces, currentMenu.interfaces);
      const a = getMenuKeyValue(value);
      const b = getMenuKeyValue(currentMenu);
      const flag = isObjectValueEqual(a, b);
      const params = {
        ...value,
        id: currentMenu.id,
        s_flag: isSame && flag ? 0 : 1, // 如果关键数据没改变
        itf_change: isSame ? 0 : 1, // 接口是否改变
        pid: value.pid || currentMenu.pid || 0,
        ori_data: isEdit ? currentMenu : {},
      };
      params.path = params.path || '';
      console.log('params', params);
      handleUpdate(params);
    } else {
      const params = {
        pid: 0,
        ...value,
      };
      params.path = params.path || '';
      handleAdd(params);
    }
  };

  const handleReset = () => {
    if (isEdit && currentMenu) {
      form.setFieldsValue({
        ...currentMenu,
        // icon: currentMenu.icon_tmp,
        pid: currentMenu.pid || '',
      });
    } else {
      form.resetFields();
    }
    handleClose();
  };

  const onSuccess = () => {
    message.success('Success');
    // 重置数据
    form.resetFields();
    handleSuccess();
  };

  const handleUpdate = (params: any) => {
    fetchData({ setLoading, request: updateMenu, onSuccess, params });
  };

  const handleAdd = (params: any) => {
    fetchData({ setLoading, request: addMenu, onSuccess, params });
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.path) {
      setPath(changeValue.path);
    }
  };
  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Menu` : `Create Menu`}
      onConfirm={handleSave}
      open={visible}
      onClose={handleReset}
      loading={loading}
      maskClosable={true}
    >
      <Form
        initialValues={MenuDefaultFormData}
        onFinish={onFinish}
        autoComplete="off"
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
      >
        <Form.Item
          label="Name"
          name="title"
          rules={[{ required: true, message: 'Please Input Name!' }]}
        >
          <Input placeholder="Please Input Name" />
        </Form.Item>
        {isEdit && (
          <Form.Item label="ID" name="id">
            <Input disabled />
          </Form.Item>
        )}
        <Form.Item
          label="Path"
          name="path"
          rules={[{ required: false, message: 'Please Input Path!' }]}
          tooltip="路由路径"
        >
          <Input placeholder="Please Input Path" />
        </Form.Item>
        <Form.Item
          label="Component Path"
          name="component"
          tooltip="组件路径，src下面的路径，例如pages/partner"
        >
          <Input placeholder="Please Input Component Path" />
        </Form.Item>
        <Form.Item
          label="Parent Menu"
          name="pid"
          rules={[{ required: !path, message: 'Please Select Parent Menu' }]}
        >
          <Select
            options={menuList.map((item) => {
              return {
                label: `${item.title}(${
                  item.component ? item.component : 'NODE'
                })`,
                value: item.id,
              };
            })}
            allowClear
            showSearch
            placeholder="Please Select Parent Menu"
            optionFilterProp="children"
            filterOption={(input, option: any) =>
              option &&
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        </Form.Item>
        <Form.Item label="Access Code" name="access">
          <Input placeholder="Please Input Access Code" />
        </Form.Item>
        <Form.Item
          label="Show In Menu"
          name="is_hide"
          rules={[{ required: true, message: 'Please Select Hide In Menu!' }]}
          tooltip="是否显示在左侧菜单上"
        >
          <Radio.Group>
            <Radio value={2}>Show</Radio>
            <Radio value={1}>Hide</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="Icon" name="icon">
          <Input placeholder="Please Input Icon" />
        </Form.Item>
        {/* 是否显示侧边菜单 */}
        <Form.Item
          label="Show Menu"
          name="menu_render"
          tooltip="左侧菜单是否显示,例如pretarget,不显示左侧菜单,而是全屏"
          rules={[{ required: true, message: 'Please Select Show Menu!' }]}
        >
          <Radio.Group>
            <Radio value={1}>Show</Radio>
            <Radio value={2}>Hide</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="Interface" name="interfaces">
          <Select
            mode="multiple"
            options={interfaceList.map((item) => {
              return {
                label: `${item.itf_name}(${item.path})`,
                value: item.id,
              };
            })}
            maxTagCount="responsive"
            allowClear
            showSearch
            placeholder="Please Select Interface"
            optionFilterProp="children"
            filterOption={(input, option: any) =>
              option &&
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        </Form.Item>
        {/* <Form.Item
            label="Status"
            name="status"
            rules={[{ required: true, message: 'Please Select Status' }]}
          >
            <Radio.Group>
              <Radio value={1}>Active</Radio>
              <Radio value={0}>Paused</Radio>
            </Radio.Group>
          </Form.Item> */}
        <Form.Item label="Remark" name="remark">
          <Input placeholder="Please Input Remark" />
        </Form.Item>
      </Form>
    </NormalDrawer>
  );
};

export default Page;
