/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-03-31 11:51:59
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-12 15:12:15
 * @Description:
 */
import styles from './index.less';
import { Tree, Spin } from 'antd';
import type { DataNode } from 'antd/es/tree';

import React, { useEffect, useMemo, useState } from 'react';
import { useModel } from 'umi';
import { LinkMenuType } from '@/constants/permission/menu';

const DefaultMenuData = [
  {
    title: 'Root',
    key: '0',
    children: [],
  },
];

type PmsContentProps = {
  currentData: PermissionAPI.RoleListItem | undefined;
  onSelectPms: (pms: PermissionAPI.RoleListItem) => void;
  accessCode?: string;
};
const App: React.FC<PmsContentProps> = ({ currentData, onSelectPms }) => {
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(false);

  // 是否是第一次进入页面
  const [first, setFirst] = useState(true);
  const [menuData, setMenuData] = useState<DataNode[]>(DefaultMenuData);
  const {
    dataSource = [],
    reload,
    loading: menuLoading,
  } = useModel('useMenuList');

  useEffect(() => {
    reload({ menu_type: LinkMenuType.Ordinary });
  }, []);

  useEffect(() => {
    if (Array.isArray(currentData?.pms_list)) {
      const checked = currentData?.pms_list.map(v => `${v}`) || [];
      if (checked.length === dataSource?.length) {
        setCusCheckedKeys({ checked: checked.concat(['0']), halfChecked: [] });
      } else {
        setCusCheckedKeys({ checked: checked, halfChecked: ['0'] });
      }
    }
  }, [currentData]);
  const handleFormatData = (
    dataSource: PermissionAPI.MenuItem[],
    key: number,
  ): DataNode[] => {
    const filterArr = dataSource
      .filter((item) => item.pid === key)
      .sort((a, b) => a.sort - b.sort);
    const restArr = dataSource.filter((item) => item.pid !== key);
    return filterArr.map((item) => {
      return {
        title: item.title,
        key: `${item.id}`,
        icon: item.icon,
        children: [...handleFormatData(restArr, item.id)],
      };
    });
  };

  useEffect(() => {
    if (dataSource && dataSource.length) {
      const treeArr: DataNode[] = handleFormatData(dataSource, 0);
      const treeData = [
        {
          title: 'Root',
          key: '0',
          children: treeArr,
        },
      ];
      setMenuData(treeData);
      const keys = dataSource.map(
        (item: PermissionAPI.MenuItem) => `${item.id}`,
      );
      keys.push('0');
      if (first) {
        setExpandedKeys(keys);
        setFirst(false);
      }
    }
  }, [dataSource]);

  const handleGetAllKey = (item: DataNode) => {
    if (!item.children || !item.children.length) {
      return [item.key];
    } else {
      let key = [item.key];
      item.children.forEach((val) => {
        key = [...key, ...handleGetAllKey(val)];
      });
      return key;
    }
  };

  const [pidMap, setPidMap] = useState<Map<string, string[]>>(new Map());
  const [cidMap, setCidMap] = useState<Map<string, string[]>>(new Map());
  const [onlyPidMap, setOnlyPidMap] = useState<Map<string, string>>(new Map());
  const treeData = useMemo(() => {
    let tmpOnylPidMap = new Map();
    let tmpPidMap = new Map();
    let tmpCidMap = new Map();
    const loop = (data: DataNode[]): DataNode[] => {
      return data.map((item: any) => {
        if (item.children) {
          return {
            ...item,
            title: item.title,
            key: item.key,
            children: loop(item.children),
            icon: item.icon,
          };
        }
        return {
          ...item,
          title: item.title,
          key: item.key,
          icon: item.icon,
        };
      });
    };
    const treeResult = loop(menuData);
    // 记录每个节点的所有直接父节点
    const loopOnlyPid = (data: DataNode[]) => {
      data.forEach((item: any) => {
        if (item.children) {
          item.children.forEach((val: any) => {
            tmpOnylPidMap.set(val.key, item.key);
          });
          loopOnlyPid(item.children);
        }
      });
    };
    // 记录每个节点的所有父节点
    const loopPid = (data: DataNode[]) => {
      data.forEach((item: any) => {
        if (item.children) {
          item.children.forEach((val: any) => {
            const keys = tmpPidMap.get(item.key) || [];
            tmpPidMap.set(val.key, [...keys, item.key]);
          });
          loopPid(item.children);
        }
      });
    };
    // 记录每个节点的所有直接子点
    const loopChild = (data: DataNode[]) => {
      data.forEach((item: any) => {
        if (item.children) {
          item.children.forEach((val: any) => {
            const keys = tmpCidMap.get(item.key) || [];
            tmpCidMap.set(item.key, [...keys, val.key]);
          });
          loopChild(item.children);
        }
      });
    };

    loopOnlyPid(treeResult);
    loopPid(treeResult);
    loopChild(treeResult);

    setOnlyPidMap(tmpOnylPidMap);
    setPidMap(tmpPidMap);
    setCidMap(tmpCidMap);

    return treeResult;
  }, [menuData, currentData]);

  const onExpand = (newExpandedKeys: any[]) => {
    setAutoExpandParent(false);
    setExpandedKeys(newExpandedKeys);
  };

  const [cusCheckedKeys, setCusCheckedKeys] = useState<{
    checked: string[];
    halfChecked: string[];
  }>({
    checked: [],
    halfChecked: [],
  });
  const handleCusCheckedKeys = (checkedKeys: any, info: any) => {
    const currenrKey = info.node.key;
    const currentIsChecked = !info.node.checked;
    const { checked, halfChecked } = checkedKeys;
    const currentCid = handleGetAllKey(info.node);
    const currentPid = pidMap.get(currenrKey) || [];
    if (!currentIsChecked) {
      // 取消选中的节点
      checkedKeys.halfChecked = [...checkedKeys.halfChecked, ...currentPid];
      checkedKeys.checked = [
        ...new Set([...checkedKeys.checked, ...currentCid]),
      ];
      checkedKeys.halfChecked = checkedKeys.halfChecked.filter(
        (item: any) => !currentCid.includes(item),
      );
      checkedKeys.checked = checkedKeys.checked
        .filter((item: any) => !currentCid.includes(item))
        .filter((item: any) => !currentPid.includes(item));
    } else {
      // 选中的节点
      const onlyPid = onlyPidMap.get(currenrKey) || '';
      const pids = handlePidChecked(checked, onlyPid, currentPid);
      checkedKeys.checked = [
        ...new Set([...checkedKeys.checked, ...currentCid, ...pids]),
      ];
      checkedKeys.halfChecked = [
        ...checkedKeys.halfChecked,
        ...currentPid.filter((item: any) => !pids.includes(item)),
      ];
    }
  };
  const handlePidChecked: any = (
    checked: any,
    onlyPid: string,
    currentPid: string[],
    pids: string[] = [],
  ) => {
    const parent = onlyPidMap.get(onlyPid) || '';
    const Nid = cidMap.get(onlyPid) || [];
    const allCidChecked = checked.filter((item: any) => Nid.includes(item));
    const isCheckedPid =
      allCidChecked.length === Nid.length || Nid.length === 1 || !Nid.length;
    if (isCheckedPid) {
      pids.push(onlyPid);
      checked.push(onlyPid);
    }
    if (!onlyPidMap.get(parent)) {
      return pids;
    } else {
      return handlePidChecked(checked, parent, currentPid, pids);
    }
  };
  const onCheck = (checkedKeys: any, info: any) => {
    handleCusCheckedKeys(checkedKeys, info);
    setCusCheckedKeys(checkedKeys);
    const checkedNode = dataSource
      ?.map((item: PermissionAPI.MenuItem) => {
        if (
          checkedKeys.checked
            .concat(checkedKeys.halfChecked)
            .includes(`${item.id}`)
        ) {
          return {
            ...item,
            interfaces: [],
          };
        }
      })
      .filter(Boolean);
    onSelectPms(checkedNode);
  };
  const getClassNames = (item: any) => {
    const isPNode = item.isPNode;
    const isRootNode = item.key === '0';
    const isLast = item.isLast;
    return `${isPNode ? styles['p-node'] : ''} ${
      isRootNode ? styles['root-node'] : ''
    } ${isLast ? styles['last-node'] : ''}`;
  };
  const isLastNode = (key: string) => {
    let currentPid = onlyPidMap.get(key);
    if (currentPid) {
      let currentNid = cidMap.get(currentPid);
      return !currentNid?.some((nid: string) => {
        return cidMap.get(nid)?.length;
      });
    }
    return true;
  };
  const getTreeNode = (data: any) => {
    if (data && data.length > 0) {
      return data.map((item: any) => {
        if (item.children) {
          if (onlyPidMap.get(item.key) === '0') {
            item.isPNode = true;
          }
          if (isLastNode(item.key)) {
            item.isLast = true;
          }
          return (
            <Tree.TreeNode
              key={item.key}
              title={item.title}
              className={getClassNames(item)}
              disabled={item.disabled}
            >
              {getTreeNode(item.children)}
            </Tree.TreeNode>
          );
        }
        return <Tree.TreeNode key={item.key} title={item.title} />;
      });
    }
    return [];
  };

  const [treeNodes, setTreeNode] = useState(<></>);
  useEffect(() => {
    const Nodes = getTreeNode(treeData);
    setTreeNode(Nodes);
  }, [treeData]);
  return (
    <Spin spinning={menuLoading}>
      <div className={styles['container']}>
        <Tree
          blockNode={false}
          checkable
          onCheck={onCheck}
          rootClassName={styles['root']}
          expandedKeys={expandedKeys}
          onExpand={onExpand}
          autoExpandParent={autoExpandParent}
          className={styles['hide-file-icon']}
          checkedKeys={cusCheckedKeys}
          checkStrictly
        >
          {treeNodes}
        </Tree>
      </div>
    </Spin>
  );
};

export default App;
