/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-12 15:04:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-03-01 17:50:50
 * @Description:
 */

import { Form, Input } from 'antd';
import Select from '@/components/Select/NormalSelect';
import NormalDrawer from '@/components/Drawer/NormalDrawer';

import React, { useEffect, useState } from 'react';

import { RoleType, RoleTypeOptions } from '@/constants/permission/role';
import { useLocation, useModel } from 'umi';

const DefaultFormData = {
  role_name: undefined,
  type: undefined,
  permissions: undefined,
};
type AddRoleDrawerProps = {
  role?: PermissionAPI.RoleListItem;
  visible: boolean;
  isEdit: boolean;
  handleClose: () => void;
  reloadRole: () => void;
  permissionList: PermissionAPI.PermissionItem[];
};
const AddRoleDrawer: React.FC<AddRoleDrawerProps> = ({
  role,
  handleClose,
  visible,
  isEdit,
  permissionList,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [pmsListOptions, setPmsListOptions] = useState<
    { label: string; value: string }[]
  >([]);
  const [type, setType] = useState<number | undefined>();

  useEffect(() => {
    if (role && isEdit && visible) {
      form.setFieldsValue(role);
    } else {
      form.resetFields();
    }
  }, [role, isEdit, visible]);

  useEffect(() => {
    if (permissionList && permissionList.length) {
      const tmp = permissionList.map((item: PermissionAPI.PermissionItem) => {
        return {
          label: item.pms_name,
          value: item.pms_name,
        };
      });
      setPmsListOptions(tmp);
    }
  }, [permissionList]);

  // useEffect(() => {
  //   if (permissionList && permissionList.length) {
  //     const operation = permissionList.find((item: PermissionAPI.PermissionItem) => item.id === role?.id);
  //     if (operation) {
  //       const pms = operation.pms_list.map((item: any) => item.pms_id);
  //       const params = {
  //         role_name: operation.role_name,
  //         type: operation.type,
  //         permissions: pms
  //       };
  //       setPermissions(pms);
  //       setType(operation.type);
  //       form.setFieldsValue(params);
  //     }
  //   }
  // }, [permissionList]);

  // const handleFinish = (values: any) => {
  //   if (id) {
  //     const pms = values.permissions;
  //     const isSame = isArrSame(pms, permissions);
  //     handleEditRole({ ...values, id, pms_change: isSame ? 0 : 1 });
  //   } else {
  //     handleAddRole(values);
  //   }
  // };

  const handleAddRole = (params: any) => {
    setLoading(true);
    // addRole(params)
    //   .then(res => {
    //     if (res && res.code === 0) {
    //       message.success('success');
    //       if (previousUrl) {
    //         history.go(-1);
    //       } else {
    //         history.push('/permission/role');
    //       }
    //     }
    //   })
    //   .catch(e => console.log(e))
    //   .finally(() => {
    //     setLoading(false);
    //   });
  };

  const handleEditRole = (params: any) => {
    setLoading(true);
    // updateRole(params)
    //   .then(res => {
    //     if (res && res.code === 0) {
    //       message.success('success');
    //       if (previousUrl) {
    //         history.go(-1);
    //       } else {
    //         history.push('/permission/role');
    //       }
    //     }
    //   })
    //   .catch(e => console.log(e))
    //   .finally(() => {
    //     setLoading(false);
    //   });
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.type) {
      setType(changeValue.type);
    }
  };
  const handleConfirm = () => {
    form.submit();
  };
  const onCancel = () => {
    form.resetFields();
    handleClose();
  };

  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Role` : `Create Role`}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={true}
    >
      <Form
        initialValues={DefaultFormData}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        form={form}
        onValuesChange={handleValueChange}
        layout="vertical"
      >
        <Form.Item
          label="Role Name"
          name="role_name"
          rules={[{ required: true, message: 'Please Input Role Name!' }]}
        >
          <Input placeholder="Please Input Role Name" />
        </Form.Item>
        {!type || type !== RoleType['Super Administrator'] ? (
          <Form.Item
            label="Type"
            name="type"
            rules={[{ required: true, message: 'Please Select Type!' }]}
          >
            <Select placeholder="Please Select Type" disabled={isEdit}>
              {RoleTypeOptions.map((item) => {
                return (
                  <Select.Option key={item.value} value={item.value}>
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        ) : null}
        {!type || type === RoleType['General User'] ? (
          <Form.Item
            label="Permission"
            name="permissions"
            rules={[{ required: false, message: 'Please Select Permission!' }]}
          >
            <Select
              mode="multiple"
              options={pmsListOptions}
              placeholder="Please Select Permission"
              optionFilterProp="children"
              filterOption={(input, option: any) =>
                option &&
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            />
          </Form.Item>
        ) : null}
      </Form>
    </NormalDrawer>
  );
};

export default AddRoleDrawer;
