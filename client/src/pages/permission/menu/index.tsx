/*
 * @Author: Peggy
 * @Date: 2023-03-16 21:07:01
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-12 12:31:31
 * @Description:
 */
import { Input, Tree, Button, Tooltip } from 'antd';
import type { DataNode, EventDataNode } from 'antd/es/tree';
import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import { InterfaceTypeToNumber as InterfaceType } from '@/constants/permission';
import {
  RedoOutlined,
  PlusCircleOutlined,
  SettingOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { useModel } from 'umi';
import MenuRight from './menu-right';
import MenuSort from './menu-sort';
import RixEngineFont from '@/components/RixEngineFont';

const Page: React.FC = () => {
  const {
    dataSource = [],
    reload,
    loading: menuLoading,
  } = useModel('useMenuList');
  const {
    dataSource: list,
    reload: reloadInterface
  } = useModel('useInterfaceList');

  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(false);
  const [currentMenu, setCurrentMenu] = useState<
    PermissionAPI.MenuItem | undefined
  >(undefined);
  const [menuData, setMenuData] = useState<DataNode[]>([]);
  const [interfaceList, setInterfaceList] = useState<
    PermissionAPI.InterfaceItem[]
  >([]);
  const [searchValue, setSearchValue] = useState('');
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [isEdit, setIsEdit] = useState(false); // 是否编辑
  const [visible, setVisible] = useState(false);
  
  useEffect(() => {
    reload();
    if (!list || !list.length) {
      reloadInterface();
    }
  }, []);

  useEffect(() => {
    if (list && list.length) {
      const data = list.filter((item: PermissionAPI.InterfaceItem) => {
        return item.type === InterfaceType.Normal;
      });
      setInterfaceList(data);
    }
  }, [list]);

  useEffect(() => {
    if (dataSource && dataSource.length) {
      const treeArr: DataNode[] = handleFormatData(dataSource, 0);
      setMenuData(treeArr);
      const keys = dataSource.map(
        (item: PermissionAPI.MenuItem) => `${item.id}`,
      );
      setExpandedKeys(keys);
    }
  }, [dataSource]);

  const handleFormatData = (
    dataSource: PermissionAPI.MenuItem[],
    key: number,
  ): DataNode[] => {
    const filterArr = dataSource
      .filter((item) => item.pid === key)
      .sort((a, b) => a.sort - b.sort);
    const restArr = dataSource.filter((item) => item.pid !== key);
    return filterArr.map((item) => {
      return {
        ...item,
        title: item.title,
        key: `${item.id}`,
        icon: item.icon,
        icon_tmp: item.icon || '',
        children: [...handleFormatData(restArr, item.id)],
      };
    });
  };

  const treeData = useMemo(() => {
    const loop = (
      data: (DataNode & { interfaces?: number[]; op_list?: any[] })[],
    ): DataNode[] => {
      return data.map((item: any) => {
        if (item.children) {
          return {
            ...item,
            title: item.title,
            key: item.key,
            children: loop(item.children),
            icon: item.icon ? <RixEngineFont type={item.icon}/> : '',
            icon_tmp: item.icon,
          };
        }
        return {
          ...item,
          title: item.title || item.op_name,
          key: item.key,
          icon: item.icon ? <RixEngineFont type={item.icon}/> : '',
          icon_tmp: item.icon,
        };
      });
    };
    return loop(menuData);
  }, [searchValue, menuData, interfaceList]);
  
  const onExpand = (newExpandedKeys: any[]) => {
    setAutoExpandParent(false);
    setExpandedKeys(newExpandedKeys);
  };

  const handleSearch = (val: string, data: DataNode[]) => {
    let arr: string[] = [];
    data.forEach((item) => {
      if ((item.title! as string).indexOf(val) > -1) {
        arr.push(item.key! as string);
      }
      if (item.children) {
        arr = [...arr, ...handleSearch(val, item.children)];
      }
    });
    return arr;
  };

  const handleSearchMenu = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    setAutoExpandParent(true);
    const newExpandedKeys = handleSearch(value, menuData);
    setExpandedKeys(newExpandedKeys as any);
  };

  const handleReload = () => {
    reload();
  };

  const onSelectTree = (
    selects: React.Key[],
    e: {
      event: 'select';
      selected: boolean;
      node: EventDataNode<DataNode> & { type?: number };
      selectedNodes: DataNode[];
      nativeEvent: MouseEvent;
    },
  ) => {
    setIsEdit(true);
    setSelectedKeys(selects as string[]);
    const nodes = e.selectedNodes;
    if (Array.isArray(nodes) && nodes.length) {
      setCurrentMenu(nodes[0] as any);
    }
  };

  const handleCancelSelect = () => {
    setSelectedKeys([]);
    setCurrentMenu(undefined);
    setIsEdit(false);
  };

  const handleAddMenu = () => {
    setIsEdit(false);
    setCurrentMenu(undefined);
    setSelectedKeys([]);
  };

  const handleSuccess = () => {
    reload();
    setSelectedKeys([]);
    setCurrentMenu(undefined);
    setIsEdit(false);
  };

  const handleOk = () => {
    setVisible(false);
    reload();
  };

  const handleCancel = () => {
    setVisible(false);
  };

  const handleOpen = () => {
    setVisible(true);
  };
  return (
    <div className={styles['menu-container']}>
      <div className={styles['menu-left-card']}>
        <div className={styles['top']}>
          <h3>菜单目录</h3>
          <div className={styles['top-right']}>
            <Tooltip title="Add Menu">
              <Button
                type="text"
                icon={
                  <PlusCircleOutlined
                    style={{ fontSize: 20, cursor: 'pointer' }}
                  />
                }
                onClick={handleAddMenu}
              />
            </Tooltip>
            <Tooltip title="Sort Setting">
              <Button
                type="text"
                icon={<SettingOutlined style={{ fontSize: 20, cursor: 'pointer' }}/>}
                onClick={handleOpen}
              />
            </Tooltip>
            <Tooltip title="Refresh">
              <Button
                icon={
                  <RedoOutlined
                    style={{ fontSize: 20, cursor: 'pointer' }}
                    spin={menuLoading}
                  />
                }
                type="text"
                onClick={handleReload}
              />
            </Tooltip>
          </div>
        </div>
        <div className={styles['select-message-container']}>
          <span>
            <InfoCircleOutlined
              style={{ color: '#1890ff', fontSize: 18, paddingRight: 8 }}
            />
            <span style={{ paddingRight: 8 }}>当前选择编辑:</span>
            {!selectedKeys.length ? <span>无</span> : ''}
            {selectedKeys.length ? (
              <span style={{ color: '#0DB4BE' }}>
                {currentMenu?.title || ''}
              </span>
            ) : (
              ''
            )}
          </span>
          {selectedKeys.length ? (
            <span
              style={{ color: '#0DB4BE', cursor: 'pointer' }}
              onClick={handleCancelSelect}
            >
              取消选择
            </span>
          ) : (
            ''
          )}
        </div>
        <div className={styles['search-container']}>
          <Input
            placeholder="Please Input Menu Name"
            onChange={handleSearchMenu}
          />
        </div>
        <div className={styles['tree-container']}>
          <Tree
            showIcon
            treeData={treeData}
            expandedKeys={expandedKeys}
            onExpand={onExpand}
            autoExpandParent={autoExpandParent}
            className={styles['menu-tree-container']}
            selectedKeys={selectedKeys}
            onSelect={onSelectTree}
          />
        </div>
      </div>
      <MenuRight
        currentMenu={currentMenu}
        interfaceList={interfaceList}
        menuList={dataSource}
        isEdit={isEdit}
        handleSuccess={handleSuccess}
      />
      <MenuSort
        visible={visible}
        handleCancel={handleCancel}
        handleOk={handleOk}
        allMenuList={dataSource}
      />
    </div>
  );
};

export default Page;
