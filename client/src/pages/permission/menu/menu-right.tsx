/*
 * @Author: <PERSON>
 * @Date: 2023-03-16 21:59:13
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-12 14:50:47
 * @Description:
 */
import styles from './index.less';
import { Input, Button, Form, Radio, Select, Spin, message, Modal } from 'antd';
import { MenuDefaultFormData, NodeType } from '@/constants/permission';
import { useEffect, useMemo, useState } from 'react';
import { fetchData, isArrSame, isObjectValueEqual } from '@/utils';
import { addMenu, updateMenu, deleteMenu } from '@/services/permission';

import { LinkMenuTypeList } from '@/constants/permission/menu';
import NormalRadio from '@/components/Radio/NormalRadio';
import { MenuType } from '@/constants/permission/menu';
import { StatusType } from '@/constants';

type MenuRightProps = {
  currentMenu?: PermissionAPI.MenuItem | undefined;
  interfaceList: PermissionAPI.InterfaceItem[];
  menuList: PermissionAPI.MenuItem[];
  isEdit: boolean;
  handleSuccess: () => void;
};
const Page: React.FC<MenuRightProps> = ({
  currentMenu,
  interfaceList,
  menuList: allMenuList,
  isEdit,
  handleSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [component, setComponent] = useState('');
  const [type, setType] = useState(MenuType.Menu);
  const [path, setPath] = useState('');
  const [menuList, setMenuList] = useState<PermissionAPI.MenuItem[]>(allMenuList);

  const activeMenuList = useMemo(() => {
    return allMenuList.filter(v => {
      return v.type === MenuType.Menu && v.status === StatusType.Active;
    });
  }, [allMenuList]);


  useEffect(() => {
    if (currentMenu && isEdit) {
      setType(currentMenu.type);
      setPath(currentMenu.path);
      setComponent(currentMenu.component || '');
      form.setFieldsValue({
        ...currentMenu,
        icon: currentMenu.icon_tmp || '',
        pid: currentMenu.pid || '',
      });
    } else {
      form.resetFields();
      setComponent('');
      setType(MenuType.Menu);
      setMenuList(activeMenuList);
    }
  }, [currentMenu, isEdit]);

  useEffect(() => {
    const data = activeMenuList.filter(v => {
      if (currentMenu) {
        return v.id !== currentMenu.id && (!path || path.includes(v.path));
      } else {
        return (!path || path.includes(v.path));
      }
    });
    setMenuList(data);
  }, [path, activeMenuList, currentMenu]);

  const handleSave = () => {
    form.submit();
  };

  const getMenuKeyValue = (value: any) => {
    return {
      path: value.path || '',
      title: value.title,
      is_hide: value.is_hide,
      icon: value.icon || '',
      component: value.component || '',
      menu_render: value.menu_render,
      status: value.status,
    };
  };

  const onFinish = (value: any) => {
    if (isEdit && currentMenu) {
      // return;
      const isSame = isArrSame(value.interfaces, currentMenu.interfaces);
      const a = getMenuKeyValue(value);
      const b = getMenuKeyValue(currentMenu);
      const flag = isObjectValueEqual(a, b);
      const params = {
        ...value,
        id: currentMenu.id,
        s_flag: isSame && flag ? 0 : 1, // 如果关键数据没改变
        itf_change: isSame ? 0 : 1, // 接口是否改变
        pid: value.pid || 0,
        ori_data: isEdit ? {...currentMenu, icon: currentMenu.icon_tmp || ''} : {},
        path: value.path || ''
      };
      handleUpdate(params);
    } else {
      const params = {
        pid: 0,
        ...value,
        path: value.path || ''
      };
      handleAdd(params);
    }
  };

  const handleReset = () => {
    if (isEdit && currentMenu) {
      form.setFieldsValue({
        ...currentMenu,
        icon: currentMenu.icon_tmp || '',
        pid: currentMenu.pid || '',
      });
    } else {
      form.resetFields();
    }
  };

  const onSuccess = () => {
    message.success('Success');
    // 重置数据
    form.resetFields();
    handleSuccess();
    setMenuList(activeMenuList);
  };

  const handleUpdate = (params: any) => {
    fetchData({ setLoading, request: updateMenu, onSuccess, params });
  };

  const handleAdd = (params: any) => {
    fetchData({ setLoading, request: addMenu, onSuccess, params });
  };

  const handleValueChange = (changeValue: any, allValue: any) => {
    setComponent(allValue.component);
    setPath(allValue.path);
    setType(allValue.type);
    if (allValue.component && allValue.node_type === NodeType.Node) {
      form.setFieldValue('node_type', NodeType.Link);
    }
    if (!allValue.component && allValue.node_type !== NodeType.Node) {
      form.setFieldValue('node_type', NodeType.Node);
    }
  };

  const handleDelete = () => {
    Modal.confirm({
      title: 'Delete',
      content: 'Are you sure to delete this menu?',
      onOk: () => {
        handleDeleteMenu();
      },
    });
  };
  const deleteSuccess = () => {
    message.success('Success');
    handleSuccess();
  };
  const handleDeleteMenu = () => {
    if (currentMenu) {
      const ids = [currentMenu.id];
      const loopId = (data: any) => {
        data.forEach((item: any) => {
          ids.push(item.id);
          if (item.children && item.children.length > 0) {
            loopId(item.children);
          }
        });
      };
      loopId(currentMenu.children);
      const params = {
        ids,
      };

      // return;
      fetchData({
        setLoading,
        request: deleteMenu,
        onSuccess: deleteSuccess,
        params,
      });
    }
  };

  return (
    <div className={styles['menu-right-card']}>
      <Spin spinning={loading}>
        <div className={styles['top']}>
          <h3>
            {
              type === MenuType.Menu ? `${isEdit ? '编辑' : '新增'}菜单` : `${isEdit ? '编辑' : '新增'}按钮`
            }
          </h3>
        </div>
        <div className={styles['menu-form']}>
          <Form
            initialValues={MenuDefaultFormData}
            onFinish={onFinish}
            autoComplete="off"
            form={form}
            layout="vertical"
            onValuesChange={handleValueChange}
          >
            <Form.Item label="Type" name="type">
              <NormalRadio disabled={isEdit}>
                <Radio value={1}>Operation</Radio>
                <Radio value={2}>Menu</Radio>
              </NormalRadio>
            </Form.Item>
            <Form.Item
              label="Name"
              name="title"
              rules={[{ required: true, message: 'Please Input Name!' }]}
            >
              <Input placeholder="Please Input Name" />
            </Form.Item>
            {
            isEdit && (
              <Form.Item
                label="ID"
                name="id"
              >
                <Input disabled/>
              </Form.Item>
            )
            }
            <Form.Item
              label="Parent Menu"
              name="pid"
            >
              <Select
                options={menuList.map((item) => {
                  return {
                    label: `${item.title}(${
                      item.component ? item.component : 'NODE'
                    })`,
                    value: item.id,
                  };
                })}
                allowClear
                showSearch
                placeholder="Please Select Parent Menu"
                optionFilterProp="children"
                filterOption={(input, option: any) =>
                  option &&
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            </Form.Item>
            <Form.Item label="Access Code" name="access">
              <Input placeholder="Please Input Access Code" />
            </Form.Item>
            { type === MenuType.Menu  && (
              <>
                <Form.Item
                  label="Route Path"
                  name="path"
                  rules={[{ required: false, message: 'Please Input Route Path!' }]}
                  tooltip="路由路径"
                >
                  <Input placeholder="Please Input Route Path" />
                </Form.Item>
                <Form.Item
                  label="Component Path"
                  name="component"
                  tooltip="组件路径，src下面的路径，例如pages/partner"
                >
                  <Input placeholder="Please Input Component Path" />
                </Form.Item>
                <Form.Item
                  label="Menu Type"
                  name="node_type"
                  tooltip="当前节点类型, Node&Link表示即是路由出口也是访问节点, link表示路由访问节点(会忽略所有子节点),node表示路由出口"
                  rules={[{ required: true, message: 'Please Select Menu Type' }]}
                >
                  <Radio.Group
                    options={[
                      { label: 'Node', value: 1, disabled: !!component }, // 路径存在的时候就不是node了
                      { label: 'Link', value: 2, disabled: !component },
                      { label: 'Node & Link', value: 3, disabled: !component }
                    ]}
                  />
                </Form.Item>
                <Form.Item
                  label="Show Menu"
                  name="menu_render"
                  tooltip="左侧菜单是否显示,例如pretarget,不显示左侧菜单,而是全屏"
                  rules={[
                    { required: true, message: 'Please Select Show Menu!' },
                  ]}
                >
                  <Radio.Group>
                    <Radio value={1}>Show</Radio>
                    <Radio value={2}>Hide</Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item
                  label="Show In Menu"
                  name="is_hide"
                  rules={[
                    { required: true, message: 'Please Select Hide In Menu!' },
                  ]}
                  tooltip="是否显示在左侧菜单上"
                >
                  <Radio.Group>
                    <Radio value={2}>Show</Radio>
                    <Radio value={1}>Hide</Radio>
                  </Radio.Group>
                </Form.Item>
                <Form.Item label="Icon" name="icon">
                  <Input placeholder="Please Input Icon" />
                </Form.Item>
              </>
            )}
            {/* 关联的接口 */}
            <Form.Item label="Interface" name="interfaces">
              <Select
                mode="multiple"
                options={interfaceList.map((item) => {
                  return {
                    label: `${item.itf_name}(${item.path})`,
                    value: item.id,
                  };
                })}
                maxTagCount="responsive"
                allowClear
                showSearch
                placeholder="Please Select Interface"
                optionFilterProp="children"
                filterOption={(input, option: any) =>
                  option &&
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            </Form.Item>
            <Form.Item label="Menu Type" name="menu_type">
              <Select
                options={LinkMenuTypeList.map((item) => {
                  return {
                    label: item.label,
                    value: item.value,
                  };
                })}
                allowClear
                placeholder="Please Select Menu Type"
              />
            </Form.Item>
            <Form.Item label="Remark" name="remark">
              <Input placeholder="Please Input Remark" />
            </Form.Item>
          </Form>
        </div>
        <div className={styles['footer']}>
          <Button type="primary" onClick={handleReset}>
            Reset
          </Button>
          <Button type="primary" onClick={handleSave}>
            Save
          </Button>
          {isEdit && (
            <Button type="primary" onClick={handleDelete} danger>
              Delete
            </Button>
          )}
        </div>
      </Spin>
    </div>
  );
};

export default Page;
