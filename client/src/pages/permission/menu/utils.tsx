/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-12 11:29:29
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-12 11:31:24
 * @Description: 
 */

import type { DataNode } from 'antd/es/tree';
import { StatusType } from '@/constants';
import RixEngineFont from '@/components/RixEngineFont';

export const getMenuKeyValue = (value: any) => {
  return {
    path: value.path || '',
    title: value.title,
    is_hide: value.is_hide,
    icon: value.icon || '',
    component: value.component || '',
    menu_render: value.menu_render,
    status: value.status,
    menu_type: value.menu_type
  };
};

export type MenuRightProps = {
  currentMenu?: PermissionAPI.MenuItem | undefined;
  interfaceList:PermissionAPI.InterfaceItem[];
  allMenuList: PermissionAPI.MenuItem[];
  isEdit: boolean;
  handleSuccess: () => void;
}

// 获取active的子菜单
export const getAllChildActiveIds = (data: PermissionAPI.MenuItem[], ids: number[]) => {
  let arr: number[] = ids;
  data.forEach(v => {
    const flag = Array.isArray(v.children) && v.children.length;
    const tmp = v.status === StatusType.Active ? [...arr, v.id] : arr;
    arr = flag ? getAllChildActiveIds(v.children || [], tmp) : tmp;
  });
  return arr;
};

export const handleFormatData = (dataSource: PermissionAPI.MenuItem[], key: number):DataNode[] => {
  const filterArr = dataSource.filter(item => item.pid === key).sort((a, b) => a.sort - b.sort);
  const restArr = dataSource.filter(item => item.pid !== key);
  return filterArr.map(item => {
    return {
      ...item,
      title: item.title,
      key: `${item.id}`,
      icon: item.icon ? <RixEngineFont type={item.icon}/> : '',
      icon_tmp: item.icon,
      children: [...handleFormatData(restArr, item.id)]
    };
  });
};

export const loop = (data: DataNode[], arr: number[]): DataNode[] => {
  return data.map((item: any) => {
    if (item.children) {
      return {
        ...item,
        title: item.title,
        key: item.key,
        children: loop(item.children, arr),
        disabled: !arr.includes(item.type)
      };
    }
    return {
      ...item,
      title: item.title,
      key: item.key,
      disabled: !arr.includes(item.type)
    };
  });
};

export const handleGetPidObj = (menu: DataNode[], obj: any) => {
  menu.forEach((item: any) => {
    if (Array.isArray(item.children) && item.children.length) {
      const arr = item.children.map((v: any) => v.id);
      arr.forEach((v: number) => {
        obj[v] = item.id;
      });
      handleGetPidObj(item.children, obj);
    }
  });
};

export const loopDrop = (
  data: DataNode[],
  key: React.Key,
  callback: (node: DataNode, i: number, data: DataNode[]) => void
) => {
  for (let i = 0; i < data.length; i++) {
    if (data[i].key === key) {
      return callback(data[i], i, data);
    }
    if (data[i].children) {
      loopDrop(data[i].children!, key, callback);
    }
  }
};

export const handleGetSortParams = (data: DataNode[], arr: MenuSortParamsItem[], pidObj: any) => {
  data.forEach((item: any, index) => {
    const obj: MenuSortParamsItem = {
      id: item.id,
      sort: index,
      pid: pidObj[item.id] || 0
    };
    arr.push(obj);
    if (Array.isArray(item.children) && item.children.length) {
      handleGetSortParams(item.children, arr, pidObj);
    }
  });
};

export type MenuSortProps = {
  allMenuList: PermissionAPI.MenuItem[];
  visible: boolean;
  handleOk: () => void;
  handleCancel: () => void;
}

export type MenuSortParamsItem = {
  id: number; // 自己的id
  sort: number; // 排序的
  pid: number; // 父菜单
}

// 获取paused的父菜单 数据线过滤掉active的
export const getAllParentPausedIds = (data: PermissionAPI.MenuItem[], pid: number, ids: number[]) => {
  const arr = ids;
  let pids: number[] = [];
  data.forEach(v => {
    if (v.id === pid) {
      arr.push(v.id);
      pids.push(v.pid);
    }
  });
  pids = pids.filter(v => v);
  const result = data.filter(v => !arr.includes(v.id));
  if (result.length && pids.length) {
    pids.forEach(v => {
      getAllParentPausedIds(result, v, arr);
    });
  }
};
