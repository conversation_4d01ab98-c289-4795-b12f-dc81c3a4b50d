.menu-container {
  display: flex;
  background-color: #f2f5f5;
  gap: 24px;
  max-height: calc(100vh - 65px);
  overflow-y: scroll;
  .menu-left-card {
    width: 400px;
    margin: 24px;
    margin-right: 0px;
    border-radius: 6px;
    background-color: #fff;
    .search-container {
      padding: 12px 16px;
      > div {
        margin-bottom: 12px;
      }
    }
    .select-message-container {
      height: 40px;
      margin: 12px 16px;
      margin-bottom: 0px;
      border: 1px solid #91d5ff;
      background-color: #e6f7ff;
      border-radius: 3px;
      padding: 0px 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .tree-container {
      padding: 12px 16px;
      max-height: calc(100vh - 290px);
      overflow-y: scroll;
    }
  }
  .menu-tree-container {
    :global {
      .ant-tree-node-content-wrapper {
        display: flex;
      }
    }
  }
  .top {
    display: flex;
    align-items: center;
    background-color: #ebeded;
    justify-content: space-between;
    height: 48px;
    line-height: 48px;
    padding: 0px 16px;
    h3 {
      margin-bottom: 0px;
    }
  }
  .menu-right-card {
    flex: 1;
    margin: 24px;
    background-color: #fff;
    border-radius: 6px;
    margin-left: 0px;
    .menu-form {
      padding: 12px 16px;
      padding-right: 0px;
      height: calc(100vh - 210px);
      overflow-y: scroll;
      :global {
        .ant-form {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
        }
        .ant-form-item {
          flex: 1;
          min-width: 350px;
          max-width: 350px;
          padding-right: 16px;
          margin-bottom: 20px;
        }
        .ant-form-vertical .ant-form-item-label {
          padding-bottom: 5px;
        }
        .ant-form-item-explain-error {
          font-size: 12px;
        }
      }
    }
    .footer {
      display: flex;
      text-align: right;
      padding: 12px 16px;
      background-color: #ebeded;
      button {
        border-radius: 6px;
      }
      button {
        margin-left: 12px;
      }
    }
  }
}

.mr12 {
  margin-right: 12px;
}

.menu-sort-container {
  //color: red;
  :global {
    .ant-modal-content {
      .ant-modal-body {
        max-height: calc(100vh - 200px);
        overflow-y: scroll;
      }
    }
  }
}
