/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-16 16:42:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-09 12:19:18
 * @Description:
 */
import { useEffect, useState } from 'react';
import { Form, message } from 'antd';
import {
  StatusOptions,
  ProfitModelOptions,
  ProfitModelType,
  StatusMap,
  YesNoMap,
} from '@/constants';
import {
  AuctionOptions,
  AuctionType,
  IvtTypeOptions,
  ImpTrackingOptions,
  ImpTrackingType,
  IvtTypeMap,
  NativeFormatOptions,
  NativeFormatType,
} from '@/constants/demand';
import { updateDemand } from '@/services/api';
import { fetchData } from '@/utils';
import Input from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';
import InputNumber from '@/components/Input/InputNumber';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
// import { RatioRule, RevShareRatioRule } from '@/constants/strategy';
import NormalSelect from '@/components/Select/NormalSelect';
import {
  BannerTransferFormatConfig,
  BannerTransferFormatOptions,
  BurlTrackTypeOptions,
  BurlTrackType,
} from '@/constants/demand/demand-columns';

type AddDemandModelProps = {
  demand?: DemandAPI.DemandListItem;
  visible: boolean;
  isEdit: boolean;
  partnerList: PartnerAPI.PartnerListItem[];
  integrationTypeList: CommonAPI.IntegrationTypeItem[];
  handleClose: () => void;
  reloadDemand: () => void;
};

const DefaultFormData = {
  buyer_name: '',
  demand_account_name: '',
  integration_type: undefined,
  profit_model: ProfitModelType.Net,
  profit_ratio: 30,
  rev_share_ratio: 100,
  profit_status: StatusMap.Active,
  auction_type: AuctionType['First Price'],
  imp_track_type: ImpTrackingType.JS,
  schain_required: StatusMap.Paused,
  filter_mraid: YesNoMap.No,
  idfa_required: StatusMap.Paused,
  pixl_ivt_type: IvtTypeMap.Unlimited,
  hm_ivt_type: IvtTypeMap.Unlimited,
  multi_format: StatusMap.Paused,
  banner_multi_size: StatusMap.Paused,
  banner_transfer_format: BannerTransferFormatConfig.Banner2Video,
  pass_display_manager: StatusMap.Paused,
  auto_qps: StatusMap.Paused,
  native_format: NativeFormatType.String,
  native_root_key: StatusMap.Paused,
  burl_track_type: BurlTrackType.ADM,
};

const AddDemandModel: React.FC<AddDemandModelProps> = ({
  demand,
  handleClose,
  visible,
  isEdit,
  reloadDemand,
  partnerList,
  integrationTypeList,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleDefaultType = () => {
    if (integrationTypeList && integrationTypeList.length) {
      form.setFieldValue('integration_type', integrationTypeList[0].id);
    }
  };

  useEffect(() => {
    if (visible && !isEdit && integrationTypeList) {
      handleDefaultType();
    }
  }, [integrationTypeList, visible]);

  useEffect(() => {
    if (demand && isEdit && visible) {
      const { max_pxl_ivt_ratio, max_hm_ivt_ratio, ...rest } = demand;

      const formData = {
        ...rest,
        max_hm_ivt_ratio,
        max_pxl_ivt_ratio,
        dp_id: demand.dp_id > 0 ? demand.dp_id : undefined,
        pixl_ivt_type:
          max_pxl_ivt_ratio === -1 ? IvtTypeMap.Unlimited : IvtTypeMap.Limited,
        hm_ivt_type:
          max_hm_ivt_ratio === -1 ? IvtTypeMap.Unlimited : IvtTypeMap.Limited,
      };
      form.setFieldsValue(formData);
    } else {
      form.resetFields();
    }
  }, [demand, isEdit, visible]);

  const handleConfirm = () => {
    form.submit();
  };

  const onSuccess = () => {
    message.success('success');
    form.resetFields();
    handleClose();
    reloadDemand();
  };

  const handleEditDemand = (params: any) => {
    fetchData({ setLoading, onSuccess, request: updateDemand, params });
  };

  const handleFinish = (values: any) => {
    if (isEdit) {
      const formatValue = Object.fromEntries(
        Object.entries(values).filter(([key, value]) => value !== undefined),
      );

      handleEditDemand(formatValue);
      // const {
      //   pass_display_manager,
      //   banner_multi_size,
      //   autostore = 2,
      //   skoverlay = 2,
      //   block_off_store_app = 2,
      //   auto_qps = 2,
      //   tnt_id = 0,
      //   buyer_id = 0,
      //   max_hm_ivt_ratio = -1,
      //   max_pxl_ivt_ratio = -1,
      //   native_format = NativeFormatType.String,
      //   native_root_key = StatusMap.Paused,
      // } = values;

      // handleEditDemand({
      //   pass_display_manager,
      //   banner_multi_size,
      //   tnt_id,
      //   buyer_id,
      //   autostore,
      //   skoverlay,
      //   block_off_store_app,
      //   auto_qps,
      //   max_hm_ivt_ratio,
      //   max_pxl_ivt_ratio,
      //   native_format,
      //   native_root_key,
      // });
    }
  };

  const onCancel = () => {
    form.resetFields();
    handleClose();
  };

  const handleValueChange = (changeValue: any) => {
    if (changeValue.pixl_ivt_type) {
      form.setFieldsValue({ max_pxl_ivt_ratio: 0 });
    }
    if (changeValue.hm_ivt_type) {
      form.setFieldsValue({ max_hm_ivt_ratio: 0 });
    }
    if (changeValue.buyer_name && !isEdit) {
      form.setFieldsValue({
        demand_account_name: changeValue.buyer_name?.trim(),
      });
      form.validateFields(['demand_account_name']);
    }
  };
  return (
    <NormalDrawer
      blackName={isEdit ? `Edit Advertiser` : `Create Advertiser`}
      grayName={isEdit ? `${demand?.buyer_name}` : ''}
      onConfirm={handleConfirm}
      open={visible}
      onClose={onCancel}
      loading={loading}
      maskClosable={false}
    >
      <Form
        initialValues={DefaultFormData}
        onFinish={handleFinish}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        form={form}
        layout="vertical"
        onValuesChange={handleValueChange}
        validateTrigger={['onChange', 'onBlur']}
      >
        <Form.Item name="buyer_id" hidden>
          <InputNumber />
        </Form.Item>
        <Form.Item name="tnt_id" hidden>
          <InputNumber />
        </Form.Item>
        {/* Basic Information */}
        <Form.Item
          name="buyer_name"
          label="Advertiser Name:"
          rules={[{ required: true, message: 'Please Enter Advertiser Name' }]}
        >
          <Input placeholder="Please Enter Advertiser Name" disabled />
        </Form.Item>
        <Form.Item
          name="demand_account_name"
          label="Advertiser Account Name:"
          rules={[
            {
              required: true,
              message: 'Please Enter Advertiser Account Name',
            },
          ]}
        >
          <Input
            placeholder="Please Enter Advertiser Account Name"
            addonBefore={isEdit ? undefined : 'Adv_'}
            disabled={isEdit}
            allowClear
          />
        </Form.Item>
        <Form.Item
          name="dp_id"
          label="Partner:"
          rules={[{ required: false, message: 'Please Select Partner' }]}
        >
          <NormalSelect
            options={partnerList.map((v) => ({
              label: `${v.partner_name}(${v.partner_id})`,
              value: v.dp_id,
            }))}
            allowClear
            disabled
          />
        </Form.Item>

        {/* Integration Settings */}
        <Form.Item
          name="integration_type"
          label="Integration Type:"
          rules={[
            { required: true, message: 'Please Select Integration Type' },
          ]}
        >
          <NormalSelect
            disabled={isEdit}
            options={integrationTypeList?.map((v) => ({
              label: v.itg_name,
              value: v.id,
            }))}
          />
        </Form.Item>
        <Form.Item
          name="auction_type"
          label="Auction Type:"
          rules={[{ required: true, message: 'Please Select Auction Type' }]}
        >
          <NormalRadio options={AuctionOptions} disabled />
        </Form.Item>
        <Form.Item
          name="imp_track_type"
          label="Impression Track Type:"
          rules={[
            { required: true, message: 'Please Select Impression Track Type' },
          ]}
        >
          <NormalRadio options={ImpTrackingOptions} disabled />
        </Form.Item>
        {/* Add the form field in the appropriate section */}
        <Form.Item
          name="burl_track_type"
          label="BURL Track Type:"
          rules={[{ required: true, message: 'Please select BURL track type' }]}
          tooltip="Select tracking method: ADM or Server"
        >
          <NormalRadio options={BurlTrackTypeOptions} />
        </Form.Item>
        {/* Native Format */}
        <Form.Item
          name="native_format"
          label="Native Format:"
          rules={[
            {
              required: true,
              message: 'Please Select Native Format',
            },
          ]}
          tooltip="Support imp.native format conversion, String by default"
        >
          <NormalRadio options={NativeFormatOptions} />
        </Form.Item>
        {/* 在 native_format 为 string 时，展示 native_root_key 的配置 */}
        {/* when native_format is string, show native_root_key configuration */}
        <Form.Item noStyle dependencies={['native_format']}>
          {({ getFieldValue }) => {
            const nativeFormat = getFieldValue('native_format');
            return (
              nativeFormat === NativeFormatType.String && (
                <Form.Item name="native_root_key" label="Native Root Key:">
                  <NormalRadio options={StatusOptions} />
                </Form.Item>
              )
            );
          }}
        </Form.Item>
        {/* Profit Settings */}
        <Form.Item
          name="profit_status"
          label="Profit:"
          rules={[{ required: true, message: 'Please Select Profit Status' }]}
        >
          <NormalRadio options={StatusOptions} disabled />
        </Form.Item>
        <Form.Item noStyle dependencies={['profit_status']}>
          {({ getFieldValue }) => {
            const profitStatus = getFieldValue('profit_status');
            return (
              profitStatus === StatusMap.Active && (
                <Form.Item
                  name="profit_ratio"
                  label="Profit Ratio:"
                  rules={[
                    { required: true, message: 'Please Input Profit Ratio' },
                  ]}
                >
                  <InputNumber
                    min={0}
                    max={60}
                    addonAfter="%"
                    style={{ width: 120 }}
                    disabled
                  />
                </Form.Item>
              )
            );
          }}
        </Form.Item>

        <Form.Item
          name="profit_model"
          label="Profit Model:"
          rules={[{ required: true, message: 'Please Select Profit Model' }]}
        >
          <NormalRadio options={ProfitModelOptions} disabled />
        </Form.Item>

        <Form.Item noStyle dependencies={['profit_model']}>
          {({ getFieldValue }) => {
            const profitModel = getFieldValue('profit_model');
            return (
              profitModel === ProfitModelType['Rev Share'] && (
                <Form.Item
                  name="rev_share_ratio"
                  label="Rev Share Ratio:"
                  rules={[
                    { required: true, message: 'Please Input Rev Share Ratio' },
                  ]}
                >
                  <InputNumber min={1} addonAfter="%" max={100} disabled />
                </Form.Item>
              )
            );
          }}
        </Form.Item>

        {/* Traffic Settings */}
        <Form.Item
          name="schain_required"
          label="Pass Supply Chain:"
          rules={[
            { required: true, message: 'Please Select Pass Supply Chain' },
          ]}
          tooltip="Whether to send schain to Demand"
        >
          <NormalRadio options={StatusOptions} disabled />
        </Form.Item>
        <Form.Item
          name="filter_mraid"
          label="Mraid Traffic Filter:"
          rules={[
            { required: true, message: 'Please Select Filtering Mraid AD' },
          ]}
          tooltip="Filter requests that lack support for Mraid"
        >
          <NormalRadio options={StatusOptions} disabled />
        </Form.Item>
        {/* IVT Settings */}
        <Form.Item
          name="pixl_ivt_type"
          label="Pixalate IVT Block:"
          rules={[{ required: true, message: 'Please Select Profit Status' }]}
        >
          <NormalRadio options={IvtTypeOptions} />
        </Form.Item>
        <Form.Item noStyle dependencies={['pixl_ivt_type']}>
          {({ getFieldValue }) => {
            const pixlIvtType = getFieldValue('pixl_ivt_type');
            return (
              pixlIvtType === IvtTypeMap.Limited && (
                <Form.Item
                  name="max_pxl_ivt_ratio"
                  label="Max Pixalate IVT:"
                  rules={[
                    {
                      required: true,
                      message: 'Please Input Max Pixalate IVT(%)',
                    },
                  ]}
                  tooltip="Requests exceeding the configured IVT threshold will be blocked"
                >
                  <InputNumber
                    min={0}
                    addonAfter="%"
                    style={{ width: 120 }}
                    defaultValue={0}
                  />
                </Form.Item>
              )
            );
          }}
        </Form.Item>

        <Form.Item
          name="hm_ivt_type"
          label="Human IVT Block:"
          rules={[{ required: true, message: 'Please Select Profit Status' }]}
        >
          <NormalRadio options={IvtTypeOptions} />
        </Form.Item>
        <Form.Item noStyle dependencies={['hm_ivt_type']}>
          {({ getFieldValue }) => {
            const hmIvtType = getFieldValue('hm_ivt_type');
            return (
              hmIvtType === IvtTypeMap.Limited && (
                <Form.Item
                  name="max_hm_ivt_ratio"
                  label="Max Human IVT:"
                  rules={[
                    {
                      required: true,
                      message: 'Please Input Max Human IVT(%)',
                    },
                  ]}
                  tooltip="Requests exceeding the configured IVT threshold will be blocked"
                >
                  <InputNumber
                    min={0}
                    addonAfter="%"
                    style={{ width: 120 }}
                    defaultValue={0}
                  />
                </Form.Item>
              )
            );
          }}
        </Form.Item>

        {/* Format Settings */}
        <Form.Item
          name="idfa_required"
          label="IFA Required:"
          rules={[{ required: true, message: 'Please Select IFA Required' }]}
          tooltip="Filter requests with an empty IFA(gaid/idfa)"
        >
          <NormalRadio options={StatusOptions} disabled />
        </Form.Item>
        <Form.Item
          name="multi_format"
          label="Multi Format"
          rules={[{ required: true, message: 'Please Select Multi Format' }]}
          tooltip="Support both a banner ad and a video ad format within a single BidRequest.imp at the same index (e.g. imp[0])"
        >
          <NormalRadio options={StatusOptions} disabled />
        </Form.Item>
        <Form.Item
          name="banner_multi_size"
          label="Banner Multi Size:"
          rules={[{ required: true, message: 'Please Select Multi Format' }]}
          tooltip="Add ad size 300*250 for requests with a 320*50 banner"
        >
          <NormalRadio options={StatusOptions} />
        </Form.Item>
        <Form.Item
          name="banner_transfer_format"
          label="Banner Transfer Format:"
          rules={[
            { required: true, message: 'Please Select Banner Transfer Format' },
          ]}
        >
          <NormalRadio options={BannerTransferFormatOptions} />
        </Form.Item>
        {/* Display Settings */}
        <Form.Item
          name="pass_display_manager"
          label="Pass Display Manager:"
          rules={[
            { required: true, message: 'Please Select Pass Display Manager' },
          ]}
          tooltip="Whether to send displaymanager to Demand"
        >
          <NormalRadio options={StatusOptions} />
        </Form.Item>
        <Form.Item
          name="skoverlay"
          label="SKOverlay:"
          tooltip="Whether to pass skoverlay for iOS traffic"
          rules={[{ required: true, message: 'Please Select SKOverlay' }]}
        >
          <NormalRadio options={StatusOptions} />
        </Form.Item>
        <Form.Item
          name="autostore"
          label="Auto-Store:"
          tooltip="Whether to pass auto-store for iOS traffic"
          rules={[{ required: true, message: 'Please Select Pass Auto-Store' }]}
        >
          <NormalRadio options={StatusOptions} />
        </Form.Item>

        <Form.Item
          name="block_off_store_app"
          label="Block non-store apps:"
          tooltip="Block requests from apps removed from the app store"
          rules={[
            { required: true, message: 'Please Select Block non-store apps' },
          ]}
        >
          <NormalRadio options={StatusOptions} />
        </Form.Item>
        <Form.Item
          name="auto_qps"
          label="Additional QPS:"
          tooltip="Allocate additional QPS to Demand sources with high ECPR when available QPS is constrained"
          rules={[
            { required: true, message: 'Please Select Enable Additional QPS' },
          ]}
        >
          <NormalRadio options={StatusOptions} />
        </Form.Item>
        {isEdit && (
          <Form.Item
            name="status"
            label="Status:"
            rules={[{ required: true, message: 'Please Select Status' }]}
          >
            <NormalRadio options={StatusOptions} disabled />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AddDemandModel;
