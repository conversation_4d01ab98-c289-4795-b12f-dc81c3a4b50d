import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Form, Radio, Button, message, Input, Typography } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';

import NormalInput from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';
import Select from '@/components/Select/NormalSelect';
import NormalModal from '@/components/Modal/NormalModal';
import RixEngineFont from '@/components/RixEngineFont';

import {
  StatusOptions,
  ResetPwdOptions,
  ResetPwdType,
  userTypeOptions,
  UserType,
  RoleType,
} from '@/constants';
import { SendEmailTyleOptions } from '@/constants';

import { addOneUser, editUser, sendEmail } from '@/services/api';

import { fetchData, downloadCsv } from '@/utils';

const { Paragraph } = Typography;
type AddUserProps = {
  isEdit: boolean;
  visible: boolean;
  setVisible: (params: boolean) => void;
  rowUserInfo?: TenantAPI.UserListItem;
  reloadUserList: () => void;
  tenantOptions: any[];
  isAdmin?: boolean;
};
const AddUser: React.FC<AddUserProps> = ({
  isEdit,
  visible,
  setVisible,
  rowUserInfo,
  reloadUserList,
  tenantOptions,
}) => {
  const [form] = Form.useForm();
  const [attentionForm] = Form.useForm();
  const [isResetPwd, setIsResetPwd] = useState(false);
  const [isRix, setIsRix] = useState(false);
  const [isChangeName, setIsChangeName] = useState(false);
  const [showAttention, setShowAttention] = useState(false);
  const [showEmail, setShowEmail] = useState(false);
  const [email, setEmail] = useState('');
  const [tntID, setTntID] = useState(null);
  const [role, setRole] = useState<number>(0);
  const [userOptions, setUserOptions] = useState<any[]>(userTypeOptions);

  useEffect(() => {
    if (isEdit && visible) {
      handleUserTypeChange(rowUserInfo!.type);
      handleRoleFromUserType(rowUserInfo!.type);
      const isrix = [
        UserType['Rix Data Analyst'],
        UserType['Rix Administrator'],
      ].includes(rowUserInfo!.type);
      const name = rowUserInfo?.account_name;
      setIsRix(isrix);
      form.setFieldsValue({
        ...rowUserInfo,
        reset_status: ResetPwdType.No,
        account_name: isrix
          ? name!.startsWith('Rix_')
            ? rowUserInfo?.account_name.slice(4)
            : name
          : name,
      });
    } else {
      setUserOptions(userTypeOptions);
      form.resetFields();
    }
  }, [isEdit, visible]);

  const handleRoleFromUserType = (type: number) => {
    if (type === UserType.TenantUser) {
      setRole(RoleType.SuperAdmin);
    }
    if (type === UserType['Rix Administrator']) {
      setRole(RoleType['Rix Administrator']);
    }
    if (type === UserType['Rix Data Analyst']) {
      setRole(RoleType['Rix Data Analyst']);
    }
  };

  const handleUserTypeChange = (userType: number) => {
    if (userType !== UserType.TenantUser) {
      const tmp = userTypeOptions.map((item) => {
        if (item.value === UserType.TenantUser) {
          return {
            ...item,
            disabled: true,
          };
        }
        return item;
      });

      setUserOptions(tmp);
    }
  };

  const handleValueChange = (changedValues: any) => {
    changedValues.reset_status &&
      setIsResetPwd(changedValues.reset_status === ResetPwdType.Yes);
    if (changedValues.type) {
      setIsRix(
        [UserType['Rix Administrator'], UserType['Rix Data Analyst']].includes(
          changedValues.type,
        ),
      );

      handleRoleFromUserType(changedValues.type);
    }
    if (changedValues.account_name) {
      setIsChangeName(
        (isRix
          ? `Rix_${changedValues.account_name}`
          : changedValues.account_name) !== rowUserInfo?.account_name,
      );
    }
  };

  const handleConfirm = () => {
    const formValues = form.getFieldsValue();
    const {
      reset_status,
      email,
      account_name,
      new_password,
      password,
      tnt_id,
    } = formValues;

    setTntID(tnt_id);
    setIsResetPwd(reset_status === ResetPwdType.Yes);
    setEmail(email);
    attentionForm.setFieldsValue({
      account_name: isRix ? `Rix_${account_name}` : account_name,
      password: new_password ? new_password : password,
      send_email: 1,
    });
    form.submit();
  };
  const handleAttentionConfirm = () => {
    if (attentionForm.getFieldValue('send_email') === 1) {
      setShowEmail(true);
    }
    setShowAttention(false);
  };

  const sendSuccess = () => {
    message.success('Send Success');
    attentionForm.resetFields();
    setIsResetPwd(false);
    setEmail('');
    setTntID(null);
    setIsRix(false);
  };
  const handleEmailConfirm = () => {
    const params = {
      ...attentionForm.getFieldsValue(),
      email,
      isResetPwd,
      tnt_id: tntID,
    };

    setShowEmail(false);
    // return;
    fetchData({ request: sendEmail, params: params, onSuccess: sendSuccess });
  };

  const onSuccess = () => {
    message.success('Success');
    reloadUserList();
    setVisible(false);
    setShowAttention(isResetPwd || !isEdit);
    form.resetFields();
    setIsRix(false);
  };
  const handleFinish = (values: any) => {
    const params = {
      ...values,
      account_name: isRix ? `Rix_${values.account_name}` : values.account_name,
      user_id: rowUserInfo?.user_id,
      role_id: isEdit ? (!isRix ? rowUserInfo?.role_id : role) : role,
      isChangeName: isEdit
        ? (isRix ? `Rix_${values.account_name}` : values.account_name) !==
          rowUserInfo?.account_name
        : isChangeName,
      ori_data: isEdit ? rowUserInfo : {},
    };

    isEdit
      ? fetchData({ request: editUser, params: params, onSuccess })
      : fetchData({ request: addOneUser, params: params, onSuccess });
  };

  const handleCancel = () => {
    setVisible(false);
    setShowAttention(false);
    setIsResetPwd(false);
    setIsRix(false);
    !showEmail && setTntID(null);
    form.resetFields();
    attentionForm.resetFields();
  };
  const handleEmailCancel = () => {
    setShowEmail(false);
    setTntID(null);
  };
  const handleDownload = () => {
    const fileName = `userInfo_${new Date().getTime()}`;
    const fields: any = [
      { label: 'User Name', value: 'account_name' },
      { label: 'Password', value: 'password' },
    ];
    downloadCsv(fileName, [attentionForm.getFieldsValue()] || [], { fields });
  };

  return (
    <>
      <NormalModal
        okText="Confirm"
        title={isEdit ? `Edit User` : `Add User`}
        onOk={handleConfirm}
        open={visible}
        onCancel={handleCancel}
        width={494}
        confirmLoading={false}
        maskClosable={true}
        style={{ top: 50 }}
        className={styles.addUserModal}
      >
        <Form
          form={form}
          layout="horizontal"
          labelCol={{ style: { width: 142 } }}
          wrapperCol={{ span: 24 }}
          labelAlign="left"
          onValuesChange={handleValueChange}
          onFinish={handleFinish}
          validateTrigger={['onBlur', 'onChange']}
        >
          {/* 防止表单自动填充 */}
          <Input.Password
            style={{
              height: '0px',
              width: '0px',
              overflow: 'hidden',
              padding: '0px',
              border: 'none',
              position: 'absolute',
            }}
            maxLength={11}
          />
          <Form.Item
            label="User Type"
            name="type"
            rules={[{ required: true, message: 'Please Select Tenant!' }]}
          >
            <Select
              placeholder="Please Select User_type"
              disabled={isEdit && rowUserInfo?.type === UserType.TenantUser}
            >
              {userOptions.map((item, index) => {
                return (
                  <Select.Option
                    key={index}
                    value={item.value}
                    disabled={item.disabled}
                  >
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="User Name"
            name="account_name"
            rules={[{ required: true }]}
          >
            <NormalInput
              style={{ maxWidth: 281 }}
              autoComplete="off"
              addonBefore={isRix ? 'Rix_' : ''}
            />
          </Form.Item>
          {!isEdit && (
            <>
              <Form.Item
                label="Password"
                name="password"
                rules={[
                  {
                    required: true,
                    message: 'Please Input New Password!',
                  },
                  {
                    type: 'string',
                    min: 6,
                    max: 25,
                    message: 'Please input 6 to 25 characters',
                  },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                      if (
                        !value ||
                        (reg.test(value) &&
                          getFieldValue('old_password') !== value)
                      ) {
                        return Promise.resolve();
                      }
                      if (!reg.test(value)) {
                        return Promise.reject(
                          new Error(
                            'Password must contain number and capitals',
                          ),
                        );
                      }
                      return Promise.reject(
                        new Error(
                          'New password can not be the same as current password',
                        ),
                      );
                    },
                  }),
                ]}
              >
                <NormalInput.Password
                  style={{ maxWidth: 281 }}
                  allowClear
                  autoComplete="off"
                />
              </Form.Item>
            </>
          )}
          {isEdit && (
            <>
              <Form.Item
                label="Status"
                name="status"
                rules={[{ required: true }]}
              >
                <NormalRadio>
                  {StatusOptions.map((item, index) => (
                    <Radio key={index} value={item.value}>
                      {item.label}
                    </Radio>
                  ))}
                </NormalRadio>
              </Form.Item>
              <Form.Item
                label="Reset Password"
                name="reset_status"
                rules={[{ required: true }]}
              >
                <NormalRadio>
                  {ResetPwdOptions.map((item, index) => (
                    <Radio key={index} value={item.value}>
                      {item.label}
                    </Radio>
                  ))}
                </NormalRadio>
              </Form.Item>
              {isResetPwd && (
                <>
                  <Form.Item
                    name="new_password"
                    label="New Password:"
                    validateTrigger={['onChange', 'onBlur']}
                    rules={[
                      {
                        required: true,
                        message: 'Please Input New Password!',
                      },
                      {
                        type: 'string',
                        min: 6,
                        max: 25,
                        message: 'Please input 6 to 25 characters',
                      },
                      () => ({
                        validator(_, value) {
                          const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                          if (!value || reg.test(value)) {
                            return Promise.resolve();
                          }
                          if (!reg.test(value)) {
                            return Promise.reject(
                              new Error(
                                'Password must contain number and capitals',
                              ),
                            );
                          }
                          return Promise.reject(
                            new Error(
                              'New password can not be the same as current password',
                            ),
                          );
                        },
                      }),
                    ]}
                    hasFeedback
                  >
                    <NormalInput.Password
                      style={{ maxWidth: 418 }}
                      allowClear
                    />
                  </Form.Item>
                  <Form.Item
                    name="repeat_password"
                    label="Repeat Password:"
                    validateTrigger={['onBlur']}
                    rules={[
                      {
                        required: true,
                        message: 'Please Repeat Password',
                      },
                      {
                        type: 'string',
                        min: 6,
                        max: 25,
                        message: 'Please input 6 to 25 characters',
                      },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                          if (
                            !value ||
                            (reg.test(value) &&
                              getFieldValue('new_password') === value)
                          ) {
                            return Promise.resolve();
                          }
                          if (!reg.test(value)) {
                            return Promise.reject(
                              new Error(
                                'Password must contain number and capitals',
                              ),
                            );
                          }
                          return Promise.reject(
                            new Error('Please enter the same password'),
                          );
                        },
                      }),
                    ]}
                    hasFeedback
                  >
                    <NormalInput.Password
                      style={{ maxWidth: 418 }}
                      allowClear
                    />
                  </Form.Item>
                </>
              )}
            </>
          )}
          <Form.Item
            label="Tenant"
            name="tnt_id"
            rules={[{ required: true, message: 'Please Select Tenant!' }]}
          >
            <Select
              showSearch
              placeholder="Please Select Tenant"
              disabled={isEdit}
              options={tenantOptions}
            />
          </Form.Item>
          {/* <Form.Item
            label="Role"
            name="role_id"
            rules={[{ message: 'Please Select Role!' }]}
          >
            <Select placeholder="Please Select Role">
              {RoleOptions?.map((item, index) => {
                return (
                  <Select.Option
                    key={index}
                    value={item.value}
                    disabled={item.disabled}
                  >
                    {item.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item> */}

          <Form.Item
            label="E-mail"
            name="email"
            rules={[
              { message: 'Please Input E-mail!' },
              {
                type: 'email',
                message: 'Please enter the correct E-mail!',
              },
            ]}
          >
            <NormalInput placeholder="Please Input E-mail"></NormalInput>
          </Form.Item>
        </Form>
      </NormalModal>
      <NormalModal
        okText="Confirm"
        title={`Attention`}
        onOk={handleAttentionConfirm}
        open={showAttention}
        onCancel={handleCancel}
        width={494}
        confirmLoading={false}
        maskClosable={false}
        style={{ top: 50 }}
        className={styles.addUserModal}
      >
        <div className={styles['tips']}>
          <ExclamationCircleOutlined className={styles['tips-icon']} />
          <div className={styles['tips-info']}>
            Please be sure to remember your password, our system will encrypt
            and save the password.
          </div>
        </div>
        <Form
          form={attentionForm}
          layout="horizontal"
          labelCol={{ style: { width: 142 } }}
          wrapperCol={{ span: 24 }}
          labelAlign="left"
          onValuesChange={handleValueChange}
          validateTrigger={['onBlur', 'onSubmit']}
        >
          <Form.Item
            label="User Name"
            name="account_name"
            rules={[{ required: true }]}
          >
            <div className={styles['copy-container']}>
              <span>{attentionForm.getFieldValue('account_name')}</span>
              <Paragraph
                copyable={{
                  tooltips: false,
                  text: `${attentionForm.getFieldValue('account_name')}`,
                  icon: 'Copy',
                }}
              ></Paragraph>
            </div>
          </Form.Item>
          <Form.Item
            label="Password"
            name="password"
            rules={[{ required: true }]}
            style={{ position: 'relative' }}
          >
            <div className={styles['copy-container']}>
              <span>{attentionForm.getFieldValue('password')}</span>
              <Paragraph
                copyable={{
                  tooltips: false,
                  text: `${attentionForm.getFieldValue('password')}`,
                  icon: 'Copy',
                }}
              ></Paragraph>
            </div>
          </Form.Item>
          <Form.Item
            name="send_email"
            label="Sent to email"
            rules={[
              {
                required: true,
                message: 'Please Select Relationship',
              },
            ]}
          >
            <NormalRadio>
              {SendEmailTyleOptions.map((item, index) => (
                <Radio key={index} value={item.value}>
                  {item.label}
                </Radio>
              ))}
            </NormalRadio>
          </Form.Item>
        </Form>
        <div className={styles['btn']}>
          <Button
            icon={<RixEngineFont type="rix-download" />}
            onClick={handleDownload}
          >
            Download
          </Button>
        </div>
      </NormalModal>

      <NormalModal
        okText="Confirm"
        title={`Attention`}
        onOk={handleEmailConfirm}
        open={showEmail}
        onCancel={handleEmailCancel}
        width={494}
        confirmLoading={false}
        maskClosable={true}
        style={{ top: 50 }}
        className={styles.addUserModal}
      >
        <div className={styles['tips']}>
          <ExclamationCircleOutlined className={styles['tips-icon']} />
          <div className={styles['tips-info']}>
            {`Attention Please confirm that your account password will be sent to the email: ${email}`}
          </div>
        </div>
      </NormalModal>
    </>
  );
};

export default AddUser;
