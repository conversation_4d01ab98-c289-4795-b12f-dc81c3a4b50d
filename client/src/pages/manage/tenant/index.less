.container {
  background-color: #fff;
  border-radius: 6px;
  margin-right: 16px;
  padding: 28px 32px;
  height: 100%;
  .top {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 32px;
    button {
      margin-left: 12px;
    }
    h3 {
      margin-bottom: 0px;
      color: #252829;
      font-weight: 700;
      font-size: 16px;
      padding-left: 14px;
    }
    svg {
      color: #8d9799;
    }
    :global {
      .ant-radio-button-wrapper {
        .ant-radio-button {
          border-radius: 6px;
        }
        background-color: #fff;
      }
      .ant-radio-group {
        background-color: #fff;
      }
      .ant-radio-button-checked {
        background-color: #f3f5f5;
      }
    }
    .top-right {
      // margin-left: auto;
      // display: flex;
      // align-items: center;
      // .ant-btn {
      //   margin-left: 12px;
      // }
    }
  }
  .copy-container {
    background: #edeff0;
    border-radius: 6px;
    height: 32px;
    max-width: 418px;
    display: flex;
    align-items: center;
    > span {
      color: #8d9799;
      padding-left: 12px;
      flex: 1;
      cursor: not-allowed;
    }
    :global {
      .ant-typography {
        margin-bottom: 0px;
        height: 32px;
        line-height: 32px;
        background: #1568d4;
        border-radius: 0px 6px 6px 0px;
        width: 51px;
        display: flex;
        .ant-typography-copy {
          display: flex;
          align-items: center;
          justify-content: center;
          color: #fff;
          text-align: center;
          // margin-left: 8px;
          margin: auto;
        }
      }
    }
  }
  :global {
    .ant-form-item-label > label {
      color: #5e6466;
    }
    .ant-input[disabled] {
      background: #edeff0;
    }
    .ant-typography-copy-success,
    .ant-typography-copy-success:hover,
    .ant-typography-copy-success:focus {
      svg {
        color: #fff;
      }
    }
  }
  .delete-btn {
    svg {
      vertical-align: bottom;
    }
  }
}
:global {
  .ant-menu-item.ant-menu-item-only-child {
    .ant-menu-title-content .ant-pro-menu-item {
      padding-left: 8px !important;
    }
  }
}
