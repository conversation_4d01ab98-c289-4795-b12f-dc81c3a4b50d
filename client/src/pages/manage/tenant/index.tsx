import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { useModel } from '@umijs/max';
import { Form, message } from 'antd';
import type { TopBarSearchItem } from '@/components/TopBar';
import PageContainer from '@/components/RightPageContainer';
import AddTenant from './components/AddTenant';
import NormalModal from '@/components/Modal/NormalModal';
import RixEngineFont from '@/components/RixEngineFont';
import OperateRender from '@/components/OperateRender';
import FrontTable, { ColumnType } from '@/components/Table/FrontTable';
import {
  TenantColumnOptions,
  TanantSearchOption,
} from '@/constants/manage/tenant';
import type { OperateRenderItem } from '@/components/OperateRender';
import { fetchData } from '@/utils';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { deleteTenant } from '@/services/api';
import { TenantBreadOptions } from '@/constants/manage/tenant';

const Page: React.FC = () => {
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [currentRow, setCurrentRow] = useState<TenantAPI.TenantListItem>();
  const [dataSource, setDataSource] = useState<TenantAPI.TenantListItem[]>([]);

  const [searchOptions, setSearchOptions] = useState<any[]>(TanantSearchOption);
  const { tenantList, reload, loading } = useModel('useTenantList');
  useEffect(() => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );

    if (Array.isArray(tenantList)) {
      const tmpTenantOptions = tenantList.map((item) => ({
        label: `${item.tnt_name}(${item.tnt_id})`,
        value: item.tnt_id,
      }));

      const sIndex = options.findIndex((item) => item.key === 'tnt_id');
      if (sIndex !== -1) {
        options[sIndex].options = tmpTenantOptions;
      }
    }
    setSearchOptions(options);
  }, [tenantList]);
  const attentionModel = (params: any) => {
    NormalModal.confirm({
      title: 'Tips',
      icon: <ExclamationCircleOutlined />,
      content: (
        <>
          <div>
            Are you sure? This user will be permanently deleted and no longer
            accessible through API.
          </div>
          <div>Do you want to delete this tenant?</div>
        </>
      ),
      okText: 'Delete',
      cancelText: 'Cancel',
      onOk: () => {
        const deleteSuccess = () => {
          message.success('Delete success');
          reload();
        };
        fetchData({
          request: deleteTenant,
          params: params,
          onSuccess: deleteSuccess,
        });
      },
      okButtonProps: {
        danger: true,
      },
    });
  };
  const handleEditUser = (params: TenantAPI.TenantListItem) => {
    setVisible(true);
    setIsEdit(true);
    setCurrentRow(params);
  };
  const handleUserDelete = (params: TenantAPI.TenantListItem) => {
    attentionModel(params);
  };
  const OperateOptions: OperateRenderItem[] = [
    {
      label: 'Edit',
      onClick: handleEditUser,
      icon: <RixEngineFont type="edit" />,
    },
    {
      label: 'Delete',
      onClick: handleUserDelete,
      icon: <RixEngineFont type="rix-trash" className={styles['delete-btn']} />,
      isDelete: true,
      hide: process.env.UMI_ENV === 'prod',
    },
  ];
  const tmpColumns: ColumnType<TenantAPI.TenantListItem>[] = [
    {
      title: 'Operation',
      dataIndex: 'operate',
      width: 170,
      fixed: 'right',
      access: 'TntOperateCol',
      render: (txt, params) => (
        <OperateRender btnOptions={OperateOptions} params={params} />
      ),
    },
  ];

  const column = [...TenantColumnOptions, ...tmpColumns];
  useEffect(() => {
    if (initialState?.currentUser) {
      form.setFieldsValue(initialState.currentUser);
    }
  }, [initialState?.currentUser]);

  useEffect(() => {
    reload();
  }, []);
  useEffect(() => {
    setDataSource(tenantList);
  }, [tenantList]);

  const handleTenant = () => {
    setVisible(true);
    setIsEdit(false);
  };
  return (
    <PageContainer options={TenantBreadOptions}>
      <FrontTable<TenantAPI.TenantListItem>
        pageTitle="Tenant Management"
        searchOptions={searchOptions}
        loading={loading}
        columns={column}
        dataSource={dataSource}
        rowKey={'tnt_id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add Tenant',
            type: 'primary',
            size: 'small',
            onClick: handleTenant,
            icon: <RixEngineFont type="add" />,
            access: 'AddTenantCode',
          },
        ]}
        labelWidth={80}
        scroll={{ x: 1000, y: 'auto' }}
      />
      <AddTenant
        visible={visible}
        isEdit={isEdit}
        setVisible={setVisible}
        rowUserInfo={currentRow}
        reloadUserList={reload}
      />
    </PageContainer>
  );
};

export default Page;
