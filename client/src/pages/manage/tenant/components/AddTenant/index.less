.addUserModal {
  :global {
    .ant-modal-content {
      border-radius: 6px;
    }
    .ant-modal-header {
      padding: 24px 32px;
    }
    .ant-modal-body {
      padding: 32px !important;
      padding-top: 0px !important;
    }
    .ant-modal-confirm {
      .ant-modal-body {
        padding-top: 20px;
      }
    }
  }
}
.tips {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 400;
  color: #252829;
  font-size: 16px;
  margin-bottom: 20px;
  .tips-info {
    max-width: 392px;
    line-height: 1.5;
  }
  .tips-icon {
    font-size: 22px;
    color: #ff9012;
    text-align: center;
  }
}
.btn {
  display: flex !important;
  justify-content: flex-end;
}

.copy-container {
  background: #edeff0;
  border-radius: 6px;
  height: 32px;
  max-width: 418px;
  display: flex;
  align-items: center;
  > span {
    color: #8d9799;
    padding-left: 12px;
    flex: 1;
    cursor: not-allowed;
  }
  :global {
    .ant-typography {
      margin-bottom: 0px;
      height: 32px;
      line-height: 32px;
      background: #1568d4;
      border-radius: 0px 6px 6px 0px;
      width: 51px;
      display: flex;
      .ant-typography-copy {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        text-align: center;
        // margin-left: 8px;
        margin: auto;
      }
    }
  }
  .ant-typography-copy-success,
  .ant-typography-copy-success:hover,
  .ant-typography-copy-success:focus {
    svg {
      color: #fff;
    }
  }
}
