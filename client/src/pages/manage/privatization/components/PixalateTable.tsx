import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import {
  PrivateBreadOptions,
  type PrivatizationBaseTableProps,
} from '@/constants/manage/privatization';
import {
  genPixalateColumnOptions,
  genPixalateSearchOption,
  RixCommonStatusMap,
} from '@/constants/manage/privatization-pixalate';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { useModel } from '@umijs/max';
import { useEffect, useMemo, useRef } from 'react';
import PixalateDetailModal, {
  PixalateDetailModalToolRef,
} from './EditPrivatization/PixalateDetailModal';
import TenantPixalateDrawer, {
  TenantPixalateDrawerToolRef,
} from './EditPrivatization/TenantPixalateDrawer';

const PixalateTable = ({
  defaultTab,
  tabOptions,
  onTabChange,
  tenantOptions,
}: PrivatizationBaseTableProps) => {
  const drawerToolRef = useRef<TenantPixalateDrawerToolRef>(null);
  const modalToolRef = useRef<PixalateDetailModalToolRef>(null);
  // 生成搜索选项
  const searchOptions = useMemo(() => {
    return genPixalateSearchOption(tenantOptions ?? []);
  }, [tenantOptions]);

  const {
    data,
    reload: reloadTenantPixalateList,
    loading,
  } = useModel('useTenantPixalate');

  useEffect(() => {
    if (!data) {
      reloadTenantPixalateList();
    }
  }, [data]);

  const handleAddPixalate = () => {
    drawerToolRef.current?.openDrawer();
  };

  const handleEditPixalate = (params: PrivateAPI.PixalateItem) => {
    drawerToolRef.current?.openDrawer(params);
  };

  const handleViewDetail = (row: PrivateAPI.PixalateItem) => {
    modalToolRef.current?.openModal(row);
  };

  const columns = useMemo(() => {
    const operateColumn = genOperateColumn<PrivateAPI.PixalateItem>({
      access: 'PixalateOperate',
      btnOptions: [
        {
          label: 'Edit',
          onClick: handleEditPixalate,
          icon: <RixEngineFont type="edit" />,
        },
      ],
    });
    return [...genPixalateColumnOptions(handleViewDetail), operateColumn];
  }, [handleEditPixalate, handleViewDetail]);

  return (
    <PageContainer options={PrivateBreadOptions}>
      <FrontTable<PrivateAPI.PixalateItem>
        labelWidth={100}
        searchOptions={searchOptions}
        initialValues={{
          use_rix_common: [RixCommonStatusMap.False],
        }}
        tabOptions={tabOptions}
        defaultTab={defaultTab}
        onTabChange={onTabChange as any}
        loading={loading}
        columns={columns}
        dataSource={data}
        rowKey="id"
        request={reloadTenantPixalateList}
        btnOptions={[
          {
            label: 'Add Pixalate',
            type: 'primary',
            size: 'small',
            onClick: handleAddPixalate,
            access: 'PixalateOperate',
            icon: <RixEngineFont type="add" />,
          },
        ]}
        scroll={{ x: 1000, y: 'auto' }}
      />
      <TenantPixalateDrawer
        tenantOptions={tenantOptions}
        drawerToolRef={drawerToolRef}
        request={reloadTenantPixalateList}
      />
      <PixalateDetailModal modalToolRef={modalToolRef} />
    </PageContainer>
  );
};

export default PixalateTable;
