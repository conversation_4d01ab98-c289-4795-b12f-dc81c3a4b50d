import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable from '@/components/Table/FrontTable';
import {
  genPrivateSearchOption,
  PrivateBreadOptions,
  PrivateColumnOptions,
  PrivatizationBaseTableProps,
} from '@/constants/manage/privatization';
import { SearchOption } from '@/types/common';
import { genOperateColumn } from '@/utils/genOperateColumn';
import { useModel } from '@umijs/max';
import { useEffect, useMemo, useState } from 'react';
import EditPrivatization from './EditPrivatization';

const PrivatizationTable = ({
  defaultTab,
  tabOptions,
  onTabChange,
  tenantOptions,
}: PrivatizationBaseTableProps) => {
  const {
    brandList,
    brandOptions,
    reload: reloadBrandList,
  } = useModel('useBrandList');
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  const searchOptions = useMemo(() => {
    // brandOptions 去重
    const uniqueBrandOptionsMap = new Map(
      (brandOptions ?? []).map((option: { value: string; label: string }) => [
        option.value,
        option,
      ]),
    );
    const uniqueBrandOptions = Array.from(
      uniqueBrandOptionsMap.values(),
    ) as SearchOption<string>[];

    return genPrivateSearchOption(tenantOptions, uniqueBrandOptions ?? []);
  }, [tenantOptions, brandOptions]);

  const [row, setRow] = useState<PrivateAPI.BrandList | undefined>(undefined);
  useEffect(() => {
    // Load on mount
    reloadBrandList();
  }, [reloadBrandList]);

  const handleAddPrivatization = () => {
    setIsEdit(false);
    setRow(undefined);
    setVisible(true);
  };
  const handleClose = () => {
    setVisible(false);
    setIsEdit(false);
    reloadBrandList();
  };

  const handleEditPrivatization = (params: PrivateAPI.BrandList) => {
    setIsEdit(true);
    setVisible(true);
    setRow(params);
  };

  const columns = useMemo(() => {
    const operateColumn = genOperateColumn<PrivateAPI.BrandList>({
      access: 'TntOperate',
      btnOptions: [
        {
          label: 'Edit',
          onClick: handleEditPrivatization,
          icon: <RixEngineFont type="edit" />,
        },
      ],
    });

    return [...PrivateColumnOptions, operateColumn];
  }, []);

  return (
    <PageContainer options={PrivateBreadOptions}>
      <FrontTable<PrivateAPI.BrandList>
        searchOptions={searchOptions}
        tabOptions={tabOptions}
        defaultTab={defaultTab}
        onTabChange={onTabChange as any}
        loading={false}
        columns={columns}
        dataSource={brandList}
        rowKey={'id'}
        request={reloadBrandList}
        btnOptions={[
          {
            label: 'Add Privatization',
            type: 'primary',
            size: 'small',
            onClick: handleAddPrivatization,
            access: 'TntOperate',
            icon: <RixEngineFont type="add" />,
          },
        ]}
        labelWidth={80}
        scroll={{ x: 1000, y: 'auto' }}
      />
      <EditPrivatization
        isEdit={isEdit}
        visible={visible}
        onClose={handleClose}
        tenantOptions={tenantOptions}
        rowInfo={row}
      />
    </PageContainer>
  );
};

export default PrivatizationTable;
