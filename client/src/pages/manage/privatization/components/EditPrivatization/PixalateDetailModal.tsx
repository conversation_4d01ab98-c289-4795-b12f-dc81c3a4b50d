import { NormalDescriptions } from '@/components/Descriptions';
import NormalModal from '@/components/Modal/NormalModal';
import { RixCommonStatus } from '@/constants/manage/privatization-pixalate';
import { useImperativeHandle, useState } from 'react';

// 组件引用类型定义
export type PixalateDetailModalToolRef = {
  /**
   * 打开模态框并设置数据
   * @param row Pixalate 项数据
   */
  openModal: (row: PrivateAPI.PixalateItem) => void;
  /**
   * 关闭模态框并清空数据
   */
  closeModal: () => void;
};

// 组件属性类型定义
export type PixalateDetailModalProps = {
  /** 模态框工具引用 */
  modalToolRef: React.RefObject<PixalateDetailModalToolRef>;
};

// 扩展的 Pixalate 配置类型
type ExtendedPixalateConfig = PrivateAPI.PixalateConfig & {
  /** 租户名称 */
  tnt_name: string;
  /** 租户 ID */
  tnt_id: number;
};

// Pixalate 配置项描述配置
const PIXALATE_CONFIG_DESCRIPTIONS = [
  {
    key: 'tnt_name',
    label: 'Tenant Name',
    render: (config: ExtendedPixalateConfig | null) =>
      config ? `${config.tnt_name}(${config.tnt_id})` : '-',
  },
  {
    key: 'use_rix_common',
    label: 'Use Default',
    render: (config: ExtendedPixalateConfig | null) =>
      config?.use_rix_common ? RixCommonStatus[config.use_rix_common] : '-',
  },
  {
    key: 'report_api',
    label: 'Report API',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.report_api || '-',
  },
  {
    key: 'report_api_key',
    label: 'Report API Key',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.report_api_key || '-',
  },
  {
    key: 'display_web_tag',
    label: 'Display Web Tag',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.display_web_tag || '-',
  },
  {
    key: 'display_app_tag',
    label: 'Display App Tag',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.display_app_tag || '-',
  },
  {
    key: 'display_web_js',
    label: 'Display Web JS',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.display_web_js || '-',
  },
  {
    key: 'display_app_js',
    label: 'Display App JS',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.display_app_js || '-',
  },
  {
    key: 'native_web_tag',
    label: 'Native Web Tag',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.native_web_tag || '-',
  },
  {
    key: 'native_app_tag',
    label: 'Native App Tag',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.native_app_tag || '-',
  },
  {
    key: 'video_web_tag',
    label: 'Video Web Tag',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.video_web_tag || '-',
  },
  {
    key: 'video_app_tag',
    label: 'Video App Tag',
    span: 2,
    render: (config: ExtendedPixalateConfig | null) =>
      config?.video_app_tag || '-',
  },
];

/**
 * Pixalate 配置详情模态框组件
 * 用于展示 Pixalate 配置的详细信息
 */
const PixalateDetailModal: React.FC<PixalateDetailModalProps> = ({
  modalToolRef,
}) => {
  // 控制模态框显示状态
  const [open, setOpen] = useState(false);
  // 存储 Pixalate 配置数据
  const [pixalateConfig, setPixalateConfig] =
    useState<ExtendedPixalateConfig | null>(null);

  // 暴露给父组件的方法
  useImperativeHandle(
    modalToolRef,
    () => ({
      /**
       * 打开模态框并设置数据
       * @param row Pixalate 项数据
       */
      openModal: (row: PrivateAPI.PixalateItem) => {
        setOpen(true);
        setPixalateConfig({
          ...row.pixalate_config,
          tnt_name: row.tnt_name,
          tnt_id: row.tnt_id,
        });
      },
      /**
       * 关闭模态框并清空数据
       */
      closeModal: () => {
        setOpen(false);
        setPixalateConfig(null);
      },
    }),
    [],
  );

  // 生成描述项配置
  const descriptionsItems = PIXALATE_CONFIG_DESCRIPTIONS.map(
    ({ key, label, render, span }) => ({
      key,
      label,
      children: render(pixalateConfig),
      span,
    }),
  );

  return (
    <NormalModal
      title="Pixalate Config"
      open={open}
      onCancel={() => setOpen(false)}
      width="min(800px, 90vw)"
      footer={null}
    >
      <NormalDescriptions
        column={2}
        labelStyle={{ width: '136px' }}
        items={descriptionsItems}
      />
    </NormalModal>
  );
};

export default PixalateDetailModal;
