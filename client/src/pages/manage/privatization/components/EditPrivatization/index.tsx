/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-01-30 11:53:42
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-08 15:09:01
 * @Description:
 */

import NormalDrawer from '@/components/Drawer/NormalDrawer';
import NormalInput from '@/components/Input/NormalInput';
import NormalSelect from '@/components/Select/NormalSelect';
import type { TenantOption } from '@/models/useTenantList';
import {
  addTenantPrivatization,
  editTenantPrivatization,
  upload,
} from '@/services/privatization';
import { fetchData, validateDomain } from '@/utils';
import { InboxOutlined, UploadOutlined } from '@ant-design/icons';
import { Button, Form, message, Upload, UploadFile, UploadProps } from 'antd';
import { RcFile } from 'antd/lib/upload';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

type Props = {
  isEdit: boolean;
  visible: boolean;
  onClose: () => void;
  rowInfo?: PrivateAPI.BrandList;
  tenantOptions: TenantOption[];
};
const DefalutValue = {
  tnt_id: undefined,
  logo: undefined,
  favicon: undefined,
  brand_name: 'RixEngine',
  sj_domain: 'rixengine.com',
};
const EditPrivatization: React.FC<Props> = ({
  isEdit,
  visible,
  onClose,
  rowInfo,
  tenantOptions = [],
}) => {
  const [form] = Form.useForm();

  const [logoUploading, setLogoUploading] = useState(false);
  const [faviconUploading, setFaviconUploading] = useState(false);
  const [logoGsUrl, setLogoGsUrl] = useState<string>('');
  const [faviconGsUrl, setFaviconGsUrl] = useState<string>('');

  useEffect(() => {
    if (isEdit && visible) {
      const { brand_logo_path, brand_favicon_path } = rowInfo || {};
      const logoName = brand_logo_path?.split('/')?.pop() || '';
      const faviconName = brand_favicon_path?.split('/')?.pop() || '';
      setLogoGsUrl(brand_logo_path || '');
      setFaviconGsUrl(brand_favicon_path || '');
      let logoFileList: UploadFile[] = [];
      let faviconFileList: UploadFile[] = [];
      if (brand_logo_path) {
        logoFileList = [
          {
            uid: `${logoName.split('.')[0]}-${Date.now()}`,
            name: logoName,
            status: 'done',
            url: `/api/privatization/download/?path=${brand_logo_path}`,
          },
        ];
      }
      if (brand_favicon_path) {
        faviconFileList = [
          {
            uid: `${faviconName.split('.')[0]}-${Date.now()}`,
            name: faviconName,
            status: 'done',
            url: `/api/privatization/download/?path=${brand_favicon_path}`,
          },
        ];
      }
      form.setFieldsValue({
        ...rowInfo,
        logo: logoFileList,
        favicon: faviconFileList,
        sj_domain: rowInfo?.sj_domain || 'rixengine.com',
      });
    } else {
      // form.resetFields();
      form.setFieldsValue(DefalutValue);
      setLogoGsUrl('');
      setFaviconGsUrl('');
    }
  }, [isEdit, visible]);

  const onPreview = (file: UploadFile<any>) => {
    if (file?.originFileObj) {
      const blob = new Blob([file.originFileObj], { type: 'image/png' });
      const url = URL.createObjectURL(blob);
      window.open(url, '_blank');
      URL.revokeObjectURL(url);
    } else if (file.url) {
      window.open(file.url, '_blank');
    }
  };
  const onRemove = (file: UploadFile<any>, type: 'logo' | 'favicon') => {
    if (type === 'logo') {
      setLogoGsUrl('');
    } else {
      setFaviconGsUrl('');
    }
  };
  const beforeUpload = (file: RcFile, type: 'logo' | 'favicon') => {
    if (['image/png', 'image/jpg', 'image/jpeg'].indexOf(file.type) === -1) {
      message.error('支持png、jpg、jpeg格式的图片');
      return false;
    }
    type === 'logo' ? setLogoGsUrl('') : setFaviconGsUrl('');
    return false;
  };
  const uploadLogoProps: UploadProps = {
    name: 'file',
    maxCount: 1,
    listType: 'picture',
    onRemove: (file) => onRemove(file, 'logo'),
    onPreview,
    beforeUpload: (file) => beforeUpload(file, 'logo'),
  };
  const uploadFaviconProps: UploadProps = {
    name: 'file',
    listType: 'picture',
    maxCount: 1,
    onRemove: (file) => onRemove(file, 'favicon'),
    onPreview,
    beforeUpload: (file) => beforeUpload(file, 'favicon'),
  };

  const handleFinish = (values: any) => {
    const { brand_name, tnt_id, sj_domain } = values;
    const params = {
      sj_domain,
      brand_name,
      tnt_id,
      brand_logo_path: logoGsUrl,
      brand_favicon_path: faviconGsUrl,
      ori_data: isEdit ? rowInfo : {},
    };
    fetchData({
      request: isEdit ? editTenantPrivatization : addTenantPrivatization,
      params,
      onSuccess: () => {
        message.success('Success');
        onClose();
      },
    });
  };
  const handleConfirm = () => {
    form.submit();
  };
  const handleCancel = () => {
    onClose();
  };
  const normFile = (e: any) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  };

  const uploadSuccess = (res: any, type: 'logo' | 'favicon') => {
    const { path } = res;
    if (!path) message.error('上传失败');
    message.success('上传成功');
    if (type === 'logo') {
      setLogoUploading(false);
      setLogoGsUrl(path);
    } else {
      setFaviconUploading(false);
      setFaviconGsUrl(path);
    }
  };
  const handleUpload = (type: 'logo' | 'favicon') => {
    const formData = new FormData();
    const file = form.getFieldValue(type)?.[0]?.originFileObj;
    if (!file) {
      message.error('请先选择文件');
      return;
    }
    type === 'logo' ? setLogoUploading(true) : setFaviconUploading(true);
    formData.append('file', file as RcFile);
    formData.append('type', type);
    fetchData({
      request: upload,
      params: formData,
      onSuccess: (res) => uploadSuccess(res, type),
    });
  };
  const validUpload = (rule: any, value: any) => {
    const { logo, favicon } = form.getFieldsValue();
    console.log(logo, favicon);
    if (!value) {
      return Promise.resolve();
    }
    if (logo?.length && !logoGsUrl) {
      return Promise.reject('请上传logo');
    } else if (favicon?.length && !faviconGsUrl) {
      return Promise.reject('请上传favicon');
    }
    return Promise.resolve();
  };
  return (
    <NormalDrawer
      blackName={
        isEdit ? `Edit Tenant Privatization` : `Add Tenant Privatization`
      }
      loading={false}
      onConfirm={handleConfirm}
      open={visible}
      onClose={handleCancel}
      maskClosable={false}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFinish}
        validateTrigger={['onBlur', 'onChange']}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        // initialValues={DefalutValue}
      >
        <Form.Item
          label="Tenant Name"
          name="tnt_id"
          rules={[{ required: true, message: 'Please Input Tenant Name!' }]}
        >
          <NormalSelect
            placeholder="Please Select Tenant Name"
            options={tenantOptions}
            disabled={isEdit}
            showSearch
          />
        </Form.Item>
        <Form.Item
          label="Brand Name"
          name="brand_name"
          rules={[{ required: true, message: 'Please Input Brand!' }]}
        >
          <NormalInput placeholder="Please Input Company"></NormalInput>
        </Form.Item>
        {/* 用于logo上传   */}
        <Form.Item
          label={
            <div className={styles['logo-label-container']}>
              <span>Logo</span>
              <Button
                type="primary"
                size="small"
                shape="round"
                onClick={() => handleUpload('logo')}
                disabled={!!logoGsUrl}
                loading={logoUploading}
                icon={<UploadOutlined />}
              ></Button>
            </div>
          }
          name="logo"
          valuePropName="fileList"
          getValueFromEvent={normFile}
          rules={[{ validator: validUpload }]}
        >
          <Upload.Dragger {...uploadLogoProps}>
            <>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                仅支持png、jpg、jpeg格式的图片，且文件大小不超过10M，建议尺寸为3270*600px
              </p>
            </>
          </Upload.Dragger>
        </Form.Item>

        {/* 用于favicon上传   */}
        <Form.Item
          label={
            <div className={styles['logo-label-container']}>
              <span>Favicon</span>
              <Button
                type="primary"
                size="small"
                shape="round"
                onClick={() => handleUpload('favicon')}
                disabled={!!faviconGsUrl}
                loading={faviconUploading}
                icon={<UploadOutlined />}
              ></Button>
            </div>
          }
          name="favicon"
          valuePropName="fileList"
          getValueFromEvent={normFile}
          rules={[{ validator: validUpload }]}
        >
          <Upload.Dragger {...uploadFaviconProps}>
            <>
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                仅支持png、jpg、jpeg格式的图片，且文件大小不超过10M，建议尺寸为1:1
              </p>
            </>
          </Upload.Dragger>
        </Form.Item>

        <Form.Item
          label=" Sellers.json Domain"
          name="sj_domain"
          rules={[
            {
              validator: (rule, value) => validateDomain(value),
            },
            { required: true, message: `Please Input  Sellers.json Domain!` },
          ]}
        >
          <NormalInput
            placeholder={`Please Input  Sellers.json Domain!`}
          ></NormalInput>
        </Form.Item>
      </Form>
    </NormalDrawer>
  );
};

export default EditPrivatization;
