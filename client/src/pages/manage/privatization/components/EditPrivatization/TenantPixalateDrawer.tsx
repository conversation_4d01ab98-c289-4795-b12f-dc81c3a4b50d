import ProFormDrawer, {
  ProFormDrawerToolRef,
} from '@/components/Drawer/ProFormDrawer';
import NormalInput from '@/components/Input/NormalInput';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import {
  RixCommonStatusMap,
  RixCommonStatusOptions,
} from '@/constants/manage/privatization-pixalate';
import { TenantOption } from '@/models/useTenantList';
import {
  addTenantPixalate,
  editTenantPixalate,
} from '@/services/privatization';
import { Form, FormInstance } from 'antd';
import React, { useImperativeHandle, useRef } from 'react';

export type FormValues = Partial<PrivateAPI.AddPixalateItem>;

export type TenantPixalateDrawerToolRef = {
  openDrawer: (row?: PrivateAPI.PixalateItem) => void;
  closeDrawer: () => void;
  form: FormInstance<FormValues> | undefined;
};

type Props = {
  tenantOptions: TenantOption[];
  drawerToolRef: React.RefObject<TenantPixalateDrawerToolRef>;
  request: () => void;
};

const InitialValues: Partial<PrivateAPI.AddPixalateItem> = {
  use_rix_common: RixCommonStatusMap.False,
};

const TenantPixalateDrawer: React.FC<Props> = ({
  tenantOptions,
  drawerToolRef,
  request,
}) => {
  const proFormDrawerRef = useRef<ProFormDrawerToolRef<FormValues>>(null);

  useImperativeHandle(drawerToolRef, () => ({
    openDrawer: (row?: PrivateAPI.PixalateItem) => {
      // 格式需要转换
      // 将 pixalate_config 打平
      if (!row) {
        proFormDrawerRef.current?.openDrawer();
        return;
      }

      const { pixalate_config, ...rest } = row;
      const formValues: FormValues = {
        ...rest,
        ...pixalate_config,
      };
      proFormDrawerRef.current?.openDrawer(formValues);
    },
    closeDrawer: () => {
      proFormDrawerRef.current?.closeDrawer();
    },
    form: proFormDrawerRef.current?.form,
  }));

  const handleSuccess = (res: any) => {
    if (res.code !== 0) {
      return;
    }

    proFormDrawerRef.current?.closeDrawer();
    request?.();
  };

  return (
    <ProFormDrawer<FormValues>
      blackName={(isEdit) =>
        isEdit ? `Edit Tenant Pixalate` : `Create Tenant Pixalate`
      }
      drawerToolRef={proFormDrawerRef}
      initialValues={InitialValues}
      request={(isEdit) => (isEdit ? editTenantPixalate : addTenantPixalate)}
      onSuccess={handleSuccess}
    >
      {() => {
        return (
          <>
            <Form.Item name="id" hidden>
              <NormalInput />
            </Form.Item>

            <Form.Item
              name="tnt_id"
              label="Tenant Name"
              rules={[{ required: true, message: 'Please select tenant' }]}
            >
              <NormalSelect options={tenantOptions} showSearch />
            </Form.Item>

            <Form.Item
              name="use_rix_common"
              label="Use Default"
              rules={[{ required: true, message: 'Please select use default' }]}
            >
              <NormalRadio options={RixCommonStatusOptions} />
            </Form.Item>

            <Form.Item noStyle dependencies={['use_rix_common']}>
              {({ getFieldValue }) => {
                const useRixCommon = getFieldValue('use_rix_common');

                if (useRixCommon === RixCommonStatusMap.True) {
                  return null;
                }
                return (
                  <>
                    <Form.Item
                      name="report_api"
                      label="Report API"
                      rules={[
                        {
                          type: 'url',
                          message: 'Please input valid url',
                        },
                      ]}
                    >
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                    <Form.Item name="report_api_key" label="Report API Key">
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                    <Form.Item name="display_web_tag" label="Display Web Tag">
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                    <Form.Item name="display_app_tag" label="Display App Tag">
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                    <Form.Item name="display_web_js" label="Display Web JS">
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                    <Form.Item name="display_app_js" label="Display App JS">
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                    <Form.Item name="native_web_tag" label="Native Web Tag">
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                    <Form.Item name="native_app_tag" label="Native App Tag">
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                    <Form.Item name="video_web_tag" label="Video Web Tag">
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                    <Form.Item name="video_app_tag" label="Video App Tag">
                      <NormalInput.TextArea autoSize />
                    </Form.Item>
                  </>
                );
              }}
            </Form.Item>
          </>
        );
      }}
    </ProFormDrawer>
  );
};

export default TenantPixalateDrawer;
