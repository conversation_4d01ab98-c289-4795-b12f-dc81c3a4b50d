import {
  PrivatizationTab,
  PrivatizationTabOptions,
  PrivatizationTabType,
} from '@/constants/manage/privatization';
import { useCurrentTabState } from '@/hooks/useCurrentTabState';
import { useModel } from '@umijs/max';
import { useEffect } from 'react';
import PixalateTable from './components/PixalateTable';
import PrivatizationTable from './components/PrivatizationTable';

/**
 * 1. 针对一个路由下的 tab 切换场景的 解决方案
 * 2. 不同 tab 的组件实现
 * 3. 共享的组件数据
 */
const Page = () => {
  // 使用自定义hook管理Tab状态
  const [currentTab, setCurrentTab] = useCurrentTabState<PrivatizationTabType>(
    PrivatizationTab.privatization,
    PrivatizationTabOptions,
  );

  const { tenantList, options: tenantOptions, reload: reloadTenantList } = useModel('useTenantList');

  useEffect(() => {
    if (!tenantList) {
      reloadTenantList();
    }
  }, []);

  const RenderTabComp =
    currentTab === PrivatizationTab.pixalate
      ? PixalateTable
      : PrivatizationTable;

  return (
    <RenderTabComp
      defaultTab={currentTab}
      tabOptions={PrivatizationTabOptions ?? []}
      onTabChange={setCurrentTab}
      tenantOptions={tenantOptions}
    />
  );
};

export default Page;
