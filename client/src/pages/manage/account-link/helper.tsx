import { TopBarSearchItem } from '@/components/TopBar';
import { StatusOptions } from '@/constants';

interface SearchOptionParams {
  userNameOptions: {
    label: string;
    value: string;
  }[];
}

export function getSearchOptions(data: SearchOptionParams): TopBarSearchItem[] {
  return [
    {
      name: 'User Name',
      type: 'select',
      key: 'account_name',
      mode: 'multiple',
      value: [],
      options: data.userNameOptions,
    },
    {
      name: 'Status',
      type: 'select',
      key: 'status',
      value: [],
      mode: 'multiple',
      options: StatusOptions,
    },
  ];
}
