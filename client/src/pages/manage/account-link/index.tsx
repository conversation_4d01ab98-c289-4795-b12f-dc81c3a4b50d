import OperateRender, { OperateRenderItem } from '@/components/OperateRender';
import PageContainer from '@/components/RightPageContainer';
import RixEngineFont from '@/components/RixEngineFont';
import FrontTable, { ColumnType } from '@/components/Table/FrontTable';
import {
  AccountLinkBreadOptions,
  AccountLinkColumnOptions,
} from '@/constants/manage/account-link';
import { PlusOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import React, { useEffect, useMemo, useState } from 'react';
import AccountLinkDrawer from './account-link-drawer';
import { getSearchOptions } from './helper';

const Page: React.FC = () => {
  const {
    accountLinkList = [],
    reload,
    loading,
  } = useModel('useAccountLinkList');

  const { dataSource, searchOptions, existSpecialUser } = useMemo(() => {
    const userNameOptions = accountLinkList.map(
      ({ account_name, user_id }: UserAPI.AccountLinkColumn) => ({
        label: `${account_name} (${user_id})`,
        value: account_name,
      }),
    );

    return {
      dataSource: accountLinkList,
      searchOptions: getSearchOptions({ userNameOptions }),
      existSpecialUser: accountLinkList.map(
        ({ user_id }: UserAPI.AccountLinkColumn) => user_id,
      ),
    };
  }, [accountLinkList]);

  const [openDrawer, setOpenDrawer] = useState(false);
  const [currentRow, setCurrentRow] =
    useState<UserAPI.AccountLinkColumn | null>(null);

  const handleCloseDrawer = () => {
    setOpenDrawer(false);
  };

  const handleAddAccountLink = () => {
    setCurrentRow(null);
    setOpenDrawer(true);
  };

  const handleEditAccountLink = (params: UserAPI.AccountLinkColumn) => {
    setCurrentRow(params);
    setOpenDrawer(true);
  };

  const columns = useMemo(() => {
    const OperateOptions: OperateRenderItem[] = [
      {
        label: 'Edit',
        onClick: handleEditAccountLink,
        icon: <RixEngineFont type="edit" />,
      },
    ];
    const tmpColumns: ColumnType<UserAPI.AccountLinkColumn>[] = [
      {
        title: 'Operation',
        dataIndex: 'operate',
        width: 80,
        fixed: 'right',
        access: 'ManageEditAccountLink',
        render: (txt, params) => (
          <OperateRender btnOptions={OperateOptions} params={params} />
        ),
      },
    ];
    return [...AccountLinkColumnOptions, ...tmpColumns];
  }, []);

  useEffect(() => {
    reload();
  }, []);

  return (
    <PageContainer options={AccountLinkBreadOptions}>
      <FrontTable<UserAPI.AccountLinkColumn>
        pageTitle="Account Link"
        searchOptions={searchOptions}
        loading={loading}
        columns={columns}
        dataSource={dataSource}
        rowKey={'user_id'}
        request={reload}
        btnOptions={[
          {
            label: 'Add Link',
            type: 'primary',
            icon: <PlusOutlined />,
            size: 'small',
            onClick: handleAddAccountLink,
            access: 'ManageAddAccountLinkCode',
          },
        ]}
        labelWidth={80}
        scroll={{ x: 1000, y: 'auto' }}
        isFold={true}
      />
      <AccountLinkDrawer
        visible={openDrawer}
        currentRow={currentRow}
        existSpecialUser={existSpecialUser}
        handleClose={handleCloseDrawer}
        refresh={reload}
      />
    </PageContainer>
  );
};

export default Page;
