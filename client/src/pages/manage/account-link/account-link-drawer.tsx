import NormalDrawer from '@/components/Drawer/NormalDrawer';
import NormalSelectAll from '@/components/Input/AllSelectSearch';
import NormalRadio from '@/components/Radio/NormalRadio';
import NormalSelect from '@/components/Select/NormalSelect';
import { StatusOptions } from '@/constants';
import useCustomRequest from '@/hooks/useCustomRequest';
import { getDict } from '@/services/common';
import { updateUserLink } from '@/services/user';
import { fetchData } from '@/utils';
import { Form, Input, message } from 'antd';
import { useEffect, useMemo, useCallback } from 'react';

interface AccountLinkDrawerProps {
  visible: boolean;
  currentRow: UserAPI.AccountLinkColumn | null;
  handleClose: (params?: UserAPI.UserListItem) => void;
  existSpecialUser: number[];
  refresh: () => void;
}

const AccountLinkDrawer = ({
  visible,
  handleClose,
  currentRow,
  existSpecialUser,
  refresh,
}: AccountLinkDrawerProps) => {
  const isEdit = currentRow !== null;
  const [form] = Form.useForm();

  // Fetch special user options
  const { data: specialUserData = [], run: fetchSpecialUsers } =
    useCustomRequest(getDict, {});

  // Fetch all link user options
  const { data: allLinkUserList = [], run: fetchAllLinkUsers } =
    useCustomRequest(getDict, {});

  const specialUserOptions = useMemo(() => {
    return specialUserData
      .filter(
        ({ user_id }: any) => isEdit || !existSpecialUser.includes(user_id),
      ) // 优化过滤条件
      .map(({ user_id, account_name }: any) => ({
        label: `${account_name} (${user_id})`,
        value: user_id,
      }));
  }, [specialUserData, isEdit, existSpecialUser]);

  const allLinkUserOptions = useMemo(() => {
    return allLinkUserList.map(
      ({ user_id, account_name, tnt_name, status }: any) => ({
        label: `${tnt_name}/${account_name} (${user_id})`,
        value: user_id,
        status: status,
      }),
    );
  }, [allLinkUserList]);

  useEffect(() => {
    fetchSpecialUsers({ dict_type: 'special_users' });
    fetchAllLinkUsers({ dict_type: 'account_list' });
  }, []);

  // Initialize form fields when drawer is visible
  useEffect(() => {
    if (visible) {
      const linkUsersIds = isEdit
        ? currentRow?.link_users.map(({ user_id }: any) => user_id)
        : [];
      form.setFieldsValue({
        link_status: 1,
        ...(currentRow && {
          id: currentRow.id,
          special_user_id: currentRow.user_id,
          link_users_ids: linkUsersIds,
          link_status: currentRow.link_status,
        }),
      });
    } else {
      form.resetFields();
    }
  }, [visible, currentRow, form, fetchSpecialUsers, fetchAllLinkUsers, isEdit]);

  // Form submit handler
  const handleConfirm = useCallback(async () => {
    try {
      const values = await form.validateFields();
      fetchData({
        request: updateUserLink,
        params: values,
        onSuccess: () => {
          message.success('Update user link successfully');
          refresh();
          handleClose();
        },
        onError: () => {
          message.error('Update user link failed');
        },
      });
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  }, [form]);

  // Handle drawer close
  const handleDrawerClose = useCallback(() => {
    handleClose();
    form.resetFields();
  }, [handleClose, form]);

  return (
    <NormalDrawer
      blackName={isEdit ? 'Edit Account Link' : 'Add Account Link'}
      loading={false}
      onConfirm={handleConfirm}
      open={visible}
      onClose={handleDrawerClose}
      maskClosable={false}
      width={'min(424px, 100%)'}
    >
      <Form layout="vertical" form={form}>
        {/* id hidden */}
        <Form.Item name="id" hidden>
          <Input type="hidden" />
        </Form.Item>
        {/* Special User */}
        <Form.Item
          label="Special User"
          name="special_user_id"
          rules={[{ required: true, message: 'Please select a special user' }]}
        >
          <NormalSelect
            disabled={isEdit} // Only disable if editing
            options={specialUserOptions}
            allowClear
            showSearch
            placeholder="Please select a special user"
          />
        </Form.Item>

        {/* Link Users */}
        <Form.Item
          label="Link Users"
          name="link_users_ids"
          rules={[{ required: true, message: 'Please select link users' }]}
        >
          <NormalSelectAll
            options={allLinkUserOptions}
            mode="multiple"
            placeholder="Please select link users"
            allowClear
          />
        </Form.Item>

        {isEdit && (
          <Form.Item
            label="Link Status"
            name="link_status"
            rules={[{ required: true }]}
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default AccountLinkDrawer;
