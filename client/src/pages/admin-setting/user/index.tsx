/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-31 22:07:18
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-27 10:57:41
 * @Description:
 */

import { useModel } from '@umijs/max';
import React, { useEffect, useState } from 'react';
import PageContainer from '@/components/RightPageContainer';
import { UserColumns, UserSearchOption, UserBtnOptions, UserBreadOptions } from '@/constants/admin-setting/user';
import { addAdminUser, updateAdminUser } from '@/services/api';
import EditUserModel from '../components/EditUserModel';
import CommonPage, { ChildrenProps } from '@/components/Table/CommonFrontPage';
import ResetPwdModal from '../components/ResetPwdModal';
import RixEngineFont from '@/components/RixEngineFont';
import { OperateRenderItem } from '@/components/OperateRender';

const UserPermission: React.FC = () => {
  const { allUserList: userList = [], reload, loading } = useModel('useAdminUserList');
  const { dataSource: roleList, reload: reloadRole, loading: roleLoading } = useModel('useAdminRoleList');
  const [visible, setVisible] = useState(false);
  const [editItem, setEditItem] = useState<UserAPI.UserListItem>();
  const [searchOptions, setSearchOptions] = useState(UserSearchOption);

  const OperateOptions: OperateRenderItem[] = [
    {
      label: '重置密码',
      icon: <RixEngineFont type="rix-reset" />,
      onClick: (params: UserAPI.UserListItem) => {
        setVisible(true);
        setEditItem(params);
      }
    }
  ];

  useEffect(() => {
    if (!roleList || !roleList.length) {
      reloadRole();
    }
  }, []);

  useEffect(() => {
    const arr = UserSearchOption.map(v => ({ ...v }));
    if (Array.isArray(userList) && userList.length) {
      const index = arr.findIndex(v => v.key === 'admin_id');
      if (index !== -1) {
        arr[index].options = userList.map(v => ({ label: v.account_name, value: v.admin_id }));
      }
    }
    if (Array.isArray(roleList) && roleList.length) {
      const index = arr.findIndex(v => v.key === 'role_id');
      if (index !== -1) {
        arr[index].options = roleList.map(v => ({ label: v.role_name, value: v.id }));
      }
    }
    setSearchOptions(arr);
  }, [userList, roleList]);

  return (
    <PageContainer options={UserBreadOptions}>
      <CommonPage<UserAPI.UserListItem>
        pageTitle="账号列表"
        searchOptions={searchOptions}
        loading={loading || roleLoading}
        reload={reload}
        dataSource={userList}
        rowKey="admin_id"
        btnOptions={UserBtnOptions}
        columns={UserColumns}
        operateOptions={OperateOptions}
        labelWidth={50}
        editAccess='EditAdminUser'
      >
        {
          // @ts-ignore
          (props: ChildrenProps) => {
            return <EditUserModel
              request={props.isEdit ? updateAdminUser : addAdminUser}
              roleList={roleList || []}
              {...props}
            />;
          }
        }
      </CommonPage>
      <ResetPwdModal
        visible={visible}
        editItem={editItem}
        onClose={() => setVisible(false)}
        onSave={() => setVisible(false)}
      />
    </PageContainer>
  );
};

export default UserPermission;
