/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-15 17:35:06
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 17:08:25
 * @Description:
 */

import React, { useState, useEffect } from 'react';
import { Form, Input, Modal } from 'antd';
import { fetchData, validateEmail } from '@/utils';
import { resetAdminUserPwd } from '@/services/api';
import NormalDrawer from '@/components/Drawer/NormalDrawer';

const { TextArea } = Input;

type ResetPwdModalProps = {
  visible: boolean;
  onClose: () => void;
  onSave: () => void;
  editItem?: UserAPI.UserListItem
};
const ResetPwdModal: React.FC<ResetPwdModalProps> = ({ visible, onClose, onSave, editItem }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && editItem) {
      const { account_name } = editItem;
      form.setFieldsValue({ account_name });
    }
  }, [visible, editItem]);

  const onConfirm = () => {
    form.submit();
  };

  const onSuccess = () => {
    Modal.success({
      title: '密码重置成功',
      content: '该帐号的新密码已发送至邮箱，请注意查收',
      okText: '确定'
    });

    form.resetFields();
    onSave();
  };

  const handleFinish = (values: any) => {
    const { new_password } = values;
    const { admin_id, account_name } = editItem || {};
    const emailStr = values.send_email;
    let send_email = [];
    if (emailStr) {
      send_email = emailStr
        .split('\n')
        .map((e: string) => e.trim())
        .filter((e: any) => e !== '');
    }
    const params = {
      password: new_password,
      send_email,
      account_name,
      admin_id
    };
    fetchData({ setLoading, params, request: resetAdminUserPwd, onSuccess });
  };

  return (
    <NormalDrawer
      open={visible}
      okText="确定"
      blackName="重置密码"
      onClose={() => {
        onClose();
        form.resetFields();
      }}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        onFinish={handleFinish}
        onFinishFailed={err => console.log(err)}
        autoComplete="off"
        layout="vertical"
      >
        <Form.Item
          name="account_name"
          label="账号"
          required
        >
          <Input disabled />
        </Form.Item>
        <Form.Item
          name="new_password"
          label="新密码"
          validateTrigger={['onChange', 'onBlur']}
          rules={[
            {
              required: true,
              message: '请输入新密码'
            },
            {
              type: 'string',
              min: 6,
              max: 25,
              message: '请输入6-25个字符'
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                const chineseReg = /[\u4e00-\u9fa5]/;
                if (
                  !value ||
                        (reg.test(value) && getFieldValue('old_password') !== value && !chineseReg.test(value))
                ) {
                  return Promise.resolve();
                }
                if (!reg.test(value)) {
                  return Promise.reject(new Error('密码必须包含字母数字'));
                }
                return Promise.reject(new Error('密码不能包含中文'));
              }
            })
          ]}
          hasFeedback
        >
          <Input.Password style={{ maxWidth: 418 }} allowClear placeholder="请输入新密码"/>
        </Form.Item>
        <Form.Item
          name="confirm_password"
          label="确认密码"
          validateTrigger={['onChange', 'onBlur']}
          dependencies={['new_password']}
          rules={[
            {
              required: true,
              message: '请再次确认密码'
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                const reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
                if (!value || (getFieldValue('new_password') === value && reg.test(value))) {
                  return Promise.resolve();
                }
                if (!reg.test(value)) {
                  return Promise.reject(new Error('密码必须包含字母数字'));
                }
                return Promise.reject(new Error('密码不匹配'));
              }
            })
          ]}
          hasFeedback
        >
          <Input.Password style={{ maxWidth: 418 }} allowClear placeholder="请再次确认密码"/>
        </Form.Item>
        <Form.Item
          name="send_email"
          label="接收邮箱"
          validateTrigger={['onChange', 'onBlur']}
          tooltip="通过邮件通知谁"
          rules={[
            {
              required: true
            },
            {
              validator: validateEmail
            }
          ]}
        >
          <TextArea rows={3} placeholder="请输入邮箱" />
        </Form.Item>
      </Form>
    </NormalDrawer>
  );
};

export default ResetPwdModal;
