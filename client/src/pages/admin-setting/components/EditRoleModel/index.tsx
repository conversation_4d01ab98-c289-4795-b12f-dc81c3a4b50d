/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-29 00:11:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-12 16:22:29
 * @Description:
 */

import React, { useState, useEffect } from 'react';
import { Form, message, Input } from 'antd';
import styles from './index.less';
import { fetchData } from '@/utils';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { RoleType } from '@/constants/admin-setting/role';

type EditRoleProps = {
  visible: boolean;
  onClose: () => void;
  onSave: () => void;
  item?: AdminSettingAPI.RoleItem;
  isEdit: boolean;
  request: any;
};
const EditRoleModal: React.FC<EditRoleProps> = ({
  visible,
  onClose,
  onSave,
  item,
  isEdit,
  request,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isEdit && visible && item) {
      form.setFieldsValue(item);
    }
  }, [item, visible, isEdit]);

  const onConfirm = () => {
    form.submit();
  };

  const onSuccess = () => {
    if (isEdit) {
      message.success('更新成功');
    } else {
      message.success('创建成功');
    }
    form.resetFields();
    onSave();
  };

  const handleFinish = (values: any) => {
    const params = {
      ...values,
      id: item?.id || '',
      ori_data: item || {},
    };
    handleSubmit(params);
  };

  const handleSubmit = (params: any) => {
    fetchData({ setLoading, params, request, onSuccess });
  };

  return (
    <NormalDrawer
      open={visible}
      className={styles['container']}
      blackName={isEdit ? '编辑角色' : '新增角色'}
      grayName={isEdit ? `${item?.id}` : ''}
      onClose={() => {
        onClose();
        form.resetFields();
      }}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        onFinish={handleFinish}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        layout="vertical"
        validateTrigger={['onChange', 'onBlur']}
      >
        <Form.Item
          name="role_name"
          label="角色名称"
          rules={[{ required: true, message: '请输入角色名称' }]}
        >
          <Input
            placeholder="请输入角色名称"
            disabled={isEdit && item?.type === RoleType['Super Administer']}
          />
        </Form.Item>
        <Form.Item name="remark" label="备注">
          <Input placeholder="请输入备注" />
        </Form.Item>
      </Form>
    </NormalDrawer>
  );
};

export default EditRoleModal;
