/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-29 00:11:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-12 16:19:40
 * @Description:
 */

import React, { useState, useEffect } from 'react';
import { Form, message, Input, Select } from 'antd';
import NormalRadio from '@/components/Radio/NormalRadio';
import { fetchData, validateEmail } from '@/utils';
import { StatusOptions } from '@/constants';
import NormalDrawer from '@/components/Drawer/NormalDrawer';

const { TextArea } = Input;
type EditUserProps = {
  visible: boolean;
  onClose: () => void;
  onSave: () => void;
  editItem?: UserAPI.UserListItem;
  isEdit: boolean;
  request: any;
  roleList: AdminSettingAPI.RoleItem[];
};
const EditUserModal: React.FC<EditUserProps> = ({
  visible,
  onClose,
  onSave,
  editItem,
  isEdit,
  request,
  roleList,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isEdit && visible && editItem) {
      form.setFieldsValue(editItem);
    }
  }, [editItem, visible, isEdit]);

  const onConfirm = () => {
    form.submit();
  };

  const onSuccess = () => {
    if (isEdit) {
      message.success('更新成功');
    } else {
      message.success('创建成功');
    }
    form.resetFields();
    onSave();
  };

  const handleFinish = (values: any) => {
    const send_email = values.send_email
      ? values.send_email.split('\n').filter((e: any) => e !== '')
      : [];
    const params = {
      ...values,
      send_email,
      admin_id: editItem?.admin_id || '',
      ori_data: editItem || {},
    };
    handleSubmit(params);
  };

  const handleSubmit = (params: any) => {
    fetchData({ setLoading, params, request, onSuccess });
  };
  return (
    <NormalDrawer
      open={visible}
      blackName={isEdit ? '编辑运营账号' : '新增运营账号'}
      onClose={() => {
        onClose();
        form.resetFields();
      }}
      onConfirm={onConfirm}
      loading={loading}
    >
      <Form
        form={form}
        onFinish={handleFinish}
        onFinishFailed={(err) => console.log(err)}
        autoComplete="off"
        layout="vertical"
        validateTrigger={['onChange', 'onBlur']}
      >
        <Form.Item
          name="display_name"
          label="昵称"
          rules={[
            {
              required: true,
              message: '请输入昵称',
            },
          ]}
        >
          <Input placeholder="请输入昵称" />
        </Form.Item>
        <Form.Item
          name="account_name"
          label="账号"
          getValueFromEvent={(e: any) => e.target.value.replace(/\s+/g, '')}
          rules={[
            {
              required: true,
              message: '请输入账号',
            },
          ]}
        >
          <Input disabled={isEdit} placeholder="请输入账号" />
        </Form.Item>
        <Form.Item
          name="role_id"
          label="角色"
          rules={[{ required: true, message: '请输入账号' }]}
        >
          <Select
            options={roleList.map((item) => {
              return {
                label: `${item.role_name}`,
                value: item.id,
              };
            })}
            allowClear
            showSearch
            placeholder="请选择角色"
            optionFilterProp="children"
            filterOption={(input, option: any) =>
              option &&
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          />
        </Form.Item>
        <Form.Item name="remark" label="备注">
          <Input placeholder="请输入备注" />
        </Form.Item>
        {isEdit && (
          <Form.Item
            name="status"
            label="状态"
            rules={[
              {
                required: true,
                message: '请选择状态',
              },
            ]}
          >
            <NormalRadio options={StatusOptions} />
          </Form.Item>
        )}
        {!isEdit && (
          <Form.Item
            name="send_email"
            label="接收邮箱"
            validateTrigger={['onChange', 'onBlur']}
            tooltip="通过邮箱通知给谁"
            rules={[
              {
                required: true,
                message: '请输入邮箱',
              },
              {
                validator: validateEmail,
              },
            ]}
          >
            <TextArea rows={3} placeholder="请输入接收邮箱" />
          </Form.Item>
        )}
      </Form>
    </NormalDrawer>
  );
};

export default EditUserModal;
