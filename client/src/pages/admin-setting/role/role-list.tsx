/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-20 18:34:14
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 17:01:33
 * @Description:
 */

import React, { useState, useEffect } from 'react';
import FrontTable from '@/components/Table/FrontTable';
import { RoleColumns, RoleSearchOption } from '@/constants/admin-setting/role';
import EditRoleModel from '../components/EditRoleModel';
import { addAdminRole, updateAdminRole } from '@/services/api';
import OperateRender from '@/components/OperateRender';
import RixEngineFont from '@/components/RixEngineFont';
import { PlusOutlined } from '@ant-design/icons';

type RoleListProps = {
  dataSource: AdminSettingAPI.RoleItem[];
  loading: boolean;
  reload: () => void
}
const RolePage: React.FC<RoleListProps> = ({ dataSource, loading, reload }) => {
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<AdminSettingAPI.RoleItem>();
  const [columns, setColumns] = useState(RoleColumns);
  const [searchOptions, setSearchOptions] = useState(RoleSearchOption);

  useEffect(() => {
    const options = RoleColumns.map(v => {
      if (v.dataIndex === 'operate') {
        v.render = onOperateRender;
      }
      return v;
    });
    setColumns(options);
  }, []);

  useEffect(() => {
    if (dataSource?.length) {
      const options = dataSource.map(v => ({ label: v.role_name, value: v.id }));
      const arr = RoleSearchOption.map(v => ({ ...v }));
      const index = arr.findIndex(v => v.key === 'id');
      if (index !== -1) {
        arr[index].options = options;
      }
      setSearchOptions(arr);
    }
  }, [dataSource]);

  const onOperateRender = (_: number, row: AdminSettingAPI.RoleItem) => {
    return (
      <OperateRender
        params={row}
        btnOptions={[{
          label: '编辑',
          icon: <RixEngineFont type="edit" />,
          onClick: () => {
            setEditItem(row);
            setIsEdit(true);
            setVisible(true);
          }
        }]}
      />
    );
  };

  const handleClose = () => {
    setVisible(false);
  };

  const handleSave = () => {
    setVisible(false);
    reload();
  };

  // 批量操作使用的selectedRows
  const handleClickBtn = () => {
    setVisible(true);
    setIsEdit(false);
  };

  return (
    <>
      <FrontTable<AdminSettingAPI.RoleItem>
        pageTitle="角色列表"
        searchOptions={searchOptions}
        loading={loading}
        request={reload}
        dataSource={dataSource}
        columns={columns}
        rowKey="id"
        labelWidth={50}
        scroll={{ x: 1000, y: 'auto' }}
        isBtnTable
        isFold
        btnOptions={[
          {
            label: '新增角色',
            type: 'primary',
            size: 'small',
            icon: <PlusOutlined />,
            onClick: handleClickBtn,
            access: 'AddAdminRole'
          }
        ]}
      />
      <EditRoleModel
        visible={visible}
        isEdit={isEdit}
        onSave={handleSave}
        onClose={handleClose}
        request={isEdit ? updateAdminRole : addAdminRole}
        item={editItem}
      />
    </>
  );
};

export default RolePage;
