.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  > div {
    &:last-child {
      flex: 1;
      > div {
        &:first-child {
          > div {
            border-top-left-radius: 0px;
          }
        }
      }
    }
  }
}


.role-list-container {
  padding: 20px;
}

.pms-container {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 6px;
  border-top-left-radius: 6px !important;
  margin-right: 16px;
  .right-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    border-radius: 6px;
    margin-right: 20px;
    margin-left: 20px;
    .right-top {
      height: 32px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 12px;
      margin-bottom: 12px;
      h3 {
        margin-bottom: 0px;
      }
    }
    .right-bottom {
      flex: 1;
    }
  }
  .right-container, .left-container {
    //background-color: #fff;
    min-height: calc(100% - 120px);
    margin-bottom: 20px;
    .scroll {
      border: 1px solid #e5e5e5;
      min-height: calc(100vh - 250px);
      border-radius: 6px;
      padding: 16px;
      max-height: calc(100vh - 250px);
      overflow-y: scroll;
      .tnt-container {
        margin-bottom: 12px;
      }
    }
  }
  .left-container {
    width: 260px;
    margin-left: 20px;
    border-radius: 6px;
    .left-top {
      margin-top: 12px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      height: 32px;
      line-height: 32px;
      // justify-content: space-between;
      h3 {
        margin-bottom: 0px;
        padding-right: 6px;
      }
    }
  }
  .left-item-container {
    width: 100%;
    >div {
      height: 36px;
      line-height: 36px;
      border-radius: 6px;
      padding: 0px 12px;
      cursor: pointer;
    }
    .left-active-item {
      background-color: #e6f7ff;
      border-radius: 6px;
      color: #1890ff;
    }
    .left-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .left-item-title {
        flex: 1;
      }
    }
  }
}
