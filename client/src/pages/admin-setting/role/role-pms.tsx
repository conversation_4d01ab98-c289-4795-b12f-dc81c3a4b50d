/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-20 18:34:31
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-12 16:24:43
 * @Description:
 */

import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Button, Tree, Spin, message } from 'antd';
import { getAllKeys } from './utils';
import { fetchData } from '@/utils';
import { useAccess } from '@umijs/max';

export type LeftType = {
  title: string;
  id: number;
  pms_list: string[];
};

type leftPermissionProps = {
  leftList: LeftType[]; // 左侧的数据
  menuData: any[]; // 右侧菜单的数据
  request: any; // 哪个接口
  reload?: () => void; // 刷新的接口
  disabled?: boolean;
  loading: boolean;
  leftTitle?: string;
};
const RolePermission: React.FC<leftPermissionProps> = ({
  leftList,
  menuData,
  request,
  reload,
  disabled,
  loading: dataLoading,
  leftTitle,
}) => {
  const access = useAccess();
  const [expandedKeys, setExpandedKeys] = useState<any[]>([]);
  const [checkedKeys, setCheckedKeys] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  // 当前的数据
  const [curData, setCurData] = useState<LeftType>();

  useEffect(() => {
    if (curData) {
      if (menuData.length) {
        const keys = getAllKeys(menuData);
        setExpandedKeys(keys);
        setCheckedKeys(curData.pms_list || []);
      } else {
        setCheckedKeys([]);
      }
    } else {
      setCheckedKeys([]);
    }
  }, [curData, menuData]);

  useEffect(() => {
    if (leftList.length) {
      if (curData) {
        const data = leftList.find((v) => v.id === curData.id);
        if (data) {
          setCurData(data);
        } else {
          setCurData(leftList[0]);
        }
      } else {
        setCurData(leftList[0]);
      }
    } else {
      setCurData(undefined);
    }
  }, [leftList, curData]);

  const handleChangeActive = (e: any) => {
    setCurData(e);
  };

  const onExpand = (expandedKeysValue: React.Key[]) => {
    setExpandedKeys(expandedKeysValue);
  };

  const onCheck = (checkedKeysValue: any) => {
    const value = Array.isArray(checkedKeysValue)
      ? checkedKeysValue
      : checkedKeysValue.checked;
    setCheckedKeys(value);
  };

  const onSuccess = () => {
    message.success('success');
    reload && reload();
  };

  const handleSubmit = () => {
    const params = {
      menus: checkedKeys,
      id: curData?.id,
      ori_data: curData || {},
    };
    fetchData({ params, request, setLoading, onSuccess });
  };

  return (
    <Spin spinning={loading || dataLoading}>
      <div className={styles['pms-container']}>
        {/* 左侧角色选择 */}
        <div className={styles['left-container']}>
          <div className={styles['left-top']}>
            <h3>{leftTitle || 'Operate'}</h3>
          </div>
          <div className={styles['scroll']}>
            <div className={styles['left-item-container']}>
              {leftList.map((v) => {
                return (
                  <div
                    key={v.id}
                    className={`${styles['left-item']} ${
                      v.id === curData?.id ? styles['left-active-item'] : ''
                    }`}
                    onClick={() => handleChangeActive(v)}
                  >
                    <span className={styles['left-item-title']}>{v.title}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
        <div className={styles['right-container']}>
          <div className={styles['right-top']}>
            <h3>菜单和按钮权限</h3>
            <Button
              type="primary"
              disabled={
                !curData || disabled || !access.isButtonAccess('SubmitRolePMS')
              }
              onClick={handleSubmit}
            >
              提交
            </Button>
          </div>
          <div className={styles['scroll']}>
            <Tree
              checkStrictly
              checkable
              selectable={false}
              treeData={menuData}
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              onCheck={onCheck}
              checkedKeys={checkedKeys}
              disabled={disabled}
            />
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default RolePermission;
