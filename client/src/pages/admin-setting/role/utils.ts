/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-20 18:50:13
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 17:02:32
 * @Description:
 */


//  flag表示是否排除自身
export const getAllKeys = (data: any[], flag = false) => {
  let arr: any[] = [];
  data.forEach(v => {
    if (!flag) {
      arr.push(v.key);
    }
    if (Array.isArray(v.children) && v.children.length) {
      arr = [...arr, ...getAllKeys(v.children, false)];
    }
    if (Array.isArray(v.btn) && v.btn.length) {
      arr = [...arr, ...getAllKeys(v.btn, false)];
    }
  });
  return arr;
};
