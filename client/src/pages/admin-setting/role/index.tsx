/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-20 18:33:47
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 16:58:29
 * @Description:
 */
import { useModel } from '@umijs/max';
import styles from './index.less';
import React, { useEffect, useState } from 'react';
import TabPane from '@/components/TabPane';
import { StatusType } from '@/constants';
import RoleListPage from './role-list';
import PageContainer from '@/components/RightPageContainer';
import { LeftType } from './role-pms';
import { handleFormatData } from '../menu/utils';
import RolePermissionPage from './role-pms';
import { RoleBreadOptions, RoleTab, RoleTabItems, RoleType } from '@/constants/admin-setting/role';
import { updateAdminRolePms } from '@/services/api';

const RolePage: React.FC = () => {
  const [currentTab, setCurrentTab] = useState<number>(RoleTab['Role List']);
  const { dataSource: roleList, reload: reloadRole, loading: roleLoading } = useModel('useAdminRoleList');
  const { dataSource: menuList, reload: reloadMenu, loading: menuLoading } = useModel('useAdminMenuList');
  const [menuData, setMenuData] = useState<any[]>([]);
  const [leftList, setLeftList] = useState<LeftType[]>([]);
  const [allRole, setAllRole] = useState<AdminSettingAPI.RoleItem[]>([]);
  // 用户的所有权限

  useEffect(() => {
    reloadRole();
    if (!menuList || !menuList.length) {
      reloadMenu();
    }
  }, []);

  useEffect(() => {
    if (Array.isArray(roleList)) {
      const data = roleList.filter(v => v.status === StatusType.Active);
      setAllRole(data);
    }
  }, [roleList]);
  // 菜单数据
  useEffect(() => {
    if (Array.isArray(menuList)) {
      const treeArr: any[] = handleFormatData(menuList, 0);
      setMenuData(treeArr);
    }
  }, [menuList]);

  useEffect(() => {
    const arr = Array.isArray(allRole) ? allRole : [];
    const tmp = arr.filter(v => v.status === StatusType.Active && v.type !== RoleType['Super Administer']);
    const data = tmp.map(v => {
      return {
        title: v.role_name,
        id: v.id,
        pms_list: v.pms_list
      };
    });
    // 左侧的数据
    setLeftList(data);
  }, [allRole]);

  const handleTabChange = (tab: any) => {
    setCurrentTab(tab as any);
  };
  return (
    <PageContainer className={styles['container']} options={RoleBreadOptions}>
      <div className={styles['main']}>
        <TabPane
          items={RoleTabItems}
          defaultTabKey={RoleTab['Role List']}
          onTabChange={handleTabChange}
        />
        {
          currentTab === RoleTab['Role List'] && (
            <RoleListPage
              dataSource={allRole}
              reload={reloadRole}
              loading={roleLoading}
            />
          )
        }
        {
          currentTab === RoleTab['Permission'] && (
            <RolePermissionPage
              leftList={leftList}
              menuData={menuData}
              loading={menuLoading || roleLoading}
              reload={reloadRole}
              request={updateAdminRolePms}
              leftTitle="角色列表"
            />
          )
        }
      </div>
    </PageContainer>
  );
};

export default RolePage;
