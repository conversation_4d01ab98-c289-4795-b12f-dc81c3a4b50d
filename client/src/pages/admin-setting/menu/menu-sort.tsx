/*
 * @Author: ch<PERSON><PERSON><PERSON> chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-03-21 18:52:47
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-12 16:25:50
 * @Description:
 */
import styles from './index.less';
import { Tree, message, Modal, TreeProps, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { fetchData } from '@/utils';
import { DataNode } from 'antd/es/tree';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { updateAdminMenuSort } from '@/services/api';
import { MenuOperationType } from '@/constants/admin-setting/menu';
import {
  handleGetPidObj,
  handleFormatData,
  loopDrop,
  handleGetSortParams,
  MenuSortProps,
  MenuSortParamsItem,
} from './utils';

const Page: React.FC<MenuSortProps> = ({
  allMenuList,
  visible,
  handleCancel,
  handleOk,
}) => {
  const [menuData, setMenuData] = useState<DataNode[]>([]);
  const [loading, setLoading] = useState(false);
  const [pidObj, setPidObj] = useState<any>({});
  const [defaultParams, setDefaultParams] = useState<MenuSortParamsItem[]>([]);
  const [menuList, setMenuList] = useState<AdminSettingAPI.MenuItem[]>([]);
  // 判断移动排序是否合法
  const [menuObj, setMenuObj] = useState<any>({});
  useEffect(() => {
    if (menuList && menuList.length) {
      const treeArr: DataNode[] = handleFormatData(menuList, 0);
      setMenuData(treeArr);
      const arr: MenuSortParamsItem[] = [];
      const obj: any = {};
      menuList.forEach((v) => {
        const tmp: MenuSortParamsItem = {
          id: v.id,
          pid: v.pid,
          sort: v.sort,
        };
        obj[v.id] = v;
        arr.push(tmp);
      });
      setDefaultParams(arr);
      setMenuObj(obj);
    }
  }, [menuList]);

  useEffect(() => {
    if (allMenuList.length) {
      const data = allMenuList.filter((v) => v.type === MenuOperationType.Menu);
      setMenuList(data);
    }
  }, [allMenuList]);

  useEffect(() => {
    if (Array.isArray(menuData) && menuData.length) {
      const obj: any = {};
      handleGetPidObj(menuData, obj);
      setPidObj(obj);
    }
  }, [menuData]);

  const onDrop: TreeProps['onDrop'] = (info) => {
    const { dragNode, node, dropToGap } = info;
    const dropKey = node.key;
    const dragKey = dragNode.key;
    const dropPos = node.pos.split('-');
    const dropPosition =
      info.dropPosition - Number(dropPos[dropPos.length - 1]);

    const dropItem = menuObj[`${dropKey}`];
    const dragItem = menuObj[`${dragKey}`];

    if (
      (!dropToGap && !dragItem?.path?.includes(dropItem.path)) ||
      (dropToGap && dragItem.pid !== dropItem.pid)
    ) {
      return message.error('非法操作，请检查');
    }

    const data = [...menuData];

    let dragObj: DataNode;
    loopDrop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });
    if (!dropToGap) {
      loopDrop(data, dropKey, (item) => {
        item.children = item.children || [];
        item.children.unshift(dragObj);
      });
    } else if (
      ((info.node as any).props.children || []).length > 0 &&
      (info.node as any).props.expanded &&
      dropPosition === 1
    ) {
      loopDrop(data, dropKey, (item) => {
        item.children = item.children || [];
        item.children.unshift(dragObj);
      });
    } else {
      let ar: DataNode[] = [];
      let i: number;
      loopDrop(data, dropKey, (_item, index, arr) => {
        ar = arr;
        i = index;
      });
      if (dropPosition === -1) {
        ar.splice(i!, 0, dragObj!);
      } else {
        ar.splice(i! + 1, 0, dragObj!);
      }
    }
    setMenuData(data);
  };

  const handleConfirm = () => {
    if (menuData.length) {
      Modal.confirm({
        title: '提示',
        icon: <ExclamationCircleOutlined />,
        content: '确定更改菜单排序么?',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          handleUpdateMenu();
        },
        onCancel: () => {
          handleCancel();
        },
      });
    } else {
      handleCancel();
    }
  };

  const onSuccess = () => {
    message.success('更新成功');
    handleOk();
  };

  const handleUpdateMenu = () => {
    const list: MenuSortParamsItem[] = [];
    handleGetSortParams(menuData, list, pidObj);
    const data = list.filter((v) => {
      return defaultParams.some((t) => {
        return t.id === v.id && (t.sort !== v.sort || t.pid !== v.pid);
      });
    });
    const ori_data = defaultParams.filter((v) => {
      return list.some((t) => {
        return t.id === v.id && (t.sort !== v.sort || t.pid !== v.pid);
      });
    });
    fetchData({
      setLoading,
      request: updateAdminMenuSort,
      params: { list: data, ori_data },
      onSuccess,
    });
  };

  return (
    <Modal
      title="菜单排序"
      open={visible}
      onOk={handleConfirm}
      onCancel={handleCancel}
      maskClosable={false}
      keyboard={false}
      className={styles['menu-sort-container']}
      style={{ top: 50 }}
    >
      <Spin spinning={loading}>
        <Tree
          showIcon
          treeData={menuData}
          draggable
          blockNode
          onDrop={onDrop}
          defaultExpandAll
        />
      </Spin>
    </Modal>
  );
};

export default Page;
