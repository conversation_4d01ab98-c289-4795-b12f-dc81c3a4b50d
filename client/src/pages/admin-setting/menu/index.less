.menu-container {
  display: flex;
  margin-right: 16px;
  .menu-left-card {
    width: 400px;
    margin-right: 16px;
    border-radius: 6px;
    background-color: #fff;
    height: calc(100vh - 130px);
    .search-container {
      padding: 12px 16px;
      > button {
        margin-top: 12px;
      }
    }
    .select-message-container {
      height: 40px;
      margin: 12px 16px;
      margin-bottom: 0px;
      border: 1px solid #91d5ff;
      background-color: #e6f7ff;
      border-radius: 3px;
      padding: 0px 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      > span {
        display: flex;
        width: 100%;
      }
    }
    .tree-container {
      padding: 12px 16px;
      max-height: calc(100vh - 285px);
      overflow-y: scroll;
    }
  }
  .menu-tree-container {
    :global {
      .ant-tree-node-content-wrapper {
        display: flex;
      }
    }
  }
  .top {
    display: flex;
    align-items: center;
    background-color: #cdcccd;
    justify-content: space-between;
    height: 48px;
    line-height: 48px;
    padding: 0px 16px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
    h3 {
      margin-bottom: 0px;
    }
    > div {
      display: flex;
      align-items: center;
    }
  }
  .menu-right-card {
    flex: 1;
    background-color: #fff;
    border-radius: 6px;
    .menu-form {
      padding: 12px 16px;
      padding-right: 0px;
      padding-bottom: 0px;
      height: calc(100vh - 190px);
      overflow-y: scroll;
      :global {
        .ant-form {
          display: flex;
          flex-wrap: wrap;
          // justify-content: space-between;
        }
        .ant-form-item {
          flex: 1;
          min-width: 330px;
          max-width: 350px;
          padding-right: 16px;
          margin-bottom: 20px;
        }
        .ant-form-vertical .ant-form-item-label {
          padding-bottom: 5px;
        }
        .ant-form-item-explain-error {
          font-size: 12px;
        }
      }
    }
    .footer {
      text-align: right;
      padding: 12px 16px;
      background-color: #ebeded;
      border-bottom-left-radius: 6px;
      border-bottom-right-radius: 6px;
      button {
        border-radius: 6px;
      }
      button:last-child {
        margin-left: 12px;
      }
    }
    :global {
      .ant-spin-nested-loading {
        height: 100%;
        .ant-spin-container {
          height: 100%;
        }
      }
    }
  }
}

.mr12 {
  margin-right: 12px;
}

.menu-sort-container {
  //color: red;
  :global {
    .ant-modal-content {
      .ant-modal-body {
        max-height: calc(100vh - 200px);
        overflow-y: scroll;
      }
    }
  }
}

.ellipsis {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}
