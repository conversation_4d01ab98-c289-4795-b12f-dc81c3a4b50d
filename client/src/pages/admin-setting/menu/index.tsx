/*
 * @Author: <PERSON>
 * @Date: 2023-03-16 21:07:01
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-21 17:04:04
 * @Description:
 */
import { Tree, Button, Tooltip } from 'antd';
import type { DataNode, EventDataNode } from 'antd/es/tree';
import React, { useEffect, useMemo, useState } from 'react';
import PageContainer from '@/components/RightPageContainer';
import styles from './index.less';
import { MenuOperationType, MenuBreadOptions } from '@/constants/admin-setting/menu';
import { RedoOutlined, PlusCircleOutlined, SettingOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import MenuRight from './menu-right';
import MenuSort from './menu-sort';
import InputSearch from '@/components/Input/InputSearch';
import { handleFormatData } from './utils';
import { loop } from './utils';

const Page: React.FC = () => {
  const { dataSource = [], reload, loading: menuLoading } = useModel('useAdminMenuList');
  const [currentMenu, setCurrentMenu] = useState<AdminSettingAPI.MenuItem | undefined>(undefined);
  const [menuData, setMenuData] = useState<DataNode[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [isEdit, setIsEdit] = useState(false); // 是否编辑
  const [visible, setVisible] = useState(false);
  const [menuList, setMenuList] = useState<AdminSettingAPI.MenuItem[]>([]);
  const [searchValueTmp, setSearchTmp] = useState('');

  useEffect(() => {
    reload();
  }, []);

  useEffect(() => {
    if (menuList && menuList.length) {
      const treeArr: DataNode[] = handleFormatData(menuList, 0);
      setMenuData(treeArr);
    } else {
      setMenuData([]);
    }
  }, [menuList]);

  useEffect(() => {
    if (Array.isArray(dataSource) && dataSource.length) {
      const data = dataSource.filter(v => {
        return (!searchValueTmp || (v.title.toLowerCase()).includes(searchValueTmp.toLowerCase()));
      });
      setMenuList(data);
    }
  }, [dataSource, searchValueTmp]);

  const treeData = useMemo(() => {
    return loop(menuData);
  }, [menuData]);

  const handleReload = () => {
    reload();
  };

  const onSelectTree = (selects: React.Key[], e: { event: 'select'; selected: boolean; node: EventDataNode<DataNode>; selectedNodes: DataNode[]; nativeEvent: MouseEvent; }) => {
    setIsEdit(true);
    setSelectedKeys(selects as string[]);
    const nodes = e.selectedNodes;
    if (Array.isArray(nodes) && nodes.length) {
      setCurrentMenu(nodes[0] as any);
    }
  };

  const handleCancelSelect = () => {
    setSelectedKeys([]);
    setCurrentMenu(undefined);
    setIsEdit(false);
  };

  const handleAddMenu = () => {
    setIsEdit(false);
    setCurrentMenu(undefined);
    setSelectedKeys([]);
  };

  const handleSuccess = () => {
    reload();
    setSelectedKeys([]);
    setCurrentMenu(undefined);
    setIsEdit(false);
  };

  const handleOk = () => {
    setVisible(false);
    reload();
  };

  const handleCancel = () => {
    setVisible(false);
  };

  const handleOpen = () => {
    setVisible(true);
  };

  const handleSearchValueChange = (val: string) => {
    setSearchValue(val || '');
  };

  const handleSearchMenu = (val: string) => {
    setSearchTmp(val);
  };

  return <PageContainer options={MenuBreadOptions} style={{ maxHeight: '100%' }}>
    <div className={styles['menu-container']}>
      <div className={styles['menu-left-card']}>
        <div className={styles['top']}>
          <h3>菜单目录</h3>
          <div className={styles['top-right']}>
            <Tooltip title="新增菜单">
              <Button
                type="text"
                icon={<PlusCircleOutlined style={{ fontSize: 20, cursor: 'pointer' }}/>}
                onClick={handleAddMenu}
              />
            </Tooltip>
            <Tooltip title="菜单排序">
              <Button
                type="text"
                icon={<SettingOutlined style={{ fontSize: 20, cursor: 'pointer' }} onClick={handleOpen}/>}
              />
            </Tooltip>
            <Tooltip title="刷新">
              <Button
                icon={<RedoOutlined style={{ fontSize: 20, cursor: 'pointer' }} spin={menuLoading}/>}
                type="text"
                onClick={handleReload}
              />
            </Tooltip>
          </div>
        </div>
        <div className={styles['select-message-container']}>
          <span>
            <InfoCircleOutlined style={{ color: '#1890ff', fontSize: 18, paddingRight: 8 }}/>
            <span style={{ paddingRight: 8 }}>
              选择
              {currentMenu?.type === MenuOperationType.Menu ? '菜单' : '按钮'}:
            </span>
            {
              !selectedKeys.length ? <span>无</span> : ''
            }
            {
              selectedKeys.length
                ? <>
                  <span style={{ color: '#1677ff' }} className={styles['ellipsis']} title={currentMenu?.title || ''}>{currentMenu?.title || ''}</span>
                  <span style={{ color: '#1677ff', cursor: 'pointer', paddingLeft: 8 }} onClick={handleCancelSelect}>取消</span>
                </>
                : ''
            }
          </span>
        </div>
        <div className={styles['search-container']}>
          <InputSearch
            placeholder="请输入名称"
            onValueChange={handleSearchValueChange}
            handleSearch={handleSearchMenu}
            value={searchValue}
          />
        </div>
        <div className={styles['tree-container']}>
          <Tree
            showIcon
            treeData={treeData}
            className={styles['menu-tree-container']}
            selectedKeys={selectedKeys}
            onSelect={onSelectTree}
          />
        </div>
      </div>
      <MenuRight
        currentMenu={currentMenu}
        allMenuList={dataSource}
        isEdit={isEdit}
        handleSuccess={handleSuccess}
      />
    </div>
    <MenuSort
      visible={visible}
      handleCancel={handleCancel}
      handleOk={handleOk}
      allMenuList={dataSource}
    />
  </PageContainer>;
};

export default Page;
