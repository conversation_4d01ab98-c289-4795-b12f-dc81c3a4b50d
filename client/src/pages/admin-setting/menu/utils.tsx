/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-20 18:52:50
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 17:07:38
 * @Description:
 */
import type { DataNode } from 'antd/es/tree';

export const loop = (data: DataNode[]): DataNode[] => {
  return data.map((item: any) => {
    if (item.children) {
      return {
        ...item,
        title: item.title,
        key: item.key,
        children: loop(item.children)
      };
    }
    return {
      ...item,
      title: item.title,
      key: item.key
    };
  });
};

export const handleFormatData = (dataSource: AdminSettingAPI.MenuItem[], key: number): DataNode[] => {
  const filterArr = dataSource.filter(item => item.pid === key).sort((a, b) => a.sort - b.sort);
  const restArr = dataSource.filter(item => item.pid !== key);
  return filterArr.map(item => {
    return {
      ...item,
      title: item.title,
      key: `${item.id}`,
      children: [...handleFormatData(restArr, item.id)]
  };
});
};

export type MenuRightProps = {
  currentMenu?: AdminSettingAPI.MenuItem | undefined;
  allMenuList: AdminSettingAPI.MenuItem[];
  isEdit: boolean;
  handleSuccess: () => void;
}

export type MenuSortProps = {
  allMenuList: AdminSettingAPI.MenuItem[];
  visible: boolean;
  handleOk: () => void;
  handleCancel: () => void;
}

export type MenuSortParamsItem = {
  id: number; // 自己的id
  sort: number; // 排序的
  pid: number; // 父菜单
}

export const handleGetPidObj = (menu: DataNode[], obj: any) => {
  menu.forEach((item: any) => {
    if (Array.isArray(item.children) && item.children.length) {
      const arr = item.children.map((v: any) => v.id);
      arr.forEach((v: number) => {
        obj[v] = item.id;
      });
      handleGetPidObj(item.children, obj);
    }
  });
};

export const loopDrop = (
  data: DataNode[],
  key: React.Key,
  callback: (node: DataNode, i: number, data: DataNode[]) => void
) => {
  for (let i = 0; i < data.length; i++) {
    if (data[i].key === key) {
      return callback(data[i], i, data);
    }
    if (data[i].children) {
      loopDrop(data[i].children!, key, callback);
    }
  }
};

export const handleGetSortParams = (data: DataNode[], arr: MenuSortParamsItem[], pidObj: any) => {
  data.forEach((item: any, index) => {
    const obj: MenuSortParamsItem = {
      id: item.id,
      sort: index,
      pid: pidObj[item.id] || 0
    };
    arr.push(obj);
    if (Array.isArray(item.children) && item.children.length) {
      handleGetSortParams(item.children, arr, pidObj);
    }
  });
};