/*
 * @Author: Peggy
 * @Date: 2023-03-16 21:59:13
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-12 16:26:08
 * @Description:
 */
import styles from './index.less';
import { Input, Button, Form, Select, Spin, message, Modal } from 'antd';
import { MenuDefaultFormData } from '@/constants/admin-setting/menu';
import { useEffect, useState } from 'react';
import { fetchData } from '@/utils';
import { addAdminMenu, updateAdminMenu, deleteAdminMenu } from '@/services/api';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { MenuRightProps } from './utils';
import NormalRadio from '@/components/Radio/NormalRadio';
import RixEngineFont from '@/components/RixEngineFont';

const Page: React.FC<MenuRightProps> = ({
  currentMenu,
  allMenuList,
  isEdit,
  handleSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (currentMenu && isEdit) {
      form.setFieldsValue({ ...currentMenu, pid: currentMenu.pid || '' });
    } else {
      form.resetFields();
    }
  }, [currentMenu, isEdit]);

  const handleSave = () => {
    form.submit();
  };

  const onFinish = (values: any) => {
    if (isEdit && currentMenu) {
      const params = {
        ...values,
        id: currentMenu.id,
        pid: values.pid || currentMenu.pid || 0,
        ori_data: currentMenu || {},
      };
      handleUpdate(params);
    } else {
      const params = {
        pid: 0,
        ...values,
      };
      handleAdd(params);
    }
  };

  const handleReset = () => {
    if (isEdit && currentMenu) {
      form.setFieldsValue({ ...currentMenu, pid: currentMenu.pid || '' });
    } else {
      form.resetFields();
    }
  };

  const onSuccess = () => {
    message.success('成功');
    // 重置数据
    form.resetFields();
    handleSuccess();
  };

  const handleUpdate = (params: any) => {
    fetchData({ setLoading, request: updateAdminMenu, onSuccess, params });
  };

  const handleAdd = (params: any) => {
    fetchData({ setLoading, request: addAdminMenu, onSuccess, params });
  };

  const handleDeleteMenu = (params: any) => {
    fetchData({ setLoading, request: deleteAdminMenu, onSuccess, params });
  };

  const handleConfirmDelete = () => {
    Modal.confirm({
      title: 'Tips',
      icon: <ExclamationCircleOutlined />,
      content: `Are you sure to delete ${currentMenu?.title}(${currentMenu?.path})?`,
      okText: 'Delete',
      cancelText: 'Cancel',
      onOk: () => {
        if (currentMenu?.id) {
          handleDeleteMenu({ id: currentMenu.id });
        }
      },
      okButtonProps: {
        danger: true,
      },
    });
  };
  return (
    <div className={styles['menu-right-card']}>
      <Spin spinning={loading}>
        <div className={styles['top']}>
          <h3>{isEdit ? '编辑菜单' : '新增菜单'}</h3>
          <div>
            {isEdit}
            {isEdit && (
              <Button
                type="text"
                icon={
                  <RixEngineFont
                    type="rix-trash"
                    style={{ fontSize: 24, color: 'red' }}
                  />
                }
                style={{ marginRight: 16 }}
                onClick={handleConfirmDelete}
              />
            )}
            <Button
              type="primary"
              onClick={handleReset}
              style={{ marginRight: 16 }}
            >
              重置
            </Button>
            <Button type="primary" onClick={handleSave}>
              提交
            </Button>
          </div>
        </div>
        <div className={styles['menu-form']}>
          <Form
            initialValues={MenuDefaultFormData}
            onFinish={onFinish}
            autoComplete="off"
            form={form}
            layout="vertical"
          >
            {isEdit && (
              <Form.Item label="ID" name="id" required>
                <Input disabled />
              </Form.Item>
            )}
            <Form.Item
              label="类型"
              name="type"
              rules={[{ required: true, message: '请选择类型' }]}
            >
              <NormalRadio
                options={[
                  { label: '菜单', value: 2 }, // 路径存在的时候就不是node了
                  { label: '按钮', value: 1 },
                ]}
                disabled={isEdit}
              />
            </Form.Item>
            <Form.Item
              label="名称"
              name="title"
              rules={[{ required: true, message: '请输入名称' }]}
            >
              <Input placeholder="请输入名称" />
            </Form.Item>
            <Form.Item
              label="父菜单"
              name="pid"
              rules={[{ required: false, message: '请选择父菜单' }]}
            >
              <Select
                options={allMenuList.map((item) => {
                  return {
                    label: `${item.title}(${
                      item.component ? item.component : '节点'
                    })`,
                    value: item.id,
                  };
                })}
                allowClear
                showSearch
                placeholder="请选择父菜单"
                optionFilterProp="children"
                filterOption={(input, option: any) =>
                  option &&
                  option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                }
              />
            </Form.Item>
            <Form.Item
              label="权限编码"
              name="access"
              tooltip="菜单的权限控制code, 如果不添加，将表示开放权限"
              getValueFromEvent={(e: any) => e.target.value.replace(/\s+/g, '')}
            >
              <Input placeholder="请输入权限编码" />
            </Form.Item>
            <Form.Item label="备注" name="remark">
              <Input placeholder="请输入备注" />
            </Form.Item>
          </Form>
        </div>
      </Spin>
    </div>
  );
};

export default Page;
