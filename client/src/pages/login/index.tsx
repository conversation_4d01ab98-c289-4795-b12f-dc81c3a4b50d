import React, { useState } from 'react';
import styles from './index.less';
import { Form, Input, Button, Layout, message, Alert } from 'antd';
import { history, useModel, useLocation } from '@umijs/max';
import { parse } from 'query-string';
import { login, getCurrentUser } from '@/services/api';

const LoginMessage: React.FC<{
  content: string;
}> = ({ content }) => (
  <Alert
    style={{
      marginBottom: 24,
    }}
    message={content}
    type="error"
    showIcon
  />
);

const Login: React.FC = () => {
  const [userLoginState, setUserLoginState] = useState<API.Result>();
  const { setInitialState } = useModel('@@initialState');
  const [loading, setLoading] = useState(false);
  const location = useLocation();

  const handleGoTo = (url: string) => {
    // 强制刷新跳转页面，预防左侧路由不更新
    window.location.href = window.location.origin + url;
    message.success('Login Success');
  };

  const fetchUserInfo = async () => {
    try {
      const msg = await getCurrentUser();
      return msg.data;
    } catch (error) {
      console.log('xx报错', error);
    }
    return undefined;
  };

  const handleLogin = async (values: UserAPI.LoginParams): Promise<any> => {
    try {
      // 登录
      const msg = await login({ ...values });
      // 如果失败去设置用户错误信息
      setUserLoginState(msg);
      if (msg.code === 0) {
        const userInfo = await fetchUserInfo();
        if (userInfo) {
          await setInitialState((s: any) => ({
            ...s,
            isCollapsed: s?.isCollapsed || true,
            currentUser: userInfo,
          }));
        }
        /** 此方法会跳转到 redirect 参数所在的位置 */
        if (!history || !userInfo) {
          return Promise.reject(new Error('Get User Info Failed!'));
        } else {
          const { redirect = '' } = parse(location.search);
          const defaultUrl =
            redirect && redirect !== '/user/login'
              ? (redirect as string)
              : '/welcome';
          return Promise.resolve({ defaultUrl, userInfo });
        }
      } else {
        return Promise.reject(new Error('Login Failed!'));
      }
    } catch (error: any) {
      return Promise.reject(new Error(error.message));
    }
  };

  const handleSubmit = async (values: UserAPI.LoginParams) => {
    setLoading(true);
    handleLogin(values)
      .then((val: any) => {
        // 默认保存到秒，也就是10位
        handleGoTo(val.defaultUrl);
      })
      .catch((err: any) => {
        console.log('error', err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Layout className={styles.container}>
      <div className={styles.top}>
        <div className={styles.logo}>
          <img src="/img/logo.png" alt="" />
        </div>
      </div>
      <div className={styles['login-container']}>
        <h3>Log in RixEngine</h3>
        <Form
          name="normal_login"
          className={styles['login-form']}
          onFinish={(values: UserAPI.LoginParams) => handleSubmit(values)}
        >
          {/* <Form.Item
            name='tnt_sign'
            rules={[{ required: true, message: 'Please Input Your Account ID!' }]}
          >
            <Input placeholder='Account ID' />
          </Form.Item> */}
          <Form.Item
            name="account_name"
            rules={[{ required: true, message: 'Please Input Your Username!' }]}
          >
            <Input placeholder="Username" style={{ height: 42 }} />
          </Form.Item>
          <Form.Item
            name="password"
            rules={[{ required: true, message: 'Please Input Your Password!' }]}
          >
            <Input
              type="password"
              placeholder="Password"
              style={{ height: 42 }}
            />
          </Form.Item>
          {userLoginState &&
            userLoginState.code !== 0 &&
            userLoginState.message && (
              <LoginMessage content={userLoginState.message} />
            )}
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              className={styles['login-form-button']}
              loading={loading}
            >
              Log in
            </Button>
          </Form.Item>
        </Form>
      </div>
    </Layout>
  );
};

export default Login;
