.container {
  height: 100%;
  width: 100%;
  background-image: url('/img/bg.png');
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: cover;
  background-clip: border-box;
}

.top {
  height: 64px;
  background: rgba(255, 255, 255, 0.4);
  border-bottom: 1px solid #e2eaeb;
  .logo {
    height: 64px;
    position: absolute;

    img {
      height: 100%;
    }
  }
}

.login-container {
  position: absolute;
  width: 352px;
  // height: 338px;
  right: 200px;
  top: 50%;
  transform: translateY(-50%);
  background: #ffffff;
  border-radius: 12px;
  padding: 0;
  h3 {
    font-weight: 700;
    font-size: 20px;
    color: #1568d4;
    padding-top: 40px;
    text-align: left;
    padding-left: 50px;
    margin-bottom: 0;
  }
  .login-form {
    padding: 20px 40px 0;
    input {
      font-size: 16px;
    }
    :global {
      .ant-input-affix-wrapper {
        border-radius: 6px;
      }
      .ant-form-item {
        margin-bottom: 16px;
      }
      .ant-form-item-control {
        font-size: 16px;
      }
    }
    .login-form-button {
      width: 100%;
      height: 42px;
      margin-top: 8px;
      margin-bottom: 24px;
      font-size: 16px;
      font-weight: bold;
    }
  }
}

@media screen and (max-width: 640px) {
  .top {
    z-index: 2;
  }
  .container {
    position: relative;
    background: none;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      padding-top: 64px;
      z-index: 0;
      background-image: url('/img/bg.png');
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: cover;
      background-clip: border-box;
      filter: blur(5px);
    }
  }
  .login-container {
    z-index: 2;
    right: 50%;
    transform: translate(50%, -50%);
  }
}
