/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-27 19:52:07
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 17:20:03
 * @Description:
 */

// ?libs
import moment from 'moment';
import { RefObject, useEffect, useState } from 'react';
import { useModel } from 'umi';

// ?types
import type {
  SearchResultItem,
  TopBarRef,
  TopBarSearchItem,
} from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
import { SorterResult } from 'antd/lib/table/interface';

// ?components
import PageContainer from '@/components/RightPageContainer';
import BackTable from '@/components/Table/BackTable';

// ?constants
import {
  AllColumns,
  BreadOptions,
  CheckboxUniqueKeyOptions,
  DefaultColumnKeys,
  DefaultDimension,
  FormatExportValueMap,
  SearchOptions,
} from '@/constants/data-report/monthly-report';

// ?utils
import { downloadCsv, fetchData } from '@/utils';

// ?api
import { DateType } from '@/constants/data-report/full-report';
import { getMonthlyReportList } from '@/services/api';
import { formatExportData } from '@/utils/export-file';

export default function Monthly() {
  const { tenantList, reload: reloadTntList } = useModel('useTenantList');
  const [searchOptions, setSearchOptions] =
    useState<TopBarSearchItem[]>(SearchOptions);
  const [dataSource, setDataSource] = useState<
    API.BackResult<FullReportingAPI.MonthlyReportItem>
  >({ data: [], total: 0 });
  const [columnKeys, setColumnKeys] = useState(DefaultColumnKeys);
  const [columns, setColumns] =
    useState<ColumnProps<FullReportingAPI.MonthlyReportItem>[]>(AllColumns);
  const [defaultSearchValue, setDefaultSearchValue] = useState<any>({});
  const [defaultParams, setDefaultParams] = useState<any>({});
  const [searchValue, setSearchValue] = useState<any>();
  const [loading, setLoading] = useState(false);
  const [sortedInfo, setSortedInfo] = useState<
    SorterResult<FullReportingAPI.MonthlyReportItem>
  >({
    columnKey: 'buyer_net_revenue',
    order: 'descend',
  });
  const [allColumns, setAllColumns] =
    useState<ColumnProps<FullReportingAPI.MonthlyReportItem>[]>(AllColumns);

  useEffect(() => {
    handleFillColumns();
    if (!tenantList || !tenantList.length) {
      reloadTntList();
    }
  }, []);

  useEffect(() => {
    handleFillColumns();
  }, [columnKeys, allColumns]);

  useEffect(() => {
    if (Array.isArray(tenantList)) {
      handleSearchOptions();
    }
  }, [tenantList]);
  useEffect(() => {
    if (sortedInfo) {
      const tmp: ColumnProps<FullReportingAPI.MonthlyReportItem>[] =
        AllColumns.map((v) => {
          return {
            ...v,
            sortOrder:
              v.sorter && sortedInfo.columnKey === v.dataIndex
                ? sortedInfo.order
                : null,
          };
        });
      setAllColumns(tmp);
    }
  }, [sortedInfo]);

  const handleFillColumns = () => {
    if (Array.isArray(columnKeys)) {
      const tmp = allColumns.filter((v) =>
        columnKeys.includes((v.key as string) || (v.dataIndex as string)),
      );
      setColumns(tmp);
    }
  };

  // 生成月份数据
  const generayeMonthData = () => {
    let diff = moment().diff(moment('2024').startOf('month'), 'months');
    const dayNumberInMonth = moment().date();
    // 一号的UTC+0 03:00之前
    if (dayNumberInMonth === 1 && moment().hours() < 3) {
      diff--;
    }
    const monthOptions = [];
    let year = 2024;
    for (let i = 0; i < diff; i++) {
      const month = (i % 12) + 1;
      if (month === 1 && i !== 0) {
        year++;
      }
      monthOptions.push({
        label: `${year}年${month}月`,
        value: moment(`${year}-${month}`, 'YYYY-MM').format('YYYYMMDD'),
        key: i,
      });
    }
    return monthOptions;
  };
  // 默认的值
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    const monthOptions = generayeMonthData();
    const index = options.findIndex((item) => item.key === 'date');

    if (index !== -1) {
      const lastMonthStart = monthOptions[monthOptions.length - 1].value;
      const lastMonthEnd = moment(lastMonthStart, 'YYYYMMDD').endOf('month');
      options[index].options = monthOptions;

      const params = {
        start: 0,
        end: 50,
        start_date: lastMonthStart,
        end_date: lastMonthEnd.format('YYYYMMDD'),
        columns: [],
      };

      setDefaultParams(params);
      const tmp = {
        date: [lastMonthStart],
        columns: [...DefaultDimension],
      };
      setDefaultSearchValue(tmp);
      setSearchValue(tmp);
    }
    const tntIndex = options.findIndex((item) => item.key === 'tnt_id');
    if (tntIndex !== -1) {
      const tntOptions = tenantList?.map((item: TenantAPI.TenantListItem) => {
        return {
          label: `${item.tnt_name}(${item.tnt_id})`,
          value: item.tnt_id,
        };
      });
      options[tntIndex].options = tntOptions;
    }

    setSearchOptions(options);
  };

  const handleExport = async (
    columns: ColumnProps<FullReportingAPI.MonthlyReportItem>[],
    sourceData?: API.BackResult<FullReportingAPI.MonthlyReportItem>,
  ) => {
    const fileName = `report_${new Date().getTime()}`;
    const fields: any = columns.map((item) => {
      return {
        label: item.title,
        value: item.dataIndex,
      };
    });
    const data = await formatExportData(
      sourceData?.data || dataSource.data,
      FormatExportValueMap,
    );

    downloadCsv(fileName, data || [], { fields });
  };

  const handleColumns = (val: SearchResultItem[]) => {
    const column_keys = ['month'];
    setColumnKeys([...column_keys, ...DefaultColumnKeys]);
  };

  const handleGetSearchParams = (
    start: number,
    end: number,
    val: SearchResultItem[],
  ) => {
    const defaultParams: Record<string, any> = {
      start: start || 0,
      end: end || 50,
      columns: ['month', 'tnt_id'],
    };

    for (const item of val) {
      if (item.value !== undefined && item.value !== null) {
        if (item.key === 'date') {
          defaultParams.months = item.value;
        } else {
          defaultParams[item.key] = item.value;
        }
      }
    }

    return defaultParams;
  };
  const onSuccess = (
    data: API.BackResult<FullReportingAPI.MonthlyReportItem>,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.MonthlyReportItem>[],
  ) => {
    const tmp = data.data;
    if (isDownloadAll) {
      handleExport(columns || [], { data: tmp, total: data.total });
    } else {
      setDataSource({ data: tmp, total: data.total });
    }
  };

  const getOrderAndKey = () => {
    let order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    if (!columnKeys.includes('month') && !columnKeys.includes('day')) {
      order_key = ['tnt_id'];
    }
    return {
      order,
      order_key,
    };
  };

  const getTableData = (
    value: any,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.MonthlyReportItem>[],
  ) => {
    let { order, order_key } = getOrderAndKey();
    const { columns: tmpColumns } = value;
    if (
      !tmpColumns?.includes('month') &&
      !tmpColumns?.includes('day') &&
      order_key.includes('date')
    ) {
      order_key = ['buyer_net_revenue'];
    }

    const params = {
      start: 0,
      end: 50,
      split_time: DateType.Month,
      order_key,
      order,
      ...value,
    };
    fetchData({
      setLoading,
      request: getMonthlyReportList,
      params,
      onSuccess: (data) => onSuccess(data, isDownloadAll, columns),
    });
  };

  const handleSortChange = async (
    page: number,
    size: number,
    search: SearchResultItem[],
    sorter: any,
    topBarRef: RefObject<TopBarRef>,
  ) => {
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });

    // TODO 只做校验，后续需要将 搜索 和 排序的 逻辑统一
    await topBarRef.current?.getFormInstance()?.validateFields();

    let params = defaultParams || {};
    if (search.length) {
      const start = size * (page - 1);
      const end = size * page;
      params = handleGetSearchParams(start, end, search);
    }
    let key = 'desc';
    let order_key = ['date'];
    if (order) {
      key = order === 'ascend' ? 'asc' : 'desc';
      order_key = [field];
    }
    params.order = key;
    params.order_key = order_key;
    params.isPaing = true;
    getTableData(params);
  };

  const handleSearchChange = (
    start: number,
    end: number,
    val: SearchResultItem[],
    isPaing?: boolean,
  ) => {
    if (val.length) {
      handleColumns(val);
    }
    const params = handleGetSearchParams(start, end, val);
    getTableData({ ...params, isPaing });
  };
  return (
    <PageContainer flexDirection="column" options={BreadOptions}>
      <BackTable<FullReportingAPI.MonthlyReportItem>
        pageTitle="Monthly Billing Reporting"
        pagination={{ pageSize: 500, pageSizeOptions: [500] }}
        searchOptions={searchOptions}
        columns={columns}
        rowKey={'id'}
        isExport={true}
        isFold={true}
        scroll={{ x: 1000, y: 'auto' }} // 非动态， 需要自己指定
        handleExport={handleExport}
        labelWidth={110}
        handleSearch={handleSearchChange}
        tableData={dataSource}
        loading={loading}
        defaultParams={defaultParams}
        defaultSearchValue={defaultSearchValue}
        searchValue={searchValue}
        sortDirections={['descend', 'ascend', 'descend']}
        handleSortChange={handleSortChange}
        maxRange={93}
        checkboxUniqueKeyOptions={CheckboxUniqueKeyOptions}
      />
    </PageContainer>
  );
}
