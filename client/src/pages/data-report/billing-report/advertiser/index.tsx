/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-27 19:52:07
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 17:20:03
 * @Description:
 */

// ?libs
import moment, { Moment } from 'moment';
import { RefObject, useEffect, useState } from 'react';
import { useModel } from 'umi';

// ?types
import type { SearchResultItem, TopBarRef, TopBarSearchItem } from '@/components/TopBar';
import { ColumnProps } from 'antd/lib/table';
import { SorterResult } from 'antd/lib/table/interface';

// ?components
import PageContainer from '@/components/RightPageContainer';
import BackTable from '@/components/Table/BackTable';

// ?constants
import {
  AllColumns,
  BreadOptions,
  CheckboxUniqueKeyOptions,
  DefaultColumnKeys,
  DefaultDimension,
  FormatExportValueMap,
  SearchOptions,
} from '@/constants/data-report/advertiser-billing-report';

// ?utils
import { downloadCsv, fetchData } from '@/utils';

// ?api
import { DateType } from '@/constants/data-report/full-report';
import { PartnerType } from '@/constants/partner';
import {
  downloadAdvertiserBillingList,
  getAdvertiserBillingList,
} from '@/services/api';
import { formatExportData } from '@/utils/export-file';
import { updateColumnsByDateRange } from '@/utils/report/billing';
import { FormInstance } from 'antd';
import ExportNotification from '../../components/ExportNotification';

export default function Advertiser() {
  const { allDemandList: demandList, reload: reloadDemand } =
    useModel('useAllDemandList');
  const { allSupplyList: sellerList, reload: reloadSupply } =
    useModel('useAllSupplyList');
  const { tenantList, reload: reloadTntList } = useModel('useTenantList');
  const { dataSource: partnerList, reload: reloadPartner } =
    useModel('usePartnerList');
  const { adSizeOptions, fetchAdSize } = useModel('useAdSizeOptions');

  const [searchOptions, setSearchOptions] =
    useState<TopBarSearchItem[]>(SearchOptions);
  const [dataSource, setDataSource] = useState<
    API.BackResult<FullReportingAPI.BillingListItem>
  >({ data: [], total: 0 });
  const [columnKeys, setColumnKeys] = useState(DefaultColumnKeys);
  const [columns, setColumns] =
    useState<ColumnProps<FullReportingAPI.BillingListItem>[]>(AllColumns);
  const [defaultSearchValue, setDefaultSearchValue] = useState<any>({});
  const [defaultParams, setDefaultParams] = useState<any>({});
  const [searchValue, setSearchValue] = useState<any>();
  const [loading, setLoading] = useState(false);
  const [sortedInfo, setSortedInfo] = useState<
    SorterResult<FullReportingAPI.BillingListItem>
  >({
    columnKey: 'buyer_net_revenue',
    order: 'descend',
  });
  const [allColumns, setAllColumns] =
    useState<ColumnProps<FullReportingAPI.BillingListItem>[]>(AllColumns);
  const [totalCount, setTotalCount] = useState<number>(0);

  const [openExport, setOpenExport] = useState(false);
  const [exportParams, setExportParams] = useState<any>({});
  const [notifyKey, setNotifyKey] = useState<string>('');
  const [downloadData, setDownloadData] = useState<any>({});

  useEffect(() => {
    handleFillColumns();
    if (!demandList || !demandList.length) {
      reloadDemand();
    }

    if (!sellerList || !sellerList.length) {
      reloadSupply();
    }
    if (!tenantList || !tenantList.length) {
      reloadTntList();
    }
    fetchAdSize();
    reloadPartner();
  }, []);

  useEffect(() => {
    handleFillColumns();
  }, [columnKeys, allColumns]);

  useEffect(() => {
    if (
      Array.isArray(demandList) &&
      Array.isArray(sellerList) &&
      Array.isArray(partnerList) &&
      Array.isArray(adSizeOptions)
    ) {
      handleSearchOptions();
    }
  }, [demandList, sellerList, partnerList, adSizeOptions]);
  useEffect(() => {
    if (sortedInfo) {
      const tmp: ColumnProps<FullReportingAPI.BillingListItem>[] =
        AllColumns.map((v) => {
          return {
            ...v,
            sortOrder:
              v.sorter && sortedInfo.columnKey === v.dataIndex
                ? sortedInfo.order
                : null,
          };
        });
      setAllColumns(tmp);
    }
  }, [sortedInfo]);
  // 默认的值
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    if (Array.isArray(demandList)) {
      const dOptions: any[] = demandList.map(
        (item: ConfigAPI.DemandListItem) => {
          return {
            label: `${item.buyer_name}(${item.buyer_id}-${item.tnt_id})`,
            value: item.buyer_id,
          };
        },
      );
      const dIndex = options.findIndex((item) => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
    }
    if (Array.isArray(sellerList)) {
      const sOptions: any[] = sellerList.map(
        (item: ConfigAPI.SupplyListItem) => {
          return {
            label: `${item.seller_name}(${item.seller_id}-${item.tnt_id})`,
            value: item.seller_id,
          };
        },
      );
      const sIndex = options.findIndex((item) => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }
    const index = options.findIndex((item) => item.key === 'date');

    if (index !== -1) {
      const lastDay = moment().subtract(1, 'day').format('YYYYMMDD');
      const lastWeek = moment().subtract(3, 'days').format('YYYYMMDD');
      options[index].value = [
        moment(lastWeek, 'YYYYMMDD'),
        moment(lastDay, 'YYYYMMDD'),
      ];
      const params = {
        start: 0,
        end: 50,
        start_date: lastWeek,
        end_date: lastDay,
        columns: [],
      };
      setDefaultParams(params);
      const tmp = {
        date: [moment(lastWeek, 'YYYYMMDD'), moment(lastDay, 'YYYYMMDD')],
        columns: [...DefaultDimension],
      };
      setDefaultSearchValue(tmp);
      setSearchValue(tmp);
    }
    const partnerIndex = options.findIndex(
      (item) => item.key === 'partner_id',
    );
    if (partnerIndex !== -1) {
      const partnerOptions = partnerList
        .filter((item) => item.type !== PartnerType.Publisher)
        ?.map((item: PartnerAPI.PartnerListItem) => {
          return {
            label: `${item.partner_name}-(${item.partner_id}-${item.tnt_id})`,
            value: item.partner_id,
          };
        });
      options[partnerIndex].options = partnerOptions;
    }
    const tntIndex = options.findIndex((item) => item.key === 'tnt_id');
    if (tntIndex !== -1) {
      const tntOptions = tenantList?.map((item: TenantAPI.TenantListItem) => {
        return {
          label: `${item.tnt_name}(${item.tnt_id})`,
          value: item.tnt_id,
        };
      });
      options[tntIndex].options = tntOptions;
    }

    const adSizeIndex = options.findIndex((item) => item.key === 'ad_size');
    if (adSizeIndex !== -1) {
      options[adSizeIndex].options = adSizeOptions;
    }

    setSearchOptions(options);
  };

  const handleFillColumns = () => {
    if (Array.isArray(columnKeys)) {
      const tmp = allColumns.filter((v) =>
        columnKeys.includes((v.key as string) || (v.dataIndex as string)),
      );
      setColumns(tmp);
    }
  };

  const handleExport = async (
    columns: ColumnProps<FullReportingAPI.BillingListItem>[],
    sourceData?: API.BackResult<FullReportingAPI.BillingListItem>,
  ) => {
    const fileName = `report_${new Date().getTime()}`;
    const fields: any = columns.map((item) => {
      return {
        label: item.title,
        value: item.dataIndex,
      };
    });
    const data = await formatExportData(
      sourceData?.data || dataSource.data,
      FormatExportValueMap,
    );
    downloadCsv(fileName, data || [], { fields });
  };

  const handleColumns = (val: SearchResultItem[]) => {
    const dimension = val.find(
      (item) =>
        item.key === 'columns' &&
        Array.isArray(item.value) &&
        item.value.length > 0,
    );

    if (!dimension) {
      setColumnKeys(DefaultColumnKeys);
      return;
    }

    const dimensionValues = dimension.value as string[];
    const timeColumns = [];

    if (dimensionValues.includes('month')) {
      timeColumns.push('month');
    } else if (dimensionValues.includes('day')) {
      timeColumns.push('day');
    }

    const otherDimensions = dimensionValues.filter(
      (v) => !['month', 'day'].includes(v),
    );
    const allColumns = [...timeColumns, ...otherDimensions];

    // dimension 中包含 app_bundle_id, country, ad_format 时，需要移除 request、response
    // 如果 val 中存在 app_bundle_id, country, ad_format，则需要移除 request、response
    const shouldRemoveRequest = ['app_bundle_id', 'country', 'ad_format'].some(
      (key) =>
        allColumns.includes(key) ||
        val.some((item) => item.key === key && item?.value?.length > 0),
    );

    const finalColumns = [
      ...allColumns,
      ...(shouldRemoveRequest
        ? DefaultColumnKeys.filter((key) => key !== 'request' && key !== 'response')
        : DefaultColumnKeys),
    ];

    setColumnKeys(finalColumns);
  };
  const handleSearchChange = (
    start: number,
    end: number,
    val: SearchResultItem[],
    isPaing?: boolean,
  ) => {
    if (val.length) {
      handleColumns(val);
    }
    const params = handleGetSearchParams(start, end, val);
    getTableData({ ...params, isPaing });
  };

  const handleGetSearchParams = (
    start: number,
    end: number,
    val: SearchResultItem[],
  ) => {
    const tmp = val.filter(
      (item) =>
        (Array.isArray(item.value) && item.value.length > 0) ||
        (!Array.isArray(item.value) && item.value),
    );

    const params: any = { start: start || 0, end: end || 50 };
    tmp.forEach((item) => {
      if (item.key === 'app_bundle_id') {
        params[item.key] = item.value
          .filter((v: string) => v && v.trim())
          .join(',');
      } else if (item.key === 'date') {
        const arr = (item.value as moment.Moment[]).map((v) =>
          v.format('YYYY-MM-DD'),
        );
        params['start_date'] = arr[0];
        params['end_date'] = arr[1];
      } else if (item.key === 'columns') {
        if (Array.isArray(item.value)) {
          // 预防污染
          const tmp = JSON.parse(JSON.stringify(item.value));
          const sizeIndex = tmp.findIndex((t: string) => t === 'ad_size');

          if (sizeIndex !== -1) {
            tmp[sizeIndex] = 'ad_width, ad_height';
          }
          params['columns'] = tmp;
        }
      } else {
        params[item.key] = item.value;
      }
    });
    return params;
  };
  const onSuccess = (
    data: API.BackResult<FullReportingAPI.BillingListItem>,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.BillingListItem>[],
  ) => {
    data.total === -1 ? (data.total = totalCount) : setTotalCount(data.total);
    const tmp = data.data.map((item) => {
      if (item.buyer_id && demandList) {
        const buyer = demandList.find(
          (v: any) => v.buyer_id === +item.buyer_id,
        );
        item.buyer = `${item.buyer_id}` || '-';
        if (buyer) {
          item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
        }
        item.buyer_id = item.buyer;
      }
      if (item.seller_id && sellerList) {
        const seller = sellerList.find(
          (v: any) => v.seller_id === +item.seller_id,
        );
        item.seller = `${item.seller_id}` || '-';
        if (seller) {
          item.seller = `${seller.seller_name}(${seller.seller_id})`;
        }
        item.seller_id = item.seller;
      }
      if (item.tnt_id && tenantList) {
        const tnt_id = tenantList.find(
          (v: { tnt_id: number }) => v.tnt_id === +item.tnt_id,
        );
        item.tnt_id = `${item.tnt_id}` || '-';
        if (tnt_id) {
          item.tnt_id = `${tnt_id.tnt_name}(${tnt_id.tnt_id})`;
        }
      }

      item.ad_size = `${item.ad_width} * ${item.ad_height}`;
      return item;
    });

    if (isDownloadAll) {
      handleExport(columns || [], { data: tmp, total: data.total });
    } else {
      setDataSource({ data: tmp, total: data.total });
    }
  };

  const getTableData = (
    value: any,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.BillingListItem>[],
  ) => {
    let { order, order_key } = getOrderAndKey();
    const { columns: tmpColumns } = value;
    if (
      !tmpColumns?.includes('month') &&
      !tmpColumns?.includes('day') &&
      order_key.includes('date')
    ) {
      order_key = ['buyer_net_revenue'];
    }
    // 当 columns 存在 month 时，split_time 为 3，存在 day 时，split_time 为 2，否则为 0
    const split_time = tmpColumns?.includes('month')
      ? DateType.Month
      : tmpColumns?.includes('day')
      ? DateType.Day
      : DateType.Null;
    const lastDay = moment().format('YYYYMMDD');
    const lastWeek = moment().subtract(2, 'days').format('YYYYMMDD');
    const params = {
      start: 0,
      end: 50,
      split_time,
      start_date: lastWeek,
      end_date: lastDay,
      order_key,
      order,
      ...value,
    };
    fetchData({
      setLoading,
      request: getAdvertiserBillingList,
      params,
      onSuccess: (data) => onSuccess(data, isDownloadAll, columns),
    });
  };
  const handleDisableDate = (currentDate: Moment) => {
    return (
      currentDate > moment().endOf('day').subtract(1, 'day') ||
      currentDate < moment().subtract(14, 'months').subtract(1, 'd') ||
      currentDate < moment('20230412')
    );
  };
  const getOrderAndKey = () => {
    let order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    if (!columnKeys.includes('month') && !columnKeys.includes('day')) {
      order_key = ['buyer_net_revenue'];
    }
    return {
      order,
      order_key,
    };
  };
  const handleDownloadAll = (
    data: any,
    columns: ColumnProps<FullReportingAPI.BillingListItem>[],
  ) => {
    const params = handleGetSearchParams(0, 50, data);
    delete params.start;
    delete params.end;
    const { order, order_key } = getOrderAndKey();
    const tmpColumns = params.columns;
    // 当 columns 存在 month 时，split_time 为 3，存在 day 时，split_time 为 2，否则为 0
    const split_time = tmpColumns?.includes('month')
      ? DateType.Month
      : tmpColumns?.includes('day')
      ? DateType.Day
      : DateType.Null;
    const tmp = {
      ...params,
      order_key,
      order,
      isAll: true,
      split_time,
    };

    fetchData({
      setLoading,
      request: downloadAdvertiserBillingList,
      params: tmp,
      onSuccess: (data) => {
        setExportParams(tmp);
        setOpenExport(true);
        setNotifyKey(new Date().getTime().toString());
        setDownloadData(data);
      },
    });
  };

  const handleSortChange = async (
    page: number,
    size: number,
    search: SearchResultItem[],
    sorter: any,
    topBarRef: RefObject<TopBarRef>,
  ) => {
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });

    // TODO 只做校验，后续需要将 搜索 和 排序的 逻辑统一
    await topBarRef.current?.getFormInstance()?.validateFields();

    let params = defaultParams || {};
    if (search.length) {
      const start = size * (page - 1);
      const end = size * page;
      params = handleGetSearchParams(start, end, search);
    }
    let key = 'desc';
    let order_key = ['date'];
    if (order) {
      key = order === 'ascend' ? 'asc' : 'desc';
      order_key = [field];
    }
    params.order = key;
    params.order_key = order_key;
    params.isPaing = true;
    getTableData(params);
  };

  const handleCloseExport = () => {
    setExportParams(undefined);
    setOpenExport(false);
  };

  const handleSearchValueChange = (
    changeValue: any,
    allChangeValue: any,
    _: boolean,
    form: FormInstance,
  ) => {
    if (changeValue.tnt_id) {
      if (Array.isArray(demandList)) {
        const dOptions = demandList
          .filter(
            (v: ConfigAPI.DemandListItem) =>
              !changeValue.tnt_id.length ||
              changeValue.tnt_id.includes(v.tnt_id),
          )
          .map((item: ConfigAPI.DemandListItem) => {
            return {
              label: `${item.buyer_name}(${item.buyer_id}-${item.tnt_id})`,
              value: item.buyer_id,
            };
          });
        const dIndex = searchOptions.findIndex(
          (item) => item.key === 'buyer_id',
        );
        if (dIndex !== -1) {
          searchOptions[dIndex].options = dOptions;
          setSearchOptions([...searchOptions]);
        }
      }
      if (Array.isArray(sellerList)) {
        const sOptions: any[] = sellerList
          .filter(
            (v: ConfigAPI.SupplyListItem) =>
              !changeValue.tnt_id.length ||
              changeValue.tnt_id.includes(v.tnt_id),
          )
          .map((item: ConfigAPI.SupplyListItem) => {
            return {
              label: `${item.seller_name}(${item.seller_id}-${item.tnt_id})`,
              value: item.seller_id,
            };
          });
        const sIndex = searchOptions.findIndex(
          (item) => item.key === 'seller_id',
        );
        if (sIndex !== -1) {
          searchOptions[sIndex].options = sOptions;
          setSearchOptions([...searchOptions]);
        }
      }
      if (Array.isArray(partnerList)) {
        const partnerOptions = partnerList
          .filter(
            (v: PartnerAPI.PartnerListItem) =>
              !changeValue.tnt_id.length ||
              changeValue.tnt_id.includes(v.tnt_id),
          )
          .map((item: PartnerAPI.PartnerListItem) => {
            return {
              label: `${item.partner_name}-${item.tnt_id}`,
              value: item.partner_id,
              type: item.type,
            };
          });
        const advPartnerOptions = partnerOptions.filter(
          (p) => p.type !== PartnerType.Publisher,
        );
        const advPartnerIndex = searchOptions.findIndex(
          (item) => item.key === 'partner_id',
        );
        if (advPartnerIndex !== -1) {
          searchOptions[advPartnerIndex].options = advPartnerOptions;
        }
        setSearchOptions([...searchOptions]);
      }
    } else if (changeValue.date) {
      const columnsOptions = searchOptions.find(
        (item) => item.key === 'columns',
      );

      if (!columnsOptions) {
        return;
      }

      const { updatedColumns, updatedColumnOptions, shouldDisableColumns } =
        updateColumnsByDateRange(
          changeValue.date,
          allChangeValue.columns ?? [],
          columnsOptions.options ?? [],
        );

      if (shouldDisableColumns) {
        form.setFieldValue('columns', updatedColumns);
      }

      setSearchOptions((prevOptions) =>
        prevOptions.map((item) =>
          item.key === 'columns'
            ? { ...item, options: updatedColumnOptions }
            : item,
        ),
      );
    }
  };
  return (
    <PageContainer flexDirection="column" options={BreadOptions}>
      <BackTable<FullReportingAPI.BillingListItem>
        pageTitle="Advertiser Billing Reporting"
        searchOptions={searchOptions}
        columns={columns}
        rowKey={'id'}
        isExport={true}
        isFold={true}
        scroll={{ x: 1000, y: 'auto' }} // 非动态， 需要自己指定
        handleExport={handleExport}
        labelWidth={110}
        handleSearch={handleSearchChange}
        tableData={dataSource}
        loading={loading}
        defaultParams={defaultParams}
        defaultSearchValue={defaultSearchValue}
        searchValue={searchValue}
        handleDisableDate={handleDisableDate}
        isExportAll={true}
        handleDownloadAll={handleDownloadAll}
        sortDirections={['descend', 'ascend', 'descend']}
        handleSortChange={handleSortChange}
        maxRange={183}
        checkboxUniqueKeyOptions={CheckboxUniqueKeyOptions}
        defaultDates={defaultSearchValue.date}
        dateRangeKeys={[
          'Yesterday',
          '3 Days',
          '7 Days',
          'This Month',
          'Last Month',
          'Last 3 Months',
          'Last 6 Months',
          'This Year',
          'Last Year',
        ]}
        handleSearchValueChange={handleSearchValueChange}
      />
      <ExportNotification
        request={downloadAdvertiserBillingList}
        open={openExport}
        onClose={handleCloseExport}
        params={exportParams}
        placement="topRight"
        downloadData={downloadData}
        notifyKey={notifyKey}
      />
    </PageContainer>
  );
}
