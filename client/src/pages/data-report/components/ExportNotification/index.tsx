/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-11-17 11:43:39
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-28 10:58:46
 * @Description:
 */

import CusLoadingIcon from '@/components/LoadingIcon';
import { ExportStatusMap } from '@/constants/data-report/export-log';
import { getExportedTaskStatus } from '@/services/data-report';
import { history } from '@umijs/max';
import { useRequest, useToggle, useUnmount } from 'ahooks';
import { notification } from 'antd';
import type { NotificationPlacement } from 'antd/es/notification';
import React, { useEffect, useRef } from 'react';
import styles from './index.less';

type ExportNotificationProps = {
  open: boolean;
  request: any;
  params: any;
  onClose: (key?: string) => void;
  placement: NotificationPlacement;
  notifyKey: string;
  downloadData?: {
    code: number;
    name: string;
    type: number;
  };
};
const ExportNotification: React.FC<ExportNotificationProps> = ({
  open,
  request,
  params,
  onClose,
  placement = 'topRight',
  notifyKey,
  downloadData,
}) => {
  const timeIntervalRef = useRef<number>(5000);
  const handleTaskStatus = (data: any) => {
    if (data?.data?.status === ExportStatusMap.Failed) {
      onDownloadError();
    }
    if (data?.data?.status === ExportStatusMap.Created) {
      // loading && ready && toggle();
      cancel();
      open &&
        notification.success({
          duration: 10,
          message: 'Export Result',
          description: (
            <div className={styles['export-notification-container']}>
              <p>{data?.data?.name || ''}</p>
              {data?.data?.url && (
                <a
                  href={data?.data?.url || ''}
                  target="_blank"
                  rel="noreferrer"
                  onClick={() => onClose}
                >
                  Download
                </a>
              )}
            </div>
          ),
          key: notifyKey,
          onClose,
        });
    }
  };
  const [ready, { toggle }] = useToggle(false);
  const { data, run, cancel, loading } = useRequest(getExportedTaskStatus, {
    pollingInterval: timeIntervalRef.current || 3000,
    onSuccess: handleTaskStatus,
    manual: true,
    pollingWhenHidden: false,
    // ready
  });

  useEffect(() => {
    if (open && params) {
      if (params.columns.includes('app_bundle_id')) {
        timeIntervalRef.current = 10 * 1000;
      }
      run({ name: downloadData?.name });
      notification.open({
        key: notifyKey,
        duration: 0,
        placement,
        message: 'Export Result',
        className: styles['export-notification'],
        description: (
          <div className={styles['export-notification-container']}>
            <div>
              <span>We are now exporting</span>
              {/* <span>We are now exporting, you can go to </span>
              <span className={styles['link-span']} onClick={handleClickGoto}>
                Export Log
              </span>
              <span> to get the result or waiting</span> */}
            </div>
            <div className={styles['export-loading']}>
              Exporting, please wait...
              <span className={styles['icon']}>
                <CusLoadingIcon />
              </span>
            </div>
          </div>
        ),
        onClose: handleClose,
      });
    }
  }, [open, params]);
  useUnmount(() => {
    notification.close(notifyKey);
  });
  const handleClickGoto = () => {
    history.push('/data-report/exported-report');
  };

  const handleClose = () => {
    onClose();
    console.log('close');
    cancel();
    ready && toggle();
    notification.close(notifyKey);
  };

  // const onDownloadSuccess = (data: { url: string; name: string }) => {
  //   if (open) {
  //     if (params.columns.includes('app_bundle_id')) {
  //       timeIntervalRef.current = 10 * 1000;
  //     }
  //     run({ name: data.name });
  //   } else {
  //     cancel();
  //     ready && toggle();
  //   }
  // };

  const onDownloadError = () => {
    if (open) {
      notification.error({
        message: 'Export Result',
        description: (
          <div className={styles['export-notification-container']}>
            <p>
              <span>Export failed, you can go to </span>
              <span className={styles['link-span']} onClick={handleClickGoto}>
                Export Log
              </span>
              <span> to get the reason</span>
            </p>
          </div>
        ),
        key: notifyKey,
        onClose,
      });
    } else {
      cancel();
      ready && toggle();
    }
  };

  return null;
};

export default ExportNotification;
