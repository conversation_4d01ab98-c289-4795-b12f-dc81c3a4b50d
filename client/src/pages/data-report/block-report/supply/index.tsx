/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:18:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-29 16:08:58
 * @Description:
 */
import PageContainer from '@/components/RightPageContainer';
import BackTable from '@/components/Table/BackTable';
import type {
  SearchResultItem,
  TopBarRef,
  TopBarSearchItem,
} from '@/components/TopBar';
import { DateType } from '@/constants/data-report/full-report';
import {
  DashboardAllColumns,
  DashboardBreadOptions,
  DashboardDefaultColumnKeys,
  DashboardDefaultDimension,
  DashBoardDefaultMetrics,
  DashboardSearchOption,
  FormatExportValueMap,
  HardCodeMetrics,
} from '@/constants/data-report/supply-block-report';
import ExportNotification from '@/pages/data-report/components/ExportNotification';
import {
  downloadPublisherBlockList,
  getPublisherBlockList,
} from '@/services/api';
import { downloadCsv, fetchData } from '@/utils';
import { formatExportData } from '@/utils/export-file';
import { useModel } from '@umijs/max';
import { FormInstance, message } from 'antd';
import { ColumnProps } from 'antd/lib/table';
import { SorterResult } from 'antd/lib/table/interface';
import moment, { Moment } from 'moment';
import React, { RefObject, useEffect, useRef, useState } from 'react';

const Page: React.FC = () => {
  // 筛选项：advertiser、publisher、tenant
  const { allDemandList: demandList, reload: reloadDemand } =
    useModel('useAllDemandList');
  const { allSupplyList: supplyList, reload: reloadSupply } =
    useModel('useAllSupplyList');
  const { tenantList, reload: reloadTntList } = useModel('useTenantList');
  // 当前table 的 展示列
  const [columns, setColumns] = useState<
    ColumnProps<FullReportingAPI.FullReportListItem>[]
  >([]);
  // 使用这个来控制下拉框的 选项
  const [searchOptions, setSearchOptions] = useState(DashboardSearchOption);
  const [dataSource, setDataSource] = useState<
    API.BackResult<FullReportingAPI.FullReportListItem>
  >({ data: [], total: 0 });
  const [loading, setLoading] = useState(false);
  // 默认的值，用来作为（列表数据）请求参数
  const [defaultParams, setDefaultParams] = useState<any>({});
  // 默认筛选项的值，初始化设置的
  const [defaultSearchValue, setDefaultSearchValue] = useState<any>({});
  // 搜索的值，初始化设置的，和defaultSearchValue值一样
  const [searchValue, setSearchValue] = useState<any>();
  // 优化点，统计数据总数
  const totalCountRef = useRef<number>(0);
  // 排序使用 默认排序
  const [sortedInfo, setSortedInfo] = useState<
    SorterResult<FullReportingAPI.FullReportListItem>
  >({
    columnKey: 'date',
    order: 'descend',
  });
  // 排序更新时，设置新的 allColumns，同时更新当前的 columns
  // 目的：存储排序状态
  const [allColumns, setAllColumns] =
    useState<ColumnProps<FullReportingAPI.FullReportListItem>[]>(
      DashboardAllColumns,
    );

  // 显示那些列
  const [columnKeys, setColumnKeys] = useState(DashboardDefaultColumnKeys);
  // 导出数据逻辑
  const [openExport, setOpenExport] = useState(false);
  const [exportParams, setExportParams] = useState<any>({});
  const [notifyKey, setNotifyKey] = useState<string>('');
  const [downloadData, setDownloadData] = useState<any>({});
  // 初始化 获取 选项数据
  useEffect(() => {
    handleFillColumns();
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!supplyList || !supplyList.length) {
      reloadSupply();
    }
    if (!tenantList || !tenantList.length) {
      reloadTntList();
    }
  }, []);
  // 设置 筛选项的 选项，以及赋 默认值
  useEffect(() => {
    handleSearchOptions();
  }, [demandList, supplyList, tenantList]);
  // 填充 pqs 相关的数据
  useEffect(() => {
    handleFillColumns();
  }, [columnKeys, allColumns]);

  // 更新 表格 排序状态（受控模式）
  useEffect(() => {
    if (sortedInfo) {
      const tmp: ColumnProps<FullReportingAPI.FullReportListItem>[] =
        DashboardAllColumns.map((v) => {
          return {
            ...v,
            // 排序受控属性
            sortOrder:
              v.sorter && sortedInfo.columnKey === v.dataIndex
                ? sortedInfo.order
                : null,
          };
        });
      setAllColumns(tmp);
    }
  }, [sortedInfo]);

  const handleFillColumns = () => {
    if (Array.isArray(columnKeys)) {
      // 通过 columnKeys 获取筛选项对应的列
      const tmp = allColumns.filter((v) =>
        columnKeys.includes((v.key as string) || (v.dataIndex as string)),
      );

      setColumns(tmp);
    }
  };
  // 默认的选项
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    // 为 buyer_id, seller_id 赋 选项
    if (Array.isArray(demandList) && Array.isArray(supplyList)) {
      const dOptions: any[] = demandList.map((item: any) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id}-${item.tnt_id})`,
          value: item.buyer_id,
        };
      });
      const sOptions = supplyList.map((item: any) => {
        return {
          label: `${item.seller_name}(${item.seller_id}-${item.tnt_id})`,
          value: item.seller_id,
        };
      });

      const dIndex = options.findIndex((item) => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
      const sIndex = options.findIndex((item) => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }

    // 存在 data 时，设置相关筛选项的默认值
    // metrics, date, columns
    const index = options.findIndex((item) => item.key === 'date');
    if (index !== -1) {
      const lastDay = moment().subtract(1, 'days').format('YYYYMMDD');
      options[index].value = [
        moment(lastDay, 'YYYYMMDD'),
        moment(lastDay, 'YYYYMMDD'),
      ];
      const params = {
        start: 0,
        end: 50,
        start_date: lastDay,
        end_date: lastDay,
        columns: [],
      };
      setDefaultParams(params);
      const tmp = {
        metrics: [...DashBoardDefaultMetrics],
        date: [moment(lastDay, 'YYYYMMDD'), moment(lastDay, 'YYYYMMDD')],
        columns: [...DashboardDefaultDimension],
      };
      setDefaultSearchValue(tmp);
      setSearchValue(tmp);
    }

    const tntIndex = options.findIndex((item) => item.key === 'tnt_id');
    if (tntIndex !== -1) {
      const tntOptions = tenantList?.map((item: TenantAPI.TenantListItem) => {
        return {
          label: `${item.tnt_name}(${item.tnt_id})`,
          value: item.tnt_id,
        };
      });
      options[tntIndex].options = tntOptions;
    }

    setSearchOptions(options);
  };
  // 处理导出数据
  const handleExport = async (
    columns: ColumnProps<FullReportingAPI.FullReportListItem>[],
    sourceData?: API.BackResult<FullReportingAPI.FullReportListItem>,
  ) => {
    const fileName = `report_${new Date().getTime()}`;
    const fields: any = columns.map((item) => {
      return {
        label: item.title,
        value: item.dataIndex,
      };
    });

    const data = await formatExportData(
      sourceData?.data || dataSource.data,
      FormatExportValueMap,
    );

    downloadCsv(fileName, data || [], { fields });
  };

  // 根据筛选项的值，处理列的展示：columnKeys
  const handleColumns = (searchItems: SearchResultItem[]) => {
    const dimension = searchItems.find(
      (item) =>
        item.key === 'columns' &&
        Array.isArray(item.value) &&
        item.value.length > 0,
    );

    let dim: any[] = dimension?.value || [];

    if (dim.length > 0) {
      const hasHour = dim.includes('day_hour');
      const hasDay = dim.includes('day');

      dim = dim.filter((v) => v !== 'day_hour' && v !== 'day');

      if (hasHour) {
        dim.unshift('day_hour');
      } else if (hasDay) {
        dim.unshift('day');
      }
    }

    const column_keys = [...dim, ...HardCodeMetrics];

    setColumnKeys(column_keys);
  };

  // 处理请求参数：去除空值，格式化参数
  const handleGetSearchParams = (
    start: number,
    end: number,
    val: SearchResultItem[],
  ) => {
    const tmp = val.filter(
      (item) =>
        (Array.isArray(item.value) && item.value.length > 0) ||
        (!Array.isArray(item.value) && item.value),
    );
    const params: any = { start: start || 0, end: end || 50 };
    tmp.forEach((item) => {
      if (item.key === 'date') {
        const [start_date, end_date] = (item.value as moment.Moment[]).map(
          (v) => v.format('YYYYMMDD'),
        );
        params['start_date'] = start_date;
        params['end_date'] = end_date;
      } else if (item.key === 'columns') {
        // 默认是day;
        params.split_time = DateType.Day;

        if (Array.isArray(item.value)) {
          // 预防污染
          const tmp = JSON.parse(JSON.stringify(item.value));
          const hourIndex = tmp.findIndex((t: string) => t === 'day_hour');
          const dayIndex = tmp.findIndex((t: string) => t === 'day');

          // Explicitly check for the presence of dimensions with clear precedence:
          // hour > day > null
          if (hourIndex !== -1) {
            // If 'day_hour' exists, set split_time to Hour regardless of 'day'
            params.split_time = DateType.Hour;
          } else if (dayIndex !== -1) {
            // Else if 'day' exists, set split_time to Day
            params.split_time = DateType.Day;
          } else {
            // Otherwise, set split_time to Null
            params.split_time = DateType.Null;
          }
          params['columns'] = tmp;
        }
      } else {
        params[item.key] = item.value;
      }
    });

    return params;
  };

  const onSuccess = (
    data: API.BackResult<FullReportingAPI.FullReportListItem>,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.FullReportListItem>[],
    hour?: boolean,
  ) => {
    if (data.total === -1) {
      data.total = totalCountRef.current;
    } else {
      totalCountRef.current = data.total;
    }
    const tmp = data.data.map((item) => {
      if (item.buyer_id && demandList) {
        const buyer = demandList.find(
          (v: any) => v.buyer_id === +item.buyer_id,
        );
        item.buyer = `${item.buyer_id}` || '-';
        if (buyer) {
          item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
        }
      }
      if (item.seller_id && supplyList) {
        const seller = supplyList.find(
          (v: { seller_id: number }) => v.seller_id === +item.seller_id,
        );
        item.seller = `${item.seller_id}` || '-';
        if (seller) {
          item.seller = `${seller.seller_name}(${seller.seller_id})`;
        }
      }
      if (item.tnt_id && tenantList) {
        const tnt = tenantList.find(
          (v: { tnt_id: number }) => v.tnt_id === +(item.tnt_id || 0),
        );
        item.tnt = `${item.tnt_id}` || '-';
        if (tnt) {
          item.tnt = `${tnt.tnt_name}(${tnt.tnt_id})`;
        }
        delete item.tnt_id;
      }
      if (item.source_tenant && tenantList) {
        const sourceTenant = tenantList.find(
          (v: { tnt_id: number }) => v.tnt_id === +(item.source_tenant || 0),
        );
        item.source_tenant = sourceTenant
          ? `${sourceTenant.tnt_name}(${sourceTenant.tnt_id})`
          : item.source_tenant;
      }

      return item;
    });

    // 值为 false
    if (isDownloadAll) {
      handleExport(columns || [], { data: tmp, total: data.total });
    } else {
      setDataSource({ data: tmp, total: data.total });
    }
  };

  const getTableData = (
    value: any,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.FullReportListItem>[],
  ) => {
    const { order, order_key } = getOrderAndKey();
    const lastDay = moment().format('YYYYMMDD');
    const params = {
      start: 0,
      end: 50,
      split_time: value.columns ? DateType.Day : DateType.Null,
      start_date: lastDay,
      end_date: lastDay,
      order_key,
      order,
      ...value,
      metrics: HardCodeMetrics,
      columns: [...new Set([...(value.columns || [])])],
    };

    fetchData({
      setLoading,
      request: getPublisherBlockList,
      params,
      onSuccess: (data) =>
        onSuccess(
          data,
          isDownloadAll,
          columns,
          params.split_time === DateType.Hour,
        ),
    });
  };
  // 设置新的 columnKeys，重新请求数据
  const handleSearchChange = (
    start: number,
    end: number,
    val: SearchResultItem[],
    isPaing?: boolean,
  ) => {
    if (val.length) {
      handleColumns(val);
    }
    const params = handleGetSearchParams(start, end, val);
    getTableData({ ...params, isPaing });
  };
  // 禁用筛选项中 date 组件选择时间的逻辑
  const handleDisableDate = (currentDate: Moment) => {
    return (
      currentDate > moment().endOf('day') ||
      currentDate < moment('20230601') ||
      currentDate < moment().subtract(1, 'months')
    );
  };

  const getOrderAndKey = () => {
    const order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    return {
      order,
      order_key,
    };
  };

  // 下载全部数据（最多1000）
  const handleDownloadAll = (
    data: any,
    columns: ColumnProps<FullReportingAPI.FullReportListItem>[],
  ) => {
    const params = handleGetSearchParams(0, 50, data);
    delete params.start;
    delete params.end;
    const { order, order_key } = getOrderAndKey();
    const tmp = {
      ...params,
      metrics: HardCodeMetrics,
      columns: [...new Set([...(params.columns || [])])],
      order_key,
      order,
    };

    fetchData({
      setLoading,
      request: downloadPublisherBlockList,
      params: tmp,
      onSuccess: (data) => {
        setExportParams(tmp);
        setOpenExport(true);
        setNotifyKey(new Date().getTime().toString());
        setDownloadData(data);
      },
    });
  };

  // 设置新的排序：表格展示（只排一列），数据请求
  const handleSortChange = async (
    page: number,
    size: number,
    search: SearchResultItem[],
    sorter: any,
    topBarRef: RefObject<TopBarRef>,
  ) => {
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });

    // TODO 只做校验，后续需要将 搜索 和 排序的 逻辑统一
    await topBarRef.current?.getFormInstance()?.validateFields();

    let params = defaultParams || {};
    if (search.length) {
      const start = size * (page - 1);
      const end = size * page;
      params = handleGetSearchParams(start, end, search);
    }
    let key = 'desc';
    let order_key = ['date'];
    if (order) {
      key = order === 'ascend' ? 'asc' : 'desc';
      order_key = [field];
    }
    params.order = key;
    params.order_key = order_key;
    // 优化统计数量，true 时不请求
    params.isPaing = true;
    getTableData(params);
  };

  const handleCloseExport = () => {
    setExportParams(undefined);
    setOpenExport(false);
  };
  // 如果 tnt_id 发生变化，需要重新设置 buyer_id, seller_id 的选项
  const handleSearchValueChange = (
    changeValue: any,
    allChangeValue: any,
    _: boolean,
    form: FormInstance,
  ) => {
    if (changeValue.tnt_id) {
      if (Array.isArray(demandList)) {
        const dOptions = demandList
          .filter(
            (v: ConfigAPI.DemandListItem) =>
              !changeValue.tnt_id.length ||
              changeValue.tnt_id.includes(v.tnt_id),
          )
          .map((item: ConfigAPI.DemandListItem) => {
            return {
              label: `${item.buyer_name}(${item.buyer_id}-${item.tnt_id})`,
              value: item.buyer_id,
            };
          });
        const dIndex = searchOptions.findIndex(
          (item) => item.key === 'buyer_id',
        );
        if (dIndex !== -1) {
          searchOptions[dIndex].options = dOptions;
          setSearchOptions([...searchOptions]);
        }
      }
      if (Array.isArray(supplyList)) {
        const sOptions: any[] = supplyList
          .filter(
            (v: ConfigAPI.SupplyListItem) =>
              !changeValue.tnt_id.length ||
              changeValue.tnt_id.includes(v.tnt_id),
          )
          .map((item: ConfigAPI.SupplyListItem) => {
            return {
              label: `${item.seller_name}(${item.seller_id}-${item.tnt_id})`,
              value: item.seller_id,
            };
          });
        const sIndex = searchOptions.findIndex(
          (item) => item.key === 'seller_id',
        );
        if (sIndex !== -1) {
          searchOptions[sIndex].options = sOptions;
          setSearchOptions([...searchOptions]);
        }
      }
    } else if (changeValue.columns) {
      // 如果是 新增了 hour 维度，限制选一天
      // 如果是 新增了 day 维度，限制选 30 天（默认）
      const hasHourDimension = changeValue.columns.includes('day_hour');
      // cache 缓存作用
      const [start, end] = allChangeValue.date;

      if (hasHourDimension && !start.isSame(end, 'day')) {
        form.setFieldValue('date', [end, end]);
        message.warn('The hour dimension limits the time range to one day');
      }

      setSearchOptions((prev) => {
        const timeLimit = hasHourDimension ? 1 : 30;

        const prevDate = prev.find((item) => item.key === 'date');
        // 避免重复设置
        if (prevDate?.timeLimit === timeLimit) {
          return prev;
        }

        return prev.map((item) => {
          if (item.key === 'date') {
            return { ...item, timeLimit };
          }
          return item;
        });
      });
    }
  };

  return (
    <PageContainer
      flexDirection="column"
      options={DashboardBreadOptions}
      id="block-report-supply"
    >
      <BackTable<FullReportingAPI.FullReportListItem>
        pageTitle="Publisher Block Reporting"
        searchOptions={searchOptions}
        columns={columns}
        rowKey={'id'}
        isExport={true}
        isFold={true}
        scroll={{ x: 1000, y: 'auto' }} // 非动态， 需要自己指定
        handleExport={handleExport}
        labelWidth={110}
        // 搜索列表逻辑
        handleSearch={handleSearchChange}
        tableData={dataSource}
        loading={loading}
        defaultParams={defaultParams}
        defaultSearchValue={defaultSearchValue}
        searchValue={searchValue}
        handleDisableDate={handleDisableDate}
        isExportAll={true}
        handleDownloadAll={handleDownloadAll}
        sortDirections={['descend', 'ascend', 'descend']}
        handleSortChange={handleSortChange}
        dateRangeKeys={[
          'Today',
          'Yesterday',
          'Before Yesterday',
          '7 Days',
          'This Month',
        ]}
        // 处理搜索项的值变化
        handleSearchValueChange={handleSearchValueChange}
      />
      <ExportNotification
        request={downloadPublisherBlockList}
        open={openExport}
        onClose={handleCloseExport}
        params={exportParams}
        placement="topRight"
        notifyKey={notifyKey}
        downloadData={downloadData}
      />
    </PageContainer>
  );
};

export default Page;
