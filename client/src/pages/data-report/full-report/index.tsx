/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 14:18:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-29 16:08:58
 * @Description:
 */
import PageContainer from '@/components/RightPageContainer';
import BackTable from '@/components/Table/BackTable';
import {
  DashboardAllColumns,
  DashboardBreadOptions,
  DashboardDefaultColumnKeys,
  DashboardDefaultDimension,
  DashBoardDefaultMetrics,
  DashboardSearchOption,
  DateType,
  FormatExportValueMap,
  SchainMap,
} from '@/constants/data-report/full-report';
import { useModel } from '@umijs/max';
import React, { RefObject, useEffect, useRef, useState } from 'react';
import ExportNotification from '../components/ExportNotification';
import styles from './index.less';

import EditButton from '@/components/Button/EditButton';
import NormalModal from '@/components/Modal/NormalModal';
import OriginalTable from '@/components/Table/OriginalTable';
import type {
  SearchResultItem,
  TopBarRef,
  TopBarSearchItem,
} from '@/components/TopBar';
import { AdFormatToLabel } from '@/constants';
import {
  QpsLevelType,
  RegionLabelMap,
  RegionListType,
} from '@/constants/config/strategy';
import { Country } from '@/constants/country';
import { ConfigQpsColumns } from '@/constants/data-report/config-qps';
import { demandCampaign } from '@/constants/demand/demand-campaign';
import { PartnerType } from '@/constants/partner';
import { TimeZoneMapLabel } from '@/constants/time-zone';
import { downloadDashboardList, getDashboardList } from '@/services/api';
import { downloadCsv, fetchData } from '@/utils';
import { formatExportData } from '@/utils/export-file';
import { FileSearchOutlined } from '@ant-design/icons';
import { ColumnProps } from 'antd/lib/table';
import { SorterResult } from 'antd/lib/table/interface';
import moment, { Moment } from 'moment';

const Page: React.FC = () => {
  // 筛选项：advertiser、publisher、qps、partner、tenant
  const { allDemandList: demandList, reload: reloadDemand } =
    useModel('useAllDemandList');
  const { allSupplyList: supplyList, reload: reloadSupply } =
    useModel('useAllSupplyList');
  // TODO 待优化。前端很重的逻辑，这里获取所有qps数据，然后根据相关id等信息来展示modal table
  const {
    data: qpsList,
    reload: reloadQpsList,
    loading: qpsLoading,
  } = useModel('useDashboardConfigQpsList');
  const { dataSource: partnerList, reload: reloadPartner } =
    useModel('usePartnerList');
  const { tenantList, reload: reloadTntList } = useModel('useTenantList');
  const { adSizeOptions, fetchAdSize } = useModel('useAdSizeOptions');
  // 当前table 的 展示列
  const [columns, setColumns] = useState<
    ColumnProps<FullReportingAPI.FullReportListItem>[]
  >([]);
  // 使用这个来控制下拉框的 选项
  const [searchOptions, setSearchOptions] = useState(DashboardSearchOption);
  const [dataSource, setDataSource] = useState<
    API.BackResult<FullReportingAPI.FullReportListItem>
  >({ data: [], total: 0 });
  const [loading, setLoading] = useState(false);
  // 默认的值，用来作为（列表数据）请求参数
  const [defaultParams, setDefaultParams] = useState<any>({});
  // 默认筛选项的值，初始化设置的
  const [defaultSearchValue, setDefaultSearchValue] = useState<any>({});
  // 搜索的值，初始化设置的，和defaultSearchValue值一样
  const [searchValue, setSearchValue] = useState<any>();
  // 优化点，统计数据总数
  const totalCountRef = useRef<number>(0);
  // 排序使用 默认排序
  const [sortedInfo, setSortedInfo] = useState<
    SorterResult<FullReportingAPI.FullReportListItem>
  >({
    columnKey: 'date',
    order: 'descend',
  });
  // 排序更新时，设置新的 allColumns，同时更新当前的 columns
  // 目的：存储排序状态
  const [allColumns, setAllColumns] =
    useState<ColumnProps<FullReportingAPI.FullReportListItem>[]>(
      DashboardAllColumns,
    );

  // 显示那些列
  const [columnKeys, setColumnKeys] = useState(DashboardDefaultColumnKeys);
  // qps 数据展示逻辑
  const [showQpsModal, setShowQpsModal] = useState(false);
  const [showConfigQps, setShowConfigQps] = useState(false);
  const [qpsModalData, setQpsModalData] = useState<StrategyAPI.QpsListItem[]>(
    [],
  );
  // 导出数据逻辑
  const [openExport, setOpenExport] = useState(false);
  const [exportParams, setExportParams] = useState<any>({});
  const [notifyKey, setNotifyKey] = useState<string>('');
  const [downloadData, setDownloadData] = useState<any>({});
  // 初始化 获取 选项数据
  useEffect(() => {
    handleFillColumns();
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!qpsList || !qpsList.length) {
      reloadQpsList();
    }
    if (!supplyList || !supplyList.length) {
      reloadSupply();
    }
    if (!partnerList || !partnerList.length) {
      reloadPartner();
    }
    if (!tenantList || !tenantList.length) {
      reloadTntList();
    }
    fetchAdSize();
  }, []);
  // 设置 筛选项的 选项，以及赋 默认值
  useEffect(() => {
    handleSearchOptions();
  }, [demandList, supplyList, partnerList]);
  // 填充 pqs 相关的数据
  useEffect(() => {
    handleFillColumns();
  }, [columnKeys, allColumns]);

  // 更新 表格 排序状态（受控模式）
  useEffect(() => {
    if (sortedInfo) {
      const tmp: ColumnProps<FullReportingAPI.FullReportListItem>[] =
        DashboardAllColumns.map((v) => {
          return {
            ...v,
            // 排序受控属性
            sortOrder:
              v.sorter && sortedInfo.columnKey === v.dataIndex
                ? sortedInfo.order
                : null,
          };
        });
      setAllColumns(tmp);
    }
  }, [sortedInfo]);

  // 更新当前的 columns 和 qps 相关的数据展示（dimension选中了seller_id，buyer_id）
  const handleFillColumns = () => {
    if (Array.isArray(columnKeys)) {
      // 通过 columnKeys 获取筛选项对应的列
      const tmp = allColumns.filter((v) =>
        columnKeys.includes((v.key as string) || (v.dataIndex as string)),
      );
      const advqpsColumnIndex = tmp.findIndex(
        (v) => v.key === 'adv_config_qps',
      );
      const pubqpsColumnIndex = tmp.findIndex(
        (v) => v.key === 'pub_config_qps',
      );

      const configQpsRender = (
        text: string,
        params: FullReportingAPI.FullReportListItem,
        colKey: string,
      ) => {
        return showConfigQps ? (
          params[colKey as keyof FullReportingAPI.FullReportListItem] ? (
            <div className={styles['qps-con']}>
              <span className={styles['text']}>
                {params[colKey as keyof FullReportingAPI.FullReportListItem]}
              </span>
              <EditButton
                onClick={(e) => handleQpsClick(e, params, colKey)}
                icon={<FileSearchOutlined />}
              ></EditButton>
            </div>
          ) : (
            '-'
          )
        ) : (
          <span>-</span>
        );
      };
      if (advqpsColumnIndex !== -1) {
        tmp[advqpsColumnIndex].render = (
          text: string,
          params: FullReportingAPI.FullReportListItem,
        ) => configQpsRender(text, params, 'adv_config_qps');
      }
      if (pubqpsColumnIndex !== -1) {
        tmp[pubqpsColumnIndex].render = (
          text: string,
          params: FullReportingAPI.FullReportListItem,
        ) => configQpsRender(text, params, 'pub_config_qps');
      }

      setColumns(tmp);
    }
  };
  // 默认的选项
  const handleSearchOptions = () => {
    const options: TopBarSearchItem[] = JSON.parse(
      JSON.stringify(searchOptions),
    );
    // 为 buyer_id, seller_id 赋 选项
    if (Array.isArray(demandList) && Array.isArray(supplyList)) {
      const dOptions: any[] = demandList.map((item: any) => {
        return {
          label: `${item.buyer_name}(${item.buyer_id}-${item.tnt_id})`,
          value: item.buyer_id,
        };
      });
      const sOptions = supplyList.map((item: any) => {
        return {
          label: `${item.seller_name}(${item.seller_id}-${item.tnt_id})`,
          value: item.seller_id,
        };
      });

      const dIndex = options.findIndex((item) => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
      const sIndex = options.findIndex((item) => item.key === 'seller_id');
      if (sIndex !== -1) {
        options[sIndex].options = sOptions;
      }
    }

    // 存在 data 时，设置相关筛选项的默认值
    // metrics, date, columns
    const index = options.findIndex((item) => item.key === 'date');
    if (index !== -1) {
      const lastDay = moment().subtract(1, 'days').format('YYYYMMDD');
      const lastWeek = moment().subtract(2, 'days').format('YYYYMMDD');
      options[index].value = [
        moment(lastWeek, 'YYYYMMDD'),
        moment(lastDay, 'YYYYMMDD'),
      ];
      const params = {
        start: 0,
        end: 50,
        start_date: lastWeek,
        end_date: lastDay,
        columns: [],
      };
      setDefaultParams(params);
      const tmp = {
        metrics: [...DashBoardDefaultMetrics],
        date: [moment(lastWeek, 'YYYYMMDD'), moment(lastDay, 'YYYYMMDD')],
        columns: [...DashboardDefaultDimension],
      };
      setDefaultSearchValue(tmp);
      setSearchValue(tmp);
    }
    // 为 hour, adv_partner_id, pub_partner_id, tnt_id 赋 选项
    const hourIndex = options.findIndex((item) => item.key === 'hour');
    if (hourIndex !== -1) {
      options[hourIndex].tooltip = TimeZoneMapLabel['Etc/UTC'];
    }
    const advPartnerIndex = options.findIndex(
      (item) => item.key === 'adv_partner_id',
    );
    if (advPartnerIndex !== -1) {
      const advPartnerOptions = partnerList
        ?.filter(
          (item: PartnerAPI.PartnerListItem) =>
            item.type !== PartnerType.Publisher,
        )
        .map((item: PartnerAPI.PartnerListItem) => {
          return {
            label: `${item.partner_name}-${item.tnt_id}`,
            value: item.partner_id,
          };
        });
      options[advPartnerIndex].options = advPartnerOptions;
    }
    const pubPartnerIndex = options.findIndex(
      (item) => item.key === 'pub_partner_id',
    );
    if (pubPartnerIndex !== -1) {
      const pubParterOptions = partnerList
        ?.filter(
          (item: PartnerAPI.PartnerListItem) =>
            item.type !== PartnerType.Advertiser,
        )
        .map((item: PartnerAPI.PartnerListItem) => {
          return {
            label: `${item.partner_name}-${item.tnt_id}`,
            value: item.partner_id,
          };
        });
      options[pubPartnerIndex].options = pubParterOptions;
    }
    const tntIndex = options.findIndex((item) => item.key === 'tnt_id');
    if (tntIndex !== -1) {
      const tntOptions = tenantList?.map((item: TenantAPI.TenantListItem) => {
        return {
          label: `${item.tnt_name}(${item.tnt_id})`,
          value: item.tnt_id,
        };
      });
      options[tntIndex].options = tntOptions;
    }
    const adSizeIndex = options.findIndex((item) => item.key === 'ad_size');
    if (adSizeIndex !== -1) {
      options[adSizeIndex].options = adSizeOptions;
    }

    setSearchOptions(options);
  };
  // 处理导出数据
  const handleExport = async (
    columns: ColumnProps<FullReportingAPI.FullReportListItem>[],
    sourceData?: API.BackResult<FullReportingAPI.FullReportListItem>,
  ) => {
    const fileName = `report_${new Date().getTime()}`;
    const fields: any = columns.map((item) => {
      return {
        label: item.title,
        value:
          item.dataIndex !== 'day' && item.dataIndex !== 'day_hour'
            ? item.dataIndex
            : 'date',
      };
    });

    const data = await formatExportData(
      sourceData?.data || dataSource.data,
      FormatExportValueMap,
    );

    downloadCsv(fileName, data || [], { fields });
  };

  // 根据筛选项的值，处理列的展示：columnKeys
  const handleColumns = (searchItems: SearchResultItem[]) => {
    const dimension = searchItems.find(
      (item) =>
        item.key === 'columns' &&
        Array.isArray(item.value) &&
        item.value.length > 0,
    );
    const metrics = searchItems.find(
      (item) =>
        item.key === 'metrics' &&
        Array.isArray(item.value) &&
        item.value.length > 0,
    );

    const is_hour =
      dimension?.value?.findIndex((v: any) => v === 'day_hour') > -1;
    const is_day = dimension?.value.findIndex((v: any) => v === 'day') > -1;
    const dim: any[] =
      dimension?.value.filter(
        (d: string) => !['day', 'day_hour'].includes(d),
      ) || [];
    const mt: any[] = metrics?.value || [];

    const column_keys = [...dim, ...mt];
    if (is_day && is_hour) {
      column_keys.unshift('day_hour');
    } else if ((is_day && !is_hour) || (!is_day && is_hour)) {
      is_day && column_keys.unshift('day');
      is_hour && column_keys.unshift('day_hour');
    }

    const includeAdvOrPub =
      dimension?.value.findIndex(
        (v: string) => v === 'seller_id' || v === 'buyer_id',
      ) > -1;

    if (includeAdvOrPub) {
      setShowConfigQps(true);
    } else {
      setShowConfigQps(false);
    }
    setColumnKeys(column_keys);
  };

  // 处理请求参数：去除空值，格式化参数
  const handleGetSearchParams = (
    start: number,
    end: number,
    val: SearchResultItem[],
  ) => {
    const tmp = val.filter(
      (item) =>
        (Array.isArray(item.value) && item.value.length > 0) ||
        (!Array.isArray(item.value) && item.value),
    );
    const params: any = { start: start || 0, end: end || 50 };
    tmp.forEach((item) => {
      if (item.key === 'app_bundle_id') {
        if (Array.isArray(item.value) && item.value.length) {
          params[item.key] = item.value
            .filter((v: string) => v && v.trim())
            .join(',');
        }
      } else if (item.key === 'date') {
        const [start_date, end_date] = (item.value as moment.Moment[]).map(
          (v) => v.format('YYYYMMDD'),
        );
        params['start_date'] = start_date;
        params['end_date'] = end_date;
      } else if (item.key === 'columns') {
        // 默认是day;
        params.split_time = DateType.Day;

        if (Array.isArray(item.value)) {
          // 预防污染
          const tmp = JSON.parse(JSON.stringify(item.value));
          const sizeIndex = tmp.findIndex((t: string) => t === 'ad_size');
          const hourIndex = tmp.findIndex((t: string) => t === 'day_hour');
          const dayIndex = tmp.findIndex((t: string) => t === 'day');

          if (sizeIndex !== -1) {
            tmp[sizeIndex] = 'ad_width, ad_height';
          }
          if (dayIndex === -1 && hourIndex === -1) {
            params.split_time = DateType.Null;
          }
          if (dayIndex !== -1) {
            params.split_time = DateType.Day;
          }
          if (hourIndex !== -1) {
            params.split_time = DateType.Hour;
          }

          params['columns'] = tmp;
        }
      } else if (['adv_partner_id', 'pub_partner_id'].includes(item.key)) {
        console.log(item.key);
        let partner_id: any[] = params['partner_id'] || [];
        if (Array.isArray(item.value) && item.value.length) {
          item.value.forEach((v: string) => {
            const partner = partnerList?.find(
              (item: PartnerAPI.PartnerListItem) => item.partner_id === +v,
            );
            const sids = partner?.sellers?.map((item) => item.seller_id) || [];
            const bids = partner?.buyers?.map((item) => item.buyer_id) || [];
            const tmpObj: any = { seller_partner_id: [], buyer_partner_id: [] };
            if (item.key === 'pub_partner_id') {
              tmpObj.seller_partner_id = sids;
            }
            if (item.key === 'adv_partner_id') {
              tmpObj.buyer_partner_id = bids;
            }
            partner_id.push(tmpObj);
          });
        }
        params['partner_id'] = partner_id;
      } else {
        params[item.key] = item.value;
      }
    });

    return params;
  };

  const onSuccess = (
    data: API.BackResult<FullReportingAPI.FullReportListItem>,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.FullReportListItem>[],
    hour?: boolean,
  ) => {
    if (data.total === -1) {
      data.total = totalCountRef.current;
    } else {
      totalCountRef.current = data.total;
    }
    const tmp = data.data.map((item) => {
      if (item.buyer_id && demandList) {
        const buyer = demandList.find(
          (v: any) => v.buyer_id === +item.buyer_id,
        );
        item.buyer = `${item.buyer_id}` || '-';
        if (buyer) {
          item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
        }
      }
      if (item.seller_id && supplyList) {
        const seller = supplyList.find(
          (v: { seller_id: number }) => v.seller_id === +item.seller_id,
        );
        item.seller = `${item.seller_id}` || '-';
        if (seller) {
          item.seller = `${seller.seller_name}(${seller.seller_id})`;
        }
      }
      if (item.tnt_id && tenantList) {
        const tnt = tenantList.find(
          (v: { tnt_id: number }) => v.tnt_id === +(item.tnt_id || 0),
        );
        item.tnt = `${item.tnt_id}` || '-';
        if (tnt) {
          item.tnt = `${tnt.tnt_name}(${tnt.tnt_id})`;
        }
        delete item.tnt_id;
      }
      if (item.source_tenant && tenantList) {
        const sourceTenant = tenantList.find(
          (v: { tnt_id: number }) => v.tnt_id === +(item.source_tenant || 0),
        );
        item.source_tenant = sourceTenant
          ? `${sourceTenant.tnt_name}(${sourceTenant.tnt_id})`
          : item.source_tenant;
      }
      item.seller_schain_complete =
        SchainMap[item.seller_schain_complete] || '-';
      item.buyer_schain_complete = SchainMap[item.buyer_schain_complete] || '-';
      item.country = (Country as any)[item.country] || 'Unknown';
      item.ad_format = AdFormatToLabel[item.ad_format];
      item.ad_size = `${item.ad_width} * ${item.ad_height}`;
      item.platform = (demandCampaign.MoblieOS as any)[item.platform];

      let adv_config_qps = 0;
      let pub_config_qps = 0;
      const advQps = qpsList.filter(
        (qps: StrategyAPI.QpsListItem) =>
          qps.level === QpsLevelType.demand && +item.buyer_id === qps.buyer_id,
      );
      const pubQps = qpsList.filter(
        (qps: StrategyAPI.QpsListItem) =>
          qps.level === QpsLevelType.supply &&
          +item.seller_id === qps.seller_id,
      );

      advQps.forEach(
        (qps: StrategyAPI.QpsListItem) => (adv_config_qps += qps.qps),
      );
      pubQps.forEach(
        (qps: StrategyAPI.QpsListItem) => (pub_config_qps += qps.qps),
      );

      if (item.region === RegionLabelMap.USE) {
        adv_config_qps = pub_config_qps = 0;
        const useAdvQps = advQps.filter(
          (item: StrategyAPI.QpsListItem) => item.region === RegionListType.USE,
        );
        const usePubQps = pubQps.filter(
          (item: StrategyAPI.QpsListItem) => item.region === RegionListType.USE,
        );
        useAdvQps.forEach(
          (qps: StrategyAPI.QpsListItem) => (adv_config_qps += qps.qps),
        );
        usePubQps.forEach(
          (qps: StrategyAPI.QpsListItem) => (pub_config_qps += qps.qps),
        );
      }
      if (item.region === RegionLabelMap.APAC) {
        adv_config_qps = pub_config_qps = 0;
        const apacAdvQps = advQps.filter(
          (item: StrategyAPI.QpsListItem) =>
            item.region === RegionListType.APAC,
        );
        const apacPubQps = pubQps.filter(
          (item: StrategyAPI.QpsListItem) =>
            item.region === RegionListType.APAC,
        );
        apacAdvQps.forEach(
          (qps: StrategyAPI.QpsListItem) => (adv_config_qps += qps.qps),
        );
        apacPubQps.forEach(
          (qps: StrategyAPI.QpsListItem) => (pub_config_qps += qps.qps),
        );
      }
      item.adv_config_qps = adv_config_qps;
      item.pub_config_qps = pub_config_qps;
      return item;
    });

    // 值为 false
    if (isDownloadAll) {
      handleExport(columns || [], { data: tmp, total: data.total });
    } else {
      setDataSource({ data: tmp, total: data.total });
    }
  };

  const getTableData = (
    value: any,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.FullReportListItem>[],
  ) => {
    const { order, order_key } = getOrderAndKey();
    const lastDay = moment().format('YYYYMMDD');
    const lastWeek = moment().subtract(2, 'days').format('YYYYMMDD');
    const params = {
      start: 0,
      end: 50,
      split_time: value.columns ? 1 : DateType.Null,
      start_date: lastWeek,
      end_date: lastDay,
      order_key,
      order,
      ...value,
    };
    fetchData({
      setLoading,
      request: getDashboardList,
      params,
      onSuccess: (data) =>
        onSuccess(data, isDownloadAll, columns, params.split_time === 2),
    });
  };
  // 设置新的 columnKeys，重新请求数据
  const handleSearchChange = (
    start: number,
    end: number,
    val: SearchResultItem[],
    isPaing?: boolean,
  ) => {
    if (val.length) {
      handleColumns(val);
    }
    const params = handleGetSearchParams(start, end, val);
    getTableData({ ...params, isPaing });
  };
  // 禁用筛选项中 date 组件选择时间的逻辑
  const handleDisableDate = (currentDate: Moment) => {
    return (
      currentDate > moment().endOf('day') ||
      currentDate < moment('20230601') ||
      currentDate < moment().subtract(6, 'months')
    );
  };

  const getOrderAndKey = () => {
    const order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    return {
      order,
      order_key,
    };
  };

  // 下载全部数据（最多1000）
  const handleDownloadAll = (
    data: any,
    columns: ColumnProps<FullReportingAPI.FullReportListItem>[],
  ) => {
    const params = handleGetSearchParams(0, 50, data);
    delete params.start;
    delete params.end;
    const { order, order_key } = getOrderAndKey();
    // TODO 导出的的时候也需要排序
    const tmp = {
      ...params,
      order_key,
      order,
    };

    fetchData({
      setLoading,
      request: downloadDashboardList,
      params: tmp,
      onSuccess: (data) => {
        setExportParams(tmp);
        setOpenExport(true);
        setNotifyKey(new Date().getTime().toString());
        setDownloadData(data);
      },
    });
  };

  // 设置新的排序：表格展示（只排一列），数据请求
  const handleSortChange = async (
    page: number,
    size: number,
    search: SearchResultItem[],
    sorter: any,
    topBarRef: RefObject<TopBarRef>,
  ) => {
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });

    // TODO 只做校验，后续需要将 搜索 和 排序的 逻辑统一
    await topBarRef.current?.getFormInstance()?.validateFields();

    let params = defaultParams || {};
    if (search.length) {
      const start = size * (page - 1);
      const end = size * page;
      params = handleGetSearchParams(start, end, search);
    }
    let key = 'desc';
    let order_key = ['date'];
    if (order) {
      key = order === 'ascend' ? 'asc' : 'desc';
      order_key = [field];
    }
    params.order = key;
    params.order_key = order_key;
    // 优化统计数量，true 时不请求
    params.isPaing = true;
    getTableData(params);
  };

  const handleQpsClick = (
    e: any,
    params: FullReportingAPI.FullReportListItem,
    colKey: string,
  ) => {
    const { buyer_id, seller_id } = params;
    console.log('colKey', colKey);
    if (colKey === 'adv_config_qps') {
      const allQps = qpsList.filter(
        (item: StrategyAPI.QpsListItem) => item.buyer_id === +buyer_id,
      );

      setQpsModalData(allQps);
    } else if (colKey === 'pub_config_qps') {
      const allQps = qpsList.filter(
        (item: StrategyAPI.QpsListItem) => item.seller_id === +seller_id,
      );
      setQpsModalData(allQps);
    } else {
      setQpsModalData([]);
    }
    setShowQpsModal(true);
  };

  const handleCancel = () => {
    setShowQpsModal(false);
  };

  const handleCloseExport = () => {
    setExportParams(undefined);
    setOpenExport(false);
  };
  // 如果 tnt_id 发生变化，需要重新设置 buyer_id, seller_id, adv_partner_id, pub_partner_id 的选项
  const handleSearchValueChange = (changeValue: any, allChangeValue: any) => {
    if (changeValue.tnt_id) {
      if (Array.isArray(demandList)) {
        const dOptions = demandList
          .filter(
            (v: ConfigAPI.DemandListItem) =>
              !changeValue.tnt_id.length ||
              changeValue.tnt_id.includes(v.tnt_id),
          )
          .map((item: ConfigAPI.DemandListItem) => {
            return {
              label: `${item.buyer_name}(${item.buyer_id}-${item.tnt_id})`,
              value: item.buyer_id,
            };
          });
        const dIndex = searchOptions.findIndex(
          (item) => item.key === 'buyer_id',
        );
        if (dIndex !== -1) {
          searchOptions[dIndex].options = dOptions;
          setSearchOptions([...searchOptions]);
        }
      }
      if (Array.isArray(supplyList)) {
        const sOptions: any[] = supplyList
          .filter(
            (v: ConfigAPI.SupplyListItem) =>
              !changeValue.tnt_id.length ||
              changeValue.tnt_id.includes(v.tnt_id),
          )
          .map((item: ConfigAPI.SupplyListItem) => {
            return {
              label: `${item.seller_name}(${item.seller_id}-${item.tnt_id})`,
              value: item.seller_id,
            };
          });
        const sIndex = searchOptions.findIndex(
          (item) => item.key === 'seller_id',
        );
        if (sIndex !== -1) {
          searchOptions[sIndex].options = sOptions;
          setSearchOptions([...searchOptions]);
        }
      }
      if (Array.isArray(partnerList)) {
        const partnerOptions = partnerList
          .filter(
            (v: PartnerAPI.PartnerListItem) =>
              !changeValue.tnt_id.length ||
              changeValue.tnt_id.includes(v.tnt_id),
          )
          .map((item: PartnerAPI.PartnerListItem) => {
            return {
              label: `${item.partner_name}-${item.tnt_id}`,
              value: item.partner_id,
              type: item.type,
            };
          });
        const advPartnerOptions = partnerOptions.filter(
          (p) => p.type !== PartnerType.Publisher,
        );
        const pubPartnerOptions = partnerOptions.filter(
          (p) => p.type !== PartnerType.Advertiser,
        );
        const advPartnerIndex = searchOptions.findIndex(
          (item) => item.key === 'adv_partner_id',
        );
        const pubPartnerIndex = searchOptions.findIndex(
          (item) => item.key === 'pub_partner_id',
        );
        if (advPartnerIndex !== -1) {
          searchOptions[advPartnerIndex].options = advPartnerOptions;
        }
        if (pubPartnerIndex !== -1) {
          searchOptions[pubPartnerIndex].options = pubPartnerOptions;
        }
        setSearchOptions([...searchOptions]);
      }
    }
  };

  return (
    <PageContainer
      flexDirection="column"
      options={DashboardBreadOptions}
      id="dashboard-page"
    >
      <BackTable<FullReportingAPI.FullReportListItem>
        pageTitle="Full Reporting"
        searchOptions={searchOptions}
        columns={columns}
        rowKey={'id'}
        isExport={true}
        isFold={true}
        scroll={{ x: 1000, y: 'auto' }} // 非动态， 需要自己指定
        handleExport={handleExport}
        labelWidth={110}
        // 搜索列表逻辑
        handleSearch={handleSearchChange}
        tableData={dataSource}
        loading={loading}
        defaultParams={defaultParams}
        defaultSearchValue={defaultSearchValue}
        searchValue={searchValue}
        handleDisableDate={handleDisableDate}
        isExportAll={true}
        handleDownloadAll={handleDownloadAll}
        sortDirections={['descend', 'ascend', 'descend']}
        handleSortChange={handleSortChange}
        // defaultDates={defaultSearchValue.date}
        dateRangeKeys={['Today', 'Yesterday', '3 Days', '7 Days']}
        // 处理搜索项的值变化
        handleSearchValueChange={handleSearchValueChange}
      />
      <NormalModal
        footer={null}
        title={'Config QPS'}
        onOk={() => {}}
        open={showQpsModal}
        onCancel={handleCancel}
        width="80vw"
        confirmLoading={false}
        maskClosable={true}
        style={{ top: 150 }}
        className={styles.addUserModal}
      >
        <OriginalTable
          columns={ConfigQpsColumns}
          dataSource={qpsModalData}
          rowKey={'id'}
          loading={qpsLoading}
          isBtnTable={true}
          scroll={{ y: 'calc(100vh - 220px)' }}
        ></OriginalTable>
      </NormalModal>
      <ExportNotification
        request={downloadDashboardList}
        open={openExport}
        onClose={handleCloseExport}
        params={exportParams}
        placement="topRight"
        notifyKey={notifyKey}
        downloadData={downloadData}
      />
    </PageContainer>
  );
};

export default Page;
