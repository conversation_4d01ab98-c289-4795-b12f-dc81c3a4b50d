/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-10-20 14:37:02
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-28 17:07:24
 * @Description:
 */
import React, { useEffect, useState } from 'react';
import { useModel } from '@umijs/max';
import BackTable from '@/components/Table/BackTable';
import {
  KwaiAllColumns,
  KwaiBreadOptions,
  KwaiChangeableColumns,
  DefaultMetrics,
  DefaultDimension,
  DefaultColumnKeys,
  KwaiSearchOption,
} from '@/constants/data-report/kwai-report';
import PageContainer from '@/components/RightPageContainer';
import type { SearchResultItem, TopBarSearchItem } from '@/components/TopBar';
import { fetchData, downloadCsv } from '@/utils';
import { ColumnProps } from 'antd/lib/table';
import moment, { Moment } from 'moment';
import { SorterResult } from 'antd/lib/table/interface';
import { getKwaiReportList, downloadKwaiReport } from '@/services/api';

const Page: React.FC = () => {
  const { allDemandList: demandList, reload: reloadDemand } =
    useModel('useAllDemandList');
  const { tenantList, reload: reloadTntList } = useModel('useTenantList');
  const [columns, setColumns] = useState<
    ColumnProps<FullReportingAPI.KwaiReportItem>[]
  >(KwaiChangeableColumns);
  const [searchOptions, setSearchOptions] = useState(KwaiSearchOption);
  const [dataSource, setDataSource] = useState<
    API.BackResult<FullReportingAPI.KwaiReportItem>
  >({ data: [], total: 0 });
  const [loading, setLoading] = useState(false);
  const [defaultParams, setDefaultParams] = useState<any>({});
  const [defaultSearchValue, setDefaultSearchValue] = useState<any>({});
  const [searchValue, setSearchValue] = useState<any>();
  // 排序使用 默认排序
  const [sortedInfo, setSortedInfo] = useState<
    SorterResult<FullReportingAPI.KwaiReportItem>
  >({
    columnKey: 'day',
    order: 'descend',
  });
  const [allColumns, setAllColumns] =
    useState<ColumnProps<FullReportingAPI.KwaiReportItem>[]>(KwaiAllColumns);
  // 显示那些列
  const [columnKeys, setColumnKeys] = useState([
    ...DefaultColumnKeys,
    ...DefaultMetrics,
  ]);

  useEffect(() => {
    handleFillColumns();
    if (!demandList || !demandList.length) {
      reloadDemand();
    }
    if (!tenantList || !tenantList.length) {
      reloadTntList();
    }
  }, []);

  useEffect(() => {
    if (Array.isArray(demandList) && Array.isArray(tenantList)) {
      handleSearchOptions(true);
    } else {
      handleSearchOptions(false);
    }
  }, [demandList, tenantList]);

  useEffect(() => {
    handleFillColumns();
  }, [columnKeys, allColumns]);

  useEffect(() => {
    if (sortedInfo) {
      const tmp: ColumnProps<FullReportingAPI.KwaiReportItem>[] =
        KwaiAllColumns.map((v) => {
          return {
            ...v,
            sortOrder:
              v.sorter && sortedInfo.columnKey === v.dataIndex
                ? sortedInfo.order
                : null,
          };
        });
      setAllColumns(tmp);
    }
  }, [sortedInfo]);

  const handleFillColumns = () => {
    if (Array.isArray(columnKeys)) {
      const tmp = allColumns.filter((v) =>
        columnKeys.includes((v.key as string) || (v.dataIndex as string)),
      );
      setColumns(tmp);
    }
  };

  // 默认的值
  const handleSearchOptions = (isFetch: boolean) => {
    const options: TopBarSearchItem[] = KwaiSearchOption.map((v) => ({
      ...v,
    }));
    if (Array.isArray(demandList)) {
      const dOptions: any[] = demandList.map(
        (item: ConfigAPI.DemandListItem) => {
          return {
            label: `${item.buyer_name}(${item.buyer_id}-${item.tnt_id})`,
            value: item.buyer_id,
          };
        },
      );

      const dIndex = options.findIndex((item) => item.key === 'buyer_id');
      if (dIndex !== -1) {
        options[dIndex].options = dOptions;
      }
    }
    if (Array.isArray(tenantList)) {
      const tOptions: any[] = tenantList.map(
        (item: TenantAPI.TenantListItem) => {
          return {
            label: `${item.tnt_name}(${item.tnt_id})`,
            value: item.tnt_id,
          };
        },
      );
      const dIndex = options.findIndex((item) => item.key === 'tnt_id');
      if (dIndex !== -1) {
        options[dIndex].options = tOptions;
      }
    }

    const index = options.findIndex((item) => item.key === 'date');
    if (index !== -1) {
      const start_date = moment().subtract(3, 'day');
      const end_date = moment().subtract(1, 'day');
      options[index].value = [start_date, end_date];
      const start_date_str = start_date.format('YYYY-MM-DD');
      const end_date_str = end_date.format('YYYY-MM-DD');
      const params = {
        start: 0,
        end: 50,
        start_date: start_date_str,
        end_date: end_date_str,
        dimension: ['day'],
      };
      setDefaultParams(params);
      const tmp = {
        date: [start_date, end_date],
        dimension: [...DefaultDimension],
        metrics: [...DefaultMetrics],
      };
      setDefaultSearchValue(tmp);
      setSearchValue(tmp);
      if (isFetch) {
        getTableData(params);
      }
    }
    setSearchOptions(options);
  };

  const handleExport = (
    columns: ColumnProps<FullReportingAPI.KwaiReportItem>[],
    sourceData?: API.BackResult<FullReportingAPI.KwaiReportItem>,
  ) => {
    const fileName = `report_${new Date().getTime()}`;
    const fields: any = columns.map((item) => {
      return {
        label: item.title,
        value: item.dataIndex,
      };
    });
    const data = sourceData?.data || dataSource.data;
    downloadCsv(fileName, data || [], { fields });
  };

  const handleColumns = (val: SearchResultItem[]) => {
    const dimension = val.find(
      (item) =>
        item.key === 'dimension' &&
        Array.isArray(item.value) &&
        item.value.length > 0,
    );
    const metrics = val.find(
      (item) =>
        item.key === 'metrics' &&
        Array.isArray(item.value) &&
        item.value.length > 0,
    );

    const mt: any[] = metrics?.value || [];
    const dim: any[] = dimension?.value || [];
    const column_keys = [...dim, ...mt];
    setColumnKeys(column_keys);
  };
  const handleSearchChange = (
    start: number,
    end: number,
    val: SearchResultItem[],
  ) => {
    if (val.length) {
      handleColumns(val);
    }
    const params = handleGetSearchParams(start, end, val);

    getTableData({ ...params });
  };

  const handleGetSearchParams = (
    start: number,
    end: number,
    val: SearchResultItem[],
  ) => {
    const tmp = val.filter(
      (item) =>
        (Array.isArray(item.value) && item.value.length > 0) ||
        (!Array.isArray(item.value) && item.value),
    );

    const params: any = { start: start || 0, end: end || 50 };
    tmp.forEach((item) => {
      if (item.key === 'app_id' || item.key === 'bundle') {
        if (Array.isArray(item.value) && item.value.length) {
          params[item.key] = item.value.filter((v: string) => v && v.trim());
        }
      } else if (item.key === 'date') {
        const arr = (item.value as moment.Moment[]).map((v) =>
          v.format('YYYY-MM-DD'),
        );
        params['start_date'] = arr[0];
        params['end_date'] = arr[1];
      } else {
        params[item.key] = item.value;
      }
      console.log('params', params);
    });

    return params;
  };

  const onSuccess = (
    data: API.BackResult<FullReportingAPI.KwaiReportItem>,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.KwaiReportItem>[],
  ) => {
    const tmp = data.data.map((item) => {
      if (item.buyer_id && demandList) {
        const buyer = demandList.find(
          (v: any) => v.buyer_id === +item.buyer_id,
        );
        item.buyer = `${item.buyer_id}` || '-';
        if (buyer) {
          item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
        }
      }
      if (item.tnt_id && tenantList) {
        const tenant = tenantList.find((v: any) => v.tnt_id === +item.tnt_id);
        item.tenant = `${item.tnt_id}` || '-';
        if (tenant) {
          item.tenant = `${tenant.tnt_name}(${tenant.tnt_id})`;
        }
      }
      return item;
    });
    if (isDownloadAll) {
      handleExport(columns || [], { data: tmp, total: data.total });
    } else {
      setDataSource({ data: tmp, total: data.total });
    }
  };

  const getOrderAndKey = () => {
    const order_key = [sortedInfo.columnKey as string];
    const order = sortedInfo.order === 'ascend' ? 'asc' : 'desc';
    return {
      order,
      order_key,
    };
  };

  const getTableData = (
    value: any,
    isDownloadAll?: boolean,
    columns?: ColumnProps<FullReportingAPI.KwaiReportItem>[],
  ) => {
    const { order, order_key } = getOrderAndKey();
    const params = {
      start: 0,
      end: 50,
      order_key,
      order,
      ...value,
    };

    fetchData({
      setLoading,
      request: getKwaiReportList,
      params,
      onSuccess: (data) => onSuccess(data, isDownloadAll, columns),
    });
  };

  const handleDownloadAll = (
    data: any,
    columns: ColumnProps<FullReportingAPI.KwaiReportItem>[],
  ) => {
    const params = handleGetSearchParams(0, 50, data);
    delete params.start;
    delete params.end;
    const { order, order_key } = getOrderAndKey();
    const tmp = {
      ...params,
      order_key,
      order,
    };
    fetchData({
      setLoading,
      request: downloadKwaiReport,
      params: tmp,
      onSuccess: (data) => onSuccess({ data, total: 0 }, true, columns),
    });
  };

  const handleSortChange = (
    page: number,
    size: number,
    search: SearchResultItem[],
    sorter: any,
  ) => {
    const { field, order } = sorter;
    setSortedInfo({ columnKey: field, order: order });
    let params = defaultParams || {};
    if (search.length) {
      const start = size * (page - 1);
      const end = size * page;
      params = handleGetSearchParams(start, end, search);
    }
    let key = 'desc';
    let order_key = ['day'];
    if (order) {
      key = order === 'ascend' ? 'asc' : 'desc';
      order_key = [field];
    }
    params.order = key;
    params.order_key = order_key;
    getTableData(params);
  };
  const handleDisableDate = (currentDate: Moment) => {
    return currentDate > moment().startOf('day');
  };

  const handleSearchValueChange = (changeValue: any, allChangeValue: any) => {
    if (changeValue.tnt_id) {
      if (Array.isArray(demandList)) {
        const dOptions: any[] = demandList
          .filter(
            (v: ConfigAPI.DemandListItem) =>
              !changeValue.tnt_id.length ||
              changeValue.tnt_id.includes(v.tnt_id),
          )
          .map((item: ConfigAPI.DemandListItem) => {
            return {
              label: `${item.buyer_name}(${item.buyer_id})`,
              value: item.buyer_id,
            };
          });
        const dIndex = searchOptions.findIndex(
          (item) => item.key === 'buyer_id',
        );
        if (dIndex !== -1) {
          searchOptions[dIndex].options = dOptions;
          setSearchOptions([...searchOptions]);
        }
      }
    }
  };
  return (
    <PageContainer flexDirection="column" options={KwaiBreadOptions}>
      <BackTable<FullReportingAPI.KwaiReportItem>
        pageTitle="Kwai Reporting"
        searchOptions={searchOptions}
        columns={columns}
        rowKey={'id'}
        isExport={true}
        isFold={true}
        defaultFold={true}
        scroll={{ x: 1000, y: 'auto' }} // 非动态， 需要自己指定
        handleExport={handleExport}
        labelWidth={110}
        handleSearch={handleSearchChange}
        tableData={dataSource}
        loading={loading}
        defaultParams={defaultParams}
        defaultSearchValue={defaultSearchValue}
        searchValue={searchValue}
        handleDisableDate={handleDisableDate}
        isExportAll={true}
        handleDownloadAll={handleDownloadAll}
        handleSortChange={handleSortChange}
        sortDirections={['descend', 'ascend', 'descend']}
        handleSearchValueChange={handleSearchValueChange}
      />
    </PageContainer>
  );
};

export default Page;
