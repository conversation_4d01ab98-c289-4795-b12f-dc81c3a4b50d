/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-29 11:37:05
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-03 16:21:24
 * @Description:
 */
declare namespace PrivateAPI {
  type BrandList = {
    id: number;
    brand_name: string;
    sj_domain: string;
    brand_logo_path: string;
    brand_favicon_path: string;
    created_time: string;
    tnt_name: string;
    tnt_id: number;
  };
  type AddBrand = {
    brand_name: string;
    brand_logo_path: string;
    brand_favicon_path: string;
  };

  type PixalateConfig = {
    use_rix_common: number;
    report_api?: string;
    report_api_key?: string;
    display_web_tag?: string;
    display_app_tag?: string;
    display_web_js?: string;
    display_app_js?: string;
    native_web_tag?: string;
    native_app_tag?: string;
    video_web_tag?: string;
    video_app_tag?: string;
  };

  type PixalateItem = {
    id: number;
    tnt_id: number;
    tnt_name: string;
    op_id: number;
    op_name: string;
    use_rix_common: number;
    pixalate_config: PixalateConfig;
  };

  type AddPixalateItem = {
    tnt_id: number;
  } & PixalateConfig;

  type EditPixalateItem = AddPixalateItem & {
    id: number;
  };
}
