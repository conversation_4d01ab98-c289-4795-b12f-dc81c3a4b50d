/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-29 11:36:54
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-30 19:03:20
 * @Description:
 */
import { request } from 'umi';

export async function getTenantPrivatizationList(
  body: PrivateAPI.BrandList,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/privatization/getTenantBrandList', {
    data: body,
    ...(options || {}),
  });
}
export async function addTenantPrivatization(
  body: PrivateAPI.AddBrand,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/privatization/addTenantBrand', {
    data: body,
    ...(options || {}),
  });
}
export async function editTenantPrivatization(
  body: PrivateAPI.AddBrand,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/privatization/editTenantBrand', {
    data: body,
    ...(options || {}),
  });
}
export async function upload(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/privatization/upload', {
    data: body,
    ...(options || {}),
  });
}

export async function getTenantPixalateList(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<PrivateAPI.PixalateItem>>('/api/privatization/getTenantPixalateList', {
    data: body,
    ...(options || {}),
  });
}

export async function addTenantPixalate(
  body: PrivateAPI.AddPixalateItem,
  options?: { [key: string]: any },
) {
  return request<API.ResultWithType<boolean>>('/api/privatization/addTenantPixalate', {
    data: body,
    ...(options || {}),
  });
}

export async function editTenantPixalate(
  body: PrivateAPI.AddPixalateItem,
  options?: { [key: string]: any },
) {
  return request<API.ResultWithType<boolean>>('/api/privatization/editTenantPixalate', {
    data: body,
    ...(options || {}),
  });
}
