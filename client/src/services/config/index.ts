/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-18 17:13:20
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-03 17:44:08
 * @Description:
 */
import { request } from '@umijs/max';
export async function addEcprConfig(
  body?: any,
  options?: { [key: string]: any },
): Promise<API.Result> {
  return request<API.Result>('/api/ecpr/addEcprConfig', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getEcprConfigList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/ecpr/getEcprConfigList', {
    data: body || {},
    ...(options || {}),
  });
}

export async function editEcprConfig(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/ecpr/editEcprConfig', {
    data: body || {},
    ...(options || {}),
  });
}

export async function deleteEcprConfig(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/ecpr/deleteEcprConfig', {
    data: body || {},
    ...(options || {}),
  });
}

export async function addAtcWL(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/atc/addAtc', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function updateAtcWl(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/atc/updateAtc', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getActWlList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/atc/getActList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getSupplyList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/atc/getSupplyList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getDemandList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/atc/getDemandList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getKwaPlacementList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/kwa/getKwaList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function addKwaPlacement(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/kwa/addKwa', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function updateKwaPlacement(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/kwa/updateKwa', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getCustomPlacementList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/custom/getCustomList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function addCustomPlacement(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/custom/addCustom', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function updateCustomPlacement(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/custom/updateCustom', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getStgChainList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/stg/getStgChainList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function addStgChain(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/stg/addStgChain', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function updateStgChain(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/stg/updateStgChain', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
export async function deleteStgChain(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/stg/deleteStgChain', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getIvtConfigList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/ivt/getIvtConfigList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function addIvtConfig(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/ivt/addIvt', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function updateIvtConfig(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/ivt/updateIvt', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getPixlPrebidList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/pixalate-prebid/getPixlPrebidList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function addPixlPrebid(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/pixalate-prebid/addPixlPrebid', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function updatePixlPrebid(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/pixalate-prebid/updatePixlPrebid', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getStgChainListV2(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/stgv2/getStgChainList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function addStgChainV2(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/stgv2/addStgChain', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function updateStgChainV2(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/stgv2/updateStgChain', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
