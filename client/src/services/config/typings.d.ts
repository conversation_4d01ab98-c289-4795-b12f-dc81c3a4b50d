/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-18 17:13:32
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-03 16:52:33
 * @Description:
 */
declare namespace ConfigAPI {
  type EcprListItem = {
    id: number;
    type: number;
    server_region: number;
    filter_qps: number;
    newer_qps: number;
    tier_ecpr: string;
    tier_qps: string;
    op_id: number;
    status: number;
    tnt_id: number;
  };
  type EditEcpr = {
    id: number;
    type: number;
    server_region: number;
    filter_qps: number;
    newer_qps: number;
    tier_ecpr: string;
    tier_qps: string;
    op_id: number;
    status: number;
    tnt_id: number;
  };

  type AtcItem = {
    id: number;
    seller_id: number;
    seller_name: string;
    buyer_id: number;
    buyer_name: string;
    tnt_id: number;
    tnt_name: string;
    region: string;
    country: string;
    bundle: string;
    ad_format: string;
    ad_size: string;
    expired: number;
    status: number;
    op_id: number;
    op_name: string;
    create_time: string;
    update_time: string;
  };

  type StgItem = {
    id: number;
    publisher_id: number;
    developer_website_domain: string;
    bundle: string;
    tnt_id: number;
    op_id: number;
    status: number;
    tnt_name: string;
    op_name: string;
    update_time: string;
  };

  type PixlItem = {
    id: number;
    tnt_id: number;
    seller_id: number[];
    seller_name: string;
    buyer_name: string;
    buyer_id: number[];
    op_id: number;
    status: number;
    tnt_name: string;
    op_name: string;
    update_time: string;
  };

  type SupplyListItem = {
    seller_id: number;
    seller_name: string;
    tnt_id: number;
  };

  type DemandListItem = {
    buyer_id: number;
    buyer_name: string;
    tnt_id: number;
  };

  type KwaiPlacementItem = {
    id: number;
    buyer_id: number;
    buyer_name: string;
    tnt_id: number;
    tnt_name: string;
    bundle: string;
    app_id: string;
    native_pid: string;
    inters_pid: string;
    reward_pid: string;
    token: string;
    mixed_status: number;
    status: number;
    op_id: number;
    op_name: string;
    create_time: string;
    update_time: string;
  };

  type CustomPlacementItem = {
    id: number;
    buyer_id: number;
    tnt_id: number;
    bundle: string;
    app_id: string;
    banner_tag_id: string;
    native_tag_id: string;
    video_tag_id: string;
    rewarded_video_tag_id: string;
    status: number;
    op_id: number;
    update_time: string;
    op_name: string;
    buyer_name: string;
    tnt_name: string;
  };

  type IvtConfigItem = {
    id: number;
    tnt_id: number;
    tnt_name: string;
    seller_id: number;
    seller_name: string;
    buyer_id: number;
    buyer_name: string;
    type: number;
    ratio: number;
    bundle: string;
    country: string;
    status: number;
    op_id: number;
    op_name: string;
    create_time: string;
    update_time: string;
  };

  type AddIvtParams = {
    tnt_id: number;
    seller_id?: number;
    buyer_id?: number;
    op_id: number;
    status?: number;
    type: number;
    ratio: number;
    bundle?: string;
  };

  type UpdateIvtParams = {
    id: number;
    tnt_id: number;
    seller_id?: number[];
    buyer_id?: number[];
    op_id: number;
    status?: number;
    isChange?: boolean;
    type: number;
    ratio: number;
    bundle?: string;
  };
}
