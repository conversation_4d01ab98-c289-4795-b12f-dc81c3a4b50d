/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:53:14
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-08 16:27:24
 * @Description:
 */
import { request } from '@umijs/max';

export async function getUserList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/tnt/getUserList', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getTenantList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/tnt/getTenantList', {
    data: body || {},
    ...(options || {}),
  });
}

export async function sendEmail(
  body?: any,
  options?: { [key: string]: any },
): Promise<API.Result> {
  return request<API.Result>('/api/tnt/sendEmail', {
    data: body || {},
    ...(options || {}),
  });
}
export async function addOneUser(
  body: { account_name: string; password: string },
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/tnt/addOneUser', {
    data: body,
    ...(options || {}),
  });
}

export async function editUser(
  body: UserAPI.UserListItem,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/tnt/editUser', {
    data: body,
    ...(options || {}),
  });
}

export async function deleteUser(
  body: { user_id: number },
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/tnt/deleteUser', {
    data: body,
    ...(options || {}),
  });
}

export async function addTenant(
  body: TenantAPI.TenantListItem,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/tnt/addTenant', {
    data: body,
    ...(options || {}),
  });
}

export async function editTenant(
  body: TenantAPI.TenantListItem,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/tnt/editTenant', {
    data: body,
    ...(options || {}),
  });
}

export async function deleteTenant(
  body: { tnt_id: number },
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/tnt/deleteTenant', {
    data: body,
    ...(options || {}),
  });
}
