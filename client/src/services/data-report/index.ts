/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-10-20 14:39:22
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-28 11:15:11
 * @Description:
 */
import { request } from '@umijs/max';
export async function getKwaiReportList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.KwaiReportItem>>(
    '/api/report/getKwaiReport',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function downloadKwaiReport(body?: any) {
  return request<any>('/api/report/downloadKwaiReport', {
    data: body || {},
  });
}

export async function getPixalateReportList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.PixalateReportItem>>(
    '/api/report/getPixalateReport',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function downloadPixalateReport(body?: any) {
  return request<any>('/api/report/downloadPixalateReport', {
    data: body || {},
  });
}
export async function getHumanReportList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.HumanReportItem>>(
    '/api/report/getHumanReport',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function downloadHumanReport(body?: any) {
  return request<any>('/api/report/downloadHumanReport', {
    data: body || {},
  });
}

export async function getDashboardList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.FullReportListItem>>(
    '/api/dashboard/getDashboardList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function downloadDashboardList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<any>('/api/dashboard/downloadDashboardReport', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getAdvertiserBillingList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.BillingListItem>>(
    '/api/billing/getAdvertiserBillingList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getPublisherBillingList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.BillingListItem>>(
    '/api/billing/getPublisherBillingList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function downloadAdvertiserBillingList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.BillingListItem>>(
    '/api/billing/downloadAdvertiserBillingList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function downloadPublisherBillingList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.BillingListItem>>(
    '/api/billing/downloadPublisherBillingList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getPublisherBlockList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.BillingListItem>>(
    '/api/block/getPublisherBlockList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function downloadPublisherBlockList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<any>('/api/block/downloadPublisherBlockList', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getAdvertiserBlockList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.BillingListItem>>(
    '/api/block/getAdvertiserBlockList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function downloadAdvertiserBlockList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<any>('/api/block/downloadAdvertiserBlockList', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getConfigQps(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<any>>('/api/dashboard/getConfigQps', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getExportedTaskStatus(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.ExportedReportItem>>(
    '/api/exported-report/getExportedTaskStatus',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getExportedReportList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.ExportedReportItem>>(
    '/api/exported-report/getExportedReportList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getMonthlyReportList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<FullReportingAPI.MonthlyReportItem>>(
    '/api/billing/getMonthlyBillingList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}
