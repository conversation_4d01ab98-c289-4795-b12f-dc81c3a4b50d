/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-21 16:45:50
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 16:45:52
 * @Description: 
 */

import { request } from '@umijs/max';

export async function addAdminUser(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/addUser', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updateAdminUser(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/updateUser', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function getAllAdminUserList(options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/getAllUserList', {
    method: 'POST',
    ...(options || {})
  });
}

export async function resetAdminUserPwd(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/resetPwd', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function addAdminMenu(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/addMenu', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updateAdminMenu(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/updateMenu', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function getAllAdminMenu(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/getAllMenu', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function deleteAdminMenu(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/deleteMenu', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function addAdminRole(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/addRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updateAdminRole(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/updateRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function getAllAdminRole(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/getAllRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updateAdminRolePms(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/updateRolePms', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}

export async function updateAdminMenuSort(body: any, options?: { [key: string]: any }) {
  return request<Record<string, any>>('/api/admin/updateMenuSort', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
