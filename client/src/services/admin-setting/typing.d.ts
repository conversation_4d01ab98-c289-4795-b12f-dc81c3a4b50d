/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-21 16:45:55
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-12 16:26:52
 * @Description:
 */

declare namespace AdminSettingAPI {
  type MenuItem = {
    id: number;
    title: string;
    pid: number;
    remark: string;
    create_time: string;
    update_time: string;
    sort: number;
    access: string;
    type: number;
    path?: string;
    component?: string;
  };

  type RoleItem = {
    role_id: number;
    id: number;
    role_name: string;
    type: number;
    pms_list: string[];
    status: number;
    remark: string;
  };
}
