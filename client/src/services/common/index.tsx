/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:52:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-02-16 18:59:10
 * @Description:
 */
import { request } from '@umijs/max';

export async function getBuyerIntegrationType(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>('/api/common/getBuyerIntegrationType', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getSellerIntegrationType(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>('/api/common/getSellerIntegrationType', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getDict(params: { dict_type: string }, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.DictItem>>('/api/common/getDict', {
    method: 'GET',
    params,
    ...(options || {}),
  });
}
