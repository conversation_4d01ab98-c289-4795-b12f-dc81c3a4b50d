/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:52:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-29 11:02:06
 * @Description:
 */
declare namespace CommonAPI {
  type IntegrationTypeItem = {
    id: number;
    itg_name: string;
    itg_key: string;
    create_time: string;
    update_time: string;
  };
  type OptionsType = {
    label: string;
    value: any;
    key?: string | number;
  };
  type MapType = {
    [key: number | string]: string;
  };

  type DictItem = Record<string, any>;
}
// @ts-ignore
/* eslint-disable */

declare namespace API {
  type CurrentUser = {
    admin_id?: number;
    username?: string;
    role?: number;
    avatar?: string;
    userid?: string;
    email?: string;
    signature?: string;
    title?: string;
    group?: string;
    tags?: { key?: string; label?: string }[];
    notifyCount?: number;
    unreadCount?: number;
    country?: string;
    geographic?: {
      province?: { label?: string; key?: string };
      city?: { label?: string; key?: string };
    };
    address?: string;
    phone?: string;
    pwd_expire_time?: number;
    access: string[]; // 权限标识
    role_list: API.RoleItem[]; // 用户角色
  };

  type LoginResult = {
    code?: number;
    message?: string;
    data?: object;
    type?: string;
    currentAuthority?: string;
  };

  type PageParams = {
    current?: number;
    pageSize?: number;
  };

  type LoginParams = {
    username?: string;
    password?: string;
  };

  type EndpointListItem = {
    id: number;
    ad_format: number;
    connect_timeout: number;
    dsp_id: number;
    gzip: number;
    server_region: number;
    socket_timeout: number;
    url: string;
  };

  type AuthSupplyListItem = {
    config_id: number;
    dsp_id: number;
    level: number;
    seller_id: number;
    seller_name: string;
  };

  type UserListItem = {
    admin_id: number;
    is_am: number;
    is_bd: number;
    role: number;
    username: string;
    name: string;
    status: number;
    status_desc: string;
    role_list: RoleItem[];
    pms_list: PermissionItem[];
  };

  type TagListItem = {
    tag_id: number;
    tag_name: string;
    type: number;
    operator: string;
  };

  type DataList<T> = {
    code: number;
    message: string;
    data?: T[];
    /** 列表的内容总数 */
    total?: number;
  };

  type DataUpdateResult = {
    code: number;
    message: string;
    data?: any;
  };

  type DemandListParams = {
    dsp_id?: number[];
  };

  type DemandListItem = {
    dsp_id: number;
    dsp_name: string;
    tag_names: string;
    type: number;
    type_desc: string;
    auction_type: number;
    auction_type_ratio: number;
    imp_track_type: number;
    banner_to_native: number;
    contextual_app_targeting: number;
    pass_direct_supply_chain: number;
    ios_opt_in_mandatory: number;
    block_off_store_app: number;
    profit_model: number;
    payment_ratio: number;
    rev_share_ratio: number;
    profit_ratio: number | null;
    valid_aat: number;
    status: number;
    status_desc: string;
    token: string;
    am_id: number;
    am_name: string;
    bd_id: number;
    bd_name: string;
    parent_id: number;
    parent_name: string;
    account_name: string;
    password: string;
    dp_parent_id?: number;
  };

  type DemandUpdateParams = DemandListItem;
  type DemandAddParams = DemandListItem;

  // Supply

  type SupplyListParams = {
    sid?: number[];
  };
  type SupplyListItem = {
    aat_domain: string;
    accountid: number;
    am_id: number;
    am_name: string;
    bd_id: number;
    bd_name: string;
    device_type: number;
    is_show_gross: number;
    is_virtual: number;
    payment_ratio: number;
    profit_model: number;
    profit_ratio: number;
    inventory_type: number;
    publisher_type: number;
    relationship: number;
    relationship_desc: string;
    rev_share_ratio: number;
    rev_track_type: number;
    sid: number;
    source: number;
    status: number;
    status_desc: string;
    tag_ids: number[];
    tag_names: string;
    token: string;
    userlevel: string;
    username: string;
    website: string;
    parent_id: number;
    parent_name: string;
    account_name: string;
    password: string;
    sp_parent_id?: number;
  };

  type SupplyUpdateParams = SupplyListItem;
  type SupplyAddParams = SupplyListItem;

  // Supply Parent
  type SupplyParentListParams = {
    sid?: number[];
  };
  type SupplyParentListItem = {
    sp_id: number;
    sp_name: string;
    status: number;
    account_name: string;
    partner_id: number;
    partner_name: string;
    password: string;
    sa_status: number;
    token: string;
    email: string;
    status_desc: string;
    sp_bill_email: string;
    sp_bill_to_name: string;
    sp_contract_subject: number;
    sp_is_gst: string;
    sp_due_days: number;
    publisher_id: number;
  };
  type SupplyParentUpdateParams = SupplyParentListItem;
  type SupplyParentAddParams = SupplyParentListItem;

  // Demand Parent
  type DemandParentListParams = {
    sid?: number[];
  };
  type DemandParentListItem = {
    dp_id: number;
    dp_name: string;
    status: number;
    account_name: string;
    partner_id: number;
    partner_name: string;
    password: string;
    sa_status: number;
    token: string;
    status_desc: string;
    dp_bill_email: string;
    dp_bill_to_name: string;
    dp_contract_subject: number;
    dp_is_gst: string;
    dp_due_days: number;
  };
  // 合作伙伴
  type PartnerListItem = {
    partner_id: number;
    partner_name: string;
    sp_id: number;
    sp_name: string;
    sp_status: number;
    sp_contract: string;
    sp_email: string;
    sp_publisher_id: number;
    sa_account_name: string;
    sa_password: string;
    sa_token: string;
    sa_sid: string;
    dp_id: number;
    dp_name: string;
    dp_status: number;
    dp_contract: string;
    dp_email: string;
    da_account_name: string;
    da_password: string;
    da_token: string;
    da_sid: string;
    status_desc: string;
    status: number;
    contract: string[];
    dp_bill_email: string;
    dp_bill_to_name: string;
    dp_contract_subject: number;
    dp_is_gst: string;
    dp_due_days: number;
    sp_bill_email: string;
    sp_bill_to_name: string;
    sp_contract_subject: number;
    sp_is_gst: string;
    sp_due_days: number;
    type: number;
    id: number;
    type_desc: string;
    dp_bill_to_name_arr: string[];
    dp_bill_email_arr: string[];
    sp_bill_to_name_arr: string[];
    sp_bill_email_arr: string[];
  };

  type DemandParentUpdateParams = DemandParentListItem;
  type DemandParentAddParams = DemandParentListItem;

  // Demand Transaction
  type DemandTransactionListParams = {
    id?: number[];
  };
  type DemandTransactionListItem = {
    amount_act: number;
    amount_due: number;
    currency: number;
    currency_desc: string;
    parent_name: string;
    due_date: string;
    id: number;
    invoice_id: number;
    invoice_name: string;
    issued_date: string;
    month: number;
    parent_id: number;
    remark_type: number;
    remark_type_desc: string;
    status: number;
    status_desc: string;
    transaction_date: string;
    contract: string;
    partner_id: number;
    invoice_path: string;
    invoice_number: string;
    bill_to_name: string;
    due_days: number;
    status_mixed: number;
    status_mixed_desc: string;
    bill_email: string;
    rate: number;
    contract_subject: number;
    is_gst: number;
  };
  type DemandTransactionUpdateParams = DemandTransactionListItem;
  type DemandTransactionAddParams = DemandTransactionListItem;

  // Supply Transaction
  type SupplyTransactionListParams = {
    id?: number[];
  };
  type SupplyTransactionListItem = {
    amount_act: number;
    amount_due: number;
    currency: number;
    currency_desc: string;
    parent_name: string;
    due_date: string;
    id: number;
    invoice_id: number;
    invoice_path: string;
    issued_date: string;
    month: number;
    parent_id: number;
    remark_type: number;
    remark_type_desc: string;
    status: number;
    status_desc: string;
    transaction_date: string;
    partner_id: number;
    contract: string;
    invoice_number: string;
    bill_to_name: string;
    due_days: number;
    status_mixed: number;
    status_mixed_desc: string;
  };
  type SupplyTransactionUpdateParams = SupplyTransactionListItem;
  type SupplyTransactionAddParams = SupplyTransactionListItem;

  type DemandAATItem = {
    ad_domain: string;
    ad_pub_key: string;
    buyer_id: number;
    id: number;
    name: string;
    on_alert: number;
    pub_acc_id: string;
    demand_partner: string;
  };

  // Sellers Json
  type SellersJsonListParams = {
    id?: number[];
  };
  type SellersJsonListItem = {
    id: number;
    seller_id: number;
    name: string;
    seller_type: number;
    seller_type_desc: string;
    domain: string;
    operator: string;
    status: number;
    status_desc: string;
  };

  type SellersJsonUpdateParams = SellersJsonListItem;
  type SellersJsonAddParams = SellersJsonListItem;

  type SupplyAuthDspListItem = {
    dsp_id: number;
    dsp_name: string;
    tag_ids: string[];
    tag_names: string;
    am_id: number;
    bd_id: number;
  };

  type SupplyAuthAdslotsItem = {
    adslotid: string;
    adslotname: string;
    adtype: string;
    debug: number;
    dsp_list: SupplyAuthDspListItem[];
    placement_status: number;
    slottype: number;
    adtype_desc: string;
    publish_type: number;
    currentApp?: any;
    loading?: boolean;
  };

  type SupplyAuthAppListItem = {
    accountid: number;
    app_cate: string;
    app_status: number;
    appid: number;
    appkey: string;
    appname: string;
    pkg: string;
    placementcount: number;
    publisher_type: number;
    quality: number;
    sid: number;
    token: string;
    username: string;
    adslots: SupplyAuthAdslotsItem[];
    dsp_list: SupplyAuthDspListItem[];
    platform: string;
  };

  type CampaignListItem = {
    id: number;
    adomain: string;
    android_bundle_id: string;
    cid: string;
    click_track: string;
    cname: string;
    country: string;
    create_time: string;
    ctype: number;
    deep_link: string;
    dsp_id: number;
    dsp_name: string;
    end_date?: any;
    exp: number;
    impression_track: string;
    ios_bundle_id: string;
    landing_page: string;
    markup: number;
    priority: number;
    status: number;
    update_time: string;
    status_desc: string;
    ctype_desc: string;
    demand: string;
  };
  type CreativesListItem = {
    id: number;
    country: string;
    pkg_name: string;
    status: number;
    sub_type: string;
    type: string;
    list: CreativesSubItem[];
    status_desc: string;
    sub_type_desc: string;
  };
  type CreativesSubItem = {
    content: string;
    id: number;
    pkg_id: number;
    status: number;
    productid: number;
    lzdcid: number;
    url: string;
    type: string;
  };
  type BlockListItem = {
    ad_format: number;
    ad_size: string;
    app_bundle_id: string;
    country: string;
    dsp_id: number;
    dsp_name: string;
    id: number;
    sid: number;
    status: number;
    username: string;
    dsp: string;
    seller: string;
    status_desc: string;
  };

  type CampaignGroupItem = {
    ad_format: number;
    cids: string;
    cr_pkg_ids: string;
    country: string;
    dsp_id: number;
    dsp_name: string;
    id: number;
    sid: number;
    status: number;
    group_name: string;
    price: number;
    dsp: string;
    ad_format_desc: string;
    status_desc: string;
    demand: string;
  };
  type BidRateItem = {
    ad_format: number;
    ad_size: string;
    app_bundle_id: number;
    cid: string;
    country: string;
    ctype: number;
    dsp_id: number;
    dsp_name: string;
    id: number;
    rate: number;
    sid: number;
    status: number;
    username: string;
    status_desc: string;
    country_desc: string;
    bundle_desc: string;
    ctype_desc: string;
    cid_desc: string;
    ad_size_desc: string;
    ad_format_desc: string;
    seller: string;
    demand: string;
  };
  type PretargetingItem = {
    campaign_id: number;
    content: string;
    create_time: string;
    dsp_id: number;
    level: number;
    pt_id: number;
    update_time: string;
  };
  type PretargetingListItem = {
    campaign_id: number;
    campaign_name: string;
    create_time: string;
    dsp_id: number;
    ext: string;
    pt_flag: string;
    status: number;
    system_status: number;
    type: number;
    update_time: string;
    items: PretargetingItem[];
  };
  type DspEndPointItem = {
    ad_format: number;
    connect_timeout: number;
    dsp_id: number;
    gzip: number;
    id: number;
    server_region: number;
    socket_timeout: number;
    url: string;
  };
  type ExchangeRateItem = {
    id: number;
    rate: number;
    month: number;
    create_time: string;
    update_time: string;
  };

  type DspAuthItem = {
    config_id: number;
    seller_name: string;
    dsp_id: number;
    seller_id: number;
    level: number;
  };
  type MenuItem = {
    id: number;
    title: string;
    path: string;
    pid: number;
    remark: string;
    create_time: string;
    update_time: string;
    is_hide: number;
    icon: string;
    component: string;
    sort: number;
    access: string;
    menu_render: number;
    interfaces: number[];
  };
  type RoleItem = {
    id: number;
    role_name: string;
    type: number;
    type_desc: string;
    permission: any[];
  };
  type OperationItem = {
    id: number;
    op_name: string;
    auth_sign: number | string;
    remark: string;
  };
  type PermissionItem = {
    id: number;
    pms_name: string;
    type: number;
    menu_list: { rsc_id: number; pms_id: number; type: number }[];
    op_list: { rsc_id: number; pms_id: number; type: number }[];
    op_type: number;
    bl_type: number;
    remark: string;
    type_desc: string;
    status_desc: string;
    status: number;
  };
  type InterfaceItem = {
    id: number;
    itf_name: string;
    path: string;
    type: number;
    op_type: number;
    create_time: string;
    update_time: string;
    type_desc: string;
    op_type_desc: string;
  };
}
