/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-15 15:49:42
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-12 11:34:14
 * @Description:
 */

// @ts-ignore
/* eslint-disable */
import { request } from 'umi';

export async function addMenu(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/menu/addMenu',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function deleteMenu(
  body: { ids: (number | string)[] },
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/menu/deleteMenu',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function updateMenu(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/menu/updateMenu',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function getAllMenu(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/menu/getAllMenu',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function addRole(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/role/addRole',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}
export async function editRole(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<any>>('/api/role/editRole', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function deleteRole(
  body: { ids: (number | string)[] },
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/role/deleteRole',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function updateRole(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/role/updateRole',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function getPlatformRole(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/role/getPlatformRole',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function addUser(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/user/addUser',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function updateUser(body: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/user/updateUser',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function getAllUserList(options?: { [key: string]: any }) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/user/getAllUserList',
    {
      method: 'POST',
      ...(options || {}),
    },
  );
}

export async function addInterface(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/interface/addInterface',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function updateInterface(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/interface/updateInterface',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function getAllInterface(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/interface/getAllInterface',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function deleteInterface(
  body: { ids: (number | string)[] },
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<CommonAPI.IntegrationTypeItem>>(
    '/api/interface/deleteInterface',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function updateMenuSort(body: any, options?:{[key: string]: any}) {
  return request<Record<string, any>>('/api/menu/updateMenuSort', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    data: body,
    ...(options || {})
  });
}
