/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-15 15:49:47
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-12 12:32:38
 * @Description:
 */
declare namespace PermissionAPI {
  type MenuItem = {
    children?: MenuItem[];
    id: number;
    title: string;
    path: string;
    pid: number;
    remark: string;
    create_time: string;
    update_time: string;
    is_hide: number;
    icon: string;
    icon_tmp: string;
    component: string;
    sort: number;
    access: string;
    menu_render: number;
    interfaces: number[];
    status: number;
    type: number;
    node_type: number;
  };
  type RoleListItem = {
    id: number;
    role_name: string;
    type: number;
    type_desc: string;
    pms_list: number[];
  };
  type OperationItem = {
    id: number;
    op_name: string;
    auth_sign: number | string;
    remark: string;
  };
  type PermissionItem = {
    id: number;
    pms_name: string;
    type: number;
    menu_list: { rsc_id: number; pms_id: number; type: number }[];
    op_list: { rsc_id: number; pms_id: number; type: number }[];
    op_type: number;
    bl_type: number;
    remark: string;
    type_desc: string;
    status_desc: string;
    status: number;
  };
  type InterfaceItem = {
    id: number;
    itf_name: string;
    path: string;
    type: number;
    op_type: number;
    create_time: string;
    update_time: string;
    type_desc: string;
    op_type_desc: string;
    pid: number;
  };
}
