/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:06:05
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-18 10:46:50
 * @Description:
 */
declare namespace API {
  type Result = {
    code: number;
    message: string;
    data: any;
  };

  type ResultWithType<T> = {
    code: number;
    message: string;
    data: T;
  };

  type ResponseResult<T> = {
    code: number;
    message: string;
    data: T[];
  };

  type BackResult<T> = {
    data: T[];
    total: number;
  };

  type BackResponseResult<T> = {
    code: number;
    message: string;
    data: {
      data: T[];
      total: number;
    };
  };

  type PropsType = {
    setLoading?: (val: boolean) => void;
    request: any;
    params?: any;
    onSuccess?: (data: any) => void;
    onError?: (e: any) => void;
    onFinally?: () => void;
  };

  type StringType = {
    [key: string]: number | string;
  };

  type NumberType = {
    [key: number]: number | string;
  };

  type StringToStringType = {
    [key: string]: string;
  };
}
