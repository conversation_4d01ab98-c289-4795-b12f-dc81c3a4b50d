/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 18:10:25
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-27 18:10:26
 * @Description:
 */
declare namespace StrategyAPI {
  type BlAndWlListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    content: string;
    op_id: number;
    seller_name: string;
    buyer_name: string;
    status: number;
    account_name: string;
    account_status: number;
    create_time: string;
    update_time: string;
  };

  type AddBlAndWlParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    content: string;
  };
  type updateBlAndWlParams = {
    id: number;
    content: string;
    status: number;
  };

  type CapListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    imp_cap: number;
    rev_cap: number;
    cur_imp: number;
    cur_rev: number;
    status: number;
    cap_status: number;
    seller_name: string;
    buyer_name: string;
    account_name: string;
    account_status: number;
    op_id: number;
    create_time: string;
    update_time: string;
    sys_update_time: string;
  };

  type AddCapParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    imp_cap: number;
    rev_cap: number;
    status: number;
    cap_status: number;
  };
  type updateCapParams = {
    id: number;
    imp_cap: number;
    rev_cap: number;
    status: number;
    cap_status: number;
  };
  type QpsListItem = {
    id: number;
    // pub_id: number;
    buyer_id: number;
    level: number;
    qps: number;
    status: number;
    op_id: number;
    seller_name: string;
    buyer_name: string;
    app_name: string;
    plm_name: string;
    create_time: string;
    update_time: string;
    app_id: number;
    plm_id: number;
    seller_id: number;
    account_status: number;
    ots_id: string;
    region: number;
  };
  type AddQpsParams = {
    pub_id: number;
    buyer_id: number;
    level: number;
    qps: number;
    status: number;
    op_id: number;
  };
  type UpdateQpsParams = {
    id: number;
    imp_cap: number;
    rev_cap: number;
    status: number;
    cap_status: number;
    op_id: number;
  };
  type ProfitListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    type: number;
    profit_ratio: number;
    status: number;
    op_id: number;
    account_name: string;
    create_time: string;
    update_time: string;
    name: string;
    tmp_id: number;
    account_status: number;
    seller_status?: number;
  };

  type PublisherAdvertiserProfitListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    type: number;
    profit_ratio: number;
    status: number;
    op_id: number;
    account_name: string;
    create_time: string;
    update_time: string;
    name: string;
    tmp_id: number;
    children?: ProfitListItem[];
    account_status: number;
    seller_status?: number;
  };
  type AddProfitParams = {
    profit_ratio: number;
    tnt_id: number;
    op_id: number;
    type: number;
    seller_id: number;
    buyer_id: number;
  };

  type UpdateProfitParams = {
    id: number;
    status: number;
    profit_ratio: number;
    tnt_id: number;
    op_id: number;
  };

  type SelectOptionsType = {
    label: string;
    value: number | string;
  };

  type FloorListItem = {
    id: number;
    type: number;
    buyer_id: number;
    seller_id: number;
    plm_id: number;
    plm_name: string;
    ad_format: number;
    country: string;
    bid_floor: number;
    op_id: number;
    status: number;
    tnt_id: number;
    account_status: number;
    account_name: string;
    seller_name: string;
    buyer_name: string;
    children?: FloorListItem[];
    name: string;
    tmp_id: number;
    key: number | string;
    update_time: string;
    isParent?: boolean;
  };
  type FloorPlmItem = {
    plm_id: number;
    plm_name: string;
    app_id: number;
    app_name: string;
    seller_id: number;
  };
  type AddFloorParams = {
    type: number;
    buyer_id: number;
    seller_id: number;
    plm_id: number;
    ad_format: number;
    country: string;
    bid_floor: number;
    op_id: number;
    status: number;
    tnt_id: number;
  };

  type UpdateFloorParams = {
    type: number;
    buyer_id: number;
    seller_id: number;
    plm_id: number;
    ad_format: number;
    country: string;
    bid_floor: number;
    op_id: number;
    status: number;
    tnt_id: number;
  };
  type DeleteFloorParams = {
    id: number;
    tnt_id: number;
  };

  type CreativeListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    type: number;
    content: string[] | string;
    expire: string;
    remark: string;
    op_name: string;
    op_status: number;
    update_time: string;
    status: number;
  };

  type AddCreativeParams = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    op_id: number;
    tnt_id: number;
    remark: string;
    content: string;
  };

  type UpdateCreativeParams = {
    id: number;
    seller_id: number;
    buyer_id: number;
    type: number;
    op_id: number;
    tnt_id: number;
    remark: string;
    content: string;
  };

  type IvtConfigItem = {
    id: number;
    tnt_id: number;
    tnt_name: string;
    seller_id: number;
    seller_name: string;
    buyer_id: number;
    buyer_name: string;
    type: number;
    ratio: number;
    bundle: string;
    status: number;
    op_id: number;
    op_name: string;
    create_time: string;
    update_time: string;
  };
  type AddIvtParams = {
    tnt_id: number;
    seller_id?: number;
    buyer_id?: number;
    op_id: number;
    status?: number;
    type: number;
    ratio: number;
    bundle?: string;
  };

  type UpdateIvtParams = {
    id: number;
    tnt_id: number;
    seller_id?: number[];
    buyer_id?: number[];
    op_id: number;
    status?: number;
    isChange?: boolean;
    type: number;
    ratio: number;
    bundle?: string;
  };
  type AtcListItem = {
    id: number;
    model: number;
    op_id: number;
    create_time: string;
    update_time: string;
  };
  type UpdateAtcModelParams = {
    id: number;
    model: number;
    op_id: number;
    tnt_id: number;
  };

  type ABTestListItem = {
    id: number;
    type: number;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    default_profit?: number;
    content: string;
    op_id: number;
    account_name: string;
    account_status: number;
    create_time: string;
    update_time: string;
    expire_time: string | string[];
  };

  type AddABTestParams = {
    seller_id: number;
    buyer_id: number;
    type: number;
    content: string;
  };
  type UpdateABTestParams = {
    id: number;
    content: string;
  };
}
