/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-05 10:17:53
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-27 19:01:19
 * @Description:
 */

declare namespace DemandAPI {
  type DemandUser = {
    account_name: string;
    user_id: number;
    status: number;
    doc: string;
    token: string;
  };

  type InfoEndpointItem = {
    APAC_Banner_ad_format: number;
    APAC_Banner_connect_timeout: number;
    APAC_Banner_gzip: number;
    APAC_Banner_server_region: number;
    APAC_Banner_socket_timeout: number;
    APAC_Banner_url: string;
    APAC_Native_ad_format: number;
    APAC_Native_connect_timeout: number;
    APAC_Native_gzip: number;
    APAC_Native_server_region: number;
    APAC_Native_socket_timeout: number;
    APAC_Native_url: string;
    APAC_Video_ad_format: number;
    APAC_Video_connect_timeout: number;
    APAC_Video_gzip: number;
    APAC_Video_server_region: number;
    APAC_Video_socket_timeout: number;
    APAC_Video_url: string;
    USE_Banner_ad_format: number;
    USE_Banner_connect_timeout: number;
    USE_Banner_gzip: number;
    USE_Banner_server_region: number;
    USE_Banner_socket_timeout: number;
    USE_Banner_url: string;
    USE_Native_ad_format: number;
    USE_Native_connect_timeout: number;
    USE_Native_gzip: number;
    USE_Native_server_region: number;
    USE_Native_socket_timeout: number;
    USE_Native_url: string;
    USE_Video_ad_format: number;
    USE_Video_connect_timeout: number;
    USE_Video_gzip: number;
    USE_Video_server_region: number;
    USE_Video_socket_timeout: number;
    USE_Video_url: string;
    EUW_Banner_ad_format: number;
    EUW_Banner_connect_timeout: number;
    EUW_Banner_gzip: number;
    EUW_Banner_server_region: number;
    EUW_Banner_socket_timeout: number;
    EUW_Banner_url: string;
    EUW_Native_ad_format: number;
    EUW_Native_connect_timeout: number;
    EUW_Native_gzip: number;
    EUW_Native_server_region: number;
    EUW_Native_socket_timeout: number;
    EUW_Native_url: string;
    EUW_Video_ad_format: number;
    EUW_Video_connect_timeout: number;
    EUW_Video_gzip: number;
    EUW_Video_server_region: number;
    EUW_Video_socket_timeout: number;
    EUW_Video_url: string;
  };
  type DemandListItem = {
    tnt_id: number;
    tnt_name: string;
    buyer_id: number;
    buyer_name: string;
    integration_type: number;
    status: number;
    create_time: string;
    update_time: string;
    status_desc: string;
    integration_type_desc: string;
    profit_model: number;
    profit_ratio: number;
    rev_share_ratio: number;
    profit_status: number;
    profit_id: number;
    user_id: number;
    schain_required: number;
    idfa_required: number;
    filter_mraid: number;
    demand_account_name: string;
    demand_account_status: number;
    imp_track_type: number;
    auction_type: number;
    token: string;
    reporting_url?: string;
    max_hm_ivt_ratio: number;
    max_pxl_ivt_ratio: number;
    pixl_ivt_type: number;
    hm_ivt_type: number;
    multi_format: number;
    banner_multi_size: number;
    banner_transfer_format: number;
    native_format: number;
    native_root_key: number;
    // 以下为Infotable字段
    auth_seller_id: number;
    auth_seller_name: string;
    dp_id: number;
    partner_id: number;
    partner_name: string;
    skoverlay: number;
    autostore: number;
    block_off_store_app: number;
    auto_qps: number;
    pass_display_manager: number;
    burl_track_type: number;
  } & InfoEndpointItem;

  type EndpointItem = {
    url: string;
    connect_timeout: number;
    socket_timeout: number;
    gzip: number;
    server_region: number;
    ad_format: number;
  };

  type DemandEndpointItem = {
    id: number;
    buyer_id: number;
    url: string;
    connect_timeout: number;
    socket_timeout: number;
    gzip: number;
    qps: number;
    server_region: number;
    ad_format: number;
    status: number;
    create_time: string;
    update_time: string;
  };
}
