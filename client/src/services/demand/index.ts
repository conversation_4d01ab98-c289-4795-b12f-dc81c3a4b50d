import { request } from '@umijs/max';
export async function getDemandDetailList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<DemandAPI.DemandListItem>>(
    '/api/demand/getDemandList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getDemandListWithTesting(body?: any, options?: { [key: string]: any }) {
  return request<API.ResponseResult<DemandAPI.DemandListItem>>('/api/demand/getDemandListWithTesting', {
    data: body || {},
    ...(options || {})
  });
}

export async function updateDemand(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.BackResponseResult<DemandAPI.DemandListItem>>(
    '/api/demand/updateDemand',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getDemandEndpoint(
  body: { buyer_id: string | number },
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<DemandAPI.DemandEndpointItem>>(
    '/api/demand/getDemandEndpoint',
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
      ...(options || {}),
    },
  );
}

export async function getDemandAuth(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/demand/getDemandAuth', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
