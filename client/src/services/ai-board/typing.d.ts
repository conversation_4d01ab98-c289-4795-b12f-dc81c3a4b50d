/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-13 15:15:06
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-02 16:31:05
 * @Description:
 */
declare namespace BoardAPI {
  type OverviewItem = {
    revenue: number;
    revenue_increase: number;
    profit: number;
    profit_increase: number;
    request: number;
    request_increase: number;
    ecpm: number;
    ecpm_increase: number;
    impression: number;
    impression_increase: number;
    hours_data?: {
      date: string;
      revenue: number;
      profit: number;
      request: number;
      impression: number;
    }[];
    update_time: string;
  };

  type TopCountryItem = {
    country: string;
    revenue: number;
    sl_revenue: number;
    tnt: string;
    top_tnts: {
      sl_revenue;
      tnt: number;
      revenue: number;
    }[];
  };

  type SevenDaysCountryItem = {
    start_date: string;
    end_date: string;
    update_time: string;
    data: {
      country: string;
      revenue: number;
      date: string;
    }[];
  };

  type TopAdFormatItem = {
    ad_format: string;
    revenue: number;
    top_ad_sizes: {
      ad_size: string;
      revenue: number;
    }[];
  };
  type CTVData = {
    date: string;
    revenue: number;
  };
  type TenantRevenueItem = {
    tnt: string;
    revenue: number;
  };

  type TopCountryEcpmAndEcprItem = {
    date: string;
    country: string;
    ecpm: number;
    ecpr: number;
    ad_format: string;
  };

  type SentMessageItem = {
    tnt_id: number;
    tnt_name: string;
    content: string;
    ext_1: string;
    op_id: number;
    op_name: string;
    mixed_key: string;
    create_time: string;
  };
}
