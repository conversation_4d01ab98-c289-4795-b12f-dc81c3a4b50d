/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-13 15:14:54
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-08 15:11:46
 * @Description:
 */
import { request } from '@umijs/max';

export async function getOverview(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<BoardAPI.OverviewItem>>(
    '/api/ai-board/getOverview',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getTopCountry(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<BoardAPI.TopCountryItem>>(
    '/api/ai-board/getTopCountry',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getSevenDaysCountry(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<BoardAPI.SevenDaysCountryItem>>(
    '/api/ai-board/getSevenDaysCountry',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getTopAdFormat(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<BoardAPI.SevenDaysCountryItem>>(
    '/api/ai-board/getTopAdFormat',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getAllTenantRevenue(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<BoardAPI.SevenDaysCountryItem>>(
    '/api/ai-board/getAllTenantRevenue',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getTopCountryEcpmAndEcpr(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<BoardAPI.SevenDaysCountryItem>>(
    '/api/ai-board/getTopCountryEcpmAndEcpr',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function getAllSentMessage(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<BoardAPI.SentMessageItem[]>>(
    '/api/ai-board/getAllSentMessage',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}
