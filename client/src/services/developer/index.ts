/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 15:42:12
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-27 19:05:00
 * @Description:
 */
import { request } from '@umijs/max';

export async function getAppList(body: any, options?: { [key: string]: any }) {
  return request<API.Result>('/api/app/getAppList', {
    data: body || {},
    ...(options || {}),
  });
}
