/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 10:17:53
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 17:22:04
 * @Description:
 */

declare namespace UserAPI {
  type UserListItem = {
    account_name: string;
    display_name: string;
    role: number;
    admin_id: number;
    role_id: number;
    role_type: number;
    is_login: boolean;
  };

  type LoginParams = {
    account_name: string;
    password: string;
  };
  type ResetPasswordParams = {
    old_password: string;
    new_password: string;
  };

  type ConfirmPasswordParams = {
    old_password: string;
  };

  type validPasswordParams = {
    password: string;
  };

  type AccountLinkColumn = {
    id?: number;
    user_id: number;
    account_name: string;
    link_users: UserListItem[];
    link_status: number;
    status: number;
  };

  type UpdateUserLinkParams = {
    id?: number;
    special_user_id: number;
    link_users_ids: number[];
    status?: number;
  };
}
