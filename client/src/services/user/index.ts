/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-05 10:17:53
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 17:13:09
 * @Description:
 */

import { request } from '@umijs/max';

export async function getCurrentUser(
  body?: any,
  options?: { [key: string]: any },
): Promise<API.Result> {
  return request<API.Result>('/api/user/getCurrentUser', {
    data: body || {},
    ...(options || {}),
  });
}

export async function logOut(options?: { [key: string]: any }) {
  return request<API.Result>('/api/user/logOut', {
    method: 'POST',
    ...(options || {}),
  });
}

export async function login(
  body: UserAPI.LoginParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/user/logIn', {
    data: body,
    ...(options || {}),
  });
}

export async function resetPassword(
  body: UserAPI.ResetPasswordParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/user/resetPassword', {
    data: body,
    ...(options || {}),
  });
}

export async function validPassword(
  body: UserAPI.validPasswordParams,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/user/validPassword', {
    data: body,
    ...(options || {}),
  });
}

export async function updateCompanyUser(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/user/updateCompanyUser', {
    data: body,
    ...(options || {}),
  });
}

export async function getAllUserLinkList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<CommonAPI.DictItem>>(
    '/api/user/getAllUserLinkList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function updateUserLink(
  body: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/user/updateUserLink', {
    data: body,
    ...(options || {}),
  });
}
