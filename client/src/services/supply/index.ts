import { request } from '@umijs/max';

export async function getSupplyDetailList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<SupplyAPI.SupplyListItem>>(
    '/api/supply/getSupplyList',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function updateSupply(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/supply/updateSupply', {
    method: 'POST',
    data: body || {},
    ...(options || {}),
  });
}

export async function getSupplyAuth(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<SupplyAPI.SellerDemandAuth>>(
    '/api/supply/getSupplyAuth',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}

/**
 * @description 前端未使用
 * @param body 
 * @param options 
 * @returns 
 */
export async function getSupplyEndpoint(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<API.ResponseResult<SupplyAPI.SupplyEndpoint>>(
    '/api/supply/getSupplyEndpoint',
    {
      data: body || {},
      ...(options || {}),
    },
  );
}
