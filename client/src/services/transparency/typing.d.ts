/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-25 15:03:59
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-25 15:04:00
 * @Description:
 */
declare namespace TransparencyAPI {
  type AppInfoItem = {
    app_bundle_id: string;
    app_name: string;
    developer_name: string;
    developer_website: string;
    aat_domain: string;
    multi_aat_domain: string;
    app_id: number;
    store_url: string;
    support_aat: number;
    aat_url: string;
    update_tag: number;
    supply_tag?: string[];
    seller_id?: string[];
    seller_type?: string[];
    name?: string[];
    category?: string[];
    supply_tag?: string[];
    rating_score?: number;
    downloads_info?: string;
    sellers?: {
      seller_id: string;
      seller_type: string;
      name: string;
      supply_tag: string;
    }[];
  };

  interface AppCrawlerRequestParams {
    app_bundle_id?: string[];
    platform?: number;
  }
  interface AppCrawlerRespData {
    app_id: number;
    app_bundle_id: string;
    platform: number;
    status: number;
    op_user: string;
  }

  type AppCrawlerResponse = API.BackResponseResult<AppCrawlerRespData>;

  interface UpdateAppCrawlerDto {
    app_id?: number;
    app_bundle_id?: string;
    platform?: number;
    status?: number;
    op_user?: string;
  }

  interface BatchCreateAppCrawlerParams {
    bundles: {
      app_bundle_id: string;
      platform: number;
    }[];
  }

  interface SchainTruncationItem {
    id: number;
    level: number;
    tnt_id: number;
    tnt_name: string;
    buyer_id: number;
    buyer_name: string;
    schain_hops: number;
    status: number;
  }

  type SchainTruncationColumns = ColumnProps<TransparencyAPI.SchainTruncationItem>
}
