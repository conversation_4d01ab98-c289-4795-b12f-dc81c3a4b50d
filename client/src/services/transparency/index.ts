/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-25 15:03:44
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-25 15:05:38
 * @Description:
 */
import { request } from 'umi';
export async function uploadSellerJson(
  body: { user_id: number },
  options?: { [key: string]: any },
) {
  return request<API.Result>('/api/seller-json/upload', {
    data: body,
    ...(options || {}),
  });
}

export async function getTransparencyAppInfo(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<any>('/api/transparency/getTransparencyAppInfo', {
    data: body || {},
    ...(options || {}),
  });
}

export async function updateAppInfo(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<any>('/api/transparency/updateTransparencyAppInfo', {
    data: body || {},
    ...(options || {}),
  });
}

export async function updateAppInfoTag(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<any>('/api/transparency/updateTransparencyAppInfoTag', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getAppCrawlerList(
  body?: TransparencyAPI.AppCrawlerRequestParams,
  options?: { [key: string]: any },
) {
  return request<TransparencyAPI.AppCrawlerResponse>(
    '/api/transparency/getAppCrawlerList',
    {
      ContentType: 'application/json',
      data: body || {},
      ...(options || {}),
    },
  );
}

export async function batchCreateAppCrawler(
  body?: TransparencyAPI.BatchCreateAppCrawlerParams,
  options?: { [key: string]: any },
) {
  return request<any>('/api/transparency/batchCreateAppCrawler', {
    data: body || {},
    ...(options || {}),
  });
}
export async function updateAppCrawler(
  body?: TransparencyAPI.UpdateAppCrawlerDto,
  options?: { [key: string]: any },
) {
  return request<any>('/api/transparency/updateAppCrawler', {
    data: body || {},
    ...(options || {}),
  });
}

export async function getSchainTruncationList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<any>('/api/transparency/getSchainTruncationList', {
    data: body || {},
    ...(options || {}),
  });
}

export async function updateSchainTruncationConfig(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<any>('/api/transparency/updateSchainTruncationConfig', {
    data: body || {},
    ...(options || {}),
  });
}
