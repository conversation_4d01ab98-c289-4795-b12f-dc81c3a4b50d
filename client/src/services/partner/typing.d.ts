/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2024-01-11 16:13:57
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-12 11:17:12
 * @Description:
 */

declare namespace PartnerAPI {
  type PartnerListItem = {
    partner_id: number,
    partner_name: string;
    sp_id: number;
    sp_name: string;
    sp_email: string;
    sp_publisher_id: number;
    dp_id: number;
    dp_name: string;
    dp_email: string;
    type: number;
    update_time: string;
    tnt_id: number;
    buyers: { buyer_name: string, buyer_id: number, dp_id: number }[];
    sellers: { seller_name: string, seller_id: number, sp_id: number }[];
  }
}
