/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-11 16:13:51
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-27 18:58:47
 * @Description:
 */

import { request } from '@umijs/max';

// 合作伙伴
export async function getPartnerList(
  body?: any,
  options?: { [key: string]: any },
) {
  return request<Record<string, any>>('/api/partner/getPartnerList', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body || {},
    ...(options || {}),
  });
}
