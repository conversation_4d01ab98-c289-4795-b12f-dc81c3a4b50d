/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-15 10:22:37
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-03 16:45:17
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-28 15:23:34
 * @Description:
 */

import { RoleType } from '@/constants/permission/role';

export default function access(initialState: any) {
  const menu_access = initialState?.currentUser?.menu_access || [];
  const btn_access = initialState?.currentUser?.btn_access || [];
  const role_type = initialState?.currentUser?.role_type || 0;
  const isSuper = role_type === RoleType['Super Administrator'];

  return {
    ReeMainBoardCode: isSuper || menu_access.includes('ReeMainBoardCode'),
    ReeAI: isSuper || menu_access.includes('ReeAI'),
    ReeSentMessageCode: isSuper || menu_access.includes('ReeSentMessageCode'),
    AiBoardPermission: isSuper || menu_access.includes('AiBoardPermission'),
    ManageCode: isSuper || menu_access.includes('ManageCode'),
    TenantUser: isSuper || menu_access.includes('TenantUser'),
    TenantManage: isSuper || menu_access.includes('TenantManage'),
    AccountLink: isSuper || menu_access.includes('AccountLink'),
    Privatization: isSuper || menu_access.includes('Privatization'),
    PermissionCode: isSuper || menu_access.includes('PermissionCode'),
    PermissionInterface: isSuper || menu_access.includes('PermissionInterface'),
    PermissionMenu: isSuper || menu_access.includes('PermissionMenu'),
    PMSRole: isSuper || menu_access.includes('PMSRole'),
    ConfigCode: isSuper || menu_access.includes('ConfigCode'),
    ConfigEcpr: isSuper || menu_access.includes('ConfigEcpr'),
    ConfigATC: isSuper || menu_access.includes('ConfigATC'),
    ConfigKwai: isSuper || menu_access.includes('ConfigKwai'),
    ConfigSupplyChain: isSuper || menu_access.includes('ConfigSupplyChain'),
    SchainTruncation: isSuper || menu_access.includes('SchainTruncation'),
    ConfigPixalatePrebid:
      isSuper || menu_access.includes('ConfigPixalatePrebid'),
    ConfigIVT: isSuper || menu_access.includes('ConfigIVT'),
    AppAds: isSuper || menu_access.includes('AppAds'),
    AdsAppInfo: isSuper || menu_access.includes('AdsAppInfo'),
    DataReport: isSuper || menu_access.includes('DataReport'),
    DataFullReport: isSuper || menu_access.includes('DataFullReport'),
    DataBillingReport: isSuper || menu_access.includes('DataBillingReport'),
    DataAdvertiserBillingReport:
      isSuper || menu_access.includes('DataAdvertiserBillingReport'),
    DataPublisherBillingReport:
      isSuper || menu_access.includes('DataPublisherBillingReport'),
    DataBlockReport: isSuper || menu_access.includes('DataBlockReport'),
    DataDemandBlockReport: isSuper || menu_access.includes('DataDemandBlockReport'),
    DataSupplyBlockReport: isSuper || menu_access.includes('DataSupplyBlockReport'),
    DataMonthlyReport: isSuper || menu_access.includes('DataMonthlyReport'),
    DataKwaiReport: isSuper || menu_access.includes('DataKwaiReport'),
    DataHumanReport: isSuper || menu_access.includes('DataHumanReport'),
    DataPixalateReport: isSuper || menu_access.includes('DataPixalateReport'),
    DataExportLog: isSuper || menu_access.includes('DataExportLog'),
    AdminSet: isSuper || menu_access.includes('AdminSet'),
    AdminMenu: isSuper || menu_access.includes('AdminMenu'),
    AdminRole: isSuper || menu_access.includes('AdminRole'),
    AdminUser: isSuper || menu_access.includes('AdminUser'),
    Transparency: isSuper || menu_access.includes('Transparency'),
    TransparencyRixSiteFile:
      isSuper || menu_access.includes('TransparencyRixSiteFile'),
    TransparencySellersJson:
      isSuper || menu_access.includes('TransparencySellersJson'),
    TransparencyAppInfo: isSuper || menu_access.includes('TransparencyAppInfo'),
    TransparencyAppCrawler: isSuper || menu_access.includes('TransparencyAppCrawler'),
    Demand: isSuper || menu_access.includes('Demand'),
    Advertiser: isSuper || menu_access.includes('Advertiser'),
    Supply: isSuper || menu_access.includes('Supply'),
    Publisher: isSuper || menu_access.includes('Publisher'),

    isButtonAccess: (key: string) => {
      return isSuper || btn_access.includes(key);
    },
  };
}
