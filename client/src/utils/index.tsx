/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:06:05
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-03 12:00:02
 * @LastEditTime: 2023-02-13 18:03:20
 * @Description:
 */
import { Parser } from 'json2csv';
import type { Options } from 'json2csv';
import moment from 'moment';
import { SpecialSuccessCode } from '@/constants';
import { SearchResultItem } from '@/components/TopBar';
// 修改为世界时UTC0
export const formatTime = function (
  val: string | Date,
  pattern = 'yyyy-MM-dd hh:mm:ss',
) {
  const time = new Date(val);
  const y = time.getUTCFullYear() + '';
  const mm = (time.getUTCMonth() + 1 + '').padStart(2, '0');
  const d = (time.getUTCDate() + '').padStart(2, '0');
  const h = (time.getUTCHours() + '').padStart(2, '0');
  const m = (time.getUTCMinutes() + '').padStart(2, '0');
  const s = (time.getUTCSeconds() + '').padStart(2, '0');
  return pattern
    .replace('yyyy', y)
    .replace('MM', mm)
    .replace('dd', d)
    .replace('hh', h)
    .replace('mm', m)
    .replace('ss', s);
};
export const formatUTC8Time = function (
  val: string | Date,
  pattern = 'yyyy-MM-dd hh:mm:ss',
) {
  const time = new Date(val);
  const y = time.getFullYear() + '';
  const mm = (time.getMonth() + 1 + '').padStart(2, '0');
  const d = (time.getDate() + '').padStart(2, '0');
  const h = (time.getHours() + '').padStart(2, '0');
  const m = (time.getMinutes() + '').padStart(2, '0');
  const s = (time.getSeconds() + '').padStart(2, '0');
  return pattern
    .replace('yyyy', y)
    .replace('MM', mm)
    .replace('dd', d)
    .replace('hh', h)
    .replace('mm', m)
    .replace('ss', s);
};
export function objectFlip(obj: { [key: string]: string | number }): {
  [key: string | number]: string;
} {
  const ret: any = {};
  Object.keys(obj).forEach((key: string) => {
    ret[obj[key]] = key;
  });
  return ret;
}

// 验证输入
export function isValidBundle(bundle: string) {
  const iosReg = /^[0-9]+$/;
  const androidReg =
    /^([a-zA-Z][a-zA-Z0-9_-]*)+([.][a-zA-Z0-9][a-zA-Z0-9_-]*)+$/;
  return iosReg.test(bundle) || androidReg.test(bundle);
}

// 接口请求
export const fetchData = ({
  setLoading,
  request,
  params,
  onSuccess,
  onError,
  onFinally,
}: API.PropsType) => {
  setLoading && setLoading(true);
  request(params)
    .then((res: any) => {
      // 增加特殊的成功code判断
      let isSpecialCode = Object.values(SpecialSuccessCode).includes(res.code);
      if (Array.isArray(res)) {
        onSuccess && onSuccess(res);
      } else {
        if (res && res.code === 0) {
          onSuccess && onSuccess(res.data);
        } else if (res.code === 1105) {
          onError && onError(res.message);
        } else if (isSpecialCode) {
          onSuccess && onSuccess(res.message);
        }
      }
    })
    .catch((e: any) => {
      onError && onError(e);
    })
    .finally(() => {
      setLoading && setLoading(false);
      onFinally && onFinally();
    });
};
// 验证数字
export const validateNumber = (rule: any, value: any) => {
  const reg = /^\d+(\.\d+)?$/;
  if ((!value && +value !== 0) || value === '') {
    return Promise.reject(new Error(``));
  } else if (!reg.test(value)) {
    return Promise.reject(new Error('Please Input Number'));
  }
  return Promise.resolve();
};

export function isNumber(val: string | number): boolean {
  return Object.prototype.toString.call(val).slice(8, -1) === 'Number';
}

export const handleFilterSelect = (input: any, option: any) => {
  const tmp = option.children || option.label || '';
  const str = tmp && Array.isArray(tmp) ? tmp.join('') : `${tmp}`;
  return (str && str.toLowerCase().indexOf(input.toLowerCase()) >= 0) || false;
};

export const validUrl = (rule: any, value: any) => {
  const reg =
    /(http|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/;
  if (!value) {
    return Promise.resolve();
  } else if (!reg.test(value)) {
    return Promise.reject(new Error('Please enter the correct URL'));
  }
  return Promise.resolve();
};

export const getLastWeekDate = () => {
  // 暂时改为三天
  const date = new Date();
  date.setDate(date.getDate() - 2);
  return date;
};

export const downloadCsv = async function (
  fileName: string,
  data: object[],
  option: Options<any>,
) {
  const json2csvParser = new Parser(option);
  const csv = json2csvParser.parse(data);
  const blob = new Blob(['\ufeff' + csv], { type: 'text/csv' });
  const a = document.createElement('a');
  a.setAttribute('href', URL.createObjectURL(blob));
  a.setAttribute('download', `${fileName}.csv`);
  a.click();
};

/**
 * @param obj
 * @description 简易深拷贝
 */
export const deepClone = (obj: any) => {
  // 判断是否需要递归
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }
  const result: any = Array.isArray(obj) ? [] : {};
  // 函数直接返回
  if (typeof obj === 'function' || obj instanceof moment) {
    return obj;
  }

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      result[key] = deepClone(obj[key]);
    }
  }
  return result;
};

export const formatMoney = (num: number) => {
  let data = num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return data;
};

export const isArrSame = (
  arr1: (number | string)[],
  arr2: (number | string)[],
) => {
  return (
    arr1.length === arr2.length &&
    arr1.every((a) => arr2.some((b) => a === b)) &&
    arr2.every((a) => arr1.some((b) => a === b))
  );
};

export function isObjectValueEqual(a: any, b: any) {
  //取对象a和b的属性名
  const aProps = Object.getOwnPropertyNames(a);
  const bProps = Object.getOwnPropertyNames(b);
  //判断属性名的length是否一致
  if (aProps.length !== bProps.length) {
    return false;
  }
  //循环取出属性名，再判断属性值是否一致
  for (let i = 0; i < aProps.length; i++) {
    const propName = aProps[i];
    if (a[propName] !== b[propName]) {
      return false;
    }
  }
  return true;
}

// 去除零宽空格
export const removeZWSpace = (str: string) => {
  if (!str) return str;
  const whitespace =
    '\n\r\t\f\x0b\xa0\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200a\u200b\u2028\u2029\u3000';

  let result = '';

  for (let i = 0; i < str.length; i++) {
    if (whitespace.indexOf(str.charAt(i)) === -1) {
      result += str.charAt(i);
    }
  }

  return result;
};

export const getQueryParams = (val: SearchResultItem[]) => {
  const params: any = {};
  const tmp = val.filter(
    (item) =>
      (Array.isArray(item.value) && item.value.length > 0) ||
      (!Array.isArray(item.value) && item.value),
  );
  tmp.forEach((item) => {
    params[item.key] = item.value;
  });
  return params;
};

export const validateEmail = (rule: any, value: string) => {
  const reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
  const arr = (value || '')
    .split('\n')
    .filter((item) => item.trim().length)
    .map((item: string) => item.trim());
  const flag = arr.every((item) => reg.test(item));
  const same = [...new Set(arr)];
  if (!value || (Array.isArray(arr) && !arr.length)) {
    // return Promise.reject('请输入邮箱');
    return Promise.resolve();
  } else if (!flag) {
    return Promise.reject('邮箱格式错误');
  } else if (same.length < arr.length) {
    return Promise.reject('邮箱重复');
  }
  return Promise.resolve();
};

/**
 * description: 数字格式化加上单位B,M,K
 * @param {number} originFixed 小于一千时保留的位数，默认2
 * @param {number} formatFixed 格式化之后保留的位数，默认2
 */
export const formatNumberToUnit = (
  num: number,
  originFixed = 2,
  formatFixed = 2,
) => {
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(formatFixed)}B`;
  }
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(formatFixed)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(formatFixed)}K`;
  }
  return num.toFixed(originFixed);
};

/**
 * @description 校验域名
 */
export const validateDomain = (value: string) => {
  if (!value) {
    return Promise.resolve();
  }
  const reg =
    /^(?!:\/\/)(?=[a-zA-Z0-9-]{1,63}\.)(xn--)?[a-zA-Z0-9]+(-[a-zA-Z0-9]+)*(\.[a-zA-Z]{1,})+$/;
  if (!reg.test(value)) {
    return Promise.reject('请输入正确的域名');
  }
  return Promise.resolve();
};

/**
 * @description: Generate key value map
 * @param {array} data - An array of objects
 * @param {keyof T} key - A key of an object within the data array item as the return map key
 * @param {keyof T} value - A key of an object within the data array item as the return map value
 * @returns {object} - An object with the value of key as the key and the value of value as the value of the
 *                     corresponding object.eg: { data.item[key]: data.item[value] }
 */
export const generateMap = <T, K extends keyof T, P extends keyof T>(
  data: T[],
  key: K,
  value: P,
) => {
  if (!Array.isArray(data)) throw new Error('data must be an array');
  const result = {} as Record<string, T[P]>;
  data.forEach((item) => {
    result[item[key] as string] = item[value];
  });
  return result;
};
