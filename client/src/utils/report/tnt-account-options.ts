// 更新买家和卖家选项
export function updatePartnerOptions(
  tnt_id: number[],
  demandList: any[],
  supplyList: any[],
  setSearchOptions: any,
) {
  const filterOptions = (list: any[], isBuyer: boolean) =>
    list
      .filter((v) => tnt_id.includes(v.tnt_id) || !tnt_id.length)
      .map((v) => ({
        label: `${isBuyer ? v.buyer_name : v.seller_name}(${
          isBuyer ? v.buyer_id : v.seller_id
        }-${v.tnt_id})`,
        value: isBuyer ? v.buyer_id : v.seller_id,
      }));

  // 更新选项
  const updateOptions = (newOptions: any[], key: string, options: any[]) => {
    const index = newOptions.findIndex((item) => item.key === key);
    if (index !== -1) newOptions[index].options = options;
  };

  const advPartnerOptions = filterOptions(demandList, true);
  const pubPartnerOptions = filterOptions(supplyList, false);

  setSearchOptions((preOptions: any[]) => {
    const newOptions = [...preOptions];
    updateOptions(newOptions, 'buyer_id', advPartnerOptions);
    updateOptions(newOptions, 'seller_id', pubPartnerOptions);
    return newOptions;
  });
}

// 更新时间维度排序逻辑
export function updateDateSort(dimensions: string[], sortedInfo: any) {
  const currentSortColumn = sortedInfo.columnKey;
  const targetColumn = dimensions.includes('month')
    ? 'month'
    : dimensions.includes('day')
    ? 'day'
    : null;

  // 当维度 day 和 month 切换时，同时没有其他维度的排序，则更新排序
  if (
    targetColumn &&
    currentSortColumn !== targetColumn &&
    (currentSortColumn === 'day' || currentSortColumn === 'month')
  ) {
    return { columnKey: targetColumn, order: sortedInfo.order };
  }
  return sortedInfo;
}
