import { TopBarSearchItem } from '@/components/TopBar';

interface Options {
  /**
   * 限制天数，默认 183 天，即 半年
   */
  maxAllowedDays: number;
  [key: string]: any;
}

/**
 * 根据日期范围调整表格列和列选项
 * @param dateRange 时间范围数组
 * @param columns 当前列名称列表
 * @param columnOptions 当前列选项（包含禁用状态）
 * @param options 配置选项
 * @returns 更新后的列名称列表和列选项列表
 */
export function updateColumnsByDateRange(
  dateRange: [moment.Moment, moment.Moment],
  columns: string[],
  columnOptions: TopBarSearchItem['options'],
  options?: Options,
) {
  const { maxAllowedDays = 183 } = options ?? {};

  const daysDifference = dateRange[1].diff(dateRange[0], 'days');
  const restrictedColumns = ['day', 'app_bundle_id', 'country', 'ad_format'];

  const shouldDisableColumns = daysDifference > maxAllowedDays;

  // 更新列选项（禁用/启用某些列）
  const updatedColumnOptions = (columnOptions ?? []).map((option) => ({
    ...option,
    disabled: shouldDisableColumns
      ? restrictedColumns.includes(option.value as string)
      : undefined,
  }));

  // 更新列名称，默认添加 month 并去重
  const updatedColumns = shouldDisableColumns
    ? Array.from(
        new Set([
          ...columns.filter((column) => !restrictedColumns.includes(column)),
          'month',
        ]),
      )
    : columns;

  return {
    shouldDisableColumns,
    updatedColumns,
    updatedColumnOptions,
  };
}
