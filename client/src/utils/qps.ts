/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-17 21:49:38
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-05-17 21:49:39
 * @Description:
 */

export const qpsList2Map = (list: any[]) => {
  const arr: { value: number; label: string }[] = [];
  const map = new Map();
  list.forEach((item) => {
    let value = parseFloat(item);
    if (item[item.length - 1] === 'k') {
      value *= 1000;
    }
    map.set(value, item);
    arr.push({ value, label: item });
  });
  return {
    arr,
    map,
  };
};

export const qpsForamt = (qps: number) => {
  if (typeof qps !== 'number') return '';

  if (qps && qps >= 1000) {
    let formatNum = (qps / 1000).toFixed(2);
    return formatNum.endsWith('.00')
      ? formatNum.slice(0, -3) + 'k'
      : formatNum.endsWith('0')
      ? formatNum.slice(0, -1) + 'k'
      : formatNum + 'k';
  } else {
    return qps.toString();
  }
};
export const handleFilterNumber = (input: any, option: any) => {
  const tmp = option.value.toString() || '';
  const str = tmp && Array.isArray(tmp) ? tmp.join('') : `${tmp}`;
  return (str && str.indexOf(input) >= 0) || false;
};
