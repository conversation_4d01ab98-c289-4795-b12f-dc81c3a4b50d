import OperateRender, {
  type OperateRenderItem,
} from '@/components/OperateRender';
import { type ColumnType } from '@/components/Table/FrontTable';
import { type ReactNode } from 'react';

type Options = {
  width?: number;
  access?: string;
  btnOptions: OperateRenderItem[];
  title?: string | ReactNode;
};

/**
 * 生成 table 操作列
 * @param options - Options 操作列配置
 * @param options.title - string | ReactNode 可选的列标题，默认为'Operation'
 * @param options.width - number 列宽度，默认为80
 * @param options.access - string 权限控制
 * @param options.btnOptions - OperateRenderItem[] 操作按钮配置
 * @returns 操作列类型
 */
export function genOperateColumn<T extends object>(
  options: Options,
): ColumnType<T> {
  const { width = 80, access, btnOptions, title = 'Operation' } = options;

  return {
    title,
    dataIndex: 'operate',
    width,
    fixed: 'right',
    access,
    render: (_, params) => (
      <OperateRender btnOptions={btnOptions} params={params} />
    ),
  };
}
