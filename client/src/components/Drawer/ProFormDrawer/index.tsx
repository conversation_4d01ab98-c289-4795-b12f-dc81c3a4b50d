import NormalDrawer from '@/components/Drawer/NormalDrawer';
import { Form, FormInstance } from 'antd';
import React, { useImperativeHandle, useRef, useState } from 'react';

export type ProFormDrawerToolRef<T> = {
  openDrawer: (params?: T) => void;
  closeDrawer: () => void;
  form: FormInstance;
};

type Props<T> = {
  drawerToolRef: React.RefObject<ProFormDrawerToolRef<T>>;
  initialValues?: Partial<T> | null;
  // form items
  blackName?: string | ((isEdit: boolean) => string);
  children: (isEdit: boolean) => React.ReactElement;
  request?: (isEdit: boolean) => (params: any) => Promise<any>;
  // 接口成功的回调
  onSuccess?: (res: any) => void;
  // 接口失败的回调
  onError?: (error: any) => void;
};

const ProFormDrawer = <T,>({
  drawerToolRef,
  initialValues,
  blackName,
  children,
  request,
  onSuccess,
  onError,
}: Props<T>) => {
  const [form] = Form.useForm<T | undefined>();
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const initialValuesRef = useRef<T | null>(null);

  useImperativeHandle(
    drawerToolRef,
    () => ({
      openDrawer: (row?: T) => {
        setOpen(true);
        form.resetFields();
        if (row) {
          form.setFieldsValue({ ...row });
          initialValuesRef.current = row;
        } else {
          if (initialValues) {
            form.setFieldsValue(initialValues);
          }
          initialValuesRef.current = null;
        }
        setIsEdit(!!row);
      },
      closeDrawer: () => {
        setOpen(false);
      },
      form,
    }),
    [drawerToolRef, form, initialValues],
  );

  const handleConfirm = async () => {
    const values = await form.validateFields();

    const formattedValues = Object.fromEntries(
      Object.entries(values || {}).filter(([k, v]) => {
        // 过滤掉 undefined 和 null
        return v !== undefined && v !== null;
      }),
    );

    setLoading(true);
    request?.(isEdit)(formattedValues as Partial<T>)
      .then((res) => {
        onSuccess?.(res);
      })
      .catch((error) => {
        onError?.(error);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getBlackName = () => {
    if (typeof blackName === 'function') {
      return blackName(isEdit);
    }
    return blackName || (isEdit ? `Edit` : `Create`);
  };

  return (
    <NormalDrawer
      blackName={getBlackName()}
      onConfirm={handleConfirm}
      open={open}
      onClose={() => {
        setOpen(false);
        setIsEdit(false);
      }}
      loading={loading}
      maskClosable={false}
    >
      <Form form={form} layout="vertical">
        {children(isEdit)}
      </Form>
    </NormalDrawer>
  );
};

export default ProFormDrawer;
