.normal-select-container {
  color: #252829;
  :global {
    .ant-select-selector {
      border-radius: 6px !important;
    }
  }
}
.normal-select-multiple-container {
  :global {
    .ant-select-selection-item {
      background: #ebeff0;
      border-radius: 6px;
      color: #252829;
    }
  }
}

.normal-select-dropdown {
  padding: 12px;
  :global {
    .ant-select-item-option-disabled {
      .ant-select-item-option-content {
        color: #8d9799;
      }
    }
    .ant-select-item-option-selected {
      border-radius: 6px;
    }
    .ant-select-item.ant-select-item-option.ant-select-item-option-active {
      border-radius: 6px !important;
    }
  }
}

.tooltip-container {
  max-height: 300px;
  overflow-y: auto;
}
.placeholder-container {
  display: flex;
  flex-direction: column;
}
