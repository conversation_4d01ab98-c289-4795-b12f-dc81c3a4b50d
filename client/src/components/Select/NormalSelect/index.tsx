/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-11 11:35:42
 * @Description:
 */
import React, { useEffect } from 'react';
import { Select, Tooltip, message } from 'antd';
import type { SelectProps } from 'antd';
import styles from './index.less';

type Props = SelectProps & {
  limitSelectCount?: number;
};
function NormalSelect(props: Props): JSX.Element {
  const flag = props.mode === 'multiple' || props.mode === 'tags';
  const { limitSelectCount, ...selectProps } = props;
  const handleChange = (selectedValues: any, option: any) => {
    if (flag && limitSelectCount && selectedValues.length > limitSelectCount) {
      message.warn(`The number of options cannot exceed ${limitSelectCount}`);
      return;
    }
    selectProps.onChange?.(selectedValues, option);
  };
  return (
    <Select
      maxTagPlaceholder={(omittedValues) => (
        <Tooltip
          overlayClassName={styles['tooltip-container']}
          placement="topRight"
          title={
            <div className={styles['placeholder-container']}>
              {omittedValues.map(({ label }, index) => (
                <span key={index}>{label}</span>
              ))}
            </div>
          }
        >
          <span style={{ cursor: 'pointer' }}>+{omittedValues.length}...</span>
        </Tooltip>
      )}
      maxTagCount="responsive"
      optionFilterProp="children"
      filterOption={(input, option: any) =>
        option &&
        option.label &&
        option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
      }
      placeholder="Please Select"
      allowClear={!!flag}
      {...selectProps}
      className={`${styles['normal-select-container']} ${
        flag ? styles['normal-select-multiple-container'] : ''
      } `}
      onChange={handleChange}
      popupClassName={styles['normal-select-dropdown']}
    />
  );
}

NormalSelect.Option = Select.Option;

export default NormalSelect;
