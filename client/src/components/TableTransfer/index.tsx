/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-01-12 15:10:20
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-18 16:53:06
 * @Description:
 */
import { Transfer } from 'antd';
import type { ColumnsType, TableRowSelection } from 'antd/es/table/interface';
import type { TransferItem, TransferProps } from 'antd/es/transfer';
import React from 'react';
import styles from './index.less';
import type { TableProps } from 'antd';
import SearchTable from '@/components/Table/SearchTable';

type TableTransferProps<T, K> = TransferProps<T> & TableProps<T> & {
  dataSource: T[];
  leftColumns: ColumnsType<T>[];
  rightColumns: ColumnsType<T>[];
  rowKey: string;
  loading: boolean;
  leftSelectKeys: React.Key[];
  rightSelectKeys: React.Key[];
  setLeftSelectKeys: (params: React.Key[]) => void;
  setRightSelectKeys: (params: React.Key[]) => void;
  searchKeys: K[];
  placeholder: string;
}

// Customize Table Transfer
function TableTransfer<T extends object>({ leftColumns, rightColumns, dataSource, rowKey,
  loading, scroll, leftSelectKeys, rightSelectKeys, placeholder, searchKeys,
  setLeftSelectKeys, setRightSelectKeys, ...restProps }: TableTransferProps<T, keyof T>): JSX.Element {
  const handleLeftRowChange = (curSelectedRowKeys: React.Key[]) => {
    setLeftSelectKeys(curSelectedRowKeys);
  };
  const handleRightRowChange = (curSelectedRowKeys: React.Key[]) => {
    setRightSelectKeys(curSelectedRowKeys);
  };

  const handleRowChange = (curSelectedRowKeys: React.Key[], direction: 'left' | 'right') => {
    if (direction === 'left') {
      handleLeftRowChange(curSelectedRowKeys);
    } else {
      handleRightRowChange(curSelectedRowKeys);
    }
  };

  return <Transfer
    {...restProps}
    dataSource={dataSource}
    className={styles['transfer-container']}
    showSelectAll={false}
  >
    {({
      direction,
      filteredItems,
      onItemSelectAll,
      disabled: listDisabled
    }) => {
      const columns = direction === 'left' ? leftColumns : rightColumns;
      const listSelectedKeys = direction === 'left' ? leftSelectKeys : rightSelectKeys;
      const rowSelection: TableRowSelection<T & TransferItem> = {
        getCheckboxProps: item => ({ disabled: listDisabled || item.disabled }),
        selectedRowKeys: listSelectedKeys,
        onChange: (curSelectedRowKeys: React.Key[]) => {
          handleRowChange(curSelectedRowKeys, direction);
          if (curSelectedRowKeys.length) {
            onItemSelectAll(curSelectedRowKeys as string[], true);
          } else {
            const keys = filteredItems.map((item: any) => item.key);
            onItemSelectAll(keys, false);
          }
        }
      };
      return (
        <SearchTable
          rowKey={rowKey}
          rowSelection={rowSelection}
          columns={columns as any}
          dataSource={filteredItems}
          loading={loading}
          searchKeys={searchKeys}
          placeholder={placeholder}
          style={{ pointerEvents: listDisabled ? 'none' : undefined }}
          onRow={(item: any) => ({
            onClick: () => {
              if (listDisabled || item.disabled) return;
              const flag = !listSelectedKeys.includes(item.key) || !listSelectedKeys.length;
              if (flag) {
                handleRowChange([item.key], direction);
                const keys = filteredItems.map((item: any) => item.key);
                onItemSelectAll(keys, false);
                onItemSelectAll([item.key], true);
              }
            }
          })}
          scroll={scroll}

        />
      );
    }}
  </Transfer>;
};
export default TableTransfer;
