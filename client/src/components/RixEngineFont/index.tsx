/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-01-05 16:48:50
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-14 16:40:38
 * @Description:
 */
import React from 'react'
import { createFromIconfontCN } from '@ant-design/icons';
import type { IconFontProps } from '@ant-design/icons/lib/components/IconFont';
import { IconFontUrl } from '@/constants';
import * as icons from '@ant-design/icons';
import styles from './index.less';

const RixEngineFont = createFromIconfontCN({
  scriptUrl: IconFontUrl[(process.env.UMI_ENV as string) || 'prod'],
});

function IconFont({
  type,
  handleClick,
  onClick,
  ...rest
}: IconFontProps & { handleClick?: (e?: any) => void }): JSX.Element {
  // 兼容，对前缀做统一处理
  const finalType = type.indexOf('rix-') === -1 ? `rix-${type}` : type;
  const isAntd = type.includes('antd')
  const AntIcon: { [key: string]: any } = icons;
  if(isAntd) {
    const icon = type.replace('antd-', '');
    if(AntIcon[icon]) {
      return React.createElement(AntIcon[icon]);
    }
    return <></>
  } else {
    return (
      <RixEngineFont
        {...rest}
        type={finalType}
        onClick={rest.disabled ? undefined : handleClick || onClick}
        className={rest.disabled ? styles['disabled'] : rest.className || ''}
      />
    );
  }
}
export default IconFont;
