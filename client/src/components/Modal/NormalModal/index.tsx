/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-29 17:38:27
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-11 11:08:14
 * @Description:
 */
import React from 'react';
import { Modal } from 'antd';
import type { ModalProps, ModalFuncProps } from 'antd';
import styles from './index.less';

function NormalModal(props: ModalProps): JSX.Element {
  const { className } = props;
  return (
    <Modal
      {...props}
      className={`${styles['normal-modal-container']} ${className}`}
    />
  );
}

NormalModal.confirm = (props: ModalFuncProps) => {
  Modal.confirm({
    ...props,
    className: styles['normal-confirm-modal-container']
  });
};

NormalModal.success = (props: ModalFuncProps) => {
  Modal.success({
    ...props,
    className: styles['normal-confirm-modal-container']
  });
};

export default NormalModal;
