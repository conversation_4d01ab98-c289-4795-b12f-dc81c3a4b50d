@import (reference) '~antd/es/style/themes/index';

@pro-header-hover-bg: rgba(0, 0, 0, 0.025);

.menu {
  box-shadow: none;
  margin-bottom: 12px;
  :global(.anticon) {
    margin-right: 8px;
  }
  :global(.ant-dropdown-menu-item) {
    width: 200px;
    margin: 0 16px;
    border-radius: 6px;
  }
  :global {
    .ant-dropdown-menu-title-content {
      color: #252829;
    }
  }
}

.right {
  display: flex;
  float: right;
  height: 48px;
  margin-left: auto;
  overflow: hidden;
  .action {
    display: flex;
    align-items: center;
    height: 48px;
    padding: 0 12px;
    cursor: pointer;
    transition: all 0.3s;
    > span {
      vertical-align: middle;
      color: #252829;
    }
  }
  .account {
    min-width: 100px;
    .avatar {
      margin-right: 8px;
      color: @primary-color;
      vertical-align: top;
      background: rgba(255, 255, 255, 0.85);
    }
  }
  .right-name {
    max-width: 160px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.dropdown-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  margin-top: 12px;
  width: 220px;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  .top {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 2px;
    width: 100%;
    p {
      margin: 0px;
      height: 26px;
      width: 100%;
      display: flex;
      align-items: center;
      color: #5E6466;
      padding: 0px 12px;
    }
  }
  :global {
    .ant-dropdown-menu {
      box-shadow: none;
      width: 100%;
      li {
        border-radius: 6px;
        margin-bottom: 3px !important;
        &:hover {
          font-weight: bold;
          color: #1677ff;
          background-color: #e6e8eb !important;
        }
        &:last-child {
          margin-bottom: 0px !important;
        }
      }
    }
  }
}
