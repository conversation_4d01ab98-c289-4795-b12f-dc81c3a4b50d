/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:06:05
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-26 17:18:06
 * @Description:
 */
import React, { useEffect } from 'react';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Divider, Dropdown, MenuProps } from 'antd';
import { history, useModel, useLocation } from 'umi';
import { stringify } from 'querystring';
import styles from './index.less';
import { logOut } from '@/services/api';
import type { ItemType } from 'antd/lib/menu/hooks/useItems';
import { LoginPath } from '@/constants';
import HoverTooltip from '@/components/Tooltip/BundleTooltip';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = () => {
  const { initialState, setInitialState } = useModel('@@initialState');
  const { setPreviousUrl } = useModel('usePreviousUrl');
  const location = useLocation();
  const primaryColor = initialState?.settings?.primaryColor ?? '';

  useEffect(() => {
    // 保存上一页的路由
    const unListen = history.listen(() => {
      if (location && location.pathname !== LoginPath) {
        setPreviousUrl(`${location.pathname}${decodeURI(location.search)}`);
      } else {
        setPreviousUrl('');
      }
    });
    return () => {
      unListen();
    };
  }, []);

  /**
   * 退出登录，并且将当前的 url 保存
   */
  const loginOut = async () => {
    logOut();
    const { search, pathname } = location;
    // Note: There may be security issues, please note
    if (window.location.pathname !== '/user/login') {
      const obj = {
        pathname: '/user/login',
        search: stringify({
          redirect: pathname + search,
        }),
      };
      history.replace(obj);
    }
  };

  const onMenuClick: MenuProps['onClick'] = ({ key }) => {
    if (key === 'logout') {
      setInitialState((s) => ({
        ...s,
        currentUser: undefined,
        isCollapsed: initialState?.isCollapsed || false,
      }));
      loginOut();
      return;
    } else if (key === 'my-account') {
      history.push('/my-account');
    }
  };
  // 没有登录
  if (
    !initialState ||
    !initialState.currentUser ||
    !initialState.currentUser.account_name
  ) {
    // 重定向到登录页面
    if (location.pathname !== LoginPath) {
      history.push(LoginPath);
    }
    return null;
  }

  const items: ItemType[] = [
    {
      label: '账号信息',
      key: 'my-account',
    },
    {
      label: 'Sign Out',
      key: 'logout',
    },
  ];

  return (
    <Dropdown
      placement="bottomLeft"
      menu={{ items: items, onClick: onMenuClick }}
      dropdownRender={(menus: React.ReactNode) => (
        <div className={styles['dropdown-container']}>
          <div className={styles['top']}>
            <Avatar
              size="large"
              style={{ marginTop: 8 }}
              icon={<UserOutlined style={{ color: primaryColor }} />}
              alt="avatar"
            />
            <p style={{ paddingTop: 8 }}>
              <span style={{ paddingRight: '8px', flexShrink: 0 }}>昵称:</span>
              <HoverTooltip
                placement="top"
                title={initialState.currentUser?.display_name}
              >
                <span>{initialState.currentUser?.display_name || '-'}</span>
              </HoverTooltip>
            </p>
            <p className={styles['border']}>
              <span style={{ paddingRight: '8px', flexShrink: 0 }}>账号:</span>
              <HoverTooltip
                placement="top"
                title={initialState.currentUser?.account_name}
              >
                <span>{initialState.currentUser?.account_name}</span>
              </HoverTooltip>
            </p>
            <Divider style={{ marginTop: 8, marginBottom: 4 }} />
          </div>
          {menus}
        </div>
      )}
    >
      <span className={`${styles.action} ${styles.account}`}>
        <Avatar
          size="small"
          className={styles.avatar}
          icon={<UserOutlined style={{ color: primaryColor }} />}
          alt="avatar"
        />
        <span className={styles['right-name']}>
          {initialState.currentUser?.display_name ||
            initialState.currentUser?.account_name}
        </span>
      </span>
    </Dropdown>
  );
};

export default AvatarDropdown;
