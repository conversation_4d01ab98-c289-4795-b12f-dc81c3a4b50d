/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-16 18:28:06
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-16 18:28:07
 * @Description: 
 */
import React from 'react'
import * as icons from '@ant-design/icons';
import * as IconMap from '@/icons';
import IconDesign from '@ant-design/icons';

// 传入Icon名称返回Icon组件
const Icon = (props: { icon: string }): JSX.Element => {
    const { icon } = props;
    const antIcon: { [key: string]: any } = icons;
    if(!icon) {
      return <></>;
    }
    if(antIcon[icon]) {
      return React.createElement(antIcon[icon]);
    } else{
      return <IconDesign component={IconMap[icon.replace('-cus', '') as (keyof {})]} style={{ fontSize: 14, width: '14px', height: '14px' }} />
    }
};

export default Icon;
