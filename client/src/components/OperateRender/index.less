.operate-btn-container {
  width: 100%;
  display: flex;
  overflow: hidden;
  flex-wrap: nowrap;
  justify-content: flex-start;
  button {
    padding-left: 0;
    &:nth-child(2) {
      margin-right: 8px;
    }
  }
}
.flagClass {
  width: 100%;
  flex: 1;
  display: flex;
  justify-content: flex-end;
}
.operate-btn-popover {
  :global {
    .ant-popover-inner {
      border-radius: 6px;
    }
    .ant-popover-inner-content {
      padding: 12px;
      min-width: 130px;
      button {
        width: 100%;
        justify-content: flex-start;
        &:hover {
          background: #edeff0;
        }
      }
    }
  }
}
