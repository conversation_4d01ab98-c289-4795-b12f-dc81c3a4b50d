/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 16:26:27
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-28 11:18:23
 * @Description:
 */
import EditButton from '@/components/Button/EditButton';
import styles from './index.less';
import RixEngineFont from '@/components/RixEngineFont';
import NormalPopover from '../Popover';

export type OperateRenderItem = {
  label: string;
  icon?: any;
  onClick?: (params: any) => void;
  hide?: boolean | ((params: any) => boolean);
  isDelete?: boolean;
  disabled?: boolean;
  handleDisabled?: (params: any) => boolean;
};

type OperateRenderProps = {
  btnOptions: OperateRenderItem[];
  params: any;
  trigger?: 'hover' | 'click';
  isExpendTable?: boolean;
};

export default function OperateRender({
  btnOptions,
  params,
  trigger = 'hover',
  isExpendTable = false,
}: OperateRenderProps): JSX.Element {
  const allOptions = btnOptions.filter((item) => {
    return (
      (typeof item.hide === 'boolean' && !item.hide) ||
      (typeof item.hide === 'function' && !item.hide(params)) ||
      typeof item.hide === 'undefined'
    );
  });

  const flag = allOptions.length <= 2;
  const restOptions = allOptions.slice(1);

  // 阻止事件冒泡
  const handleClick = (e: any, item: OperateRenderItem) => {
    e.stopPropagation();
    item.onClick && item.onClick(params);
  };
  return (
    <div
      className={`${
        isExpendTable ? styles['flagClass'] : styles['operate-btn-container']
      }`}
    >
      {flag ? (
        // <div className={styles['flagClass']}></div>
        allOptions.map((item, index) => (
          <EditButton
            onClick={(e) => item.onClick && handleClick(e, item)}
            key={index}
            icon={item.icon}
            danger={item.isDelete}
            disabled={item.disabled || item.handleDisabled?.(params)}
          >
            {item.label}
          </EditButton>
        ))
      ) : (
        <>
          <EditButton
            onClick={(e) =>
              allOptions[0].onClick && handleClick(e, allOptions[0])
            }
            icon={allOptions[0].icon}
          >
            {allOptions[0].label}
          </EditButton>
          <NormalPopover
            content={restOptions.map((item, index) => (
              <EditButton
                onClick={(e) => item.onClick && handleClick(e, item)}
                key={index}
                icon={item.icon}
              >
                {item.label}
              </EditButton>
            ))}
            trigger={trigger}
          >
            <EditButton icon={<RixEngineFont type="more" />}>More</EditButton>
          </NormalPopover>
        </>
      )}
    </div>
  );
}
