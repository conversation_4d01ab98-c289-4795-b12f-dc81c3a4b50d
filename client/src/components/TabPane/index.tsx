/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-21 16:53:39
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 16:53:41
 * @Description: 
 */

import React, { useEffect, useState } from 'react';
import styles from './index.less';
import { Tooltip } from 'antd';

export type TabPaneItem = {
  label: string;
  key: string | number;
  icon?: string;
  tooltip?: string;
}

type TabPaneProps = {
  items: TabPaneItem[];
  onTabChange?: (key: string | number) => void;
  defaultTabKey?: string | number;
}

function TabPane({ items, onTabChange, defaultTabKey }: TabPaneProps): JSX.Element {
  const [currentTab, setCurrentTab] = useState(defaultTabKey);

  const handleTabChange = (item: TabPaneItem) => {
    setCurrentTab(item.key);
    onTabChange && onTabChange(item.key);
  };
  useEffect(() => {
    if (defaultTabKey) {
      setCurrentTab(defaultTabKey);
    }
  }, [defaultTabKey]);
  return (
    <div className={styles['tab-pane-container']}>
      {
        items.map((item, index) =>
          <div key={index} className={currentTab === item.key ? styles['active'] : ''} onClick={() => handleTabChange(item)}>
            {
              item.tooltip ? <Tooltip title={item.tooltip}>
                <span>{item.label}</span>
              </Tooltip> : <span>{item.label}</span>
            }

          </div>)
      }
    </div>
  );
}

export default TabPane;
