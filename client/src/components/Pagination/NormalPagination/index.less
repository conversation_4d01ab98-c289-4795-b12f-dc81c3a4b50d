.pagination-container {
  height: 40px;
  display: flex;
  align-items: center;
  li {
    height: 32px !important;
    line-height: 32px !important;
  }
  :global {
    .ant-pagination-options > div .ant-select-selector {
      height: 32px !important;
      border-radius: 6px;
      display: flex;
      align-items: center;
      color: #252829;
    }
    .ant-pagination-item {
      a {
        color: #5e6466;
        font-weight: 700;
        &:hover {
          background: #f3f5f5;
          border-radius: 6px;
        }
      }
    }
    .ant-pagination-item-active {
      border-radius: 6px;
      background: #daf3f7;
      border: none;
      a {
        color: #4692f5;
      }
    }
    .ant-pagination-total-text {
      color: #252829;
    }
    .ant-pagination-prev,
    .ant-pagination-next,
    .ant-pagination-item {
      min-width: 36px !important;
      margin-right: 6px !important;
    }
    .ant-pagination-next,
    .ant-pagination-prev {
      .ant-pagination-item-link {
        color: #5e6466;
        &:hover {
          background: #f3f5f5;
          border-radius: 6px;
          color: #5e6466;
        }
      }
    }
    .ant-pagination-disabled .ant-pagination-item-link,
    .ant-pagination-disabled:hover .ant-pagination-item-link {
      color: #d7dadb;
      &:hover {
        background: none;
      }
    }
  }
}
