import React, { useState, useEffect } from 'react';
import { Input, Button, notification, Modal } from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { isValidBundle } from '@/utils';
import styles from './index.less';

type BundleProps = {
  visible: boolean;
  handleOk: (val: any) => void;
  handleCancel: () => void;
  defaultList: string[];
  validator?: (val: string) => boolean;
  title?: string;
  tips?: string;
  maskClosable?: boolean;
  keyboard?: boolean;
  isReverse?: boolean; // 值是否是反过来的
  isEdit?: boolean; // 单个编辑
  currentEditBundle?: string;
};

function BundleInput({
  defaultList,
  visible,
  handleCancel,
  handleOk,
  validator,
  title,
  tips,
  maskClosable = false,
  keyboard = false,
  isReverse = false,
  isEdit,
  currentEditBundle,
}: BundleProps): JSX.Element {
  const [value, setValue] = useState('');
  const [editBundleList, setEditBundleList] = useState<string[]>([]);
  const [singleBundle, setSingleBundle] = useState<string>('');

  useEffect(() => {
    if (isEdit) {
      setSingleBundle(currentEditBundle as string);
    }
  }, [isEdit, currentEditBundle]);

  // 校验是否合规跟重复
  const handleAdd = () => {
    const data = isReverse
      ? [...editBundleList, ...defaultList]
      : [...defaultList, ...editBundleList];
    const newBundleList = [...new Set(data)];
    // 查找重复
    const repeat = defaultList.filter((bundle) => {
      return [...new Set(editBundleList)].includes(bundle);
    });
    const validBundle: string[] = [];
    const invalidBundle: string[] = [];

    newBundleList.forEach((bundle) => {
      if ((validator && validator(bundle)) || isValidBundle(bundle)) {
        validBundle.push(bundle);
      } else {
        invalidBundle.push(bundle);
      }
    });

    const diff = validBundle.filter((bundle) => {
      return !defaultList.includes(bundle);
    });
    handleOk(validBundle);
    // 关闭弹窗
    handleCancel();
    if (
      invalidBundle.length > 0 ||
      repeat.length > 0 ||
      validBundle.length > 0
    ) {
      const args = {
        message: 'Add',
        description: (
          <>
            {diff.length > 0 && (
              <div>
                <CheckCircleOutlined
                  style={{
                    color: '#0DB4BE',
                    fontSize: '16px',
                    paddingRight: '5px',
                  }}
                />
                <span>{`succeed: `}</span>
                <div className={styles['tips-content']}>
                  {diff.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
            {repeat.length > 0 && (
              <div>
                <CloseCircleOutlined
                  style={{
                    color: '#F75941',
                    fontSize: '16px',
                    paddingRight: '5px',
                  }}
                />
                <span>{`repeated: `}</span>
                <div className={styles['tips-content']}>
                  {repeat.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
            {invalidBundle.length > 0 && (
              <div>
                <CloseCircleOutlined
                  style={{
                    color: '#F75941',
                    fontSize: '16px',
                    paddingRight: '5px',
                  }}
                />
                <span>{`invalid: `}</span>
                <div className={styles['tips-content']}>
                  {invalidBundle.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </>
        ),
        className: 'tips-billing-contract',
        duration: 5,
      };
      notification.open(args);
    }
    setValue('');
  };
  const handleDelete = () => {
    let newBundleList = [...new Set(defaultList)];

    const notFound = editBundleList.filter((bundle) => {
      return !newBundleList.includes(bundle);
    });

    newBundleList = newBundleList.filter((bundle) => {
      return !editBundleList.includes(bundle);
    });

    const diff = defaultList.filter((bundle) => {
      return !newBundleList.includes(bundle);
    });
    handleOk(newBundleList);
    // 关闭弹窗
    handleCancel();
    if (diff.length > 0 || notFound.length > 0) {
      const args = {
        message: 'Delete',
        description: (
          <>
            {diff.length > 0 && (
              <div>
                <CheckCircleOutlined
                  style={{
                    color: '#0DB4BE',
                    fontSize: '16px',
                    paddingRight: '5px',
                  }}
                />
                <span>{`succeed: `}</span>
                <div className={styles['tips-content']}>
                  {diff.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
            {notFound.length > 0 && (
              <div>
                <CloseCircleOutlined
                  style={{
                    color: '#F75941',
                    fontSize: '16px',
                    paddingRight: '5px',
                  }}
                />
                <span>{`not found failure: `}</span>
                <div className={styles['tips-content']}>
                  {notFound.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </>
        ),
        className: 'tips-billing-contract',
        duration: 5,
      };
      notification.open(args);
    }
    setValue('');
  };
  const handleOverWrite = () => {
    const newBundleList = [...new Set(editBundleList)];
    const validBundle: string[] = [];
    const invalidBundle: string[] = [];
    newBundleList.forEach((bundle) => {
      if ((validator && validator(bundle)) || isValidBundle(bundle)) {
        validBundle.push(bundle);
      } else {
        invalidBundle.push(bundle);
      }
    });
    // 翻转数据
    const data = validBundle;
    handleOk(data);
    // 关闭弹窗
    handleCancel();
    if (invalidBundle.length > 0) {
      const args = {
        message: 'Overwrite',
        description: (
          <>
            {validBundle.length > 0 && (
              <div>
                <CheckCircleOutlined
                  style={{
                    color: '#0DB4BE',
                    fontSize: '16px',
                    paddingRight: '5px',
                  }}
                />
                <span>{`succeed: `}</span>
                <div className={styles['tips-content']}>
                  {validBundle.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
            {invalidBundle.length > 0 && (
              <div>
                <CloseCircleOutlined
                  style={{
                    color: '#F75941',
                    fontSize: '16px',
                    paddingRight: '5px',
                  }}
                />
                <span>{`invalid: `}</span>
                <div className={styles['tips-content']}>
                  {invalidBundle.map((v, index) => (
                    <p key={index} style={{ marginBottom: 0 }}>
                      {v}
                    </p>
                  ))}
                </div>
              </div>
            )}
          </>
        ),
        className: 'tips-billing-contract',
        duration: 5,
      };
      notification.open(args);
    }
    setValue('');
    setEditBundleList([]);
  };

  const handleSaveSingle = () => {
    const newBundleList = [...new Set(defaultList)].filter((v) => v);
    const findIndex = newBundleList.findIndex(
      (bundle) => bundle === currentEditBundle,
    );
    if (findIndex > -1) {
      newBundleList[findIndex] = singleBundle;
    }
    handleOk(newBundleList);
    handleCancel();
    setValue('');
    setEditBundleList([]);
  };

  const handleChangeValue = (val: { target: { value: string } }) => {
    if (isEdit) {
      setSingleBundle(val.target.value);
    } else {
      const tmp = val.target.value.split('\n').map((bundle) => {
        const val = bundle.trim();
        return val.replaceAll(',', '');
      });
      const res = tmp.filter((item, index) => {
        return item !== '' || (!item && index === tmp.length - 1);
      });
      // 解决空值问题
      setValue(res.join('\n'));
      setEditBundleList(tmp.filter((bundle) => bundle !== ''));
    }
  };
  return (
    <Modal
      title={title || 'Edit app ads List'}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={maskClosable}
      keyboard={keyboard}
      footer={
        <>
          {!isEdit && (
            <>
              <Button type="primary" onClick={handleAdd}>
                Add
              </Button>
              <Button type="primary" onClick={handleDelete}>
                Delete
              </Button>
              <Button type="primary" onClick={handleOverWrite}>
                OverWrite
              </Button>
            </>
          )}
          {isEdit && (
            <Button type="primary" onClick={handleSaveSingle}>
              Save
            </Button>
          )}
        </>
      }
    >
      {!isEdit && (
        <Input.TextArea
          rows={4}
          placeholder={tips || 'Enter the app ads (ad_domain,pub_acc_id,name)'}
          value={value}
          onChange={handleChangeValue}
        />
      )}
      {isEdit && (
        <Input value={singleBundle} onChange={handleChangeValue}></Input>
      )}
    </Modal>
  );
}

export default BundleInput;
