/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-09 16:13:55
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-29 19:15:55
 * @Description:
 */
import styles from './index.less';
import React, {
  ForwardedRef,
  forwardRef,
  ReactNode,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import {
  Typography,
  Checkbox,
  Button,
  Form,
  Tooltip,
  Row,
  Radio,
  message,
  Dropdown,
  DatePicker,
  DatePickerProps,
} from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import useSizeHook from '@/hooks/useSizeHook';
import { history, useModel } from '@umijs/max';
import {
  DownOutlined,
  UpOutlined,
  QuestionCircleOutlined,
  CloudDownloadOutlined,
  PaperClipOutlined,
} from '@ant-design/icons';
import { RangePickerProps } from 'antd/lib/date-picker';

import Select from '@/components/Select/NormalSelect';
import Input from '@/components/Input/NormalInput';
import AllSelectSearch from '@/components/Input/AllSelectSearch';
import type { FormInstance, FormProps } from 'antd';
import RangePicker from '@/components/NormalDatePicker';
import RixEngineFont from '@/components/RixEngineFont';
import NormalRadio from '@/components/Radio/NormalRadio';
import SearchBundle from '../SearchBundle';

import { UIConfig, TopBarButtonAuth } from '@/constants';
import {
  MetricsShortcutOptionsMap,
  MetricsShortcutOptions,
} from '@/constants/data-report/full-report';
import { TimeZoneMapLabel } from '@/constants/time-zone';
import { Rule } from 'antd/lib/form';

const { Title } = Typography;
const { SiderWidth, SiderCollapsedWidth } = UIConfig;

const DatePickerRange = {
  Today: [moment(), moment()],
  Yesterday: [
    moment().startOf('day').subtract(1, 'day'),
    moment().endOf('day').subtract(1, 'day'),
  ],
  'Before Yesterday': [
    moment().startOf('day').subtract(2, 'day'),
    moment().endOf('day').subtract(2, 'day'),
  ],
  '3 Days': [
    moment().startOf('day').subtract(3, 'day'),
    moment().subtract(1, 'day'),
  ],
  '7 Days': [
    moment().startOf('day').subtract(6, 'day'),
    moment().subtract(1, 'day'),
  ],
  'This Month': [moment().startOf('month'), moment().subtract(1, 'day')],
  'Last Month': [
    moment().subtract(1, 'months').startOf('month'),
    moment().subtract(1, 'months').endOf('month'),
  ],
  'Last 3 Months': [
    moment().subtract(3, 'month').startOf('month'),
    moment().subtract(1, 'months').endOf('month'),
  ],
  'Last 6 Months': [
    moment().subtract(6, 'month').startOf('month'),
    moment().subtract(1, 'months').endOf('month'),
  ],
  'This Year': [moment().startOf('year'), moment().subtract(1, 'day')],
  'Last Year': [
    moment().subtract(1, 'year').startOf('year'),
    moment().subtract(1, 'year').endOf('year'),
  ],
};

export type DateRangeKey = keyof typeof DatePickerRange;

// checkboxFold类型为需要自己折叠的类型
const types = [
  'select',
  'input',
  'checkbox',
  'date',
  'textarea',
  'checkboxFold',
  'selectAll',
  'bundle',
  'mutli-dates',
  'single-date',
] as const;
export type TopBarSearchItem = {
  name: string;
  type: (typeof types)[number];
  options?: {
    label: string | number;
    value: string | number;
    key?: string | number;
    disabled?: boolean;
    tooltip?: {
      title: string;
      placement?: 'top' | 'bottom' | 'left' | 'right';
      showTitle: boolean;
    };
  }[];
  value:
    | string
    | number
    | Moment
    | undefined
    | string[]
    | number[]
    | boolean
    | moment.Moment[];
  placeholder?: string;
  mode?: 'single' | 'multiple';
  key: string; // 唯一标识
  width?: number;
  allowClear?: boolean;
  tooltip?: ReactNode;
  defaultValue?:
    | string
    | number
    | Moment
    | undefined
    | string[]
    | number[]
    | boolean
    | moment.Moment[];
  minLength?: number;
  timeLimit?: number; // 默认的限制时间范围，单位为天
  dimensionTimeLimit?: {
    dimensionKey: string;
    limit: number;
    searchLimit?: number;
    hourLimit?: number; // 选中今天的时候限制的日期天数，不传默认为limit
  }[]; // 限制维度的可选时间，每一项对应着每个维度的时间限制
  labelIcon?: string;
  isNoExact?: boolean;
  handleClickLabelIcon?: () => void;
  required?: boolean;
  limit?: number;
  splitBy?: RegExp;
  disabled?: boolean;
  rules?: Rule[];
  limitSelectCount?: number; // 多选下拉框最多选择几个
};

export type SearchResultItem = {
  value: any;
  key: string;
  mode?: 'single' | 'multiple';
  isNoExact?: boolean; // 是否精确匹配
};

export type CheckboxUniqueKeyOptionsType = {
  key: string; // checkbox的key
  value: string[]; // 字段名称
};
type RangeValue = [Moment | null, Moment | null] | null;
type TopBarProps = FormProps & {
  title?: string;
  searchOptions: TopBarSearchItem[];
  isFold?: boolean;
  isExport?: boolean;
  toolButtons?: ReactNode;
  handleSearchChange: (value: SearchResultItem[]) => void;
  handleReload?: () => void;
  handleExport?: () => void;
  defaultFold?: boolean;
  labelWidth?: number; // label宽度 默认80
  loading?: boolean;
  isFront?: boolean; // 前端分页还是后端分页
  tabOptions?: { label: string; value: string | number }[];
  defaultTab?: string | number;
  onTabChange?: (tab: string | number) => void;
  defaultFormItemWidth?: number; // 通用的每个条件宽度
  defaultBoxItemWidth?: number; // checkbox label最长宽度
  defaultValue?: any;
  searchValue?: any;
  handleDisableDate?: (current: any) => boolean;
  isExportAll?: boolean;
  handleDownloadAll?: (data: any) => void;
  buttonAuth?: TopBarButtonAuth;
  disabledExport?: boolean;
  onCalendarChange?: (
    dates: any,
    dateStrings: [string, string],
    info: any,
  ) => void;
  defaultDates?: RangeValue;
  checkboxUniqueKeyOptions?: CheckboxUniqueKeyOptionsType[];
  dateRangeKeys?: DateRangeKey[]; // 默认全部
  downloadUrl?: string; // 直接下载文件的url
  downloadUrlTips?: string; // tooltip
  handleSearchValueChange?: (
    changeValue: any,
    allValue: any,
    isDefaultFold: boolean,
    form: FormInstance,
  ) => void;
  handleDefaultSearchChange?: (data: SearchResultItem[]) => void;
  ExtraRangePickerOptions?: RangePickerProps;
  allowSaveSearch?: boolean; // 是否允许路由保存搜索条件
  currentRouteSearch?: any; // 当前路由url中的搜索条件
  ExtraDatePickerOptions?: DatePickerProps;
};

export type TopBarRef = {
  getFormInstance: () => FormInstance;
};

function TopBar(
  {
    title,
    isFold,
    handleSearchChange,
    defaultFold = true,
    loading = false,
    isFront = true,
    isExport = false,
    toolButtons,
    handleExport,
    handleReload = () => {},
    labelWidth = 80,
    defaultFormItemWidth = 340,
    labelAlign,
    tabOptions = [],
    defaultTab = '',
    onTabChange = () => {},
    defaultBoxItemWidth = 270,
    defaultValue,
    searchOptions,
    searchValue,
    handleDisableDate,
    initialValues = {},
    isExportAll = false,
    handleDownloadAll,
    buttonAuth = {},
    disabledExport,
    defaultDates,
    checkboxUniqueKeyOptions,
    dateRangeKeys,
    downloadUrl,
    downloadUrlTips,
    handleSearchValueChange,
    handleDefaultSearchChange,
    ExtraRangePickerOptions,
    allowSaveSearch = false,
    currentRouteSearch,
    ExtraDatePickerOptions,
  }: TopBarProps,
  ref: ForwardedRef<TopBarRef>,
): JSX.Element {
  // 每行显示多少个
  const [normalNum, setNormalNum] = useState(3);
  const [boxNum, setBoxNum] = useState(5);
  // 通用的折叠
  const [isDefaultFold, setDefaultFold] = useState(defaultFold);
  const { size } = useSizeHook({});
  const { initialState } = useModel('@@initialState');
  const [form] = Form.useForm();
  const [currentTab, setCurrentTab] = useState(defaultTab);
  const [formValue, setFormValue] = useState<any>({});
  const [timeLimitObj, setTimeLimit] = useState<any>({}); // 获取存在时间限制的
  const [pickerRange, setPickerRange] = useState<any>({});
  // 是否显示折叠的
  const [showFold, setShowFold] = useState(true);

  useImperativeHandle(ref, () => ({
    getFormInstance: () => form,
  }));

  useEffect(() => {
    if (defaultValue) {
      form.setFieldsValue(defaultValue);
      setFormValue(defaultValue);
      const params = form.getFieldsValue();
      const data = handleTrimValue(params);
      handleDefaultSearchChange && handleDefaultSearchChange(data);
    }
  }, [defaultValue]);

  useEffect(() => {
    if (Array.isArray(dateRangeKeys)) {
      const obj: any = {};
      Object.keys(DatePickerRange).forEach((v: any) => {
        if (dateRangeKeys.includes(v)) {
          obj[v] = (DatePickerRange as any)[v];
        }
      });
      setPickerRange(obj);
    }
  }, [dateRangeKeys]);

  useEffect(() => {
    const timeLimitObj: any = {};
    searchOptions.forEach((v) => {
      if (v.type === 'date' && v.timeLimit) {
        timeLimitObj[v.key] = v.timeLimit;
      }
    });
    setTimeLimit(timeLimitObj);
  }, [searchOptions]);

  useEffect(() => {
    if (searchValue) {
      form.setFieldsValue(searchValue);
      setFormValue(searchValue);
    }
    // 前端表格
    if (currentRouteSearch) {
      form.setFieldsValue(currentRouteSearch);
    }
  }, [searchValue, currentRouteSearch]);

  const [isBottomFold, setBottomFold] = useState<Record<string, boolean>>({}); // checkbox折叠使用
  const handleBottomFold = (key: string) => {
    const tmp = JSON.parse(JSON.stringify(isBottomFold));
    tmp[key] = !isBottomFold[key];
    setBottomFold(tmp);
  };

  const { normalOptions, boxOptions } = useMemo(() => {
    const { width } = size;
    const tmpWidth = width - 16 * 2 - 20; // 去掉几个16padding,容器总宽度

    const rightWidth = initialState?.isCollapsed
      ? tmpWidth - SiderCollapsedWidth
      : tmpWidth - SiderWidth;

    const num = Math.floor(rightWidth / defaultFormItemWidth);

    // 去掉label的宽度以及fold宽度
    const box = Math.floor(
      (rightWidth - labelWidth - 65) / defaultBoxItemWidth,
    );

    setNormalNum(num);
    setBoxNum(box);

    const tmp = Math.floor((100 / (num || 1)) * 100) / 100;

    const boxTmp = Math.floor((100 / box) * 100) / 100;

    const normalOptions: TopBarSearchItem[] = [];
    const boxOptions: TopBarSearchItem[] = [];

    searchOptions.forEach((item) => {
      if (item.type === 'checkboxFold') {
        boxOptions.push({
          ...item,
          width: boxTmp,
        });
      } else {
        normalOptions.push({
          ...item,
          width: tmp,
        });
      }
    });

    if (normalOptions.length <= num) {
      setShowFold(false);
    } else {
      setShowFold(true);
    }
    // 初始化checkboxFold的折叠
    setBottomFold((prev) => {
      const updatedFold: Record<string, boolean> = {};
      boxOptions.forEach((item) => {
        updatedFold[item.key] = prev[item.key] ?? defaultFold;
      });
      return updatedFold;
    });

    return {
      normalOptions,
      boxOptions,
    };
  }, [
    searchOptions,
    size,
    initialState?.isCollapsed,
    isDefaultFold,
    defaultFold,
  ]);

  const handleTrimValue = (values: any) => {
    return Object.keys(values).map((item) => {
      let val: any = values[item];
      const tmp = searchOptions.find((v) => v.key === item);
      if (Array.isArray(values[item])) {
        val = values[item].map((v: any) => {
          if (typeof v === 'string') {
            return v.trim();
          }
          return v;
        });
      } else if (typeof values[item] === 'string') {
        val = values[item].trim();
      }
      return {
        value: val,
        key: item,
        mode: tmp?.mode,
      };
    });
  };

  const handleFinish = (values: any) => {
    if (allowSaveSearch) {
      const searchStr = Object.keys(values).reduce((pre, cur) => {
        if (Array.isArray(values[cur])) {
          if (values[cur].length) {
            return `${pre}${cur}=${values[cur].join(',')}&`;
          }
          return `${pre}`;
        } else if (values[cur] || +values[cur] === 0) {
          return `${pre}${cur}=${values[cur]}&`;
        } else {
          return pre;
        }
      }, '');
      history.replace({
        pathname: location.pathname,
        search: encodeURI(searchStr),
      });
    }
    const data = handleTrimValue(values);
    handleSearchChange(data);
    // 过滤掉空值
    const tmp = data.filter((item) => {
      if (Array.isArray(item.value)) {
        return item.value.length;
      } else {
        return item.value || +item.value === 0;
      }
    });
    // 前端刷新数据
    if (!tmp.length && isFront) {
      handleReload();
    }
  };

  const handleSearch = () => {
    form.submit();
  };

  const handleReset = () => {
    form.resetFields();
    if (defaultValue) {
      form.setFieldsValue(defaultValue);
      setFormValue(defaultValue);
    }
    handleSearchValueChange &&
      handleSearchValueChange({}, defaultValue || {}, isDefaultFold, form);
  };

  const handleChangeFold = () => {
    if (isDefaultFold) {
      setDefaultFold(false);
    } else {
      setDefaultFold(true);
    }
  };

  const handleTabChange = (e: any) => {
    form.resetFields();
    form.submit();
    setCurrentTab(e.target.value);
    onTabChange(e.target.value);
  };

  const handleDownload = () => {
    handleExport && handleExport();
  };

  const handleDisabledDateBy = (val: any) => {
    return (handleDisableDate && handleDisableDate(val)) || false;
  };

  const handleDateOpenChange = (open: boolean) => {
    if (!open) {
      const value = form.getFieldsValue();

      const arr = Object.keys(value);

      const keys = arr.filter((v) => timeLimitObj[v]);

      if (keys.length) {
        handleFormatTime(keys[0], value[keys[0]], timeLimitObj[keys[0]]);
      }

      handleDimensionLimit(form.getFieldValue('columns'));
    }
  };

  const handleDimensionLimit = (columns: string[]) => {
    // 这里注意，暂时只对一个日期选框做兼容，多个需要修改代码

    // 获取需要限制的维度
    const dimTimeOption = searchOptions
      ?.filter(
        (item: TopBarSearchItem) =>
          item.key === 'date' && item.dimensionTimeLimit,
      )
      ?.find(
        (item: TopBarSearchItem) => item.dimensionTimeLimit,
      )?.dimensionTimeLimit;

    const dimLimitKeys = dimTimeOption?.map((v) => v.dimensionKey);

    const formValue = form.getFieldsValue();

    // 是否填了需要限制的搜索条件
    const isSearchLimit = Object.keys(formValue)
      .filter((v) => formValue[v])
      ?.some((v) => dimLimitKeys?.includes(v));

    // 是否选中了需要限制的维度
    const isLimit = columns?.some((item) =>
      dimTimeOption?.map((v) => v.dimensionKey).includes(item),
    );
    if (dimTimeOption && dimTimeOption.length) {
      // 维度的限制天数
      const limit = dimTimeOption.sort((a, b) => a.limit - b.limit)[0].limit;
      // 搜索条件的限制天数
      const searchLimit = dimTimeOption.sort((a, b) => a.limit - b.limit)[0]
        .searchLimit;
      // 选了hour的限制天数
      const hourLimit = dimTimeOption.sort((a, b) => a.limit - b.limit)[0]
        .hourLimit;
      // 是否选中了hour
      const isHour = columns.includes('day_hour');
      if (isSearchLimit) {
        const message = `The Bundle or Unit ID filter conditions is limited to ${searchLimit} days.`;
        handleFormatTime('date', formValue.date, searchLimit || 0, message);
        // return true;
      }
      if (limit || hourLimit) {
        const finallyLimit = isHour && hourLimit ? hourLimit : limit;

        const message = isHour
          ? `The Hour dimension is limited to ${finallyLimit} days.`
          : `The Bundle or Unit ID dimension is limited to ${finallyLimit} days.`;
        isLimit &&
          handleFormatTime('date', formValue.date, finallyLimit, message);
        return true;
      }
    }
    return false;
  };
  const handleFormatTime = (
    key: string,
    val: Moment[],
    limit: number,
    msg?: string,
  ) => {
    let startDate: any = null;

    const str = msg || `The date interval needs to be within ${limit} days.`;

    if (Array.isArray(val) && val[0] && val[1]) {
      startDate = moment(moment(val[1])).subtract(limit - 1, 'days');
    }
    if (
      startDate &&
      val[0] &&
      val[0] < startDate &&
      startDate.format('YYYY-MM-DD') !== val[0].format('YYYY-MM-DD')
    ) {
      form.setFieldValue(key, [startDate, val[1]]);
      setFormValue({
        ...formValue,
        [key]: [startDate, val[1]],
      });
      message.warning(str);
    }
  };

  const disabledCheckBox = (item: TopBarSearchItem, val: any) => {
    const value = formValue[item.key];
    let flag = false;
    if (Array.isArray(value) && value.length) {
      flag = !!(
        item.minLength &&
        value.length <= item.minLength &&
        value.includes(val.value)
      );
      if (checkboxUniqueKeyOptions?.length) {
        const unique = checkboxUniqueKeyOptions.find((v) => v.key === item.key);
        if (unique) {
          const other = value.filter((v) => {
            return unique.value.includes(v);
          });
          if (
            unique.value.includes(val.value) &&
            !value.includes(val.value) &&
            other.length
          ) {
            flag = true;
          }
        }
      }
      // TODO 针对 billing 的特殊逻辑
    }
    return flag;
  };

  const handleDownloadAllTmp = () => {
    const data = form.getFieldsValue();
    const params: any = handleTrimValue(data);
    if (handleDownloadAll) {
      handleDownloadAll(params);
    }
  };

  const handleValueChange = (changeValue: any, allValue: any) => {
    if (changeValue?.columns) {
      const columns = changeValue.columns;
      handleDimensionLimit(columns);
    }
    if ('app_bundle_id' in changeValue || 'placement_id' in changeValue) {
      const formValue = form.getFieldsValue();
      handleDimensionLimit(formValue['columns']);
    }
    handleSearchValueChange &&
      handleSearchValueChange(changeValue, allValue, isDefaultFold, form);
    setFormValue(allValue);
  };

  const handleDownloadUrl = () => {
    if (downloadUrl) {
      const a = document.createElement('a');
      const arr = downloadUrl.split('/');
      const fileName = arr[arr.length - 1];
      a.setAttribute('href', downloadUrl);
      a.setAttribute('download', fileName);
      a.click();
    }
  };

  return (
    <div className={styles['topbar-container']}>
      <div className={styles['topbar-top-btn-container']}>
        {title && <Title level={5}>{`${title}`}</Title>}
        {tabOptions.length > 0 && (
          <NormalRadio value={currentTab} onChange={handleTabChange}>
            {tabOptions.map((item, index) => (
              <Radio key={index} value={item.value}>
                {item.label}
              </Radio>
            ))}
          </NormalRadio>
        )}
        <div className={styles['topbar-top-btn-right']}>
          {toolButtons}
          <Tooltip title="Search">
            <Button
              type="primary"
              icon={<RixEngineFont type="rix-search" />}
              style={{ width: 40 }}
              onClick={handleSearch}
              loading={loading}
              disabled={buttonAuth?.Search}
            />
          </Tooltip>
          {isExport && (
            <Tooltip title="Export">
              <Button
                type="primary"
                icon={<RixEngineFont type="download" />}
                style={{ width: 40 }}
                onClick={handleDownload}
                disabled={buttonAuth?.Export || loading || disabledExport}
              />
            </Tooltip>
          )}
          {isExportAll && (
            <Tooltip title="Export all, only supports exporting up to 10,000 rows">
              <Button
                type="primary"
                icon={<CloudDownloadOutlined />}
                style={{ width: 40 }}
                onClick={handleDownloadAllTmp}
                disabled={buttonAuth?.ExportAll || loading || disabledExport}
              />
            </Tooltip>
          )}
          <Tooltip title="Reset">
            <Button
              style={{ width: 40, background: '#F3F5F5' }}
              onClick={handleReset}
              icon={<RixEngineFont type="clear" color="red" />}
              disabled={loading}
            />
          </Tooltip>
          {downloadUrl && (
            <Tooltip title={downloadUrlTips}>
              <Button
                type="link"
                icon={<PaperClipOutlined />}
                style={{ width: 40 }}
                onClick={handleDownloadUrl}
              />
            </Tooltip>
          )}
          {isFold && showFold && (
            <div className={styles['fold-btn']} onClick={handleChangeFold}>
              <span>{isDefaultFold ? 'Unfold' : 'Fold'}</span>
              {isDefaultFold ? <DownOutlined /> : <UpOutlined />}
            </div>
          )}
        </div>
      </div>
      <div className={styles['topbar-center-container']}>
        <Form
          initialValues={initialValues}
          onFinish={handleFinish}
          onFinishFailed={(err) => console.log(err)}
          autoComplete="off"
          form={form}
          labelAlign={labelAlign || 'right'}
          labelCol={{
            style: isDefaultFold
              ? { maxWidth: labelWidth }
              : { width: labelWidth },
          }}
          onValuesChange={handleValueChange}
        >
          <div className={styles['search-item-container']}>
            {normalOptions.map((item, index) => {
              return (
                <div
                  key={index}
                  style={{
                    width: `${item.width}%`,
                    display:
                      index < normalNum || !isDefaultFold
                        ? 'inline-block'
                        : 'none',
                  }}
                  className={styles['search-item']}
                >
                  <React.Fragment key={index}>
                    {item.type === 'input' && (
                      <Form.Item
                        name={item.key}
                        label={item.name}
                        tooltip={item.tooltip}
                      >
                        <Input
                          placeholder={
                            item.placeholder || `Please Input ${item.name}`
                          }
                          allowClear
                          disabled={item.disabled}
                        />
                      </Form.Item>
                    )}

                    {item.type === 'textarea' && (
                      <Form.Item
                        name={item.key}
                        label={item.name}
                        tooltip={item.tooltip}
                      >
                        <Input.TextArea
                          placeholder={
                            item.placeholder ||
                            `Please Input ${item.name}(one per line)`
                          }
                          allowClear
                          rows={1}
                          autoSize={{ maxRows: 3, minRows: 1 }}
                        />
                      </Form.Item>
                    )}
                    {item.type === 'select' && (
                      <Form.Item
                        name={item.key}
                        label={item.name}
                        tooltip={item.tooltip}
                        rules={item.rules}
                      >
                        <Select
                          mode={
                            item.mode === 'multiple' ? 'multiple' : undefined
                          }
                          placeholder={
                            item.placeholder || `Please Select ${item.name}`
                          }
                          options={item.options || []}
                          allowClear={
                            typeof item.allowClear === 'boolean'
                              ? item.allowClear
                              : true
                          }
                          limitSelectCount={item.limitSelectCount}
                        />
                      </Form.Item>
                    )}
                    {item.type === 'selectAll' && (
                      <Form.Item
                        name={item.key}
                        label={item.name}
                        tooltip={item.tooltip}
                        rules={item.rules}
                      >
                        <AllSelectSearch
                          mode={
                            item.mode === 'multiple' ? 'multiple' : undefined
                          }
                          placeholder={
                            item.placeholder || `Please Select ${item.name}`
                          }
                          options={item.options || []}
                          allowClear
                          placement="bottomLeft"
                          limitSelectCount={item.limitSelectCount}
                        />
                      </Form.Item>
                    )}
                    {item.type === 'checkbox' && (
                      <Form.Item
                        name={item.key}
                        label={item.name}
                        tooltip={item.tooltip}
                      >
                        <div className={styles['form-item-checkbox-container']}>
                          <Form.Item name={item.key}>
                            <Checkbox.Group
                              options={item.options || []}
                              className={styles['top-bar-checkbox-group']}
                            ></Checkbox.Group>
                          </Form.Item>
                        </div>
                      </Form.Item>
                    )}
                    {item.type === 'date' && (
                      <Form.Item
                        name={item.key}
                        label={item.name}
                        tooltip={item.tooltip || TimeZoneMapLabel['Etc/UTC']}
                        rules={item.rules}
                      >
                        <RangePicker
                          allowClear={item.allowClear}
                          style={{ width: '100%' }}
                          disabledDate={handleDisabledDateBy}
                          onOpenChange={handleDateOpenChange}
                          suffixIcon={<RixEngineFont type="rix-date" />}
                          ranges={pickerRange}
                          {...ExtraRangePickerOptions}
                        />
                      </Form.Item>
                    )}
                    {item.type === 'single-date' && (
                      <Form.Item
                        name={item.key}
                        label={item.name}
                        tooltip={item.tooltip || TimeZoneMapLabel['Etc/UTC']}
                      >
                        <DatePicker
                          allowClear={item.allowClear}
                          style={{ width: '100%' }}
                          disabledDate={handleDisabledDateBy}
                          onOpenChange={handleDateOpenChange}
                          suffixIcon={<RixEngineFont type="rix-date" />}
                          {...ExtraDatePickerOptions}
                        />
                      </Form.Item>
                    )}
                    {item.type === 'bundle' && (
                      <Form.Item
                        name={item.key}
                        label={item.name}
                        tooltip={item.tooltip}
                        required={item.required}
                      >
                        <SearchBundle
                          limit={item.limit}
                          splitBy={item.splitBy}
                          placeholder={item.placeholder}
                        />
                      </Form.Item>
                    )}
                  </React.Fragment>
                </div>
              );
            })}
            {boxOptions.map((item, index) => (
              <div
                key={index}
                style={{ width: '100%', marginTop: !index ? 12 : 0 }}
                className={styles['search-item-checkbox']}
              >
                <React.Fragment key={index}>
                  {item.type === 'checkboxFold' && (
                    <div className={styles['form-item-checkbox-container']}>
                      <Form.Item
                        name={item.key}
                        label={
                          <div key={index}>
                            <span>{item.name}</span>
                            {item.labelIcon && (
                              <Dropdown
                                autoAdjustOverflow
                                placement="bottomRight"
                                dropdownRender={(menus: React.ReactNode) => (
                                  <div className={styles['dropdown-container']}>
                                    {menus}
                                  </div>
                                )}
                                menu={{
                                  items: MetricsShortcutOptions,
                                  selectable: true,
                                  defaultSelectedKeys: ['default'],
                                  onSelect: ({ key }) => {
                                    const metrics =
                                      MetricsShortcutOptionsMap[key];
                                    form.setFieldsValue({ metrics });
                                  },
                                }}
                                trigger={['click']}
                              >
                                <RixEngineFont
                                  type={item.labelIcon || ''}
                                  className={styles['metrics-label']}
                                />
                              </Dropdown>
                            )}
                          </div>
                        }
                        labelCol={{ style: { width: labelWidth } }}
                      >
                        <Checkbox.Group style={{ width: '100%' }}>
                          <Row
                            style={{
                              display: 'fle',
                              justifyContent: 'flex-start',
                            }}
                          >
                            {item.options &&
                              item.options.map((val, i) => {
                                return (
                                  // <React.Fragment key={i}></React.Fragment>
                                  <span
                                    style={{ width: `${item.width}%` }}
                                    key={i}
                                  >
                                    <Checkbox
                                      value={val.value}
                                      key={i}
                                      style={{
                                        // width: `${item.width}%`,
                                        marginLeft: 0,
                                        marginBottom: 12,
                                        display:
                                          JSON.stringify(isBottomFold) ===
                                            '{}' && i >= boxNum
                                            ? 'none'
                                            : i < boxNum ||
                                              !isBottomFold[item.key]
                                            ? ''
                                            : 'none',
                                      }}
                                      disabled={
                                        val.disabled ??
                                        disabledCheckBox(item, val)
                                      }
                                    >
                                      {val.label}
                                      {val.tooltip?.showTitle && (
                                        <Tooltip title={val.tooltip.title}>
                                          <QuestionCircleOutlined
                                            style={{
                                              color: '#5e6466',
                                              paddingLeft: '3px',
                                              cursor: 'pointer',
                                            }}
                                          />
                                        </Tooltip>
                                      )}
                                    </Checkbox>
                                  </span>
                                );
                              })}
                          </Row>
                        </Checkbox.Group>
                      </Form.Item>
                      {isFold &&
                        item.options &&
                        boxNum < item.options.length && (
                          <div
                            className={styles['fold-btn']}
                            onClick={() => handleBottomFold(item.key)}
                          >
                            <span>
                              {isBottomFold[item.key] ||
                              JSON.stringify(isBottomFold) === '{}'
                                ? 'Unfold'
                                : 'Fold'}
                            </span>
                            {isBottomFold[item.key] ||
                            JSON.stringify(isBottomFold) === '{}' ? (
                              <DownOutlined />
                            ) : (
                              <UpOutlined />
                            )}
                          </div>
                        )}
                    </div>
                  )}
                </React.Fragment>
              </div>
            ))}
          </div>
        </Form>
      </div>
    </div>
  );
}

export default forwardRef(TopBar);
