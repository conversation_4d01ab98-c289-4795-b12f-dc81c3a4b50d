.topbar-container {
  background-color: #fff;
  border-radius: 6px;
  padding: 16px;
  padding-bottom: 0px;
  margin-bottom: 20px;
  margin-right: 20px;
  .topbar-top-btn-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    h5 {
      color: #252829;
    }
    .topbar-top-btn-right {
      display: flex;
      align-items: center;
      button {
        margin-right: 12px;
        &:last-child {
          margin-right: 0px;
        }
      }
      :global {
        .ant-tooltip-disabled-compatible-wrapper {
          margin-right: 12px;
        }
      }
    }
  }
  .topbar-center-container {
    padding-bottom: 4px;
    :global {
      .ant-form {
        display: flex;
      }
      .ant-form-item {
        width: 100%;
        margin-bottom: 12px;
      }
      .ant-form-item-row {
        display: flex;
        > div {
          &:last-child {
            flex: 1;
          }
        }
      }
    }
    .topbar-center-right {
      button {
        margin-right: 12px;
        &:last-child {
          margin-right: 0px;
        }
      }
    }
    .metrics-label {
      position: relative;
      margin-left: 6px;
      font-size: 18px;
      cursor: pointer;
      &:hover {
        color: #4692f5;
      }
    }
  }
  .search-item-container {
    width: 0;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    flex: 1;
    > div {
      padding-right: 12px;
      > span {
        padding-right: 6px;
      }
    }
    .btn-container {
      display: inline-block;
      button {
        width: 118px;
        margin: 0px;
        height: 32px;
        &:first-child {
          margin-right: 12px;
        }
      }
    }
    .search-item-checkbox {
      padding-right: 0px;
    }
    :global {
      .ant-form-item-label > label {
        color: #5e6466;
      }
    }
  }
  .top-bar-checkbox-group {
    display: flex;
    flex-wrap: wrap;
  }
  .fold-btn {
    color: #4692f5;
    cursor: pointer;
    font-size: 14px;
    min-width: 61px;
    > span {
      &:first-child {
        padding-right: 3px;
      }
      svg {
        font-size: 10px;
      }
    }
  }
  .form-item-checkbox-container {
    display: flex;
    justify-content: space-between;
    :global {
      .ant-form-item {
        flex: 1;
        margin-bottom: 0px;
      }

      .ant-dropdown-menu {
        box-shadow: none;
        width: 100%;
        li {
          border-radius: 6px;
        }
      }
    }
    .fold-btn {
      width: 65px;
      text-align: end;
    }
  }
  .search-item-checkbox {
    :global {
      .ant-form-item {
        margin-bottom: 6px;
      }
      .ant-form-item-label > label {
        align-items: flex-start;
      }
    }
  }
}

.dropdown-container {
  position: 'absoulte';
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding: 12px;
  border-radius: 6px;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);

  :global {
    .ant-dropdown-menu {
      box-shadow: none;
      width: 100%;
      li {
        border-radius: 6px;
      }
    }
  }
}
