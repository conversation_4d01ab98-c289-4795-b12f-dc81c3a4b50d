/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-04 14:47:15
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-17 15:05:53
 * @Description: 详情描述页
 */

import { Spin, Tabs } from 'antd';
import React, { useState, useEffect } from 'react';
import DescriptionsCard, {
  DescriptionsCardColumnsProps,
} from './DescriptionsCard';
export type { DescriptionsCardProps } from './DescriptionsCard';
import NormalDrawer from '@/components/Drawer/NormalDrawer';
import styles from './index.less';
import { useAccess } from '@umijs/max';
import CusLoadingIcon from '../LoadingIcon';
export type InfoBarColumnsProps<T> = DescriptionsCardColumnsProps<T>;

type InfoBarTabItem<T> = {
  title: React.ReactNode;
  key?: string;
  columns?: InfoBarColumnsProps<T>[];
  width?: number;
  render?: (row: T) => React.ReactNode;
  type?:
    | 'default'
    | 'collapse'
    | 'whole-line'
    | 'table'
    | 'collapse-table'
    | 'collapse-line';
  children?: InfoBarTabItem<T>[]; // 选项选择
  rowNum?: number; // 自己输入的每行列数
  rowKey?: string; // 表格的key,type为table时生效
  tableData?: any[]; // 表格数据
  access?: string; // 权限编码
  showCopy?: boolean; // 是否显示复制,只对单行有效
  copyText?: (row: T) => string; // 复制的文本
  titleIcon?: {
    icon: React.ReactNode;
    onClick?: (dataSource?: T) => void;
  };
};
export type InfoBarTabsProps<T> = InfoBarTabItem<T>[];

export type InfoBarTab<T> = {
  title: string;
  columns?: InfoBarColumnsProps<T>[];
  render?: (row: T) => React.ReactNode;
};

export type InfoBarProps<T> = {
  tabs: InfoBarTabsProps<T>;
  dataSource?: T;
  title: string;
  handleClose?: () => void;
  mode?: 'left' | 'right' | 'top' | 'bottom';
  width?: number;
  grayKey?: keyof T;
  handleTabChange?: (activeKey: string) => void;
  loading?: boolean;
  copyText?: (row: T) => string; // 复制的文本
  defaultActiveKey: string;
};

function InfoBar<T extends object, K = unknown>({
  tabs,
  dataSource,
  title,
  handleClose,
  mode = 'top',
  width = 430,
  grayKey,
  handleTabChange,
  loading = false,
  copyText,
  defaultActiveKey,
}: InfoBarProps<T>): JSX.Element {
  // const access = useAccess();
  const [visible, setVisible] = useState(false);
  const [finalTabs, setFinalTabs] = useState(tabs);
  const minWidth = 320; // 最小宽度，默认两列

  const finWidth = width >= minWidth ? width : minWidth;
  const rowNum = Math.floor((width * 2) / minWidth) || 1;

  const getColumnMaxWidth = (width: number, columns?: number) => {
    const defaultColumns = rowNum;
    return Math.floor((width - 66) / (columns || defaultColumns));
  };
  useEffect(() => {
    if (dataSource) {
      setVisible(true);
    } else {
      setVisible(false);
    }
  }, [dataSource]);

  useEffect(() => {
    const arr = tabs.filter((v) => {
      // return !v.access || access[v.access as keyof typeof access];
      // admin不需要权限
      return true;
    });
    setFinalTabs(arr);
  }, [tabs]);

  const handleClickIcon = (
    e: React.MouseEvent,
    onClick?: (dataSource?: T) => void,
  ) => {
    e.stopPropagation();
    onClick && onClick(dataSource);
  };
  return (
    <NormalDrawer
      destroyOnClose
      blackName={title}
      grayName={grayKey && dataSource ? `${dataSource[grayKey]}` || '' : ''}
      open={visible}
      onClose={() => {
        setVisible(false);
        handleClose && handleClose();
      }}
      loading={false}
      maskClosable={false}
      showFooter={false}
      mask={false}
      width={finWidth} // 给个最小宽度， 需要适配两行情况
      titleMaxWidth={finWidth - 110}
      contentMaxHeight="calc(100vh - 70px)"
    >
      {dataSource && (
        <Tabs
          defaultActiveKey={defaultActiveKey || finalTabs[0].key}
          onChange={(activeKey) => handleTabChange?.(activeKey)}
          tabPosition={mode}
          className={styles['info-bar-tab']}
          items={finalTabs.map(
            (
              {
                title,
                columns,
                children,
                render,
                type,
                rowNum: oriRowNum,
                titleIcon,
                key,
                rowKey,
                tableData = [],
              },
              iTb,
            ) => {
              return {
                key: `${key}`,
                label: (
                  <div className={styles['tab-tile']}>
                    <div>{title}</div>
                    {titleIcon && (
                      <div
                        className={styles['title-icon']}
                        onClick={(e) =>
                          handleClickIcon(
                            e,
                            (dataSource) =>
                              titleIcon.onClick &&
                              titleIcon.onClick(dataSource),
                          )
                        }
                      >
                        {titleIcon.icon}
                      </div>
                    )}
                  </div>
                ),
                children: (
                  <Spin spinning={loading} indicator={<CusLoadingIcon />}>
                    {render ? (
                      render(dataSource)
                    ) : children?.length ? (
                      children.map((v, index) => (
                        <DescriptionsCard<T>
                          dataSource={dataSource}
                          columns={v.columns || []}
                          type={v.type}
                          rowNum={v.rowNum || oriRowNum || rowNum}
                          rowKey={rowKey}
                          maxWidth={getColumnMaxWidth(width, v.rowNum)}
                          key={index}
                          title={v.title}
                          showCopy={v.showCopy}
                          copyText={v.copyText}
                          tableData={tableData}
                        />
                      ))
                    ) : (
                      <DescriptionsCard<T>
                        dataSource={dataSource}
                        columns={columns || []}
                        type={type}
                        rowNum={oriRowNum || rowNum}
                        rowKey={rowKey}
                        maxWidth={getColumnMaxWidth(width, oriRowNum)}
                        copyText={copyText}
                        tableData={tableData}
                      />
                    )}
                  </Spin>
                ),
              };
            },
          )}
        />
      )}
    </NormalDrawer>
  );
}

export default InfoBar;
