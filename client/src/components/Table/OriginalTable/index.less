.original-table {
  :global {
    .ant-table .ant-table-container {
      border-radius: 6px;
      border: 1px solid #e2eaeb;
      table {
        border-radius: 6px;
      }
      .ant-table-thead {
        > tr {
          &:first-child {
            > th {
              height: 40px;
              background: #edeff0;
              color: #252829;
              font-weight: 700;
              font-size: 14px;
              &:first-child {
                border-top-left-radius: 6px;
              }
              &:last-child {
                border-top-right-radius: 6px;
              }
            }
          }
        }
      }
      .ant-table-tbody {
        tr {
          height: 40px;
          &:last-child {
            td {
              &:first-child {
                border-bottom-left-radius: 6px;
              }
              &:last-child {
                border-bottom-right-radius: 6px;
              }
            }
          }
          td {
            color: #252829;
            padding: 8px 8px;
          }
        }
      }
    }
    .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(.ant-table-row-expand-icon-cell):not([colspan])::before {
      display: none;
    }
  }
}
.original-btn-table {
  :global {
    .ant-table .ant-table-container {
      .ant-table-tbody {
        .ant-table-row-hover {
          background: #edeff0;
          & > td {
            color: #edeff0;
          }
        }
        tr {
          &:hover:not(.ant-table-expanded-row) > td {
            background: #edeff0;
          }
          td {
            color: #252829;
            padding: 3.5px 8px;
          }
        }
      }
    }
  }
}

.content-empty {
  // border: 1px solid #d9d9d9;

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 6px;
  border-radius: 6px;
  .empty-svg svg {
    padding-top: 12px;
  }
  span {
    padding-bottom: 12px;
    color: #8a8a8a;
  }
  width: 100px;
  position: sticky;
  left: 50%;
}
