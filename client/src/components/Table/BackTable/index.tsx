/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-29 17:33:23
 * @Description:
 */
import Pagination from '@/components/Pagination/NormalPagination';
import TopBar, {
  CheckboxUniqueKeyOptionsType,
  DateRangeKey,
  SearchResultItem,
  TopBarRef,
  TopBarSearchItem,
} from '@/components/TopBar';
import type { TopBarButtonAuth } from '@/constants';
import type {
  DatePickerProps,
  FormInstance,
  TablePaginationConfig,
  TableProps,
} from 'antd';
import { Button } from 'antd';
import type { ButtonProps } from 'antd/es/button';
import { ColumnProps } from 'antd/es/table';
import { RangePickerProps } from 'antd/lib/date-picker';
import { Moment } from 'moment';
import {
  ReactNode,
  RefObject,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import Table from '../OriginalTable';
import styles from './index.less';

export type ButtonType = ButtonProps & {
  label: string;
  key?: string;
  btnType?: 'button' | 'dropdown';
  access?: string; // 权限code
};

export type ColumnsType<T> = ColumnProps<T> & {
  access?: string | string[]; // 权限code
};

type NormalTableProps<T> = TableProps<T> & {
  loading: boolean;
  columns: ColumnProps<T>[];
  tableData: API.BackResult<T>;
  rowKey: string;
  btnOptions?: ButtonType[];
  pagination?: TablePaginationConfig;
  pageTitle?: string;
  searchFormRef?: RefObject<FormInstance<any> | undefined>;
  searchOptions: TopBarSearchItem[];
  isFold?: boolean;
  isExport?: boolean;
  toolButtons?: ReactNode;
  handleExport?: (params: ColumnProps<T>[]) => void;
  labelWidth?: number;
  isBtnTable?: boolean;
  handleSearch: (
    start: number,
    end: number,
    val: SearchResultItem[],
    isPaing?: boolean,
  ) => void;
  defaultParams?: any;
  defaultSearchValue?: any;
  defaultFold?: boolean;
  searchValue?: any;
  handleDisableDate?: (currentDate: Moment) => boolean;
  emptyRender?: () => JSX.Element;
  isExportAll?: boolean;
  handleDownloadAll?: (data: any, columns: ColumnProps<T>[]) => void;
  buttonAuth?: TopBarButtonAuth;
  defaultFormItemWidth?: number;
  defaultBoxItemWidth?: number; // checkbox label最长宽度
  handleSortChange?: (
    curPage: number,
    pageSize: number,
    searchVal: SearchResultItem[],
    sorter: any,
    topBarRef: RefObject<TopBarRef>,
  ) => void;
  onCalendarChange?: (
    dates: any,
    dateStrings: [string, string],
    info: any,
  ) => void;
  maxRange?: number;
  defaultDates?: [Moment | null, Moment | null] | null;
  checkboxUniqueKeyOptions?: CheckboxUniqueKeyOptionsType[];
  handleMetricsChange?: (val: any) => void;
  dateRangeKeys?: DateRangeKey[];
  showTopBar?: boolean;
  showTable?: boolean;
  downloadUrl?: string; // 直接下载文件的url pixalate report
  downloadUrlTips?: string; // tooltip pixalate report
  handleSearchValueChange?: (
    changeValue: any,
    allValue: any,
    isFold: boolean,
    form: FormInstance,
  ) => void;
  ExtraRangePickerOptions?: RangePickerProps;
  ExtraDatePickerOptions?: DatePickerProps;
};

const PageSize = 50;
const DefCurrentPage = 1;
const PageSizeOptions = [10, 50, 100, 500];

export type NormalTableRef = {
  getFormInstance: () => FormInstance | undefined;
};

const NormalTable = function NormalTable<T extends object>({
  columns,
  rowKey,
  btnOptions = [],
  pagination,
  pageTitle,
  labelWidth = 80,
  tableData,
  isExport,
  toolButtons,
  handleExport,
  searchFormRef,
  searchOptions,
  isFold,
  isBtnTable = true,
  handleSearch,
  loading,
  defaultSearchValue,
  searchValue,
  defaultFold = true,
  handleDisableDate,
  emptyRender,
  isExportAll,
  handleDownloadAll,
  buttonAuth,
  defaultFormItemWidth,
  defaultBoxItemWidth,
  handleSortChange,
  onCalendarChange,
  maxRange,
  defaultDates,
  checkboxUniqueKeyOptions,
  handleMetricsChange,
  dateRangeKeys,
  showTopBar = true,
  showTable = true,
  downloadUrl,
  downloadUrlTips,
  handleSearchValueChange,
  ExtraRangePickerOptions,
  ExtraDatePickerOptions,
  ...rest
}: NormalTableProps<T>): JSX.Element {
  const [currentPage, setCurrentPage] = useState<number>(DefCurrentPage);
  const [pageSize, setPageSize] = useState<number>(PageSize);
  const [searchVal, setSearchVal] = useState<SearchResultItem[]>([]);
  const topBarRef = useRef<TopBarRef>(null);

  useImperativeHandle(searchFormRef, () => topBarRef.current?.getFormInstance(), [
    topBarRef.current,
  ]);

  const handlePageChange = (page: number, size: number) => {
    setCurrentPage(page);
    setPageSize(size);
    const start = size * (page - 1);
    const end = size * page;
    handleSearch(start, end, searchVal, true);
  };

  useEffect(() => {
    reset();
  }, []);

  const resetTable = () => {
    setCurrentPage(DefCurrentPage);
    setPageSize(PageSize);
  };

  // 重置
  const reset = () => {
    resetTable();
  };

  const handleSearchChange = (val: SearchResultItem[]) => {
    setSearchVal(val);
    resetTable();
    handleSearch(0, 50, val);
  };

  const handleDownload = () => {
    handleExport && handleExport(columns);
  };

  const handleDownLoadTmp = (data: any) => {
    handleDownloadAll && handleDownloadAll(data, columns);
  };

  // 目前只支持sorter，以后如果有其他的，需要做下兼容
  const handleTableChange: TableProps<T>['onChange'] = (
    pagination: any,
    filters: any,
    sorter: any,
  ) => {
    if (handleSortChange) {
      handleSortChange(currentPage, pageSize, searchVal, sorter, topBarRef);
    }
  };
  const handleDefaultSearchChange = (val: SearchResultItem[]) => {
    setSearchVal(val);
  };

  return (
    <div className={styles['back-table-container']}>
      {showTopBar && (
        <TopBar
          title={pageTitle}
          handleSearchChange={handleSearchChange}
          isFront={false}
          handleExport={handleDownload}
          isExport={isExport}
          isFold={isFold}
          ref={topBarRef}
          toolButtons={toolButtons}
          labelWidth={labelWidth}
          defaultValue={defaultSearchValue}
          defaultFold={defaultFold}
          searchOptions={searchOptions}
          searchValue={searchValue}
          handleDisableDate={handleDisableDate}
          loading={loading}
          isExportAll={isExportAll}
          handleDownloadAll={handleDownLoadTmp}
          buttonAuth={buttonAuth}
          disabledExport={!tableData?.total}
          onCalendarChange={onCalendarChange}
          defaultDates={defaultDates}
          checkboxUniqueKeyOptions={checkboxUniqueKeyOptions}
          defaultFormItemWidth={defaultFormItemWidth}
          defaultBoxItemWidth={defaultBoxItemWidth}
          dateRangeKeys={dateRangeKeys}
          downloadUrl={downloadUrl}
          downloadUrlTips={downloadUrlTips}
          handleSearchValueChange={handleSearchValueChange}
          handleDefaultSearchChange={handleDefaultSearchChange}
          ExtraRangePickerOptions={ExtraRangePickerOptions}
          ExtraDatePickerOptions={ExtraDatePickerOptions}
        />
      )}
      {showTable && (
        <div className={styles['bottom-container']}>
          <div className={styles['bottom-top']}>
            <div className={styles['top-left']}>
              {btnOptions?.map(
                ({ label, onClick, ...rest }: ButtonType, index) => {
                  return (
                    <Button {...rest} key={index} onClick={onClick}>
                      {label}
                    </Button>
                  );
                },
              )}
            </div>
            <div className={styles['top-right']}>
              <Pagination
                onChange={handlePageChange}
                total={tableData.total}
                current={currentPage}
                pageSize={pageSize}
                showSizeChanger
                size="small"
                responsive
                showTotal={(total) => `Total ${total} items`}
                pageSizeOptions={PageSizeOptions}
                {...(pagination as TablePaginationConfig)}
              />
            </div>
          </div>
          <Table<T>
            {...rest}
            columns={columns}
            dataSource={tableData.data}
            rowKey={rowKey}
            loading={loading}
            isBtnTable={isBtnTable}
            scroll={{ y: 'calc(100vh - 220px)' }}
            emptyRender={emptyRender}
            onChange={handleTableChange}
          />
        </div>
      )}
    </div>
  );
};

export default NormalTable;
