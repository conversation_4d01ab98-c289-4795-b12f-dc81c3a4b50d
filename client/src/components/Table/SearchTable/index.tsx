/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-01-18 16:35:34
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-18 17:04:45
 * @Description:
 */
import { useEffect, useState } from 'react';
import OriginalTable from '@/components/Table/OriginalTable';
import InputSearch from '@/components/Input/InputSearch';
import type { TableProps } from 'antd';
import { ColumnProps } from 'antd/es/table';
import styles from './index.less';

type SearchTableProps<T, K> = TableProps<T> & {
  loading: boolean,
  columns: ColumnProps<T>[],
  dataSource: T[],
  rowKey: string;
  searchKeys: K[];
  placeholder: string;
}

export default function SearchTable<T extends object>({
  columns, dataSource, rowKey, searchKeys, placeholder, ...rest }
  :SearchTableProps<T, keyof T>): JSX.Element {
  const [filterList, setFilterList] = useState<T[]>([]);
  const [searchVal, setSearchVal] = useState('');
  useEffect(() => {
    setSearchVal('');
    if (dataSource && dataSource.length) {
      setFilterList(dataSource);
    } else {
      setFilterList([]);
    }
  }, [dataSource]);
  const handleSearch = (val: string) => {
    if (val) {
      const list = dataSource.filter((item: T) => {
        return searchKeys.some(tmp => {
          return `${item[tmp]}`.toLowerCase().indexOf(val.toLowerCase()) !== -1;
        });
      });
      setFilterList(list);
    } else {
      setFilterList(dataSource);
    }
  };
  const handleValueChange = (val: string) => {
    setSearchVal(val);
  };
  return <div className={styles['search-table-container']}>
    <InputSearch
      placeholder={placeholder}
      handleSearch={handleSearch}
      value={searchVal}
      onValueChange={handleValueChange}
    />
    <OriginalTable<T>
      {...rest}
      rowKey={rowKey}
      columns={columns as any}
      dataSource={filterList}
    />
  </div>;
}
