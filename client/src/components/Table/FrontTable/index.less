.bottom-container {
  flex: 1;
  background-color: #fff;
  margin-right: 16px;
  padding: 16px;
  border-radius: 6px;
  overflow: hidden;
  .bottom-top {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    .top-left {
      flex: 1;
      button {
        height: 32px;
      }
    }
  }
  
}

.front-table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.front-table-header {
  // color: red;
  min-height: 132px;
}