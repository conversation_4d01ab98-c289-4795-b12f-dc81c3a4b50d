/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-21 17:26:32
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 19:37:10
 * @Description: 
 */
import React, { useState, useEffect } from 'react';
import FrontTable from '@/components/Table/FrontTable';
import type { ColumnsType, ButtonType } from '@/components/Table/BackTable';
import { TopBarSearchItem } from '@/components/TopBar';
import OperateRender, { OperateRenderItem } from '../../OperateRender';
import IconFont from '@/components/RixEngineFont';

type CommonPageProps<T, K> = {
  children?: React.ReactNode;
  pageTitle?: string;
  searchOptions: TopBarSearchItem[];
  labelWidth?: number;
  isFold?: boolean;
  btnOptions?: ButtonType[];
  rowKey: keyof T;
  columns: ColumnsType<T>[];
  defaultSearchValue?: any;
  operateOptions?: OperateRenderItem[]; // 空数组表示不需要operate列 不给就显示默认的
  showEditOperate?: boolean; // 是否显示column里的edit列, 默认true
  isRowSelection?: boolean;
  dataSource?: T[];
  reload: () => Promise<void>;
  loading: boolean;
  editAccess?: string; // 列表编辑的权限
  style?: React.CSSProperties;
}

export type ChildrenProps = {
  visible: boolean;
  onClose: () => void;
  onSave: () => void;
  isEdit: boolean;
}

function CommonPage<T extends object>({
  children, pageTitle, searchOptions, labelWidth = 50, isRowSelection = false, dataSource, reload, loading,
  isFold = true, btnOptions, rowKey, columns, defaultSearchValue: ori_defaultSearch, operateOptions, showEditOperate = true,
  editAccess, style
}: CommonPageProps<T, keyof T>): JSX.Element {
  const [visible, setVisible] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [editItem, setEditItem] = useState<T>();
  const [finalColumns, setFinalColumns] = useState(columns);
  const [defaultSearchValue, setDefaultSearch] = useState({ status: [1] });

  const EditOperateItem = {
    label: '编辑',
    icon: <IconFont type="edit" />,
    access: editAccess,
    onClick: (params: any) => {
      setEditItem(params);
      setIsEdit(true);
      setVisible(true);
    }
  };

  useEffect(() => {
    reload();
  }, []);

  useEffect(() => {
    if (ori_defaultSearch) {
      setDefaultSearch(ori_defaultSearch);
    }
  }, [ori_defaultSearch]);

  useEffect(() => {
    const arr = columns.map(v => ({ ...v }));
    const idx = arr.findIndex(v => v.dataIndex === 'operate');
    if (idx !== -1) {
      if (!operateOptions) {
        arr[idx].render = (_: any, row: any) => {
          return <OperateRender btnOptions={[EditOperateItem]} params={row} />;
        };
      } else {
        const opArr = showEditOperate ? [EditOperateItem, ...operateOptions] : operateOptions;
        arr[idx].render = (_: any, row: any) => {
          return <OperateRender btnOptions={opArr} params={row} />;
        };
      }
    }
    setFinalColumns(arr);
  }, [columns, operateOptions, showEditOperate, editAccess]);

  const onClose = () => {
    setVisible(false);
  };

  const onSave = () => {
    setVisible(false);
    reload();
  };

  const handleCommonClickBtn = (item: ButtonType) => {
    if (item.key === 'create') {
      setVisible(true);
      setIsEdit(false);
    }
  }

  return (
    <React.Fragment>
      <FrontTable<T>
        pageTitle={pageTitle}
        searchOptions={searchOptions}
        loading={loading}
        dataSource={dataSource || []}
        columns={finalColumns}
        defaultValue={defaultSearchValue as any}
        rowKey={rowKey as any}
        labelWidth={labelWidth}
        btnOptions={btnOptions}
        scroll={{ x: 1000, y: 'auto' }}
        isBtnTable
        isFold={isFold}
        request={reload}
        style={style}
        handleClickBtn={handleCommonClickBtn}
      />
      {
        // @ts-ignore
        children ? children({ visible, onClose, onSave, isEdit, editItem }) : null
      }
    </React.Fragment>
  );
};

export default CommonPage;
