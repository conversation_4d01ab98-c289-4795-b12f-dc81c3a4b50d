/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-09-30 11:31:37
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-03 14:44:27
 * @Description:
 */
import React from 'react';
import Breadcrumb, { BreadcrumbItem } from '@/components/Breadcrumb';
import { Button, Spin, SpinProps } from 'antd';
import styles from './index.less';
import { LeftOutlined } from '@ant-design/icons';
import { history } from '@umijs/max';
import CusLoadingIcon from '@/components/LoadingIcon';
type PageProps = SpinProps & {
  options?: BreadcrumbItem[];
  loading?: boolean;
  flexDirection?: 'row' | 'column';
  className?: string;
  isBack?: boolean;
  backUrl?: string;
  handleGoBack?: () => void;
};
const PageContainer: React.FC<PageProps & React.HTMLProps<HTMLDivElement>> = ({
  options,
  children,
  loading = false,
  size,
  className,
  flexDirection = 'column',
  isBack = false,
  backUrl,
  handleGoBack,
}) => {
  const handleBack = () => {
    if (backUrl) {
      history.push(backUrl);
    }
    if (!backUrl && handleGoBack) {
      handleGoBack();
    }
  };
  return (
    <Spin spinning={loading} size={size} wrapperClassName={styles['spin-container']} indicator={<CusLoadingIcon />}>
      <div className={`${styles['page-container']} ${className}`} style={{ flexDirection: flexDirection }}>
        <div className={styles['page-top']}>
          {isBack && (
            <div className={styles['page-back']} onClick={handleBack}>
              <LeftOutlined style={{ fontSize: 13 }} />
              <span style={{ paddingRight: '6px' }}>Back</span>
              {options && options.length && <span style={{ color: '#c1cbcc', padding: '0px 6px' }}>|</span>}
            </div>
          )}
          {options && options.length && <Breadcrumb options={options} separator=">" />}
        </div>
        {children}
      </div>
    </Spin>
  );
};

export default PageContainer;
