.page-container {
  background-color: #f3f5f5;
  height: 100%;
  display: flex;
  padding-top: 16px;
  padding-bottom: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: calc(100vh - 65px);
  nav {
    // padding-bottom: 12px;
    padding-left: 5px;
    background-color: #f3f5f5;
    li {
      height: 22px;
      display: flex;
      align-items: center;
      :global {
        .ant-breadcrumb-separator {
          color: #c1cbcc;
        }
      }
    }
  }
}

.spin-container {
  height: 100%;
  > div {
    height: 100%;
  }
}

.page-top {
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  .page-back {
    color: #5e6466;
    cursor: pointer;
    &:hover {
      color: #4692f5;
    }
  }
}
