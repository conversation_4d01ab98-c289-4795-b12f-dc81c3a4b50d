import { Descriptions, DescriptionsProps } from 'antd';
import clsx from 'classnames';
import { useMemo } from 'react';
import { NormalDescriptionsItem, NormalDescriptionsItemConfig } from './item';
import styles from './style.less';

type NormalDescriptionsProps = Omit<DescriptionsProps, 'children' | 'items'> & {
  items: NormalDescriptionsItemConfig[];
};

export const NormalDescriptions = ({
  items,
  className,
  ...rest
}: NormalDescriptionsProps) => {
  const renderItems = useMemo(() => {
    return items.map((item) => {
      return <NormalDescriptionsItem {...item} />;
    });
  }, [items]);

  return (
    <Descriptions {...rest} className={clsx(styles['descriptions'], className)}>
      {renderItems}
    </Descriptions>
  );
};
