/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-01 20:29:07
 * @LastEditors: chen<PERSON>dan <EMAIL>
 * @LastEditTime: 2023-03-15 14:53:50
 * @Description:
 */

import React, { useState, useEffect, useRef, useMemo } from 'react';
import styles from './index.less';
import { Badge, Input, Typography } from 'antd';
import RixEngineFont from '../RixEngineFont';
import { CheckOutlined, SearchOutlined } from '@ant-design/icons';
const { Paragraph } = Typography;
import HoverToolTip from '../Tooltip/HoverTooltip';
import LongContentMore from '../LongContentMore';
import { DataSourceType, ObjectDataSourceType } from '.';
import { StatusMap } from '../Tag/StatusTag';

type PopContentProps = {
  dataSource: DataSourceType;
  contentWidth?: number; // popover宽度
  contentHeight?: number; // content的最大高度
  open: boolean;
  defaultShowNum?: number; // 默认显示多少条，数据量大的时候
  setOpen: (flag: boolean) => void;
};

const PopContent: React.FC<PopContentProps> = ({
  dataSource,
  contentWidth = 272,
  contentHeight = 270,
  open,
  defaultShowNum = 100,
  setOpen,
}) => {
  const divRef = useRef<HTMLDivElement | null>(null);
  const [filterList, setFilterList] = useState<ObjectDataSourceType>([]);
  // 渲染的数据 默认渲染300条
  const [renderData, setRenderData] = useState<ObjectDataSourceType>([]);
  // 过滤的值 只支持单选
  const [filterValue, setFilterValue] = useState('');

  useEffect(() => {
    setFilterValue('');
    if (open) {
      window.addEventListener('wheel', handleScroll);
    } else {
      window.removeEventListener('wheel', handleScroll);
    }
    return () => {
      window.removeEventListener('wheel', handleScroll);
    };
  }, [open]);

  const formatDataSource = useMemo(() => {
    // 目前是需要翻转数据的
    return (
      dataSource?.reverse().map((el) => {
        if (typeof el === 'string') {
          return { value: el, status: undefined };
        }
        return el;
      }) || []
    );
  }, [dataSource]);

  useEffect(() => {
    const val = filterValue.toUpperCase();
    setFilterList(() => {
      if (val) {
        return formatDataSource.filter((el) => {
          return `${el.value}`.toUpperCase().includes(val);
        });
      }
      return formatDataSource;
    });
  }, [formatDataSource, filterValue]);

  const handleScroll = (e: MouseEvent) => {
    const { x, y } = e;
    // 判断鼠标位置是否在可视区域内
    if (divRef.current && open) {
      const rect = divRef.current.getBoundingClientRect();
      const { width, height, x: pointX, y: pointY } = rect;
      const flag =
        x >= pointX &&
        x <= pointX + width &&
        y >= pointY &&
        y <= pointY + height;
      if (!flag) {
        setOpen(false);
      }
    } else {
      setOpen(false);
    }
  };

  const handleShowDataChange = (val: DataSourceType | number[]) => {
    setRenderData(val as ObjectDataSourceType);
  };

  return (
    <div className={styles.list} style={{ width: contentWidth }} ref={divRef}>
      <div className={styles.header}>
        <Input
          placeholder="Filter Data"
          onChange={(e) => setFilterValue(e.target.value)}
          prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
          value={filterValue}
        />
        <div className={styles['header-total']}>
          <span>Total: {filterList.length}</span>
        </div>
      </div>
      {/* 定高度 */}
      <LongContentMore
        contentMaxHeight={contentHeight}
        defaultShowNum={defaultShowNum}
        open={open}
        onShowDataChange={handleShowDataChange}
        dataSource={filterList}
        border={false}
      >
        {renderData.map((el, idx) => {
          return (
            <div className={styles.item} key={`pop-${el}-${idx}`}>
              <HoverToolTip
                maxWidth={contentWidth - 24}
                mouseEnterDelay={0.8}
                title={el.value}
              >
                {el.status ? (
                  <Badge color={StatusMap[el.status]} text={el.value} />
                ) : (
                  <span>{el.value}</span>
                )}
              </HoverToolTip>
            </div>
          );
        })}
      </LongContentMore>
      <div className={styles['content-bottom']}>
        <Paragraph
          copyable={{
            tooltips: ['Copy the above list', 'Copied'],
            text: filterList.map((el) => el.value).join('\n'),
            icon: [
              <div
                className={styles['copy-container']}
                key={0}
                style={{ width: contentWidth - 24 }}
              >
                <RixEngineFont
                  type="rix-copy"
                  style={{ width: 30, fontSize: 18 }}
                />
                Copy
              </div>,
              <div
                className={styles['copy-container']}
                key={1}
                style={{ width: contentWidth - 24 }}
              >
                <CheckOutlined style={{ color: '#fff' }} />
              </div>,
            ],
          }}
        />
      </div>
    </div>
  );
};

export default PopContent;
