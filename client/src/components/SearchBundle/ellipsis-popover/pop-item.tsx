/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-13 10:30:58
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-26 15:58:22
 * @Description:
 */
import React, { useState, useEffect } from 'react';
import { Input } from 'antd';
import RixEngineFont from '@/components/RixEngineFont';
import HoverTooltip from '@/components/Tooltip/BundleTooltip';
import styles from './pop.less';
import { CheckCircleOutlined } from '@ant-design/icons';

type PopItemProps = {
  value: string;
  contentWidth: number;
  editValue: string;
  onDelete: (val: string) => void;
  onChange: (olVal: string, newVal: string) => void;
  onEditChange: (val: string) => void;
  open: boolean;
  splitBy?: RegExp;
};

const PopItem: React.FC<PopItemProps> = ({
  value: defaultValue,
  contentWidth,
  onDelete,
  onChange,
  onEditChange,
  editValue,
  open,
  splitBy = /,|，|\s+/g,
}) => {
  const [isEdit, setIsEdit] = useState(false);
  const [value, setValue] = useState(defaultValue);

  useEffect(() => {
    setValue(defaultValue);
  }, [defaultValue]);

  useEffect(() => {
    if (editValue && editValue !== defaultValue) {
      setIsEdit(false);
      // 恢复数据
      setValue(defaultValue);
    }
  }, [editValue, defaultValue]);

  useEffect(() => {
    // 重置值
    if (!open) {
      setIsEdit(false);
      setValue(defaultValue);
    }
  }, [open]);

  const handleValueChange = (e: any) => {
    const str = e.target.value.replace(splitBy, '');
    setValue(str);
  };

  const onPressEnter = (e: any) => {
    const str = e.target.value.replace(splitBy, '');
    setIsEdit(false);
    if (str?.length) {
      setValue(str);
      onChange(defaultValue, str);
    } else {
      setValue(defaultValue);
    }
  };

  const handleSaveValue = () => {
    setIsEdit(false);
    if (value?.length) {
      onChange(defaultValue, value);
    } else {
      setValue(defaultValue);
    }
  };

  return (
    <div className={styles.item}>
      {!isEdit ? (
        <HoverTooltip
          maxWidth={contentWidth - 24 - 45}
          mouseEnterDelay={0.8}
          title={value}
        >
          <span>{value}</span>
        </HoverTooltip>
      ) : (
        <Input
          value={value}
          onChange={handleValueChange}
          onPressEnter={onPressEnter}
          style={{ width: contentWidth - 24 - 45 }}
          placeholder="Please Input"
        />
      )}
      <div className={styles['item-edit']}>
        {!isEdit ? (
          <>
            <RixEngineFont
              type="rix-edit"
              className={styles['svg-edit']}
              onClick={() => {
                console.log('xxbianji1');
                setIsEdit(true);
                onEditChange(defaultValue);
              }}
            />
            <RixEngineFont
              type="rix-trash"
              className={styles['svg-trash']}
              onClick={() => onDelete(defaultValue)}
            />
          </>
        ) : (
          <>
            <CheckCircleOutlined
              className={`${styles['svg-check']} ${
                defaultValue !== value ? styles['svg-checked'] : ''
              }`}
              onClick={handleSaveValue}
            />
          </>
        )}
      </div>
    </div>
  );
};

export default PopItem;
