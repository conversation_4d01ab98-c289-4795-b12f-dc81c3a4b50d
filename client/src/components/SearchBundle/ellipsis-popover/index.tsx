/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-12 18:32:18
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-25 11:26:06
 * @Description:
 */

import React, { useState, useEffect, useRef } from 'react';
import { Tag, Popover } from 'antd';
import styles from './pop.less';
import { TooltipPlacement } from 'antd/es/tooltip';
import PopContent from './pop-content';

const EllipsisPopover: React.FC<{
  dataSource: string[];
  contentWidth?: number; // popover宽度
  contentHeight?: number; // content的最大高度
  defaultShowNum?: number; // pop默认显示数据
  tdShowNum?: number; // 表格默认渲染数量
  onValueChange?: (val: string[]) => void;
  limit?: number;
  splitBy?: RegExp;
  placeholder?: string;
}> = ({ 
  dataSource, contentWidth = 400, contentHeight = 270, defaultShowNum = 100, 
  tdShowNum = 15, onValueChange, limit,splitBy=/,|，|\s+/g, placeholder = 'Input to filter/add,separated by comma/space'
 }) => {
  const viewRef = useRef<HTMLDivElement | null>(null);
  const [open, setOpen] = useState(false);
  const [innerWidth, setInnerWidth] = useState(160);
  const [placement, setPlacement] = useState<TooltipPlacement>('top');
  const [isShow, setIsShow] = useState(false);
  // 截取前面10条显示 减少不必要渲染
  // 倒序显示
  const dataArr = dataSource.length <= tdShowNum ? dataSource : dataSource.slice(0, tdShowNum);

  useEffect(() => {
    if (open) {
      setIsShow(open);
      window.addEventListener('resize', handleResize);
    } else {
      window.removeEventListener('resize', handleResize);
    }
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [open]);

  useEffect(() => {
    checkEllipsis(viewRef.current);
    handlePlacement(viewRef.current);
  }, [viewRef.current, dataSource]);

  const handlePlacement = (dom: any) => {
    if (dom) {
      // 显示高度
      const height = document.body.offsetHeight;
      const rect = dom.getBoundingClientRect();
      const { top, left } = rect;
      const minTopBottom = contentHeight + 135 + 50;
      const minLeft = contentWidth + 35;
      const bottom = height - top - 50; // 50是行高
      if (top >= minTopBottom) {
        setPlacement('top');
      }
      if (bottom >= minTopBottom && top < minTopBottom) {
        setPlacement('bottom');
      }
      // 以上都不符合的时候
      if (top < minTopBottom && bottom < minTopBottom) {
        if (left >= minLeft) {
          setPlacement('left');
        } else {
          // 最后的选择
          setPlacement('right');
        }
      }
    }
  };

  const handleResize = () => {
    setOpen(false);
    if (viewRef.current) {
      checkEllipsis(viewRef.current);
      handlePlacement(viewRef.current);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (open) {
      handlePlacement(viewRef.current);
    }
    setOpen(open);
  };
  const checkEllipsis = (dom: any) => {
    setInnerWidth(dom.offsetWidth);
  };

  const handleClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    e.stopPropagation();
  };

  const handleValueChange = (val: string[]) => {
    onValueChange && onValueChange(val);
    setOpen(false);
  };

  return (
    <Popover
      placement={placement}
      content={
        <React.Fragment>
          {/* 首次不渲染，点开了就可以渲染，解决dom渲染过多卡顿问题 */}
          {(open || isShow) && (
            <PopContent
              dataSource={dataSource}
              open={open}
              contentHeight={contentHeight}
              contentWidth={contentWidth}
              defaultShowNum={defaultShowNum}
              setOpen={setOpen}
              onValueChange={handleValueChange}
              limit={limit}
              splitBy={splitBy}
              placeholder={placeholder}
            />
          )}
        </React.Fragment>
      }
      trigger="click"
      open={open}
      onOpenChange={handleOpenChange}
      overlayClassName={styles['ellipsis-popover-overaly']}
    >
      <div
        className={`${styles['ellipsis']} ${styles['tag-container']} ${dataArr.length > 0 ? '' : styles['content-empty']}`}
        ref={viewRef}
        onClick={handleClick}
      >
        {dataArr.length > 0 ? dataArr.map((el, idx) => (
          <Tag key={`tag-${el}-${idx}`} className={`${styles['list-tag']} `}>
            {/* 30 需要预留...的空间, 20是去掉padding */}
            <span
              className={styles['ellipsis']}
              style={{ display: 'inline-block', maxWidth: innerWidth - 30 - 20 }}
              title={el}
            >
              {el}
            </span>
          </Tag>
        )) : <>Click To Input {limit ? `(up to ${limit})` : ''}</>}
      </div>
    </Popover>
  );
};

export default EllipsisPopover;
