.list-tag {
  height: 26px;
  line-height: 26px;
  border-radius: 4px;
  background: #eef0f0;
  color: #303333;
  cursor: pointer;
  display: inline-block;
}
.tag-container {
  position: relative;
  width: 100%;
  height: 26px;
  top: 3px;
  cursor: pointer;
}
.content-empty {
  width: 100%;
  height: 100%;
  color: #bfbfbf;
  padding-top: 2px;
  &:hover {
    border-color: #1568d4;
  }
}
.ellipsis {
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  .list-tag {
    margin-bottom: 0px;
  }
}
.list {
  padding: 12px;
  .pop-long-container {
    border-bottom: 1px solid rgba(5, 5, 5, 0.06) !important;
    border-radius: 0px;
  }
  .header {
    .header-total {
      background: #f3f5f5;
      height: 32px;
      color: #5e6466;
      line-height: 32px;
      border-radius: 6px;
      margin-top: 12px;
      span {
        padding-left: 12px;
      }
    }
  }
  .item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    height: 39px;
    line-height: 39px;
    border-block-end: 1px solid rgba(5, 5, 5, 0.06);
    color: #252829;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &:last-child {
      border-block-end: none;
    }
    .item-edit {
      display: flex;
      align-items: center;
      width: 41px;
      svg {
        font-size: 18px;
        cursor: pointer;
      }
      .svg-edit {
        svg {
          &:hover {
            color: #4692f5;
          }
        }
      }
      .svg-trash {
        padding-left: 5px;
        svg {
          &:hover {
            color: #ff4d4f;
          }
        }
      }
      .svg-check {
        padding-left: 3px;
        svg {
          font-size: 16px;
          &:hover {
            color: #4692f5;
          }
        }
      }
      .svg-checked {
        svg {
          color: #4692f5;
        }
      }
    }
  }
  > div {
    width: 100%;
    display: inline-block;
  }
  .search-top {
    width: 100%;
    display: flex;
    align-items: center;
    > span {
      flex: 1;
    }
  }
}

.ellipsis-popover-overaly {
  :global {
    .ant-popover-inner {
      border-radius: 6px;
      .ant-popover-inner-content {
        padding: 0px;
      }
    }
    .ant-popover-inner-content {
      padding: 12px;
      min-width: 130px;
    }
  }
}

.content-no-data {
  border: 1px solid #d9d9d9;
  display: flex !important;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin-top: 12px;
  border-radius: 6px;
  width: 100%;
  .empty-svg svg {
    padding-top: 12px;
  }
  span {
    padding-bottom: 12px;
    color: #8a8a8a;
  }
}
