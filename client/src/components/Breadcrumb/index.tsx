/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-12 17:01:49
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-02-03 15:59:11
 * @Description:
 */
import { Breadcrumb, BreadcrumbProps } from 'antd';
import { history } from 'umi';
import styles from './index.less';
import RixEngineFont from '@/components/RixEngineFont';

export type BreadcrumbItem = {
  name: string;
  url?: string;
  icon?: string;
  key?: string;
}

type DefaultBreadcrumbProps = BreadcrumbProps & {
  options: BreadcrumbItem[];
}

function NormalBreadcrumb({ options, ...rest }: DefaultBreadcrumbProps): JSX.Element {
  const handleClick = (item: BreadcrumbItem) => {
    if (item.url) {
      history.push(item.url);
    }
  };
  return (
    <Breadcrumb {...rest} className={styles['bread-container']}>
      {
        options.map((item, index) => {
          return <Breadcrumb.Item key={index} className={styles['bread-item']}>
            <span
              onClick={() => handleClick(item)}
              className={index === options.length - 1 ? styles['last'] : styles['active']}
            >
              {
                item.icon && (
                  <RixEngineFont
                    type={`${item.icon}`}
                    style={{ fontSize: 20, marginRight: 6 }}
                  />
                )
              }
              {item.name}
            </span>
          </Breadcrumb.Item>;
        })
      }
    </Breadcrumb>
  );
};

export default NormalBreadcrumb;
