.bread-item {
  height: 22px;
  display: inline-block;
  span {
    color: #5e6466;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      width: 20px !important;
      height: 20px !important;
    }
    &.last {
      font-weight: 700;
      color: #252829;
    }
    &.active {
      color: #5e6466;
      cursor: pointer;
      svg {
        color: #5e6466;
      }
      &:hover {
        color: #4692f5;
        svg {
          color: #4692f5;
        }
      }
    }
  }
}

.bread-container {
  ol {
    li {
      span {
        color: #4692f5;
        svg {
          color: #4692f5;
        }
      }
      &:last-child {
        span {
          color: #252829;
        }
      }
    }
  }
}
