/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-29 17:38:27
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-02-21 16:28:14
 * @Description:
 */
import { DatePicker } from 'antd';
import { RangePickerProps } from 'antd/lib/date-picker';
import styles from './index.less';
const { RangePicker } = DatePicker;

function NormalRangePicker(props: RangePickerProps): JSX.Element {
  return <RangePicker separator="一" popupClassName={styles['range-picker-container']} {...props} />;
}
export default NormalRangePicker;
