.range-picker-container {
  color: red;
  :global {
    .ant-picker-header {
      .ant-picker-header-prev-btn,
      .ant-picker-header-super-prev-btn,
      .ant-picker-header-next-btn,
      .ant-picker-header-super-next-btn {
        color: #5e6466;
      }
      .ant-picker-header-view {
        color: #252829;
      }
    }
    .ant-picker-cell-today {
      .ant-picker-cell-inner {
        &:before {
          cursor: pointer;
          border: none;
          top: 24px;
          left: 50%;
          width: 4px;
          height: 4px;
          border-radius: 4px;
          background: #4692f5;
          transform: translateX(-50%);
        }
      }
    }
    .ant-picker-cell.ant-picker-cell-in-view {
      .ant-picker-cell-inner {
        border-radius: 6px;
      }
    }

    .ant-picker-content {
      th {
        color: #8d9799;
      }
      tr {
        height: 36px;
      }
      .ant-picker-cell-disabled {
        color: #d7dadb;
        &::before {
          height: 32px;
        }
      }
    }

    .ant-picker-cell-in-view.ant-picker-cell-range-start,
    .ant-picker-cell-in-view.ant-picker-cell-range-end {
      &:before {
        background: #e1ecfa;
        position: absolute;
        top: 50%;
        right: 0;
        left: 0;
        z-index: 1;
        height: 32px;
        content: '';
      }

      &.ant-picker-cell-range-start:before {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
      &.ant-picker-cell-range-end:before {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      .ant-picker-cell-inner {
        border-radius: 6px !important;
        background: #1568d4;
      }
    }

    .ant-picker-cell-range-hover-start,
    .ant-picker-cell-range-hover-end,
    .ant-picker-cell-range-hover {
      &::after {
        border: none !important;
      }
      &::before {
        border: none;
        background-color: #b8edf5 !important;
        height: 32px;
      }
      &.ant-picker-cell-range-hover-end::before {
        border-top-right-radius: 6px;
        border-bottom-right-radius: 6px;
      }
      &.ant-picker-cell-range-hover-start::before {
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
      }
    }

    .ant-picker-cell-range-start-near-hover.ant-picker-cell-selected,
    .ant-picker-cell-range-end-near-hover.ant-picker-cell-selected {
      &.ant-picker-cell-range-start-near-hover::before {
        border-radius: 0 !important;
      }
      &.ant-picker-cell-range-end-near-hover::before {
        border-radius: 0 !important;
      }
    }

    .ant-picker-cell-in-view.ant-picker-cell-in-range {
      .ant-picker-cell-inner::after {
        background: none !important;
      }
      &::before {
        height: 32px;
      }
      &.ant-picker-cell-range-hover::before {
        background-color: #b8edf5 !important;
      }
    }
  }
}
