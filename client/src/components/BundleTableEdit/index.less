.content-container {
  .content-btn {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    span {
      &:first-child {
        margin-right: 6px;
      }
    }
  }

  .content-empty {
    border: 1px solid #d9d9d9;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin-top: 6px;
    border-radius: 6px;

    .empty-svg svg {
      padding-top: 12px;
    }

    span {
      padding-bottom: 12px;
      color: #8a8a8a;
    }
  }

  .content-body {
    border-bottom: 1px solid #d9d9d9;
    display: flex;
    align-items: center;
    color: #252829;

    .left {
      text-align: center;
    }

    .center {
      border-right: 1px solid #d9d9d9;
      border-left: 1px solid #d9d9d9;
      overflow: hidden;
      height: 28px;
      line-height: 28px;

      span {
        padding: 0px 12px;
        text-align: left;
      }
    }
  }

  .svg-default {
    cursor: pointer;

    &:hover {
      color: #4692f5;
    }
  }

  .svg-trash {
    &:hover {
      color: #ff4d4f;
    }
  }

  .svg-disabled {
    color: #d7dadb;
    pointer-events: none;
    cursor: not-allowed;
    // &:hover {
    //   color: #d7dadb;
    // }
  }

  .ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
  }
}
