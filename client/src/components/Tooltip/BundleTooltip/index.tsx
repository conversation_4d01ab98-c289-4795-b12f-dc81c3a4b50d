/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-14 18:55:30
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 17:36:15
 * @Description:
 */
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Tooltip } from 'antd';
import type { TooltipProps } from 'antd';
type HoverToolTipProps = TooltipProps & {
  maxWidth?: number;
  toolTip?: boolean;
  childClass?: any; // 样式
  onChildClick?: () => void; // 子元素的点击事件
};
// 必须有一个包裹元素
function HoverToolTip({ children, maxWidth = 0, toolTip = true, childClass, onChildClick = () => {}, ...rest }: HoverToolTipProps): JSX.Element {
  const childrenRef = useRef<HTMLElement | null>(null);
  const createChildren = useCallback(
    () =>
      React.cloneElement(children as any, {
        ref: childrenRef,
        // 补充文本溢出的处理
        // todo 不确定会不会覆盖子元素样式和类名
        style: maxWidth ? { maxWidth } : {},
        className: `ellipsis ${childClass || ''}`,
        onClick: onChildClick
      }),
    [children]
  );

  const [visible, setVisible] = useState(false);
  const showToolTip = () => {
    const targetWidth = childrenRef.current?.offsetWidth || 0;
    setVisible(false);
    if (maxWidth) {
      if (targetWidth >= maxWidth) {
        setVisible(true);
      }
    } else {
      const parentWidth = childrenRef.current?.parentElement?.offsetWidth || 0;
      const scrollWidth = childrenRef.current?.scrollWidth || 0;
      let parentTmpWidth = parentWidth;
      // 父元素需要去掉padding
      if (childrenRef.current?.parentElement) {
        const style = window.getComputedStyle(childrenRef.current?.parentElement, null);
        const paddingL = parseFloat(style.getPropertyValue('padding-left')); // 获取左侧内边距
        const paddingR = parseFloat(style.getPropertyValue('padding-right')); // 获取右侧内边距

        parentTmpWidth = parentTmpWidth - paddingL - paddingR;
      }
      if (targetWidth >= parentTmpWidth) {
        setVisible(true);
      }
      if (targetWidth < scrollWidth) {
        setVisible(true);
      }
    }
  };

  useEffect(() => {
    if (childrenRef.current) {
      childrenRef.current.onmouseenter = showToolTip;
    }
  }, [childrenRef.current, maxWidth]);

  const handleOpenChange = (flag: boolean) => {
    if (!flag) {
      setVisible(false);
    }
  };

  return (
    <Tooltip
      placement={'topLeft'}
      {...rest}
      trigger="hover"
      open={toolTip ? visible : false}
      onOpenChange={handleOpenChange}
    >
      {/* 子元素不能纯文本 */}
      {createChildren()}
    </Tooltip>
  );
}

export default HoverToolTip;
