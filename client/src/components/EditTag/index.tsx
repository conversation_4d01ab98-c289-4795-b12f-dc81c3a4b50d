/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2023-05-18 17:31:39
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-10-20 18:55:45
 * @Description:
 */
import styles from './index.less';
import { PlusOutlined } from '@ant-design/icons';
import type { InputRef } from 'antd';
import { Input, Tag, Tooltip } from 'antd';
import React, { useEffect, useRef, useState } from 'react';

interface Value {
  tags: string[];
}
type EditTagProps = {
  tags: string[];
  setTags: (params: string[]) => void;
  showAdd: boolean;
  colseable?: boolean;
  value?: string;
  onChange?: (value: Value) => void;
};
const EditTag: React.FC<EditTagProps> = ({
  tags,
  setTags,
  showAdd,
  colseable,
  onChange,
}) => {
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [editInputIndex, setEditInputIndex] = useState(-1);
  const [editInputValue, setEditInputValue] = useState('');
  const inputRef = useRef<InputRef>(null);
  const editInputRef = useRef<InputRef>(null);
  const triggerChange = (changedValue: { tags: string[] }) => {
    onChange?.({ ...changedValue });
  };
  useEffect(() => {
    if (inputVisible) {
      inputRef.current?.focus();
    }
  }, [inputVisible]);

  useEffect(() => {
    editInputRef.current?.focus();
  }, [inputValue]);
  useEffect(() => {
    if (editInputIndex !== -1) {
      editInputRef.current?.focus();
    }
  }, [editInputIndex, editInputValue]);
  const handleClose = (removedTag: string) => {
    const newTags = tags.filter((tag, index) => {
      return `${tag + index}` !== removedTag;
    });

    triggerChange({ tags: newTags });
    setTags(newTags);
  };

  const showInput = () => {
    setInputVisible(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputConfirm = () => {
    // && tags.indexOf(inputValue) === -1
    if (inputValue) {
      setTags([...tags, inputValue]);
      triggerChange({ tags: [...tags, inputValue] });
    }
    setInputVisible(false);
    setInputValue('');
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = () => {
    const newTags = [...tags];
    newTags[editInputIndex] = editInputValue;
    triggerChange({ tags: newTags });
    setTags(newTags);
    setEditInputIndex(-1);
    setInputValue('');
  };

  return (
    <>
      {tags.map((tag, index) => {
        if (editInputIndex === index) {
          return (
            <Input
              ref={editInputRef}
              key={tag + index}
              size="small"
              className={styles['tag-input']}
              value={editInputValue}
              onChange={handleEditInputChange}
              onBlur={handleEditInputConfirm}
              onPressEnter={handleEditInputConfirm}
            />
          );
        }

        const isLongTag = tag.length > 20;

        const tagElem = (
          <Tag
            className={styles['edit-tag']}
            key={tag + index}
            closable={colseable}
            onClose={() => handleClose(tag + index)}
          >
            <span
              onDoubleClick={(e) => {
                setEditInputIndex(index);
                setEditInputValue(tag);
                e.preventDefault();
              }}
            >
              {isLongTag ? `${tag.slice(0, 20)}...` : tag}
            </span>
          </Tag>
        );
        return isLongTag ? (
          <Tooltip title={tag} key={tag + index}>
            {tagElem}
          </Tooltip>
        ) : (
          tagElem
        );
      })}
      {inputVisible && (
        <Input
          ref={inputRef}
          type="text"
          size="small"
          className={styles['tag-input']}
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputConfirm}
          onPressEnter={handleInputConfirm}
        />
      )}
      {!inputVisible && showAdd && (
        <Tag className={styles['site-tag-plus']} onClick={showInput}>
          <PlusOutlined /> New Tire QPS
        </Tag>
      )}
    </>
  );
};

export default EditTag;
