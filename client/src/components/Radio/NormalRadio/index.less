// radio button的统一样式
.radio-button-container,
.more-radio-button-container {
  background: #f3f5f5;
  border-radius: 6px;
  padding: 3px;
  min-height: 32px;
  color: #5e6466;
  label {
    margin-right: 3px;
    &:last-child {
      margin-right: 0px;
    }
  }
  :global {
    .ant-radio-button-wrapper {
      height: 26px;
      border: none;
      background: #f3f5f5;
      line-height: 26px;
      color: #5e6466;
      &.ant-radio-button-wrapper-checked:focus-within {
        box-shadow: none;
      }
      &:hover {
        background: #ffffff;
        border-radius: 4px;
        font-weight: 700;
        color: #1568d4;
        &:not(
            [class*=' ant-radio-button-wrapper-disabled']
          ).ant-radio-button-wrapper:first-child {
          border: none;
        }
      }
      &.ant-radio-button-wrapper-disabled {
        color: #d7dadb;
        background: #f3f5f5;
      }
      &.ant-radio-button-wrapper-checked {
        background: #ffffff;
        border-radius: 4px;
        font-weight: 700;
        color: #1568d4;
      }
    }
    .ant-radio-button-wrapper:not(:first-child)::before {
      display: none;
    }
  }
}

.more-radio-button-container {
  background: #f3f5f5;
  padding-bottom: 0;
  color: #5e6466;
  max-width: 584px;
  label {
    margin-right: 3px;
    margin-bottom: 3px;
    &:last-child {
      margin-right: 0px;
    }
    :global {
      .ant-radio-button-checked {
        display: inline-block;
        background: #ffffff;
        border-radius: 4px;
        font-weight: 700;
        color: #1568d4;
        position: absolute;
        width: 100%;
        // border: 1px solid #1568D4;
      }
    }
  }
}
