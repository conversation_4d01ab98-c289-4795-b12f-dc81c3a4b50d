/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-29 17:38:27
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-02-16 22:03:38
 * @Description:
 */
import { useState } from 'react';
import { Radio } from 'antd';
import type { RadioGroupProps } from 'antd';
import styles from './index.less';

function NormalRadio(props: RadioGroupProps & { bgColor?: string }): JSX.Element {
  const [btnNums, setBtnNums] = useState(props.options?.length || 0);
  return (
    <Radio.Group
      className={`${styles['radio-button-container']} ${btnNums > 4 ? styles['more-radio-button-container'] : ''}`}
      {...props}
      optionType="button"
    />
  );
}

export default NormalRadio;
