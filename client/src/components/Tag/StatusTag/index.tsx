/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-09 10:40:03
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-29 12:10:15
 * @Description:
 */
import { Badge, Tooltip } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import type { TagProps } from 'antd/lib/tag';

const StatusDescMap: { [key: number]: string } = {
  1: 'Active',
  2: 'Paused',
  3: 'Delete',
};

type StatusTagProps = TagProps & {
  value: number;
  textColor?: string;
  statusDescMap?: { [key: number]: string };
  tips?: string; // 警告提示
  cusStatusMap?: { [key: string | number]: string }; // 自定义颜色映射
};

const ColorMap = {
  orange: '#FFB114',
  blue: '#476DF5',
  green: '#1CD880',
  red: '#FA5A42',
} as const;

export const StatusMap: { [key: number]: string } = {
  1: ColorMap.green,
  2: ColorMap.red,
  3: ColorMap.red,
};
function StatusTag({
  value = 1,
  textColor = '#606266',
  statusDescMap,
  tips,
  cusStatusMap,
  ...rest
}: StatusTagProps): JSX.Element {
  return (
    <span>
      <Badge
        {...rest}
        color={cusStatusMap ? cusStatusMap[value] : StatusMap[value]}
        text={statusDescMap ? statusDescMap[value] : StatusDescMap[value]}
        style={{ color: textColor }}
      />
      {tips && (
        <Tooltip title={tips}>
          <ExclamationCircleOutlined
            style={{
              fontSize: 16,
              cursor: 'pointer',
              color: '#faad14',
              paddingLeft: 6,
            }}
          />
        </Tooltip>
      )}
    </span>
  );
}

export default StatusTag;
