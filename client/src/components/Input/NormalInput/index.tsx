/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-11-16 10:24:44
 * @LastEditors: chen<PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-01-12 16:37:19
 * @Description:
 */
import React from 'react';
import { Input } from 'antd';
import type { InputProps } from 'antd';
import styles from './index.less';

function NormalInput(props: InputProps): JSX.Element {
  return (
    <Input
      {...props}
      className={styles['normal-input-radius-6']}
    />
  );
}
NormalInput.TextArea = Input.TextArea;
NormalInput.Password = Input.Password;
export default NormalInput;
