/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-06 12:35:14
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-09-06 11:50:46
 * @Description: 全选反选组件
 */

import React, { useState, useEffect } from 'react';
import { Badge, Button, Divider, Tag, message } from 'antd';
import type { SelectProps } from 'antd';
import Select from '@/components/Select/NormalSelect';
import InputSearch from '@/components/Input/InputSearch';
import { CheckOutlined } from '@ant-design/icons';
import styles from './index.less';
import type { CustomTagProps } from 'rc-select/lib/BaseSelect';
import { StatusMap } from '@/components/Tag/StatusTag';

type AllSelectProps = SelectProps & {
  limitNum?: number; // 至少多少个出现全选反选
  onChange?: (value: any) => void;
  limitSelectCount?: number; // 最多选择多少个选项
};

// 自定义tag
const TagRender: React.FC<CustomTagProps> = (props: CustomTagProps) => {
  const { label, value, closable, onClose } = props;
  const onPreventMouseDown = (event: React.MouseEvent<HTMLSpanElement>) => {
    event.preventDefault();
    event.stopPropagation();
  };
  return (
    <Tag
      onMouseDown={onPreventMouseDown}
      closable={closable}
      onClose={() => onClose(value)}
      className={styles['tag-container']}
    >
      {label}
    </Tag>
  );
};

function NormalSelectAll({
  limitNum = 5,
  limitSelectCount,
  ...props
}: AllSelectProps): JSX.Element {
  const [value, setValue] = useState<any>(props.value);
  const [searchOptions, setSearchOptions] = useState(props.options || []);
  const [showAll, setShowAll] = useState(false); // 是否显示全选反选
  const [searchValue, setSearchValue] = useState('');

  useEffect(() => {
    setValue(props.value);
  }, [props.value]);

  useEffect(() => {
    const flag1 = props.mode === 'multiple' || props.mode === 'tags';
    const flag2 =
      Array.isArray(props.options) && props.options.length >= limitNum;
    setShowAll(flag1 && flag2);
  }, [props.mode, limitNum, props.options]);

  useEffect(() => {
    setSearchOptions(props.options || []);
  }, [props.options]);

  const handleSearch = (val: string) => {
    const allOptions = props.options || [];
    if (val) {
      const options = allOptions.filter((v) => {
        const label = v.label ? `${v.label}`.toLowerCase() : '';
        return label.includes(val.toLowerCase());
      });
      setSearchOptions(options);
    } else {
      setSearchOptions(allOptions);
    }
  };

  const handleSelectAll = () => {
    const ori_value = props?.value ? props?.value : [];
    let arr = searchOptions.map((v) => v.value);
    arr = [...new Set([...ori_value, ...arr])];
    props?.onChange?.(arr);
  };

  const handleReverse = () => {
    const ori_value: any[] = props?.value ? props?.value : [];
    const allValue = searchOptions.map((v) => v.value);
    const rev_value = ori_value.filter((v) => allValue.includes(v));
    const rest_value = ori_value.filter((v) => !allValue.includes(v));
    const arr = allValue.filter((v) => !rev_value.includes(v));

    const f_arr = [...rest_value, ...arr];
    props?.onChange?.([...new Set(f_arr)]);
  };

  const onDropdownVisibleChange = (open: boolean) => {
    setSearchValue('');
    if (open) {
      setSearchOptions(props.options || []);
    }
  };

  const onSearchValueChange = (val: string) => {
    setSearchValue(val);
  };

  const handleSelectItem = (val: any) => {
    const flag1 = props.mode === 'multiple' || props.mode === 'tags';
    if (flag1) {
      const ori = props?.value || [];
      let arr = [...new Set([...ori, val])];
      if (ori.includes(val)) {
        arr = arr.filter((v) => v !== val);
      }
      if (flag1 && limitSelectCount && arr.length > limitSelectCount) {
        message.warn(`The number of options cannot exceed ${limitSelectCount}`);
        return;
      }
      props?.onChange?.(arr);
    } else {
      props?.onChange?.(val);
    }
  };

  const handleValueChange = (val: any) => {
    const flag = props.mode === 'multiple' || props.mode === 'tags';
    if (!showAll) {
      props?.onChange?.(val);
    }
  };

  const handleClear = () => {
    const flag1 = props?.mode === 'multiple' || props?.mode === 'tags';
    if (flag1) {
      props?.onChange?.([]);
    } else {
      props?.onChange?.(undefined);
    }
  };

  const handleCloseItem = (val: any) => {
    const flag1 = props.mode === 'multiple' || props.mode === 'tags';
    if (flag1) {
      const ori = props?.value || [];
      const arr = ori.filter((v: any) => v !== val);
      props?.onChange?.(arr);
    } else {
      props?.onChange?.(undefined);
    }
  };

  return (
    <Select
      maxTagCount="responsive"
      optionFilterProp="children"
      placeholder="Please Select"
      allowClear
      {...props}
      onChange={handleValueChange}
      onClear={handleClear}
      tagRender={(props) => <TagRender {...props} onClose={handleCloseItem} />}
      value={value}
      showSearch={!showAll}
      options={props.options || []}
      onDropdownVisibleChange={onDropdownVisibleChange}
      dropdownRender={() => {
        return (
          <div style={{ width: '100%' }}>
            {showAll && (
              <div className={styles['container']}>
                <InputSearch
                  handleSearch={handleSearch}
                  style={{ marginBottom: 12 }}
                  allowClear
                  value={searchValue}
                  onValueChange={onSearchValueChange}
                  placeholder="Please Input"
                />
              </div>
            )}
            <div className={styles['drop-container']}>
              {searchOptions.map((v, index) => {
                return (
                  <div
                    key={index}
                    onClick={() => handleSelectItem(v.value)}
                    className={
                      props?.value &&
                      (props.value === v.value ||
                        (Array.isArray(props.value) &&
                          props.value.includes(v.value)))
                        ? styles['drop-container-item-active']
                        : undefined
                    }
                  >
                    <div
                      className={styles['ellipsis']}
                      title={(v.label as string) || ''}
                    >
                      {v.status ? (
                        <Badge color={StatusMap[v.status]} text={v.label} />
                      ) : (
                        v.label
                      )}
                    </div>
                    {props?.value &&
                      (props.value === v.value ||
                        (Array.isArray(props.value) &&
                          props.value.includes(v.value))) && (
                        <CheckOutlined
                          style={{ color: '#0db4be', fontWeight: 600 }}
                        />
                      )}
                  </div>
                );
              })}
            </div>
            {showAll && (
              <div className={styles['container']}>
                <Divider style={{ marginTop: 8, marginBottom: 0 }} />
                <div className={styles['btn-container']}>
                  <Button
                    type="primary"
                    style={{ marginRight: 16 }}
                    onClick={handleSelectAll}
                  >
                    Select All
                  </Button>
                  <Button
                    type="primary"
                    style={{ marginRight: 16 }}
                    onClick={handleReverse}
                  >
                    Reverse
                  </Button>
                </div>
              </div>
            )}
          </div>
        );
      }}
    />
  );
}

NormalSelectAll.Option = Select.Option;

export default NormalSelectAll;
