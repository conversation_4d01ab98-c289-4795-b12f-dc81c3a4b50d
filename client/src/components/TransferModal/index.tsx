import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, Button } from 'antd';
import TableTransfer from '@/components/TableTransfer';
import { ColumnProps } from 'antd/es/table';
import styles from './index.less';
import NormalTitle from '@/components/NormalTitle';

type AuthorizationModalProps<T, K> = {
  visible: boolean;
  onCancel: () => void;
  onSave: (params: string[]) => void;
  authorizeList: string[]; // 已授权
  dataList: T[];
  leftTableColumns: ColumnProps<T>[];
  rightTableColumns: ColumnProps<T>[];
  rowKey: any;
  leftTitle: string;
  rightTitle: string;
  searchKeys: K[];
  placeholder: string;
}

function AuthorizationModal<T extends object>({ visible, onCancel, onSave, leftTableColumns,
  authorizeList, dataList, rightTableColumns, rowKey, leftTitle, rightTitle, searchKeys, placeholder }:
AuthorizationModalProps<T, keyof T>): JSX.Element {
  const [targetKeys, setTargetKeys] = useState(authorizeList);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  // 左边的
  const [leftSelectKeys, setLeftSelectKeys] = useState<React.Key[]>([]);
  // 右边的
  const [rightSelectKeys, setRightSelectKeys] = useState<React.Key[]>([]);

  useEffect(() => {
    const arr: any[] = [...leftSelectKeys, ...rightSelectKeys];
    setSelectedKeys(arr);
  }, [leftSelectKeys, rightSelectKeys]);

  const onChange = (nextTargetKeys: string[], direction: 'left' | 'right') => {
    setTargetKeys(nextTargetKeys);
    setLeftSelectKeys([]);
    setRightSelectKeys([]);
  };

  // 重置数据
  const reset = () => {
    setLeftSelectKeys([]);
    setRightSelectKeys([]);
    setSelectedKeys([]);
  };

  useEffect(() => {
    setTargetKeys(authorizeList);
  }, [authorizeList]);

  const handleConfirm = () => {
    onSave(targetKeys);
  };

  const handleCancel = () => {
    reset();
    onCancel();
    setTargetKeys(authorizeList);
  };

  return (
    <Modal
      open={visible}
      okText="Confirm"
      closable={false}
      keyboard={false}
      maskClosable={false}
      width={'100%'}
      style={{ height: '100%', top: 0, margin: 0, padding: 0, maxWidth: '100%' }}
      maskTransitionName="" // 解决弹窗闪烁问题
      className={styles['container']}
      footer={
        <>
          <Button onClick={() => handleCancel()}>Cancel</Button>
          <Button
            type='primary'
            onClick={() => handleConfirm()}
            disabled={authorizeList.length === targetKeys.length}
          >Confirm</Button>
        </>
      }
    >
      <TableTransfer<T>
        disabled={false}
        showSearch={false}
        dataSource={dataList}
        targetKeys={targetKeys}
        selectedKeys={selectedKeys}
        searchKeys={searchKeys}
        placeholder={placeholder}
        selectAllLabels={[
          ({ selectedCount, totalCount }) => (
            <NormalTitle
              blackName={leftTitle}
              isTitle={true}
              bottom={16}
              top={16}
              toolTip={false}
              num={selectedCount ? `${selectedCount} / ${totalCount}` : `${totalCount}`}
            />
          ), ({ selectedCount, totalCount }) => (
            <NormalTitle
              blackName={rightTitle}
              isTitle={true}
              bottom={16}
              top={16}
              toolTip={false}
              num={selectedCount ? `${selectedCount} / ${totalCount}` : `${totalCount}`}
            />
          )
        ]}
        onChange={onChange as any}
        loading={false}
        rowKey={rowKey}
        leftColumns={leftTableColumns as any}
        rightColumns={rightTableColumns as any}
        scroll={{ y: `calc(100vh - 180px)` }}
        leftSelectKeys={leftSelectKeys}
        rightSelectKeys={rightSelectKeys}
        setLeftSelectKeys={setLeftSelectKeys}
        setRightSelectKeys={setRightSelectKeys}
      ></TableTransfer>
    </Modal>);
}

export default AuthorizationModal;
