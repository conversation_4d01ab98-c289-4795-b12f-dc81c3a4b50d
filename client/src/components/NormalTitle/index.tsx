/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-05 14:45:51
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-18 15:26:09
 * @Description:
 */
import React from 'react';
import styles from './index.less';
import HoverTooltip from '@/components/Tooltip/HoverTooltip';

type NormalTitleProps = {
  blackName: string;
  grayName?: string;
  num?: string;
  isTitle?: boolean;
  bottom?: number;
  top?: number;
  className?: string;
  maxWidth?: number;
  toolTip?: boolean;
};

function NormalTitle({
  blackName,
  grayName,
  isTitle = false,
  bottom = 4,
  top = 0,
  className,
  maxWidth = 0,
  toolTip = true,
  num = '',
}: NormalTitleProps): JSX.Element {
  return (
    <HoverTooltip
      title={`${blackName} ${grayName}`}
      maxWidth={maxWidth || undefined}
      toolTip={toolTip}
    >
      <span
        className={`${styles['title-container']} ${className}`}
        style={{
          marginBottom: bottom,
          marginTop: top,
          maxWidth,
        }}
      >
        <span
          className={`${styles['black']} ${isTitle ? styles['title'] : ''} ${
            maxWidth ? styles['max-width'] : ''
          }`}
        >
          {blackName}
        </span>
        {grayName && (
          <>
            <span className={styles['line']}>|</span>
            <span
              className={`${styles['gray']} ${isTitle ? styles['title'] : ''}`}
            >
              {grayName}
            </span>
            {num && (
              <span className={styles['line']} style={{ marginRight: 0 }}>
                |
              </span>
            )}
          </>
        )}
        {num && (
          <>
            <span className={styles['span-num']}>{num}</span>
          </>
        )}
      </span>
    </HoverTooltip>
  );
}

export default NormalTitle;
