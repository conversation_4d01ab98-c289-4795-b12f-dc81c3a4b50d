import type {
  Settings as LayoutSettings,
  MenuDataItem,
} from '@ant-design/pro-layout';
import { PageLoading } from '@ant-design/pro-layout';
import { message } from 'antd';
import { history } from 'umi';
import { getCurrentUser as queryCurrentUser } from './services/api';
import defaultSettings from '../config/defaultSettings';
import type { RunTimeLayoutConfig } from '@umijs/max';
import RightContent from '@/components/RightContent';
import type { RequestConfig } from 'umi';
import { IconFontUrl, isNoAuthPath, LoginPath } from '@/constants';
import RixEngineFont from '@/components/RixEngineFont';
import { UIConfig } from '@/constants';
const { SiderWidth } = UIConfig;
import CusLoadingIcon from './components/LoadingIcon';
import ErrorBound from './pages/error-page';
import { SpecialSuccessCode } from '@/constants';
import moment from 'moment-timezone';
import { ClickToComponent } from 'click-to-react-component';

// 设置时区
moment.tz.setDefault('Etc/UTC');
// 接口报错处理 比如超时
const errorHandler = function (error: any) {
  const { message: errMsg } = error;
  console.log('xx报错', error);
  errMsg && message.error(errMsg);
  throw error;
};

export const request: RequestConfig = {
  timeout: 500000,
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  errorConfig: {
    errorHandler: errorHandler,
    errorThrower() {},
  },
  requestInterceptors: [],
  responseInterceptors: [
    (response: any) => {
      const { data } = response;
      const isSpecialCode = Object.values(SpecialSuccessCode).includes(
        data.code,
      );

      if ((data && (data.code === 0 || isSpecialCode)) || Array.isArray(data)) {
        return response;
      }
      if (data && data.code === 1102) {
        if (history.location.pathname !== LoginPath) {
          history.push(LoginPath);
        }
      } else {
        if (data && data.code !== 1101 && data.code !== 1105) {
          message.error(data?.message || 'system error', 4);
        }
      }

      return response;
    },
  ],
};

const fetchUserInfo = async () => {
  try {
    const msg = await queryCurrentUser();
    return msg.data;
  } catch (error) {
    history.push(LoginPath);
  }
  return undefined;
};

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings> & { pwa?: boolean; logo?: string };
  currentUser?: UserAPI.UserListItem;
  loading?: boolean;
  fetchUserInfo?: () => Promise<UserAPI.UserListItem | undefined>;
  isCollapsed: boolean; // 菜单是否打开
}> {
  // 如果是登录页面，不执行
  if (!isNoAuthPath(history.location.pathname)) {
    const currentUser = await fetchUserInfo();
    return {
      fetchUserInfo,
      currentUser,
      settings: defaultSettings,
      isCollapsed: false,
    };
  }
  return {
    fetchUserInfo,
    settings: defaultSettings,
    currentUser: undefined,
    isCollapsed: false,
  };
}

export const layout: RunTimeLayoutConfig = ({
  initialState,
  setInitialState,
}) => {
  const logo = initialState?.settings?.logo;
  const { location } = history;
  const setting: any = initialState?.settings || defaultSettings;
  return {
    iconfontUrl: IconFontUrl[(process.env.UMI_ENV as string) || 'prod'],
    siderWidth: SiderWidth,
    rightContentRender: () => <RightContent />,
    disableContentMargin: false,
    waterMarkProps: {
      content: initialState?.currentUser?.account_name,
    },
    // footerRender: () => <Footer />,
    onPageChange: () => {
      // 如果没有登录，重定向到 login
      if (!initialState?.currentUser && !isNoAuthPath(location.pathname)) {
        history.push(LoginPath);
      }
    },
    onCollapse: (collapsed: boolean) => {
      setInitialState({
        ...initialState,
        isCollapsed: collapsed,
      });
    },
    menuDataRender: (menuData: MenuDataItem[]) => {
      // 控制菜单展示
      return menuData.map((item) => {
        return {
          ...item,
          icon:
            typeof item.icon === 'string' ? (
              <RixEngineFont
                type={item.icon}
                style={{ fontSize: item.icon.includes('antd-') ? 30 : 20 }}
              />
            ) : (
              item.cion
            ),
        };
      });
    },
    defaultCollapsed: false,
    breakpoint: false,
    // 增加一个 loading 的状态
    childrenRender: (children: any) => {
      if (initialState?.loading)
        return <PageLoading indicator={<CusLoadingIcon />} />;
      return (
        <ErrorBound>
          <ClickToComponent editor="cursor" />
          <div style={{ background: '#fff', width: '100%', height: '100%' }}>
            {children}
          </div>
        </ErrorBound>
      );
    },
    contentStyle: {
      margin: 0,
    },
    ...setting,
    logo: <img style={{ height: 64 }} src={logo} alt="Logo" />,
  };
};
const xhr = new XMLHttpRequest();
const baseUrl = process.env.UMI_ENV === 'dev' ? '/webroot/' : '/';
export async function onRouteChange() {
  const Etag = localStorage.getItem('sourceEtag') || '';
  const result: any = await new Promise(function (resolve, reject) {
    xhr.open('GET', baseUrl, true);
    xhr.timeout = 1000; // 设置超时时间为1秒
    xhr.ontimeout = function () {
      resolve(this);
    };
    xhr.onload = function () {
      if (this.status === 200 || this.status === 304) {
        resolve(this);
      } else {
        reject(this);
      }
    };
    xhr.onerror = function () {
      reject(this);
    };
    xhr.setRequestHeader('If-None-Match', Etag);
    xhr.setRequestHeader('Cache-Control', 'max-age=0');
    xhr.send();
  });
  if (result.status === 200) {
    localStorage.setItem('sourceEtag', result.getResponseHeader('Etag'));
    Etag && window.location.reload();
  }
}
