/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:06:05
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-03 16:42:13
 * @Description:
 */
export default [
  {
    path: '/',
    redirect: '/welcome',
  },
  {
    path: '/user',
    name: 'User',
    layout: false,
    routes: [
      {
        name: 'login',
        path: '/user/login',
        component: '@/pages/login',
      },
    ],
  },
  {
    path: '/welcome',
    name: 'welcome',
    component: '@/pages/welcome',
    hideInMenu: true,
  },
  {
    path: '/ai-board',
    name: 'Dashboard',
    icon: 'rix-dashboard',
    access: 'AiBoardPermission',
    routes: [
      {
        path: '/ai-board/main-board',
        name: 'Overview',
        component: '@/pages/ai-board/main-board',
        access: 'ReeMainBoardCode',
      },
    ],
  },
  {
    path: '/ree-ai',
    name: '<PERSON><PERSON> <PERSON>',
    icon: 'rix-board',
    access: 'ReeA<PERSON>',
    routes: [
      {
        path: '/ree-ai/sent-message',
        name: 'Trading Message',
        component: '@/pages/ai-board/sent-message',
        access: 'ReeSentMessageCode',
      },
    ],
  },
  {
    path: '/data-report',
    name: 'Data Report',
    icon: 'antd-LineChartOutlined',
    access: 'DataReport',
    routes: [
      {
        path: '/data-report/full-reporting',
        name: 'Full Reporting',
        component: '@/pages/data-report/full-report',
        access: 'DataFullReport',
      },
      {
        path: '/data-report/billing-report',
        name: 'Billing Reporting',
        access: 'DataBillingReport',
        routes: [
          {
            path: '/data-report/billing-report/advertiser',
            name: 'Advertiser',
            component: '@/pages/data-report/billing-report/advertiser',
            access: 'DataAdvertiserBillingReport',
          },
          {
            path: '/data-report/billing-report/publisher',
            name: 'Publisher',
            component: '@/pages/data-report/billing-report/publisher',
            access: 'DataPublisherBillingReport',
          },

          {
            path: '/data-report/billing-report/monthly',
            name: 'Monthly',
            component: '@/pages/data-report/billing-report/monthly',
            access: 'DataMonthlyReport',
          },
        ],
      },
      {
        // block report: demand, supply
        path: '/data-report/block-report',
        name: 'Block Reporting',
        access: 'DataBlockReport',
        routes: [
          {
            path: '/data-report/block-report/demand',
            name: 'Advertiser',
            component: '@/pages/data-report/block-report/demand',
            access: 'DataDemandBlockReport',
          },
          {
            path: '/data-report/block-report/supply',
            name: 'Publisher',
            component: '@/pages/data-report/block-report/supply',
            access: 'DataSupplyBlockReport',
          },
        ],
      },
      {
        path: '/data-report/kwai-reporting',
        name: 'Kwai Reporting',
        component: '@/pages/data-report/kwai-reporting',
        access: 'DataKwaiReport',
      },
      {
        path: '/data-report/human',
        name: 'Human Reporting',
        component: '@/pages/data-report/human-report',
        access: 'DataHumanReport',
      },
      {
        path: '/data-report/pixalate',
        name: 'Pixalate Reporting',
        component: '@/pages/data-report/pixalate-report',
        access: 'DataPixalateReport',
      },
      {
        path: '/data-report/export-log',
        name: 'Export Log',
        component: '@/pages/data-report/export-log',
        access: 'DataExportLog',
      },
    ],
  },

  {
    path: '/demand',
    name: 'Demand',
    icon: 'rix-demand',
    access: 'Demand',
    routes: [
      {
        path: '/demand/advertiser',
        name: 'Advertiser',
        component: '@/pages/demand/advertiser',
        access: 'Advertiser',
      },
    ],
  },
  {
    path: '/supply',
    name: 'Supply',
    icon: 'rix-supply',
    access: 'Supply',
    routes: [
      {
        path: '/supply/publisher',
        name: 'Publisher',
        component: '@/pages/supply/publisher',
        access: 'Publisher',
      },
    ],
  },
  {
    path: '/config',
    name: 'Config',
    icon: 'antd-SettingOutlined',
    access: 'ConfigCode',
    routes: [
      {
        path: '/config/ecpr',
        name: 'Ecpr',
        component: '@/pages/config/ecpr',
        access: 'ConfigEcpr',
      },
      {
        path: '/config/atc',
        name: 'ATC WL',
        component: '@/pages/config/atc-wl',
        access: 'ConfigATC',
      },
      {
        path: '/config/kwa',
        name: 'Custom Placement',
        component: '@/pages/config/kwai-placement',
        access: 'ConfigKwai',
      },

      // {
      //   path: '/config/stgv2',
      //   name: 'Supply Chain V2',
      //   component: '@/pages/config/stg-chain-v2',
      //   access: 'ConfigSupplyChain',
      // },
      {
        path: '/config/pixalate-prebid',
        name: 'Pixalate Prebid',
        component: '@/pages/config/pixalate-prebid',
        access: 'ConfigPixalatePrebid',
      },
      {
        path: '/config/ivtconfig',
        name: 'IVT Config',
        component: '@/pages/config/ivt',
        access: 'ConfigIVT',
      },
    ],
  },

  // {
  //   path: '/app-ads',
  //   name: 'App-ads.txt',
  //   icon: 'rix-apps',
  //   access: 'AppAds',
  //   routes: [
  //     {
  //       path: '/app-ads/app-info',
  //       name: 'App Info',
  //       component: '@/pages/app-ads/app-info',
  //       access: 'AdsAppInfo',
  //     },
  //   ],
  // },

  {
    path: '/transparency',
    name: 'Transparency',
    icon: 'rix-transparency',
    access: 'Transparency',
    routes: [
      {
        path: '/transparency/stg',
        name: 'Supply Chain',
        component: '@/pages/config/stg-chain',
        access: 'ConfigSupplyChain',
      },
      {
        path: '/transparency/schain-truncation',
        name: 'Schain Truncation',
        component: '@/pages/transparency/schain-truncation',
        access: 'SchainTruncation',
      },
      {
        path: '/transparency/rix-site-files',
        name: 'Rix Site Files',
        component: '@/pages/transparency/rix-site-files',
        access: 'TransparencyRixSiteFile',
      },
      {
        path: '/transparency/aat-tool',
        name: 'App-ads.txt Tool',
        component: '@/pages/app-ads/app-info',
        access: 'AdsAppInfo',
      },
      {
        path: '/transparency/app-info',
        name: 'App Info',
        component: '@/pages/transparency/app-info',
        access: 'TransparencyAppInfo',
      },
      {
        path: '/transparency/app-crawler',
        name: 'App Crawler',
        component: '@/pages/transparency/app-crawler',
        access: 'TransparencyAppCrawler',
      },
    ],
  },

  {
    path: '/manage',
    name: 'Manage',
    icon: 'antd-BorderOuterOutlined',
    access: 'ManageCode',
    routes: [
      {
        path: '/manage/tenant',
        name: 'Tenant',
        component: '@/pages/manage/tenant',
        access: 'TenantManage',
      },
      {
        path: '/manage/user',
        name: 'User',
        component: '@/pages/manage/user',
        access: 'TenantUser',
      },
      {
        path: '/manage/privatization',
        name: 'Privatization',
        component: '@/pages/manage/privatization',
        access: 'Privatization',
      },
      {
        path: '/manage/account-link',
        name: 'Account Link',
        component: '@/pages/manage/account-link',
        access: 'AccountLink',
      },
    ],
  },
  {
    path: '/menu-management',
    name: 'Permission',
    access: 'PermissionCode',
    icon: 'antd-UsergroupAddOutlined',
    routes: [
      {
        path: '/menu-management/interface',
        name: 'Interface',
        component: '@/pages/permission/interface',
        access: 'PermissionInterface',
      },
      {
        path: '/menu-management/menu',
        name: 'Menu',
        component: '@/pages/permission/menu',
        access: 'PermissionMenu',
      },
      {
        path: '/menu-management/role',
        name: 'Default Role',
        component: '@/pages/permission/role',
        access: 'PMSRole',
      },
    ],
  },
  {
    path: '/admin',
    name: 'Admin Setting',
    icon: 'rix-advanced',
    access: 'AdminSet',
    routes: [
      {
        path: '/admin/menu',
        name: 'Menu',
        component: '@/pages/admin-setting/menu',
        access: 'AdminMenu',
      },
      {
        path: '/admin/role',
        name: 'Role',
        component: '@/pages/admin-setting/role',
        access: 'AdminRole',
      },
      {
        path: '/admin/user',
        name: 'User',
        component: '@/pages/admin-setting/user',
        access: 'AdminUser',
      },
    ],
  },
  {
    path: '/my-account',
    name: '账号信息',
    component: '@/pages/my-account',
    hideInMenu: true,
  },
  {
    path: '/open-service',
    name: 'Open Service',
    component: '@/pages/open-service',
    hideInMenu: true,
    layout: false,
  },
  {
    path: '*',
    component: '@/pages/404.tsx',
  },
];
