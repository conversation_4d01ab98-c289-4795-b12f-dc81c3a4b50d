/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-21 14:17:54
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-26 11:48:53
 * @Description:
 */

/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 */
export default {
  dev: {
    '/api/': {
      // 要代理的地址
      // target: 'https://admin-t.rixengine.com',
      target: 'http://127.0.0.1:5090/',
      // 配置了这个可以从 http 代理到 https
      // 依赖 origin 的功能可能需要这个，比如 cookie
      changeOrigin: true,
    },
    '/webroot/': {
      // 重定向到本地的 webroot 目录
      target: 'http://127.0.0.1:5090/',
      changeOrigin: true,
    },
  },
};
