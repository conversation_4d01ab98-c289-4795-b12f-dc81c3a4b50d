/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-09-30 11:31:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-07 15:51:56
 * @Description:
 */
import { Settings as LayoutSettings } from '@ant-design/pro-layout';

const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
  openKeys?: boolean;
} = {
  navTheme: 'light',
  headerTheme: 'light',
  // 拂晓蓝
  primaryColor: '#1568D4',
  layout: 'mix',
  contentWidth: 'Fluid',
  splitMenus: false,
  fixedHeader: true,
  fixSiderbar: true,
  colorWeak: false,
  title: '',
  headerHeight: 64,
  pwa: false,
  logo: '/img/logo.png',
  menu: {
    locale: false,
  },
  openKeys: false,
};

export default Settings;
