server {
    listen              8080;
    server_name         admin-t.rixengine.com;
    underscores_in_headers on;

    location ~* /(\.svn|CVS|Entries){
        deny all;
    }

    location ~* /((.*)\.(.*)\/(.*)\.php){
        deny all;
    }

    location ~* /\.(sql|bak|inc|old|map)$ {
        deny all;
    }

    location ~* /(js|img|css|fonts)/ {
        expires 7d;
    root /data/htdocs/saas.rix-admin/server/webroot;
    }

    location /heartbeat {
        access_log off;
        default_type text/html;
        return 200 'ok, I am alive...';
    }

    location / {
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header Host $host;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Connection "keep-alive";
        proxy_pass http://127.0.0.1:3002;
    }

}
