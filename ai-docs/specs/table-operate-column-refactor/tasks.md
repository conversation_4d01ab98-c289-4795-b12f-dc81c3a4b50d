# Implementation Plan

- [ ] 1. 分析现有代码并识别需要重构的文件
 - 搜索项目中所有使用 OperateRender 组件或包含操作列的文件
 - 分析每个文件中操作列的实现方式（直接定义、useEffect动态修改等）
 - 确定重构的优先级和顺序
 - _Requirements: 1.1, 1.3_

- [ ] 2. 重构 client/src/pages/config/pixalate-prebid/index.tsx
- [ ] 2.1 移除 useEffect 动态修改操作列的逻辑
   - 删除用于动态设置操作列 render 函数的 useEffect 代码
   - 移除相关的 onOperateRender 函数
   - 移除不必要的 columns 状态管理
   - _Requirements: 1.3, 1.6_

- [ ] 2.2 使用 genOperateColumn 重构操作列
   - 导入 genOperateColumn 工具函数
   - 使用 useMemo 包装 columns 定义
   - 使用 genOperateColumn 生成操作列，传入正确的 btnOptions 和 access 属性
   - _Requirements: 1.1, 1.2, 1.4, 1.5_

- [ ] 2.3 验证重构后的功能一致性
   - 确保操作按钮功能与原实现完全相同
   - 确保权限控制（access属性）正常工作
   - 确保按钮的样式、图标和交互行为不变
   - _Requirements: 1.5_

- [ ] 3. 重构 client/src/pages/demand/advertiser/index.tsx
- [ ] 3.1 移除直接在 columns 数组中定义操作列的代码
   - 删除 columns 数组中直接定义的操作列对象
   - 提取操作按钮配置到单独的 btnOptions 数组
   - _Requirements: 1.1, 1.3_

- [ ] 3.2 使用 genOperateColumn 重构操作列
   - 导入 genOperateColumn 工具函数
   - 使用 useMemo 包装 columns 定义
   - 使用 genOperateColumn 生成操作列，传入正确的 btnOptions、access 和 width 属性
   - 确保按钮的禁用状态逻辑与原实现一致
   - _Requirements: 1.1, 1.2, 1.4, 1.5_

- [ ] 3.3 验证重构后的功能一致性
   - 确保操作按钮功能与原实现完全相同
   - 确保按钮的禁用状态逻辑正常工作
   - 确保按钮的样式、图标和交互行为不变
   - _Requirements: 1.5_

- [ ] 4. 重构 client/src/pages/manage/user/index.tsx
- [ ] 4.1 移除直接在 columns 数组中定义操作列的代码
   - 删除 columns 数组中直接定义的操作列对象
   - 保留现有的 OperateOptions 数组
   - _Requirements: 1.1, 1.3_

- [ ] 4.2 使用 genOperateColumn 重构操作列
   - 导入 genOperateColumn 工具函数
   - 使用 useMemo 包装 columns 定义
   - 使用 genOperateColumn 生成操作列，传入正确的 btnOptions、access 和 width 属性
   - 确保删除按钮的隐藏逻辑与原实现一致
   - _Requirements: 1.1, 1.2, 1.4, 1.5_

- [ ] 4.3 验证重构后的功能一致性
   - 确保操作按钮功能与原实现完全相同
   - 确保删除按钮的隐藏逻辑正常工作
   - 确保按钮的样式、图标和交互行为不变
   - _Requirements: 1.5_

- [ ] 5. 重构 client/src/pages/supply/publisher/index.tsx
- [ ] 5.1 移除直接在 columns 数组中定义操作列的代码
   - 删除 columns 数组中直接定义的操作列对象
   - 保留现有的 OperateOptions 数组
   - 保留 onCell 点击事件阻止冒泡的逻辑
   - _Requirements: 1.1, 1.3_

- [ ] 5.2 使用 genOperateColumn 重构操作列
   - 导入 genOperateColumn 工具函数
   - 使用 useMemo 包装 columns 定义
   - 使用 genOperateColumn 生成操作列，传入正确的 btnOptions 和 width 属性
   - 确保 onCell 点击事件阻止冒泡的逻辑与原实现一致
   - _Requirements: 1.1, 1.2, 1.4, 1.5_

- [ ] 5.3 验证重构后的功能一致性
   - 确保操作按钮功能与原实现完全相同
   - 确保点击事件阻止冒泡逻辑正常工作
   - 确保按钮的样式、图标和交互行为不变
   - _Requirements: 1.5_

- [ ] 6. 重构其他包含操作列的文件
- [ ] 6.1 重构 client/src/pages/config/stg-chain/index.tsx
   - 移除 useEffect 动态修改操作列的逻辑
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.2 重构 client/src/pages/config/ivt/index.tsx
   - 移除 useEffect 动态修改操作列的逻辑
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.3 重构 client/src/pages/config/ecpr/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.4 重构 client/src/pages/config/atc-wl/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.5 重构 client/src/pages/config/kwai-placement/kwai.tsx
   - 移除 useEffect 动态修改操作列的逻辑
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.6 重构 client/src/pages/config/kwai-placement/custom.tsx
   - 移除 useEffect 动态修改操作列的逻辑
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.7 重构 client/src/pages/config/stg-chain-v2/index.tsx
   - 移除 useEffect 动态修改操作列的逻辑
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.8 重构 client/src/pages/permission/interface/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.9 重构 client/src/pages/transparency/app-crawler/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.10 重构 client/src/pages/transparency/app-info/index.tsx
   - 移除 useEffect 动态修改操作列的逻辑
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.11 重构 client/src/pages/transparency/rix-site-files/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.12 重构 client/src/pages/admin-setting/user/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.13 重构 client/src/pages/transparency/schain-truncation/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.14 重构 client/src/pages/admin-setting/role/role-list.tsx
   - 移除 useEffect 动态修改操作列的逻辑
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.15 重构 client/src/pages/manage/account-link/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.16 重构 client/src/pages/manage/tenant/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 6.17 重构 client/src/pages/data-report/export-log/index.tsx
   - 移除直接在 columns 数组中定义操作列的代码
   - 使用 genOperateColumn 重构操作列
   - 验证重构后的功能一致性
   - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_

- [ ] 7. 进行全面测试和验证
- [ ] 7.1 单元测试
   - 测试 genOperateColumn 函数的正确性
   - 测试操作按钮的点击事件是否正确触发
   - 测试操作按钮的禁用状态、隐藏状态等逻辑
   - _Requirements: 1.5_

- [ ] 7.2 集成测试
   - 测试重构后的表格是否正常显示
   - 测试操作列是否正确显示在表格中
   - 测试操作按钮的功能是否与原实现一致
   - _Requirements: 1.5_

- [ ] 7.3 回归测试
   - 确保重构后的功能与原实现完全一致
   - 确保没有引入新的 bug
   - _Requirements: 1.5, 1.6_
