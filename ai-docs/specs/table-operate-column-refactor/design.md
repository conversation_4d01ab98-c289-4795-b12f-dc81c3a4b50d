# Design Document

## Overview

本文档描述了重构 client 项目中所有表格操作列逻辑的设计方案。当前项目中存在多种不同的表格操作列实现方式，为了统一代码风格、提高维护性和性能，我们将所有表格操作列逻辑统一使用 `genOperateColumn` 工具函数实现。

## Architecture

### 当前实现分析

通过代码分析，我们发现当前项目中存在以下几种表格操作列的实现方式：

1. **直接在 columns 数组中定义操作列**：
   - 在 columns 数组中直接定义操作列对象
   - 在 render 函数中直接使用 OperateRender 组件
   - 例如：`client/src/pages/demand/advertiser/index.tsx`、`client/src/pages/manage/user/index.tsx`

2. **使用 OperateRender 组件但未使用 genOperateColumn**：
   - 定义单独的 OperateOptions 数组
   - 在 columns 数组中定义操作列，在 render 函数中使用 OperateRender
   - 例如：`client/src/pages/supply/publisher/index.tsx`

3. **通过 useEffect 动态修改操作列 render 函数**：
   - 在 useEffect 中查找操作列并动态设置 render 函数
   - 例如：`client/src/pages/config/pixalate-prebid/index.tsx`

4. **使用 genOperateColumn 工具函数的标准实现**：
   - 使用 `genOperateColumn` 函数生成操作列
   - 例如：`client/src/pages/manage/privatization/components/PixalateTable.tsx`

### 目标架构

我们将所有表格操作列逻辑统一为使用 `genOperateColumn` 工具函数的实现方式，具体架构如下：

1. **使用 `genOperateColumn` 生成操作列**：
   - 在 columns 定义中使用 `genOperateColumn` 函数
   - 传入操作按钮配置、权限控制等参数

2. **使用 `useMemo` 优化性能**：
   - 使用 `useMemo` 包装 columns 定义
   - 避免不必要的重新渲染

3. **移除不必要的 useEffect 和状态管理**：
   - 移除用于动态修改操作列的 useEffect
   - 移除不必要的状态管理代码

## Components and Interfaces

### genOperateColumn 工具函数

`genOperateColumn` 工具函数位于 `client/src/utils/genOperateColumn.tsx`，其接口定义如下：

```typescript
type Options = {
  width?: number;
  access?: string;
  btnOptions: OperateRenderItem[];
  title?: string | ReactNode;
};

export function genOperateColumn<T extends object>(
  options: Options,
): ColumnType<T> {
  // 实现细节
}
```

### OperateRenderItem 接口

`OperateRenderItem` 接口定义了操作按钮的配置：

```typescript
export type OperateRenderItem = {
  label: string;
  icon?: any;
  onClick?: (params: any) => void;
  hide?: boolean | ((params: any) => boolean);
  isDelete?: boolean;
  disabled?: boolean;
  handleDisabled?: (params: any) => boolean;
};
```

### 重构后的操作列实现

重构后的操作列实现将遵循以下模式：

```typescript
const columns = useMemo(() => {
  const operateColumn = genOperateColumn<DataType>({
    access: 'PermissionKey',
    btnOptions: [
      {
        label: 'Edit',
        onClick: handleEdit,
        icon: <RixEngineFont type="edit" />,
      },
      {
        label: 'Delete',
        onClick: handleDelete,
        icon: <RixEngineFont type="rix-trash" />,
        isDelete: true,
      },
    ],
  });
  return [...otherColumns, operateColumn];
}, [handleEdit, handleDelete]);
```

## Data Models

### 重构前后数据模型对比

#### 重构前：直接在 columns 数组中定义操作列

```typescript
const columns = [
  // 其他列...
  {
    title: 'Operation',
    dataIndex: 'operate',
    width: 100,
    fixed: 'right',
    render: (txt, params) => (
      <OperateRender
        btnOptions={[
          {
            label: 'Edit',
            onClick: handleEdit,
            icon: <RixEngineFont type="edit" />,
          },
        ]}
        params={params}
      />
    ),
  },
];
```

#### 重构后：使用 genOperateColumn

```typescript
const columns = useMemo(() => {
  const operateColumn = genOperateColumn<DataType>({
    width: 100,
    access: 'PermissionKey',
    btnOptions: [
      {
        label: 'Edit',
        onClick: handleEdit,
        icon: <RixEngineFont type="edit" />,
      },
    ],
  });
  return [...otherColumns, operateColumn];
}, [handleEdit]);
```

#### 重构前：通过 useEffect 动态修改操作列

```typescript
const [columns, setColumns] = useState(ColumnOptions);

useEffect(() => {
  const columns = [...ColumnOptions];
  const index = columns.findIndex((v) => v.dataIndex === 'operate');
  if (index !== -1) {
    columns[index].render = onOperateRender;
  }
  setColumns(columns);
}, []);

const onOperateRender = (_: number, row: DataType) => {
  return (
    <OperateRender
      params={row}
      btnOptions={[
        {
          label: 'Edit',
          icon: <RixEngineFont type="edit" />,
          onClick: () => handleEdit(row),
        },
      ]}
    />
  );
};
```

#### 重构后：使用 genOperateColumn

```typescript
// 在重构前需要把 ColumnOptions 中的 operate 列删除
const columns = useMemo(() => {
  const operateColumn = genOperateColumn<DataType>({
    btnOptions: [
      {
        label: 'Edit',
        onClick: handleEdit,
        icon: <RixEngineFont type="edit" />,
      },
    ],
  });
  return [...ColumnOptions, operateColumn];
}, [handleEdit]);
```

## Error Handling

### 重构过程中的错误处理

1. **类型错误处理**：
   - 确保 `genOperateColumn` 的泛型参数与行数据类型匹配
   - 确保 `btnOptions` 中的 onClick 函数参数类型正确

2. **权限控制错误处理**：
   - 确保 `access` 属性值正确
   - 确保权限控制逻辑与原实现一致

3. **功能一致性错误处理**：
   - 确保所有操作按钮的功能与原实现一致
   - 确保按钮的禁用状态、隐藏状态等逻辑与原实现一致

### 重构后的错误处理

1. **运行时错误处理**：
   - `genOperateColumn` 函数内部对参数进行验证
   - 确保传入的 `btnOptions` 不为空

2. **类型安全**：
   - 使用 TypeScript 的类型检查确保类型安全
   - 确保操作按钮的 onClick 函数参数类型正确

## Testing Strategy

### 单元测试

1. **genOperateColumn 函数测试**：
   - 测试函数返回的操作列对象结构是否正确
   - 测试不同参数配置下的行为

2. **操作按钮功能测试**：
   - 测试操作按钮的点击事件是否正确触发
   - 测试操作按钮的禁用状态、隐藏状态等逻辑

### 集成测试

1. **表格功能测试**：
   - 测试重构后的表格是否正常显示
   - 测试操作列是否正确显示在表格中

2. **操作按钮功能测试**：
   - 测试操作按钮的功能是否与原实现一致
   - 测试权限控制是否正常工作

### 回归测试

1. **功能回归测试**：
   - 确保重构后的功能与原实现完全一致
   - 确保没有引入新的 bug

## 重构计划

### 第一阶段：分析现有代码

1. 识别所有需要重构的文件
2. 分析每个文件中操作列的实现方式
3. 确定重构的优先级

### 第二阶段：重构核心文件

1. 重构 `client/src/pages/config/pixalate-prebid/index.tsx`
2. 重构 `client/src/pages/demand/advertiser/index.tsx`
3. 重构 `client/src/pages/manage/user/index.tsx`
4. 重构 `client/src/pages/supply/publisher/index.tsx`

### 第三阶段：重构其他文件

1. 重构其他包含操作列的文件
2. 确保所有文件都使用 `genOperateColumn` 工具函数

### 第四阶段：测试和验证

1. 进行单元测试
2. 进行集成测试
3. 进行回归测试
4. 修复发现的问题

## 总结

通过本次重构，我们将统一项目中所有表格操作列的实现方式，提高代码的一致性和可维护性，同时优化性能。重构后的代码将更加清晰、简洁，并且更容易维护和扩展。
