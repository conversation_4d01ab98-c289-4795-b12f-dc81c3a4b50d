# Requirements Document

## Introduction

本文档描述了重构 client 项目中所有表格操作列逻辑的需求。当前项目中存在多种不同的表格操作列实现方式，包括直接在 columns 数组中定义操作列、使用 OperateRender 组件但未使用 genOperateColumn 工具函数、通过 useEffect 动态修改操作列 render 函数等。为了统一代码风格、提高维护性和性能，我们需要将所有表格操作列逻辑统一使用 `genOperateColumn` 工具函数实现。

## Requirements

### Requirement 1

**User Story:** 作为开发人员，我希望将所有表格操作列逻辑统一使用 `genOperateColumn` 工具函数，以便提高代码的一致性和可维护性。

#### Acceptance Criteria
1. WHEN 查看表格操作列实现 THEN 系统 SHALL 识别所有需要重构的操作列代码
2. WHEN 重构操作列 THEN 系统 SHALL 使用 `genOperateColumn` 工具函数替代现有实现
3. WHEN 重构完成后 THEN 系统 SHALL 确保所有表格操作功能与原实现完全相同

### Requirement 2

**User Story:** 作为开发人员，我希望以 `client/src/pages/manage/privatization/components/PixalateTable.tsx` 中的 `genOperateColumn` 用法为标准模板，以便保持代码风格的一致性。

#### Acceptance Criteria
1. WHEN 重构操作列 THEN 系统 SHALL 参考标准模板的实现方式
2. WHEN 定义操作列 THEN 系统 SHALL 使用 `genOperateColumn` 函数生成操作列
3. WHEN 使用 `genOperateColumn` 函数 THEN 系统 SHALL 传入正确的参数配置

### Requirement 3

**User Story:** 作为开发人员，我希望重构特定类型的操作列实现，包括直接在 columns 数组中定义操作列的代码、使用 OperateRender 组件但未使用 genOperateColumn 的实现、通过 useEffect 动态修改操作列 render 函数的代码，以便全面统一操作列的实现方式。

#### Acceptance Criteria
1. WHEN 识别需要重构的代码 THEN 系统 SHALL 找出直接在 columns 数组中定义操作列的代码
2. WHEN 识别需要重构的代码 THEN 系统 SHALL 找出使用 OperateRender 组件但未使用 genOperateColumn 的实现
3. WHEN 识别需要重构的代码 THEN 系统 SHALL 找出通过 useEffect 动态修改操作列 render 函数的代码
4. WHEN 重构完成后 THEN 系统 SHALL 确保所有识别出的代码都已使用 `genOperateColumn` 工具函数

### Requirement 4

**User Story:** 作为开发人员，我希望重构指定的目标文件，包括 `client/src/pages/config/pixalate-prebid/index.tsx`、`client/src/pages/demand/advertiser/index.tsx`、`client/src/pages/manage/user/index.tsx`、`client/src/pages/supply/publisher/index.tsx` 以及其他包含类似操作列逻辑的文件，以便完成重构任务。

#### Acceptance Criteria
1. WHEN 重构目标文件 THEN 系统 SHALL 重构 `client/src/pages/config/pixalate-prebid/index.tsx` 中的操作列逻辑
2. WHEN 重构目标文件 THEN 系统 SHALL 重构 `client/src/pages/demand/advertiser/index.tsx` 中的操作列逻辑
3. WHEN 重构目标文件 THEN 系统 SHALL 重构 `client/src/pages/manage/user/index.tsx` 中的操作列逻辑
4. WHEN 重构目标文件 THEN 系统 SHALL 重构 `client/src/pages/supply/publisher/index.tsx` 中的操作列逻辑
5. WHEN 重构目标文件 THEN 系统 SHALL 重构其他包含类似操作列逻辑的文件

### Requirement 5

**User Story:** 作为开发人员，我希望在重构后保持功能一致性，确保重构后的操作按钮功能与原实现完全相同，保留原有的权限控制（access属性），保持按钮的样式、图标和交互行为不变，以便不影响现有功能。

#### Acceptance Criteria
1. WHEN 重构操作列 THEN 系统 SHALL 确保操作按钮功能与原实现完全相同
2. WHEN 重构操作列 THEN 系统 SHALL 保留原有的权限控制（access属性）
3. WHEN 重构操作列 THEN 系统 SHALL 保持按钮的样式、图标和交互行为不变

### Requirement 6

**User Story:** 作为开发人员，我希望在重构时遵循代码规范，使用 `useMemo` 包装 columns 定义以优化性能，保持一致的命名约定和代码结构，移除重构后不再需要的 `useEffect` 和状态管理代码，以便提高代码质量和性能。

#### Acceptance Criteria
1. WHEN 重构操作列 THEN 系统 SHALL 使用 `useMemo` 包装 columns 定义以优化性能
2. WHEN 重构操作列 THEN 系统 SHALL 保持一致的命名约定和代码结构
3. WHEN 重构操作列 THEN 系统 SHALL 移除重构后不再需要的 `useEffect` 和状态管理代码
