# ATC白名单功能技术文档

## 1. 业务功能介绍

### 1.1 业务目的和核心功能

ATC（Auto Traffic Control）白名单功能是用于控制自动流量分配的核心机制，主要目的是确保只有符合白名单规则的流量在向上游送量时不受ATC策略控制。该功能通过设置白名单规则，确保特定条件的流量能够绕过ATC策略限制，直接进行交易。

核心功能包括：
- 确保符合白名单规则的流量不受ATC策略控制
- 控制特定发布商与广告商之间的流量交易
- 按地域、广告格式、广告尺寸等维度进行流量过滤
- 设置白名单规则的生效时间和过期时间
- 支持批量配置和单个配置

### 1.2 字段含义说明

| 字段名 | 含义 | 说明 |
|-------|------|------|
| tenant | 租户 | 系统中的租户标识，用于隔离不同客户的数据 |
| publisher | 发布商 | 流量的提供方，可以是特定发布商或所有发布商 |
| advertiser | 广告商 | 流量的需求方，可以指定特定广告商或不指定 |
| server region | 服务器区域 | 流量所在的服务器区域（USE、APSE、EUW） |
| country | 国家 | 流量来源的国家，支持多选 |
| ad format | 广告格式 | 广告的展示格式（Banner、Native、Video、Reward Video） |
| ad size | 广告尺寸 | 广告的具体尺寸规格 |
| bundle | 应用包名 | 移动应用的唯一标识符（Bundle ID） |

### 1.3 生效机制和过期时间管理

ATC白名单的生效机制基于数据库中的`stg_atc_white_list`表存储的规则。系统在处理流量时会根据当前请求的参数匹配白名单规则：

1. **匹配规则**：系统会根据请求中的发布商ID、广告商ID、租户ID和服务器区域进行匹配
2. **生效时间**：通过`expired`字段控制规则的过期时间（近7天），系统只匹配未过期的规则
3. **状态控制**：通过`status`字段控制规则的启用/禁用状态

## 2. 权限控制分析

### 2.1 权限控制逻辑

ATC功能的权限控制基于UMI框架的access机制，通过`client/src/access.ts`文件定义权限点，结合后端用户角色进行访问控制。

### 2.2 涉及的权限码

| 权限码 | 说明 | 访问位置 |
|--------|------|----------|
| ConfigATC | ATC配置菜单访问权限 | 菜单级别 |
| AddAtcWlCode | 新增ATC白名单权限 | 按钮级别 |
| EditConfigAtc | 编辑ATC配置权限 | 按钮级别 |

### 2.3 不同角色的访问权限

根据`client/src/constants/permission/role.tsx`中定义的角色类型：

1. **超级管理员（Super Administrator）**：拥有所有权限，可以无限制访问ATC功能
2. **管理员（Administrator）**：需要配置相应权限码才能访问
3. **操作员（Operator）**：需要配置相应权限码才能访问
4. **其他角色**：需要配置相应权限码才能访问

权限检查在前端通过`access.isButtonAccess()`方法进行，在后端通过用户会话进行验证。

## 3. 代码架构图

```mermaid
graph TD
    A --> C[API服务]
    A[前端页面] --> B[常量配置]
    C --> D[后端路由]
    D --> E[控制器]
    E --> F[服务层]
    F --> G[数据模型]
    G --> H[(数据库)]
    
    subgraph "前端"
        A --> A1[client/src/pages/config/atc-wl/index.tsx]
        A --> A2[client/src/pages/config/components/AddAtcWl/index.tsx]
        B --> B1[client/src/constants/config/atc.tsx]
        C --> C1[client/src/services/config/index.ts]
    end
    
    subgraph "后端"
        D --> D1[server/src/routers/api/config/atc-wl.ts]
        E --> E1[server/src/controllers/config/atc-wl.ts]
        F --> F1[server/src/services/config/atc-wl.ts]
        G --> G1[server/src/models/config/atc-wl.ts]
    end
    
    subgraph "数据库"
        H --> H1[stg_atc_white_list表]
    end
    
    style A1 fill:#000,stroke:#333
    style A2 fill:#000,stroke:#333
    style B1 fill:#000,stroke:#333
    style C1 fill:#000,stroke:#333
    style D1 fill:#000,stroke:#333
    style E1 fill:#000,stroke:#333
    style F1 fill:#000,stroke:#333
    style G1 fill:#000,stroke:#333
    style H1 fill:#000,stroke:#333
```

## 4. 文件职责分析

### 4.1 主页面文件
**文件路径**：`client/src/pages/config/atc-wl/index.tsx`
**职责**：
- 展示ATC白名单列表
- 提供新增和编辑功能入口
- 处理页面状态管理和数据获取
- 集成搜索和筛选功能

### 4.2 配置常量文件
**文件路径**：`client/src/constants/config/atc.tsx`
**职责**：
- 定义服务器区域选项
- 定义广告格式选项
- 生成表格列配置
- 生成搜索选项配置
- 提供默认表单数据

### 4.3 路由文件
**文件路径**：`server/src/routers/api/config/atc-wl.ts`
**职责**：
- 定义ATC白名单相关的API路由
- 映射HTTP请求到对应的控制器方法
- 设置路由前缀为`/atc`

### 4.4 服务层文件
**文件路径**：`server/src/services/config/atc-wl.ts`
**职责**：
- 实现业务逻辑的封装
- 调用数据模型进行数据操作
- 提供统一的服务接口给控制器使用

### 4.5 数据模型文件
**文件路径**：`server/src/models/config/atc-wl.ts`
**职责**：
- 直接操作数据库表`stg_atc_white_list`
- 实现数据的增删改查操作
- 处理SQL查询和结果返回

## 5. 核心逻辑伪代码

### 5.1 新增ATC白名单核心逻辑

```javascript
// 新增ATC白名单的核心逻辑
function addAtcWhitelist(options) {
  // 1. 参数验证
  validateParameters(options);
  
  // 2. 检查是否已存在相同记录
  const isExist = checkExistence(options);
  if (isExist) {
    return { success: false, message: "记录已存在" };
  }
  
  // 3. 数据预处理
  const processedData = preprocessData(options);
  
  // 4. 批量插入处理（支持多个发布商）
  const result = batchInsert(processedData);
  
  // 5. 记录操作日志
  logOperation("addAtc", options);
  
  return { success: result, message: result ? "新增成功" : "新增失败" };
}

// 数据预处理函数
function preprocessData(options) {
  return {
    seller_id: options.seller_id,
    buyer_id: options.buyer_id || 0,
    tnt_id: options.tnt_id,
    region: options.region,
    country: options.country.join(','),
    bundle: options.bundle.join(','),
    ad_format: options.ad_format.join(','),
    ad_size: options.ad_size.join(','),
    expired: convertToTimestamp(options.expired),
    status: StatusMap.Active,
    op_id: options.op_id
  };
}

// 批量插入处理
function batchInsert(data) {
  const values = data.seller_id.map(sid => [
    sid,
    data.buyer_id,
    data.tnt_id,
    data.region,
    data.country,
    data.bundle,
    data.ad_format,
    data.ad_size,
    data.expired,
    data.status,
    data.op_id
  ]);
  
  const sql = "INSERT INTO stg_atc_white_list(seller_id, buyer_id, tnt_id, region, country, bundle, ad_format, ad_size, expired, status, op_id) VALUES ?";
  return executeQuery(sql, [values]);
}
```

### 5.2 编辑ATC白名单核心逻辑

```javascript
// 编辑ATC白名单的核心逻辑
function updateAtcWhitelist(options) {
  // 1. 参数验证
  validateParameters(options);
  
  // 2. 数据预处理
  const processedData = preprocessUpdateData(options);
  
  // 3. 更新数据库记录
  const result = updateRecord(processedData);
  
  // 4. 记录操作日志
  logOperation("updateAtc", options);
  
  return { success: result, message: result ? "更新成功" : "更新失败" };
}

// 数据预处理函数
function preprocessUpdateData(options) {
  return {
    id: options.id,
    country: options.country.join(','),
    ad_size: options.ad_size.join(','),
    ad_format: options.ad_format.join(','),
    bundle: options.bundle.join(','),
    expired: convertToTimestamp(options.expired),
    status: options.status,
    op_id: options.op_id,
    tnt_id: options.tnt_id
  };
}

// 更新数据库记录
function updateRecord(data) {
  const updateFields = buildSQLSetClause([
    ['country', data.country],
    ['ad_size', data.ad_size],
    ['ad_format', data.ad_format],
    ['bundle', data.bundle],
    ['expired', data.expired],
    ['status', data.status],
    ['op_id', data.op_id]
  ]);
  
  const sql = "UPDATE stg_atc_white_list SET ? WHERE id=? AND tnt_id=?";
  return executeQuery(sql, [updateFields, data.id, data.tnt_id]);
}
```

### 5.3 查询ATC白名单列表核心逻辑

```javascript
// 查询ATC白名单列表的核心逻辑
function getAtcWhitelist() {
  // 1. 构造SQL查询语句
  const sql = `
    SELECT
      st.id as id,
      st.expired as expired,
      st.status as status,
      st.update_time as update_time,
      st.tnt_id as tnt_id,
      st.seller_id as seller_id,
      st.buyer_id as buyer_id,
      st.region as region,
      st.country as country,
      st.bundle as bundle,
      st.ad_format as ad_format,
      st.ad_size as ad_size,
      ad.account_name as op_name,
      s.seller_name as seller_name,
      b.buyer_name as buyer_name,
      t.tnt_name as tnt_name
    FROM stg_atc_white_list as st
    LEFT JOIN admin as ad ON ad.admin_id=st.op_id
    LEFT JOIN seller as s ON s.seller_id=st.seller_id
    LEFT JOIN buyer as b ON b.buyer_id=st.buyer_id
    LEFT JOIN tenant as t ON t.tnt_id=st.tnt_id
    ORDER BY st.update_time DESC
  `;
  
  // 2. 执行查询
  const result = executeQuery(sql);
  
  // 3. 返回结果
  return result;
}
```
