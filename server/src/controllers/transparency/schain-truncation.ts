import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import { schainTruncationService } from '@/services';
import { TransparencyAPI } from '@/types/transparency';
import { getCtxResult } from '@/utils/response';
import { Context } from 'koa';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class SchainTruncationController implements TransparencyAPI.SchainTruncationController {
  async getSchainTruncationList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body || {};
    const data = await schainTruncationService.getSchainTruncationList(formData);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    getLogger('app').info(`getSchainTruncationList params=[${JSON.stringify(formData)}]`);

    ctx.body = result;
  }

  async updateSchainTruncationConfig(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body || {};
    const isSuccess = await schainTruncationService.updateSchainTruncationConfig(formData);
    if (isSuccess) {
      result = getCtxResult('SUCCESS', { app_id: formData.app_id });
    }
    getLogger('app').info(`updateSchainTruncationConfig params=[${JSON.stringify(formData)}]`);

    ctx.body = result;
  }
}

export const schainTruncationCtrl = new SchainTruncationController();
