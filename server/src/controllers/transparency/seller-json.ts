/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-01-25 11:25:16
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-25 19:28:14
 * @Description:
 */
import { Context } from 'koa';
import type { File } from 'formidable';
import { TransparencyAPI } from '@/types/transparency';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { transparencyService } from '@/services';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class TransparencyCtrl implements TransparencyAPI.SellerJsonCtrl {
  async upload(ctx: Context) {
    let result = { ...RESULT };
    const { file } = ctx.request.files!;
    const data = await transparencyService.upload(file as File);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }

    ctx.body = result;
  }
}

export const transparencyCtrl = new TransparencyCtrl();
