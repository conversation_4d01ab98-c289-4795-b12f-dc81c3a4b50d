/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-19 18:48:01
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-19 19:17:17
 * @Description:
 */
import { Context } from 'koa';
import { transparencyAppInfoService } from '@/services';
import { getLogger } from '@/config/log4js';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class TransparencyAppInfoCtrl {
  async getTransparencyAppInfo(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body || {};
    const data = await transparencyAppInfoService.getTransparencyAppInfo(formData);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    getLogger('app').info(`getAppInfo params=[${JSON.stringify(formData)}]`);

    ctx.body = result;
  }

  async updateTransparencyAppInfo(ctx: Context) {
    let result = { ...RESULT };
    const { multi_aat_domain, developer_id } = ctx.request.body || {};
    const data = await transparencyAppInfoService.updateTransparencyAppInfo({
      multi_aat_domain,
      developer_id,
    });
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    getLogger('app').info(`updateTransparencyAppInfo params=[${JSON.stringify(ctx.request.body)}]`);
    ctx.body = result;
  }

  async updateTransparencyAppInfoTag(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body || {};
    if (typeof formData.app_id !== 'number' || typeof formData.update_tag !== 'number') {
      throw new Error('app_id and update_tag are required');
    }

    const isSuccess = await transparencyAppInfoService.updateTransparencyAppInfoTag(formData);
    if (!isSuccess) {
      throw new Error('update failed');
    }
    result = getCtxResult('SUCCESS', { app_id: formData.app_id });
    getLogger('app').info(`updateTransparencyAppInfoTag params=[${JSON.stringify(formData)}]`);
    ctx.body = result;
  }
}

export const transparencyAppInfoCtrlCtrl = new TransparencyAppInfoCtrl();
