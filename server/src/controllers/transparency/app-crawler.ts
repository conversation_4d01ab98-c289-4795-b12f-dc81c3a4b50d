import { Context } from 'koa';
import { transparencyAppCrawlerService } from '@/services';
import { getLogger } from '@/config/log4js';
import { getCtxResult } from '@/utils/response';

class TransparencyAppCrawlerCtrl {
  async batchCreateAppCrawler(ctx: Context) {
    // 获取操作人
    const { account_name } = ctx.state || {};
    const formData = ctx.request.body || {};

    // 添加操作人信息
    const options = {
      ...formData,
      op_user: account_name || 'system'
    };

    const success = await transparencyAppCrawlerService.batchCreateAppCrawler(options);

    if (success) {
      ctx.body = getCtxResult('SUCCESS', success);
    } else {
      ctx.body = getCtxResult('ERROR_SYS', {});
    }
  }

  async updateAppCrawler(ctx: Context) {
    // 操作人获取
    const { account_name } = ctx.state || {};
    const options = {
      ...(ctx.request.body || {}),
      op_user: account_name
    };
    let success: boolean = false;
    if (options.app_id) {
      // 编辑逻辑
      success = await transparencyAppCrawlerService.updateAppCrawler(options);
    } else {
      // 新增逻辑
      success = await transparencyAppCrawlerService.createAppCrawler(options);
    }
    getLogger('app').info(`updateTransparencyAppCrawler params=[${JSON.stringify(options)}]`);
    if (success) {
      ctx.body = getCtxResult('SUCCESS', success);
    } else {
      ctx.body = getCtxResult('ERROR_SYS', {});
    }
  }

  async getAppCrawlerList(ctx: Context) {
    const formData = ctx.request.body || {};
    const data = await transparencyAppCrawlerService.getAppCrawlerList(formData);
    getLogger('app').info(`getAppCrawlerList params=[${JSON.stringify(formData)}]`);

    if (data) {
      ctx.body = getCtxResult('SUCCESS', data);
    } else {
      ctx.body = getCtxResult('ERROR_SYS', []);
    }
  }
}

export const transparencyAppCrawlerCtrl = new TransparencyAppCrawlerCtrl();
