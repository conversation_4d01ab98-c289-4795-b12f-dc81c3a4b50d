/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-08 15:25:24
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-08 15:27:06
 * @Description:
 */

import { Context } from 'koa';
import { SentMsgAPI } from '@/types/ai-board';
import { sentMsgService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};
class SentMsgCtrl implements SentMsgAPI.SentMsgCtrl {
  async getAllSentMessage(ctx: Context) {
    let result = RESULT;
    const data = await sentMsgService.getAllSentMessage();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const sentMsgCtrl = new SentMsgCtrl();
