/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:45:47
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-27 17:42:39
 * @Description:
 */

import { Context } from 'koa';
import { boardService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { BoardAPI } from '@/types/ai-board';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class BoardCtrl {
  // 获取总体指标(revenue, profit, request, impression) 用于main-board顶部数据展示
  async getOverview(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const data = await boardService.getOverview(formData, {
      tag: api_url
    });
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getTopCountry(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const { cur_hour, cache_cur_hour } = ctx.request.body;
    const data = await boardService.getTopCountry(cur_hour || cache_cur_hour, {
      tag: api_url
    });
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getSevenDaysCountry(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const { cur_hour, cache_cur_hour } = ctx.request.body;
    const data = await boardService.getSevenDaysCountry(cur_hour || cache_cur_hour, {
      tag: api_url
    });
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getTopAdFormat(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const { cur_hour, cache_cur_hour } = ctx.request.body;
    const data = await boardService.getTopAdFormat(cur_hour || cache_cur_hour, {
      tag: api_url
    });
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getAllTntRevenue(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const { cur_hour, cache_cur_hour } = ctx.request.body;
    const data = await boardService.getAllTntRevenue(cur_hour || cache_cur_hour, {
      tag: api_url
    });
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getTopCountryEcpmAndEcpr(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const { cur_hour, cache_cur_hour } = ctx.request.body;
    const data = await boardService.getTopCountryEcpmAndEcpr(cur_hour || cache_cur_hour, {
      tag: api_url
    });
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const boardController = new BoardCtrl();
