import { getLogger } from '@/config/log4js';
import { RESULT } from '@/constants';
import { BeOpenServiceRegisterScheme } from '@/schema/backend-api/open-service';
import { openService } from '@/services/backend-api/open-service';
import { tenantService } from '@/services/tenant-setting/tenant';
import { getCtxResult } from '@/utils/response';
import { validateBeApiBody } from '@/utils/validate-params';
import { Context } from 'koa';

class OpenServiceCtrl {
  @validateBeApiBody(BeOpenServiceRegisterScheme, false)
  async registerUser(ctx: Context) {
    let result = { ...RESULT };

    const formData = ctx.request.body;

    try {
      // 判断是否存在 email
      const isExitEmail = await tenantService.isExitEmail({
        email: formData.email
      });
      if (isExitEmail) {
        result = getCtxResult('EMAIL_EXISTS');
        ctx.body = result;
        return;
      }

      const userData = await openService.registerUser(formData);
      result = getCtxResult('SUCCESS', userData);
    } catch (error: any) {
      getLogger('error').error(`registerUser error: ${error.message}`);
      if (error.message === 'ext_uid already exists') {
        result = getCtxResult('EXT_UID_EXISTS', 'ext_uid already exists');
      }
    }

    ctx.body = result;
  }
}

export const openServiceCtrl = new OpenServiceCtrl();
