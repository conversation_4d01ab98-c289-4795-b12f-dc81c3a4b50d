/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 14:21:12
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2024-01-12 11:38:20
 * @Description:
 */

import { Context } from 'koa';
import { menuService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { UpdateMenuSortSchema } from '@/schema/permission/menu';
import { validateParams } from '@/utils/validate-params';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class MenuController {
  async addMenu(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { pid, path } = options;
    let flag = !path || pid === undefined;
    if (path && pid !== undefined) {
      const repeat = await menuService.isMenuExist(options);
      flag = !repeat.length || !repeat;
    }
    if (flag) {
      const data = await menuService.addMenu(options);
      if (data) {
        getLogger('app').info(`addMenu success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('MENU_PATH_EXISTS', []);
    }
    ctx.body = result;
  }

  async updateMenu(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const repeat = await menuService.isMenuExist(options);
    if (!repeat.length || !repeat || repeat[0].id === options.id) {
      const data = await menuService.updateMenu(options);
      if (data) {
        getLogger('app').info(`updateMenu success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('MENU_PATH_EXISTS', []);
    }
    ctx.body = result;
  }

  async getAllMenu(ctx: Context) {
    let result = { ...RESULT };
    const data = await menuService.getAllMenu();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async deleteMenu(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { ids } = options;
    const data = await menuService.deleteMenu(ids);
    if (data) {
      getLogger('app').info(`deleteMenu success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateParams(UpdateMenuSortSchema)
  async updateMenuSort(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const data = await menuService.updateMenuSort(formData);
    if (data) {
      getLogger('app').info(`updateMenu success formData=[${JSON.stringify(formData)}]`);
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const menuController = new MenuController();
