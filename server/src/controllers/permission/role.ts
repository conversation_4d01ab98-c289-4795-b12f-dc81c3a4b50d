/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 15:34:16
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 17:10:40
 * @Description:
 */
import { Context } from 'koa';
import { roleService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};
class RoleController {
  async addRole(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const repeat = await roleService.isRoleExist(options.role_name);
    if (!repeat || !repeat.length) {
      const data = await roleService.addRole(options, ctx.session!.admin_id);
      if (data) {
        getLogger('app').info(`addRole success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('ROLE_NAME_EXISTS', repeat);
    }
    ctx.body = result;
  }

  async editRole(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { tnt_id } = ctx.session!;
    options.tnt_id = tnt_id;
    const data = await roleService.editRole(options);
    if (data) {
      getLogger('app').info(`updateRole success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async updateRole(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const data = await roleService.updateRole(options);
    if (data) {
      getLogger('app').info(`updateRole success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getAllRoleAndPms(ctx: Context) {
    let result = { ...RESULT };
    const data = await roleService.getAllRoleAndPms();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const roleController = new RoleController();
