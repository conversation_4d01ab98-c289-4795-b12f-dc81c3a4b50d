/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 16:59:02
 * @LastEditors: chen<PERSON>dan <EMAIL>
 * @LastEditTime: 2023-08-08 17:10:28
 * @Description:
 */

import { Context } from 'koa';
import { interfaceService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class InterfaceController {
  async addInterface(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const repeat = await interfaceService.isRoutePathExist(options);
    if (!repeat || !repeat.length) {
      const data = await interfaceService.addInterface(options);
      if (data) {
        getLogger('app').info(`addInterface success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('ROUTE_PATH_EXISTS', repeat);
    }
    ctx.body = result;
  }

  async updateInterface(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const repeat = await interfaceService.isRoutePathExist(options);

    if (!repeat.length || !repeat || +repeat[0].id === +options.id) {
      const data = await interfaceService.updateInterface(options);
      if (data) {
        getLogger('app').info(`updateInterface success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('ROUTE_PATH_EXISTS', repeat);
    }
    ctx.body = result;
  }

  async getAllInterface(ctx: Context) {
    let result = { ...RESULT };
    const data = await interfaceService.getAllInterface();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async deleteInterface(ctx: Context) {
    let result = { ...RESULT };
    const { id } = ctx.request.body;
    const data = await interfaceService.deleteInterface(id);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const interfaceController = new InterfaceController();
