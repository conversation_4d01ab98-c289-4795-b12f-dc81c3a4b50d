/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-11 15:28:42
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-28 15:20:31
 * @Description:
 */

import { Context } from 'koa';
import { partnerService } from '@/services';

import { RESULT } from '@/constants';
import { getCtxResult } from '@/utils/response';

class PartnerCtrl {
  async getPartnerList(ctx: Context) {
    let result = { ...RESULT };
    const data = await partnerService.getPartnerList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const partnerCtrl = new PartnerCtrl();
