/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-19 18:48:01
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-19 19:17:17
 * @Description:
 */
import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import { GetAppInfoSchema } from '@/schema/app-ads';
import { appInfoService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateParams } from '@/utils/validate-params';
import { Context } from 'koa';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class AppInfoCtrl {
  @validateParams(GetAppInfoSchema)
  async getAppInfo(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body || {};
    const data = await appInfoService.getAppInfo(formData);
    getLogger('app').info(`getAppInfo params=[${JSON.stringify(formData)}]`);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const appInfoCtrl = new AppInfoCtrl();
