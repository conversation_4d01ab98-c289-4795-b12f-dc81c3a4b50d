/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:41:35
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-28 11:26:05
 * @Description:
 */
import { Context } from 'koa';
import { getLogger } from '@/config/log4js';
import { tenantService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { tenantModel } from '@/models';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class TenantCtrl {
  async getTenantList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id } = ctx.session!;
    formData.tnt_id = tnt_id;
    try {
      const data = await tenantService.getTenantList();
      result = getCtxResult('SUCCESS', data);
    } catch (error) {
      result = getCtxResult('ERROR_SYS');
      getLogger('error').error(`getTenantList error ${error}`);
    }

    ctx.body = result;
  }

  async addTenant(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.op_id = ctx.session!.admin_id;
    const isExitTenant = await tenantService.isExitTenant(formData);

    if (isExitTenant) {
      result = getCtxResult('TENANT_NAME_EXISTS');
      ctx.body = result;
    } else {
      const data = await tenantService.addTenant(formData);
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(`addTenant success formData=[${JSON.stringify(formData)}]`);
    }
    ctx.body = result;
  }

  async editTenant(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    try {
      const isExitTenant = await tenantService.isExitTenant(formData, formData.tnt_id);

      if (isExitTenant) {
        result = getCtxResult('TENANT_NAME_EXISTS');
        ctx.body = result;
      } else {
        const data = await tenantService.editTenant(formData);
        result = getCtxResult('SUCCESS', data);
        getLogger('app').info(`editTenant success formData=[${JSON.stringify(formData)}]`);
      }
    } catch (error) {
      result = getCtxResult('ERROR_SYS');
      getLogger('error').error(`addTenant error ${error}`);
    }

    ctx.body = result;
  }

  async deleteTenant(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const data = await tenantService.deleteTenant(formData);
    result = getCtxResult('SUCCESS', data);
    getLogger('app').info(`deleteTenant success formData=[${JSON.stringify(formData)}]`);

    ctx.body = result;
  }
}

export const tntController = new TenantCtrl();
