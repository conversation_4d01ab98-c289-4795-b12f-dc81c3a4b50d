/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:41:38
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-22 11:46:02
 * @Description:
 */
import { Context } from 'koa';
import { getLogger } from '@/config/log4js';
import { tntUserService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { validateParams } from '@/utils/validate-params';
import { AddUserScheme, EditUserScheme } from '@/schema/common';
import { md5, getConfig } from '@/utils';

const { passwordConfig } = getConfig();
const { platform_prefix } = passwordConfig;

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class UserCtrl {
  @validateParams(AddUserScheme)
  async addOneUser(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.password = md5(platform_prefix + formData.password);
    const data = await tntUserService.isAccountNameExists(formData, false);
    const UserCount = await tntUserService.getUserListCount(formData.tnt_id);
    if (data && data.length) {
      result = getCtxResult('ACCOUNT_NAME_EXISTS');
    } else if (UserCount >= 1000) {
      result = getCtxResult('USER_COUNT_LIMIT');
    } else {
      await tntUserService.addOneUser(formData);
      result = getCtxResult('SUCCESS');
      getLogger('app').info(`addUser success formData=[${JSON.stringify(formData)}]`);
    }
    ctx.body = result;
  }

  async getUserList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { tnt_id } = ctx.session!;
    formData.tnt_id = tnt_id;
    try {
      const data = await tntUserService.getUserList(formData);
      result = getCtxResult('SUCCESS', data);
    } catch (error) {
      result = getCtxResult('ERROR_SYS');
      getLogger('error').error(`getUserList error ${error}`);
    }

    ctx.body = result;
  }

  @validateParams(EditUserScheme)
  async editUser(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;

    if (formData.new_password) {
      formData.new_password = md5(platform_prefix + formData.new_password);
    }
    const exited = await tntUserService.isAccountNameExists(formData, true);

    if (Array.isArray(exited) && exited.length) {
      result = getCtxResult('ACCOUNT_NAME_EXISTS');
      ctx.body = result;
      return;
    }
    const data = await tntUserService.editUser(formData);
    if (data) {
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(`editUser success formData=[${JSON.stringify(formData)}]`);
    } else {
      getLogger('error').error(`editUser error formData=[${JSON.stringify(formData)}]`);
    }

    ctx.body = result;
  }

  async sendEmail(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    try {
      await tntUserService.sendEmailToUser(formData);
      result = getCtxResult('SUCCESS');
      getLogger('app').info(`sendEmail success formData=[${JSON.stringify(formData)}]`);
    } catch (error) {
      result = getCtxResult('ERROR_SYS');
    }
    ctx.body = result;
  }
}

export const tntUserController = new UserCtrl();
