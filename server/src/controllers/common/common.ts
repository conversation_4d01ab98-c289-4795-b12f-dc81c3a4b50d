/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:45:47
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 14:33:43
 * @Description:
 */

import { Code, Message } from '@/codes';
import { commonService } from '@/services';
import { CommonAPI } from '@/types/common';
import { getCtxResult } from '@/utils/response';
import { Context } from 'koa';

const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class CommonCtrl implements CommonAPI.CommonCtrl {
  async getBuyerIntegrationType(ctx: Context) {
    let result = { ...RESULT };
    const data = await commonService.getBuyerIntegrationType();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getSellerIntegrationType(ctx: Context) {
    let result = { ...RESULT };
    const data = await commonService.getSellerIntegrationType();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  /**
   * 获取字典数据
   * @param ctx
   */
  async getDict(ctx: Context) {
    const { dict_type } = (ctx.request.query || {}) as { dict_type: string };
    const data = await commonService.getDict(dict_type);
    ctx.body = getCtxResult('SUCCESS', data);
  }
}

export const commonCtrl = new CommonCtrl();
