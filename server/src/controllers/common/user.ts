/**
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @file:
 * @Date: 2022-12-02 11:02:49
 */
import uid from 'uid-safe';
import { Context } from 'koa';
import { getLogger } from '@/config/log4js';
import { userService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { UserAPI } from '@/types/user';
import { validateParams } from '@/utils/validate-params';
import { sessionConfig } from '@/config/session';
import {
  LoginPasswordScheme,
  ResetPasswordScheme,
  ConfirmPasswordScheme,
  UpdateCompanyUserSchema,
  UpdateUserLinkSchema
} from '@/schema/common';
import { md5, getConfig } from '@/utils';
import { delRedisByKey, setRedisByKey } from '@/db/redis';

const { passwordConfig, redisConfig } = getConfig();
const { admin_prefix } = passwordConfig;

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

const UserKeys = ['admin_id', 'account_name', 'role', 'role_id', 'role_type', 'display_name'];

class UserCtrl {
  @validateParams(LoginPasswordScheme)
  async login(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { session } = ctx;
    const password = md5(admin_prefix + formData.password);
    const userResult = await userService.login({ ...formData, password });
    if (userResult && userResult.length) {
      const user = userResult[0];
      const key = md5(`${redisConfig.admin_key}_${user.admin_id}`);
      const session_id = uid.sync(24);
      const session_tmp: any = { session_id };
      UserKeys.forEach((k) => (session_tmp[k] = user[k as keyof UserAPI.AdminListItem]));
      session!.session_id = session_id;
      session!.admin_id = user.admin_id;
      session!.account_name = user.account_name;
      // 存session 直接更新 不用删除
      await setRedisByKey(key, session_tmp, sessionConfig.maxAge);
      result = getCtxResult('SUCCESS', { user_id: user.admin_id });
      getLogger('app').info(`login, session is ${session!.session_id}`);
    } else {
      result = getCtxResult('FAIL_USER_NAME_OR_PASSWORD_ERROR');
    }
    ctx.body = result;
  }

  async logout(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id } = ctx.session!;
    const key = md5(`${redisConfig.admin_key}_${admin_id}`);
    // 全部登录账号都退出
    await delRedisByKey(key);
    ctx.session = null;
    result = getCtxResult('SUCCESS', {});
    ctx.body = result;
  }

  @validateParams(ResetPasswordScheme)
  async resetPasswordByUserId(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { admin_id } = ctx.session!;
    const key = md5(`${redisConfig.admin_key}_${admin_id}`);
    const data: any = ctx.state;
    formData.admin_id = admin_id;
    formData.account_name = data.account_name;
    formData.old_password = md5(passwordConfig.admin_prefix + formData.old_password);
    formData.new_password = md5(passwordConfig.admin_prefix + formData.new_password);
    const tmp = await userService.confirmPassword(formData);
    if (Array.isArray(tmp) && tmp.length > 0) {
      const userResult = await userService.resetPasswordByUserId(formData);
      if (userResult) {
        result = getCtxResult('SUCCESS');
        // 删除对应的redis
        await delRedisByKey(key);
        getLogger('app').info(`resetPasswordByUserId success formData=[${JSON.stringify(formData)}]`);
      }
    } else {
      result = getCtxResult('CURRENT_PASSWORD_ERROR');
    }
    ctx.body = result;
  }

  @validateParams(ConfirmPasswordScheme)
  async confirmPassword(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.admin_id = ctx.session!.admin_id;
    const { old_password } = formData;
    const data = await userService.confirmPassword(formData);

    if (Array.isArray(data) && data.length > 0) {
      const { password } = data[0];
      if (md5(passwordConfig.admin_prefix + old_password) === password) {
        result = getCtxResult('SUCCESS', true);
      } else {
        result = getCtxResult('CURRENT_PASSWORD_ERROR');
      }
    }
    ctx.body = result;
  }

  async currentUser(ctx: Context) {
    let result = { ...RESULT };
    const res: any = ctx.state;
    const data: any = {};
    UserKeys.forEach((v) => {
      data[v] = res[v];
    });
    data.menu_access = res.menu_access || [];
    data.btn_access = res.btn_access || [];
    data.role_type = res.role_type;
    result = getCtxResult('SUCCESS', data);
    ctx.body = result;
  }

  @validateParams(UpdateCompanyUserSchema)
  async updateCompanyUser(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { display_name } = formData;
    formData.admin_id = ctx.session!.admin_id;
    const flag = await userService.updateCompanyUser(formData);
    if (flag) {
      result = getCtxResult('SUCCESS');
      const key = md5(`${redisConfig.admin_key}_${ctx.session!.admin_id}`);
      const data = ctx.state;
      const tmp = { ...data, display_name };
      await setRedisByKey(key, tmp);
      getLogger('app').info(`updateCompanyUser success formData=[${formData}]`);
    }
    ctx.body = result;
  }

  async validPassword(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.admin_id = ctx.session!.admin_id;
    formData.password = md5(passwordConfig.admin_prefix + formData.password);
    const data = await userService.validPassword(formData);
    if (Array.isArray(data) && data.length) {
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(`validPassword success formData=[${JSON.stringify(formData)}]`);
    } else {
      result = getCtxResult('CURRENT_PASSWORD_ERROR');
    }
    ctx.body = result;
  }

  async getAllUserLinkList(ctx: Context) {
    let result = { ...RESULT };
    const data = await userService.getAllUserLinkList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
      getLogger('app').info(`getAllUserLinkList success formData=[${JSON.stringify(ctx.request.body)}]`);
    }
    ctx.body = result;
  }

  @validateParams(UpdateUserLinkSchema)
  async updateUserLink(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { admin_id } = ctx.state;
    const isSuccess = await userService.updateUserLink({ ...formData, admin_id });
    if (isSuccess) {
      // 删除当前账号的 session
      const key = md5(`${redisConfig.platform_key}_${formData.special_user_id}_1083`);
      await delRedisByKey(key);
      result = getCtxResult('SUCCESS');
    }
    ctx.body = result;
  }
}

export const userController = new UserCtrl();
