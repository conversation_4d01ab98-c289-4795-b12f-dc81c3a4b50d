/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-05 11:50:05
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 14:28:32
 * @FilePath: /saas.rix-platform/server-ts/src/controllers/supply/supply.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Context } from 'koa';
import { supplyService } from '@/services';
import { SupplyAPI } from '@/types/supply';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class SupplyCtrl implements SupplyAPI.SupplyCtrlInterface {
  async getSupplyList(ctx: Context) {
    let result = { ...RESULT };
    const list = await supplyService.getSupplyList();
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  async updateSupply(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const list = await supplyService.updateSupply(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(`updateSupply success formData=[${JSON.stringify(formData)}]`);
    }
    ctx.body = result;
  }

  async getSupplyAuth(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const list = await supplyService.getSupplyAuth(formData.seller_id, formData.tnt_id);
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  /**
   * @description 前端未使用
   * @param ctx
   */
  async getSupplyEndpoint(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    formData.tnt_id = ctx.session!.tnt_id;
    const list = await supplyService.getSupplyEndpoint(formData.tnt_id, formData.seller_id);

    if (list) {
      result = getCtxResult('SUCCESS', list[0]);
      getLogger('app').info(`getSupplyEndpoint success formData=[${JSON.stringify(formData)}]`);
    }
    ctx.body = result;
  }
}

export const supplyCtrl = new SupplyCtrl();
