import { Code, Message } from '@/codes';
import { tenantPixalateService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { Context } from 'koa';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

// 开放Pixalate PostBid相关内容配置，包括ADM TAG、Report API。

// 平台存储配置字段内容参考如下：
// display_web_tag(string)      ->  Desktop/Mobile Web Display 1x1
// display_app_tag(string)      ->  Mobile InApp 1x1
// display_web_js(string)       ->  JS Non video Desktop/Mobile Web
// display_app_js(string)       ->  JS Non video Mobile InApp
// native_web_tag(string)       ->  Web Native 1x1
// native_app_tag(string)       ->  InApp Native 1x1
// video_web_tag(string)        ->  Desktop/Mobile Web Video 1x1
// video_app_tag(string)        ->  Mobile InApp  Video /CTV Video 1x1

// report_api(string)           -> pixalate 提供的report API
// report_api_key(string)       -> pixalate 提供的report API Key

// # 表示是否使用rixengine 的pixalate，默认为1（true），如果使用rixengine的则不给单独配置
// use_rix_common(integer, 1 -> true, 2 -> false)
// 将上述配置信息保存为json字符串保存到 privatization 表。

class PixalateCtrl {
  async getTenantPixalateList(ctx: Context) {
    let result = { ...RESULT };
    const data = await tenantPixalateService.getTenantPixalateList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async addTenantPixalate(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;

    const isSuccess = await tenantPixalateService.addTenantPixalate(formData);
    if (isSuccess) {
      result = getCtxResult('SUCCESS', isSuccess);
    }
    ctx.body = result;
  }

  async editTenantPixalate(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;

    const isSuccess = await tenantPixalateService.editTenantPixalate(formData);
    if (isSuccess) {
      result = getCtxResult('SUCCESS', isSuccess);
    }
    ctx.body = result;
  }
}

export const pixalateCtrl = new PixalateCtrl();
