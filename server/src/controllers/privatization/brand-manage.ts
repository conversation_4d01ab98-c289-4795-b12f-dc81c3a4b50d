/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-30 10:57:08
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-30 19:09:16
 * @Description:
 */
import { Context } from 'koa';
import type { File } from 'formidable';
import { PrivateAPI } from '@/types/privatization';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { brandService } from '@/services';
import { branModel } from '@/models';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class BrandCtrl implements PrivateAPI.BrandController {
  async getTenantBrandList(ctx: Context) {
    let result = { ...RESULT };
    const data = await brandService.getTenantBrandList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async addTenantBrand(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const isExist = await branModel.isBrandExist(formData.tnt_id);
    if (isExist) {
      result = getCtxResult('BRAND_EXIST', '');
      ctx.body = result;
      return;
    }
    const data = await brandService.addTenantBrand(formData);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async editTenantBrand(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const data = await brandService.editTenantBrand(formData);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async upload(ctx: Context) {
    let result = { ...RESULT };
    const { file } = ctx.request.files!;
    const { type } = ctx.request.body;
    let gsPath = '';
    if (type === 'logo') {
      gsPath = await brandService.uploadLogo(file as File);
    } else {
      gsPath = await brandService.uploadFavicon(file as File);
    }
    const data = {
      path: gsPath
    };
    if (gsPath) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async download(ctx: Context) {
    const { type, path = '' } = ctx.request.query;
    if (!path) {
      ctx.body = getCtxResult('ERROR_SYS', '');
      return;
    }
    const stream = await brandService.downloadLogo({
      fileName: path as string
    });
    ctx.set('content-type', 'image/png');
    ctx.body = stream;
  }
}

export const brandCtrl = new BrandCtrl();
