/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-02-27 18:52:16
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 18:53:39
 * @Description:
 */
import { AppListAPI } from '@/types/app';
import { getCtxResult } from '@/utils/response';
const RESULT = getCtxResult('ERROR_SYS');
class AppCtrl implements AppListAPI.AppController {
  async getAppList(ctx: any) {
    let result = { ...RESULT };
    const data = await ctx.appService.getAppList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}
export const appController = new AppCtrl();
