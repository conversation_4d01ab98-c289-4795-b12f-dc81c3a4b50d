/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-31 23:27:47
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 10:07:04
 * @Description:
 */

import { Context } from 'koa';
import { RESULT } from '@/constants';
import { adminUserService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { AddUserSchema, EditUserSchema, ResetPasswordSchema } from '@/schema/admin-setting/user';
import { validateParams } from '@/utils/validate-params';
import { getLogger } from '@/config/log4js';

class UserController {
  @validateParams(AddUserSchema)
  async addUser(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const { account_name } = formData;
    const repeat = await adminUserService.isUserExist(account_name);
    if (!repeat || !repeat.length) {
      const userResult = await adminUserService.addOneUser(ctx.origin, formData);
      if (userResult) {
        getLogger('app').info(`addUser success formData=[${JSON.stringify(formData)}]`);
        result = getCtxResult('SUCCESS', []);
      }
    } else {
      result = getCtxResult('USER_NAME_EXISTS', []);
      getLogger('app').info(`addUser failed formData=[${JSON.stringify(formData)}]`);
    }
    ctx.body = result;
  }

  @validateParams(EditUserSchema)
  async updateUser(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const userResult = await adminUserService.updateOneUser(formData);
    if (userResult) {
      getLogger('app').info(`updateUser formData=[${JSON.stringify(formData)}]`);
      result = getCtxResult('SUCCESS');
    } else {
      result = getCtxResult('UPDATE_USER_FAILED', []);
    }
    ctx.body = result;
  }

  // 获取所有用户 包含角色 权限那使用
  async getAllUserList(ctx: Context) {
    let result = { ...RESULT };
    const userInfo = await adminUserService.getAllUserList();
    if (userInfo) {
      result = getCtxResult('SUCCESS', userInfo);
    }
    ctx.body = result;
  }

  @validateParams(ResetPasswordSchema)
  async resetPassword(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const userResult = await adminUserService.resetPassword(ctx.origin, formData);
    if (userResult) {
      getLogger('app').info(`resetPassword formData=[${JSON.stringify(formData)}]`);
      result = getCtxResult('SUCCESS');
    }
    ctx.body = result;
  }
}

export const adminUserCtrl = new UserController();
