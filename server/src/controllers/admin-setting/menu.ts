/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-20 17:46:28
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 14:40:49
 * @Description:
 */

import { Context } from 'koa';
import { adminMenuService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { RESULT } from '@/constants';
import { DeleteMenuSchema, AddMenuSchema, EditMenuSchema, UpdateMenuSortSchema } from '@/schema/admin-setting/menu';
import { validateParams } from '@/utils/validate-params';

class MenuController {
  @validateParams(AddMenuSchema)
  async addMenu(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const flag = await adminMenuService.isMenuExist(options);
    if (!flag) {
      const data = await adminMenuService.addMenu(options);
      if (data) {
        getLogger('app').info(`addAdminMenu success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('MENU_TITLE_EXISTS', []);
    }
    ctx.body = result;
  }

  @validateParams(EditMenuSchema)
  async updateMenu(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const repeat = await adminMenuService.isMenuExist(options);
    if (!repeat) {
      const data = await adminMenuService.updateMenu(options);
      if (data) {
        getLogger('app').info(`addAdminMenu success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('MENU_TITLE_EXISTS', []);
    }
    ctx.body = result;
  }

  async getAllMenu(ctx: Context) {
    let result = { ...RESULT };
    const data = await adminMenuService.getAllMenu();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateParams(UpdateMenuSortSchema)
  async updateMenuSort(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const data = await adminMenuService.updateMenuSort(formData);
    if (data) {
      getLogger('app').info(`updateAdminMenu success formData=[${JSON.stringify(formData)}]`);
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateParams(DeleteMenuSchema)
  async deleteMenu(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const data = await adminMenuService.deleteMenu(formData.id);
    if (data) {
      getLogger('app').info(`deleteAdminMenu success formData=[${JSON.stringify(formData)}]`);
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const adminMenuCtrl = new MenuController();
