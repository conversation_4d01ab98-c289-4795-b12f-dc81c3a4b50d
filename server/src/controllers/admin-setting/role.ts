/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-20 17:46:32
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-20 17:58:24
 * @Description:
 */
import { Context } from 'koa';
import { adminRoleService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { RESULT } from '@/constants';
import { EditRoleSchema, EditRolePms, AddRoleSchema } from '@/schema/admin-setting/role';
import { validateParams } from '@/utils/validate-params';
import { getLogger } from '@/config/log4js';

class RoleController {
  @validateParams(AddRoleSchema)
  async addRole(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { role_name } = options;
    const repeat = await adminRoleService.isRoleExist(role_name);
    if (!repeat) {
      const data = await adminRoleService.addRole(options);
      if (data) {
        getLogger('app').info(`addAdminRole success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('ROLE_NAME_EXISTS', repeat);
    }
    ctx.body = result;
  }

  @validateParams(EditRoleSchema)
  async updateRole(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { id, role_name } = options;
    const repeat = await adminRoleService.isRoleExist(role_name, id);
    if (!repeat) {
      const data = await adminRoleService.updateRole(options);
      if (data) {
        getLogger('app').info(`updateAdminRole success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('ROLE_NAME_EXISTS', repeat);
    }
    ctx.body = result;
  }

  async getAllRole(ctx: Context) {
    let result = { ...RESULT };
    const data = await adminRoleService.getAllRole();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateParams(EditRolePms)
  async updateRolePms(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const data = await adminRoleService.updateRolePms(formData);
    if (data) {
      getLogger('app').info(`updateAdminRolePms success formData=[${JSON.stringify(formData)}]`);
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const adminRoleCtrl = new RoleController();
