/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 17:29:41
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-29 15:57:57
 * @Description:
 */

import { Context } from 'koa';
import { monthlyBillingService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxBackResult } from '@/utils/response';

const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};
class MonthlyBillingCtrl {
  async getMonthlyBillingList(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const { tnt_status } = formData;

    let list = [];
    if (tnt_status === 2) {
      list = await monthlyBillingService.getPausedTenantMonthlyBillingList(formData, { tag: api_url });
    } else {
      list = await monthlyBillingService.getMonthlyBillingList(formData, { tag: api_url });
    }

    if (list) {
      result = getCtxBackResult('SUCCESS', list);
    }

    ctx.body = result;
  }
}

export const monthlyBillingController = new MonthlyBillingCtrl();
