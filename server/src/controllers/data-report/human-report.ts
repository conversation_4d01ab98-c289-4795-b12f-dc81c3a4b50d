/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-28 15:12:36
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 16:36:43
 * @Description:
 */
import { PassThrough } from 'stream';
import { Context } from 'koa';
// @ts-ignore
import JSONStream from 'JSONStream';
import { humanService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateParams } from '@/utils/validate-params';
import {
  HumanReportScheme,
  DownloadHumanReportSchema
} from '@/schema/report/human-report';
import { getLogger } from '@/config/log4js';

const RESULT = getCtxResult('ERROR_SYS');

class HumanController {
  @validateParams(HumanReportScheme)
  async getHumanReport(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const params = humanService.getReportParams(formData);
    const report = await humanService.getHumanReport(params, api_url);
    if (report) {
      result = getCtxResult('SUCCESS', report);
    }
    ctx.body = result;
  }

  @validateParams(DownloadHumanReportSchema)
  async downloadHumanReport(ctx: Context) {
    const result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const params = humanService.getReportParams(formData);
    const stream = await humanService.downloadHumanReport(params, api_url);
    if (stream) {
      ctx.body = stream
        .on('error', (err) => {
          getLogger('error').error(`downloadHumanReport error=[${err?.message}]`);
          stream.end();
          ctx.body = result;
        })
        .on('end', () => {
          stream.end();
        })
        .pipe(JSONStream.stringify())
        .pipe(new PassThrough());
    } else {
      ctx.body = result;
    }
  }
}
export const humanController = new HumanController();
