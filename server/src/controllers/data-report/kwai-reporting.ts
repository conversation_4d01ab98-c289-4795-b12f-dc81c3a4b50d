/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-10-20 15:28:25
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-19 19:21:08
 * @Description:
 */

import { PassThrough } from 'stream';
// @ts-ignore
import JSONStream from 'JSONStream';
import { Context } from 'koa';
import { kwaiReportService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';

const RESULT = getCtxResult('ERROR_SYS');

class KwaiReportController {
  async getKwaiReport(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const params = kwaiReportService.getReportParams(formData);
    const data = await kwaiReportService.getKwaiReport(params, api_url);
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async downloadKwaiReport(ctx: Context) {
    const result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const params = kwaiReportService.getReportParams(formData);
    const data = await kwaiReportService.downloadKwaiReport(params, api_url);

    if (data) {
      ctx.body = data
        .on('error', (err) => {
          getLogger('error').error(`downloadDashboardList error=[${err?.message}]`);
          data.end();
          ctx.body = result;
        })
        .on('end', () => {
          data.end();
        })
        .pipe(JSONStream.stringify())
        .pipe(new PassThrough());
    } else {
      ctx.body = result;
    }
  }
}

export const kwaiReportCtrl = new KwaiReportController();
