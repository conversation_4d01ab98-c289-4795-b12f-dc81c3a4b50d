/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-28 15:10:26
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 16:37:01
 * @Description:
 */

import { PassThrough } from 'stream';
import { Context } from 'koa';
// @ts-ignore
import JSONStream from 'JSONStream';
import { pixalateService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateParams } from '@/utils/validate-params';
import { PixalateReportScheme, DownloadPixalateReportSchema } from '@/schema/report/pixalate-report';
import { getLogger } from '@/config/log4js';

const RESULT = getCtxResult('ERROR_SYS');

class PixalateController {
  @validateParams(PixalateReportScheme)
  async getPixalateReport(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const params = pixalateService.getReportParams(formData);
    const report = await pixalateService.getPixalateReport(params, api_url);
    if (report) {
      result = getCtxResult('SUCCESS', report);
    }
    ctx.body = result;
  }

  @validateParams(DownloadPixalateReportSchema)
  async downloadPixalateReport(ctx: Context) {
    const result = { ...RESULT };
    const formData = ctx.request.body;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const params = pixalateService.getReportParams(formData);
    const stream = await pixalateService.downloadPixalateReport(params, api_url);
    if (stream) {
      ctx.body = stream
        .on('error', (err) => {
          getLogger('error').error(`downloadPixalateReport error=[${err?.message}]`);
          stream.end();
          ctx.body = result;
        })
        .on('end', () => {
          stream.end();
        })
        .pipe(JSONStream.stringify())
        .pipe(new PassThrough());
    } else {
      ctx.body = result;
    }
  }
}
export const pixalateController = new PixalateController();
