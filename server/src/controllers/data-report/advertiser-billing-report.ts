/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 17:29:41
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-29 15:57:57
 * @Description:
 */

import { Context } from 'koa';
import { advBillingService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxBackResult, getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';
import { notifyFeishu } from '@/utils';
const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};
class AdvBillingCtrl {
  async getAdvertiserBillingList(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const list = await advBillingService.getAdvertiserBillingList(formData, {
      tag: api_url
    });

    if (list) {
      result = getCtxBackResult('SUCCESS', list);
    }

    ctx.body = result;
  }

  async downloadAdvertiserBillingList(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const { admin_id } = ctx.session!;
    try {
      const data = await advBillingService.downloadAllReport(formData, admin_id, { tag: api_url });
      if (data && data.code === Code.ERROR_SYS) {
        result = getCtxResult('DOWNLOAD_ERROR');
      } else if (data && data.code === Code.SUCCESS) {
        result = getCtxResult('SUCCESS', data);
      }
    } catch (err: any) {
      getLogger('error').error(`downloadAllReport error=[${err?.message}]`);
      result = getCtxResult('DOWNLOAD_ERROR');
      const { admin_id = '', account_name = '', session_id = '' } = ctx.session!;
      const params = ctx.request.body || {};
      const ua = ctx.request.header['user-agent'] || '';
      const msg = `
      url=[${ctx.request.originalUrl}],
      origin=[${ctx.request.origin}],
      params=[${JSON.stringify(params, null, 2)}],
      error=[${err.message || err}],
      username=[${account_name}],
      admin_id=[${admin_id || 0}],
      session_id=[${session_id || ''},
      ua=[${ua}]`;
      notifyFeishu(`Saas Std Catch Error, env=[${process.env.NODE_ENV}]`, msg);
    } finally {
      ctx.body = result;
    }
  }
}

export const advBillingController = new AdvBillingCtrl();
