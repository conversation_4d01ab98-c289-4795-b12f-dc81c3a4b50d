/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 17:49:03
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-29 14:43:50
 * @Description:
 */
import { Code, Message } from '@/codes';
import { getLogger } from '@/config/log4js';
import { dashboardService } from '@/services';
import { notifyFeishu } from '@/utils';
import { getCtxBackResult, getCtxResult } from '@/utils/response';
import { Context } from 'koa';

const RESULT: any = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class FullReportCtrl {
  // @validateBody(DashboardScheme)
  async getDashboardList(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const list = await dashboardService.getDashboardList(formData, { tag: api_url });
    if (Array.isArray(list.data)) {
      result = getCtxBackResult('SUCCESS', list);
    } else if (typeof list === 'string') {
      result = getCtxBackResult('PARAMS_INVALID', list);
    }

    ctx.body = result;
  }

  async downloadAllReport(ctx: Context) {
    let result = { ...RESULT };
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const formData = ctx.request.body;
    const { admin_id } = ctx.session!;

    try {
      const data = await dashboardService.downloadAllReport(formData, admin_id, { tag: api_url });
      if (data && data.code === Code.ERROR_SYS) {
        result = getCtxResult('DOWNLOAD_ERROR');
        getLogger('app').error(`downloadAllReport error=[${data.message}]`);
      } else if (data && data.code === Code.SUCCESS) {
        result = getCtxResult('SUCCESS', data);
      }
    } catch (err: any) {
      getLogger('error').error(`downloadAllReport error=[${err?.message}]`);
      result = getCtxResult('DOWNLOAD_ERROR');
      const { user_id = '', account_name = '', session_id = '' } = ctx.session!;
      const params = ctx.request.body || {};
      const ua = ctx.request.header['user-agent'] || '';
      const msg = `
      url=[${ctx.request.originalUrl}],
      origin=[${ctx.request.origin}],
      params=[${JSON.stringify(params, null, 2)}],
      error=[${err.message || err}],
      username=[${account_name}],
      user_id=[${user_id || 0}],
      session_id=[${session_id || ''},
      ua=[${ua}]`;
      notifyFeishu(`Saas Std Catch Error, env=[${process.env.NODE_ENV}]`, msg);
    } finally {
      ctx.body = result;
    }
  }

  async getConfigQps(ctx: Context) {
    let result = { ...RESULT };
    const data = await dashboardService.getConfigQps(ctx.session!.tnt_id);
    if (Array.isArray(data)) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const fullReportCtrl = new FullReportCtrl();
