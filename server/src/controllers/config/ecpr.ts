/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-18 20:47:33
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 17:10:06
 * @Description:
 */

import { Context } from 'koa';
import { ecprService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { ConfigAPI } from '@/types/config';
import { getLogger } from '@/config/log4js';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class EcprController implements ConfigAPI.EcprController {
  async getEcprConfigList(ctx: Context) {
    let result = { ...RESULT };
    const data = await ecprService.getEcprConfigList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async addEcprConfig(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { count } = await ecprService.isEcprConfigExist(options);
    if (!count) {
      const data = await ecprService.addEcprConfig(options);
      if (data) {
        getLogger('app').info(`addEcprConfig success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS', data);
      }
    } else {
      result = getCtxResult('ECPR_CONFIG_EXISTS');
    }
    ctx.body = result;
  }

  async editEcprConfig(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const { uniKeyChange } = options;
    if (uniKeyChange) {
      const { count } = await ecprService.isEcprConfigExist(options);
      if (count) {
        result = getCtxResult('ECPR_CONFIG_EXISTS');
        ctx.body = result;
        return;
      }
    }
    const data = await ecprService.editEcprConfig(options);
    if (data) {
      getLogger('app').info(`editEcprConfig success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async deleteEcprConfig(ctx: Context) {
    let result = { ...RESULT };
    const options = ctx.request.body;
    const data = await ecprService.deleteEcprConfig(options);
    if (data) {
      getLogger('app').info(`deleteEcprConfig success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const ecprController = new EcprController();
