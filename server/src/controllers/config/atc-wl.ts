/*
 * @Author: ch<PERSON><PERSON><PERSON> chen<PERSON><EMAIL>
 * @Date: 2023-08-08 17:11:07
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-09-25 18:34:36
 * @Description:
 */

import { Context } from 'koa';
import { atcService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { validateParams } from '@/utils/validate-params';
import { AddAtcSchema, UpdateAtcSchema } from '@/schema/config';
import { getLogger } from '@/config/log4js';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

// 区分正式以及测试环境 校验权限
// const AuthUsers = ['xiezicong', 'xiaoxialan', 'lishuntao'];

class AtcController {
  @validateParams(AddAtcSchema, true)
  async addAtc(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    // if (!AuthUsers.includes(account_name)) {
    //   ctx.body = getCtxResult('FAIL_USER_NO_AUTH');
    //   return;
    // }

    const data = await atcService.addAtc(options);
    if (data) {
      getLogger('app').info(`addAtc success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS');
    }

    ctx.body = result;
  }

  @validateParams(UpdateAtcSchema, true)
  async updateAtc(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    // if (!AuthUsers.includes(account_name)) {
    //   ctx.body = getCtxResult('FAIL_USER_NO_AUTH');
    //   return;
    // }
    const data = await atcService.updateAtc(options);
    if (data) {
      getLogger('app').info(`updateAtc success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS');
    }

    ctx.body = result;
  }

  async getActList(ctx: Context) {
    let result = { ...RESULT };
    const data = await atcService.getActList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getSupplyList(ctx: Context) {
    let result = { ...RESULT };
    const data = await atcService.getSupplyList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async getDemandList(ctx: Context) {
    let result = { ...RESULT };
    const data = await atcService.getDemandList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const atcCtrl = new AtcController();
