/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 17:11:07
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-31 16:19:27
 * @Description:
 */

import { Context } from 'koa';
import { ivtService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { validateParams } from '@/utils/validate-params';
import { AddIvtConfigSchema, UpdateIvtConfigSchema } from '@/schema/config';
import { StatusDesc, StatusMap } from '@/constants';
import { getLogger } from '@/config/log4js';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

// 区分正式以及测试环境 校验权限
// const AuthUsers = ['xiezicong', 'xia<PERSON><PERSON>an', 'lishuntao'];

class IvtController {
  @validateParams(AddIvtConfigSchema)
  async addIvt(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    // if (!AuthUsers.includes(account_name)) {
    //   result = getCtxResult('FAIL_USER_NO_AUTH');
    // } else {
    // }
    const isExist = await ivtService.isIvtExist(options);
    if (Array.isArray(isExist) && isExist.length > 0) {
      const acticveIndex = isExist.findIndex(
        (item) => item.status === StatusMap.Active
      );
      if (acticveIndex !== -1) {
        const { status, id } = isExist[acticveIndex];
        result = getCtxResult(
          'PIXALATE_EXISTS',
          [],
          `New data is invalid because there is a more fine-grained dimension.
          The config ID is ${id}, status is ${StatusDesc[status]}, please edit it`
        );
      } else {
        const res = await ivtService.updateIvt(
          { ...options, status: StatusMap.Active },
          true
        );
        if (res) {
          getLogger('app').info(`addIvtConfig success formData=[${JSON.stringify(options)}]`);
          result = getCtxResult('UPDATE_PIXALATE');
        }
      }
    } else {
      const data = await ivtService.addIvt(options);
      if (data) {
        getLogger('app').info(`addIvtConfig success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS');
      }
    }

    ctx.body = result;
  }

  @validateParams(UpdateIvtConfigSchema)
  async updateIvt(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    // if (!AuthUsers.includes(account_name)) {
    //   result = getCtxResult('FAIL_USER_NO_AUTH');
    // } else {
    // }
    const isExist = await ivtService.isIvtExist(options);
    if (
      Array.isArray(isExist) &&
      isExist.length > 0 &&
      options.status === StatusMap.Active
    ) {
      const { status, id } = isExist[0];
      result = getCtxResult(
        'PIXALATE_EXISTS',
        [],
        `New data is invalid because there is a more fine-grained dimension.The config ID is ${id}, status is ${StatusDesc[status]},please edit it`
      );
    } else {
      const data = await ivtService.updateIvt(options);
      if (data) {
        getLogger('app').info(`updateIvt success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS');
      }
    }
    ctx.body = result;
  }

  async getIvtList(ctx: Context) {
    let result = { ...RESULT };
    const data = await ivtService.getIvtList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const ivtCtrl = new IvtController();
