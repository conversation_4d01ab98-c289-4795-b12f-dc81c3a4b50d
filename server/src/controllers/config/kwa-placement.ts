/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-15 15:49:56
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-09-25 18:35:37
 * @Description:
 */

import { Context } from 'koa';
import { kwaService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateParams } from '@/utils/validate-params';
import { AddKwaSchema, UpdateKwaSchema } from '@/schema/config';
import { getLogger } from '@/config/log4js';

const RESULT = getCtxResult('ERROR_SYS');

// 区分正式以及测试环境 校验权限
// const AuthUsers = ['xiezicong', 'xiaoxialan', 'lishuntao', 'xuyujing'];

class KwaController {
  @validateParams(AddKwaSchema)
  async addKwa(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    // if (!AuthUsers.includes(account_name)) {
    //   result = getCtxResult('FAIL_USER_NO_AUTH');
    // } else {
    // }
    const isExist = await kwaService.isExistKwa(options);
    if (isExist) {
      result = getCtxResult('KWA_EXISTS', []);
    } else {
      const data = await kwaService.addKwa(options);
      if (data) {
        getLogger('app').info(`addKwa success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS');
      }
    }

    ctx.body = result;
  }

  @validateParams(UpdateKwaSchema)
  async updateKwa(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    // if (!AuthUsers.includes(account_name)) {
    //   result = getCtxResult('FAIL_USER_NO_AUTH');
    // } else {
    // }
    const data = await kwaService.updateKwa(options);
    if (data) {
      getLogger('app').info(`updateKwa success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS');
    }

    ctx.body = result;
  }

  async getKwaList(ctx: Context) {
    let result = { ...RESULT };
    const data = await kwaService.getKwaList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const kwaCtrl = new KwaController();
