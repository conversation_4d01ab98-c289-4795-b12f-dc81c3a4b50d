import { Context } from 'koa';
import { customService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { validateParams } from '@/utils/validate-params';
import { AddCustomSchema, UpdateCustomSchema } from '@/schema/config';
import { getLogger } from '@/config/log4js';

const RESULT = getCtxResult('ERROR_SYS');

class CustomController {
  @validateParams(AddCustomSchema)
  async addCustom(ctx: Context) {
    const { admin_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    const list = await customService.getAppAndBundleList(options);
    const { app_id = '', bundle = '' } = options;
    const result = list.some((item: any) => {
      if (!item.bundle && !bundle) {
        ctx.body = getCtxResult('KWA_BUNDLE_EMPTY', []);
        return true;
      }
      if (!item.app_id && !app_id) {
        ctx.body = getCtxResult('KWA_APPID_EMPTY', []);
        return true;
      }
      if (item.bundle === bundle) {
        ctx.body = getCtxResult('KWA_BUNDLE_EXISTS', []);
        return true;
      }
      return false;
    });

    if (result) return;

    const data = await customService.addCustom(options);
    if (data) {
      getLogger('app').info(`addCustom success formData=[${JSON.stringify(options)}]`);
      ctx.body = getCtxResult('SUCCESS');
    } else {
      ctx.body = RESULT;
    }
  }

  @validateParams(UpdateCustomSchema)
  async updateCustom(ctx: Context) {
    const { admin_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;

    const list = await customService.getAppAndBundleList(options);
    const { app_id = '' } = options;
    const result = list.some((item: any) => {
      if (!item.app_id && !app_id) {
        ctx.body = getCtxResult('KWA_APPID_EMPTY', []);
        return true;
      }
      return false;
    });

    if (result) return;

    const data = await customService.updateCustom(options);
    if (data) {
      getLogger('app').info(`updateCustom success formData=[${JSON.stringify(options)}]`);
      ctx.body = getCtxResult('SUCCESS');
    } else {
      ctx.body = RESULT;
    }
  }

  async getCustomList(ctx: Context) {
    let result = { ...RESULT };
    const data = await customService.getCustomList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }
}

export const customCtrl = new CustomController();
