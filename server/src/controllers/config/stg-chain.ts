/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-19 10:52:53
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-31 14:21:45
 * @Description:
 */

import { Context } from 'koa';
import { stgChainService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { ConfigAPI } from '@/types/config';
import { validateParams } from '@/utils/validate-params';
import { AddStgSchema, UpdateStgSchema } from '@/schema/config';
import { getEscapeString } from '@/utils/params';
import { getLogger } from '@/config/log4js';
const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class StgChainController implements ConfigAPI.StgChainController {
  async getStgChainList(ctx: Context) {
    let result = { ...RESULT };
    const data = await stgChainService.getStgChainList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  @validateParams(AddStgSchema)
  async addStgChain(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    options.bundle = getEscapeString(options.bundle);
    const { count } = await stgChainService.isStgChainExist(options);
    if (count) {
      result = getCtxResult('STG_CHAIN_EXISTS', []);
    } else {
      const data = await stgChainService.addStgChain(options);
      if (data) {
        getLogger('app').info(`addStg success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS');
      }
    }

    ctx.body = result;
  }

  @validateParams(UpdateStgSchema)
  async updateStgChain(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    options.isEdit = true;
    options.bundle = getEscapeString(options.bundle);
    const { count } = await stgChainService.isStgChainExist(options);
    if (count) {
      result = getCtxResult('STG_CHAIN_EXISTS', []);
      ctx.body = result;
      return;
    }
    const data = await stgChainService.updateStgChain(options);
    if (data) {
      getLogger('app').info(`updateStg success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS');
    }

    ctx.body = result;
  }
}

export const stgController = new StgChainController();
