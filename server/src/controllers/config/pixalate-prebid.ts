/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-04-03 17:35:19
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-03 17:40:11
 * @Description:
 */

import { Context } from 'koa';
import { pixlPrebidService } from '@/services';
import { getCtxResult } from '@/utils/response';
import { ConfigAPI } from '@/types/config';
import { RESULT } from '@/constants';
import { getLogger } from '@/config/log4js';

class PixlPrebidController implements ConfigAPI.PixlPrebidController {
  async getPixlPrebidList(ctx: Context) {
    let result = { ...RESULT };
    const data = await pixlPrebidService.getPixlPrebidList();
    if (data) {
      result = getCtxResult('SUCCESS', data);
    }
    ctx.body = result;
  }

  async addPixlPrebid(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id, account_name } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    const { count } = await pixlPrebidService.isPixlPrebidExist(options);
    if (count) {
      result = getCtxResult('PIXALATE_EXISTS', []);
    } else {
      const data = await pixlPrebidService.addPixlPrebid(options);
      if (data) {
        getLogger('app').info(`addPixlPrebid success formData=[${JSON.stringify(options)}]`);
        result = getCtxResult('SUCCESS');
      }
    }

    ctx.body = result;
  }

  async updatePixlPrebid(ctx: Context) {
    let result = { ...RESULT };
    const { admin_id } = ctx.session!;
    const options = ctx.request.body;
    options.op_id = admin_id;
    options.isEdit = true;
    const { count } = await pixlPrebidService.isPixlPrebidExist(options);
    if (count) {
      result = getCtxResult('STG_CHAIN_EXISTS', []);
      ctx.body = result;
      return;
    }
    const data = await pixlPrebidService.updatePixlPrebid(options);
    if (data) {
      getLogger('app').info(`updatePixlPrebid success formData=[${JSON.stringify(options)}]`);
      result = getCtxResult('SUCCESS');
    }

    ctx.body = result;
  }
}

export const pixlPrebidController = new PixlPrebidController();
