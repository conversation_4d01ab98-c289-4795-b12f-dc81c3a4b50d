/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-05 11:50:05
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-27 19:43:21
 * @FilePath: /saas.rix-platform/server-ts/src/controllers/demand/demand.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Context } from 'koa';
import { demandService } from '@/services';
import { Code, Message } from '@/codes';
import { getCtxResult } from '@/utils/response';
import { getLogger } from '@/config/log4js';

const RESULT = {
  code: Code.ERROR_SYS,
  message: Message.ERROR_SYS,
  data: ''
};

class DemandCtrl {
  async getDemandList(ctx: Context) {
    let result = { ...RESULT };
    const list = await demandService.getDemandList();
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  async getDemandListTesting(ctx: Context) {
    let result = { ...RESULT };
    const list = await demandService.getDemandList(true);
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  async updateDemand(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const list = await demandService.updateDemand(formData);
    if (list) {
      result = getCtxResult('SUCCESS', list);
      getLogger('app').info(`updateDemand success formData=[${JSON.stringify(formData)}]`);
    }
    ctx.body = result;
  }

  async getDemandEndpoint(ctx: Context) {
    let result = { ...RESULT };
    const list = await demandService.getDemandEndpoint();
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }

  async getDemandAuth(ctx: Context) {
    let result = { ...RESULT };
    const formData = ctx.request.body;
    const list = await demandService.getDemandAuth(formData.buyer_id, formData.tnt_id);
    if (list) {
      result = getCtxResult('SUCCESS', list);
    }
    ctx.body = result;
  }
}

export const demandController = new DemandCtrl();
