/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 12:35:22
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-07 10:52:17
 * @Description:
 */

import { Context, Next } from 'koa';
import { getLogger } from '@/config/log4js';
import { notifyFeishu } from '@/utils';
import { getCtxResult } from '@/utils/response';
import storage from '@/db/localStorage';
// 捕获全局异常
export async function resolveError(ctx: Context, next: Next) {
  try {
    return await next();
  } catch (error: any) {
    console.log('resolveError', ctx.state);
    const { log_operator, basic_msg, ori_msg, params_msg } = ctx.state;
    const log_id = storage.getItem('log_id') || '';

    const msg = `
      log_id=[${log_id}],
      api=[${ctx.request.originalUrl}],
      pageUrl=[${ctx.request.header.referer}],
      operator=[${log_operator}],
      session_id=[${ctx.session!.session_id || ''}]
      params=[${JSON.stringify(ctx.request.body || {}, undefined, 2)}]
      error=[${error.message || error}],
      ua=[${ctx.request.headers['user-agent'] || ''}]
    `;

    // Saas Std Catch Error
    notifyFeishu(`Saas Admin Catch Error, env=[${process.env.NODE_ENV}]`, msg);
    const err_msg = `resolveError,${basic_msg},error=[${error.message || error}], ${ori_msg}, ${params_msg}`;
    getLogger('error').error(err_msg);
    getLogger('error').error(params_msg);
    const result = getCtxResult('ERROR_SYS', null);
    result.message = process.env.NODE_ENV === 'prod' ? result.message : error.message;
    ctx.body = result;
  }
}
