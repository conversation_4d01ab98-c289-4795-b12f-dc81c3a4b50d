// 接口对指定 ip 开放

import { getLogger } from '@/config/log4js';
import { getUserIp } from '@/utils';
import { getCtxResult } from '@/utils/response';
import { Context, Next } from 'koa';

// 测试：*************
// 生产：**************、***********、*************、**************
// 这是topon平台机器的出口IP，需要加上限制，只有IP白名单才可以访问open API，

// 办公室的IP，**************，测试环境和正式环境的IP白名单
const isDev = process.env.NODE_ENV === 'development';

// 新增一个中间件，检测 ip 是否在白名单中
const OpenServiceWhiteIpList = [
  '*************',
  '**************',
  '***********',
  '*************',
  '**************',
  '**************'
];

export const checkOpenServiceWhiteIp = async (ctx: Context, next: Next) => {
  if (isDev) {
    await next();
    return;
  }

  const ip = getUserIp(ctx);
  getLogger('app').info(`checkOpenServiceWhiteIp: ${ip}`);
  if (!OpenServiceWhiteIpList.includes(ip)) {
    ctx.body = getCtxResult('REQUEST_IP_INVALID', null);
    return;
  }
  await next();
};
