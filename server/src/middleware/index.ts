/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><EMAIL>
 * @Date: 2023-03-27 16:18:25
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-07 11:36:49
 * @Author: chen<PERSON>dan
 * @Date: 2023-12-22 10:34:26
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-27 10:18:28
 * @Description:
 */

import { Context, Next } from 'koa';

import { boardModel } from '@/models';
import { md5, getConfig } from '@/utils';

import { getLogger } from '@/config/log4js';
const { redisConfig } = getConfig();
const { platform_key: redis_ } = redisConfig;
// 验证是否登录 去掉拦截
import { getCtxResult } from '@/utils/response';
import { getRedisByKey, setRedisByKey } from '@/db/redis';
import { sessionConfig } from '@/config/session';

// 登出的正则
const IgnoreCookiePage = [/^\/api\/user\/logOut$/i];

// 开放接口
const AllowPage = [/^\/api\/user\/logIn$/i, /^\/api\/user\/logOut$/i];

// 验证是否登录 去掉拦截
export async function isLogin(ctx: Context, next: Next) {
  if (AllowPage.some((item) => item.test(ctx.originalUrl))) {
    return await next();
  }
  // session拦截
  if (!ctx.session || !ctx.session!.session_id) {
    ctx.session = null;
    const result = getCtxResult('FAIL_USER_NO_LOGIN');
    ctx.body = result;
  } else {
    return await next();
  }
}

export async function resolveCtxDefaultData(ctx: Context, next: Next) {
  const Domain = getConfig().domainConfig;
  const env = process.env.NODE_ENV || '';
  // skip if run in development
  if (!['prod', 'test'].includes(env)) {
    ctx.state.cs_domain = `allowed.${Domain.console}`;
  } else {
    ctx.state.cs_domain = ctx.request.host || '';
  }
  const { admin_id } = ctx.session!;
  const formData = ctx.request.body || {};
  formData.cur_admin_id = admin_id;
  ctx.request.body = formData;
  return await next();
}

export async function validSession(ctx: Context, next: Next) {
  if (AllowPage.some((item) => item.test(ctx.originalUrl))) {
    return await next();
  }
  const { admin_id } = ctx.session!;
  const key = md5(`${redisConfig.admin_key}_${admin_id}`);
  const data: any = await getRedisByKey(key);
  if (!data) {
    // 删除对应session
    ctx.session = null;
    const result = getCtxResult('FAIL_USER_NO_LOGIN');
    ctx.body = result;
  } else {
    await setRedisByKey(key, data, sessionConfig.maxAge);
    ctx.state = { ...ctx.state, ...data };
    return await next();
  }
}

// 刷新过期时间
export function refreshSessionExpired(ctx: Context, next: Next) {
  if (IgnoreCookiePage.some((v) => v.test(ctx.originalUrl))) {
    return next();
  }
  // 不是退出接口刷新登出时间
  const cookie = ctx.cookies.get(sessionConfig.key);
  // 更新cookie时间
  ctx.cookies.set(sessionConfig.key, cookie, {
    maxAge: sessionConfig.maxAge,
    signed: sessionConfig.signed
  });
  return next();
}

// 添加上角色的权限
export async function resolveCtxRole(ctx: Context, next: Next) {
  const data = ctx.state || {};
  if (data && data.role_id) {
    const key = md5(`${redisConfig.admin_key}_ROLE_${data.role_id}`);
    const tmp: any = (await getRedisByKey(key)) || {};
    const res = { ...data, ...tmp };
    ctx.state = res;
  }
  await next();
}

// 检查图表是否有缓存数据
export async function checkChartCache(ctx: Context, next: Next) {
  const cache_key = `${redis_}${md5(`RIXENGINE_REDIS_CACHE_bdLhLhz4wfQD_CUR_HOUR`)}`;
  const cache_data: any = await getRedisByKey(cache_key);
  console.log('checkChartCache', cache_key, cache_data ? '已缓存cur_hour' : '无缓存');
  if (cache_data) {
    ctx.request.body.cache_cur_hour = cache_data.cur_hour;
    getLogger('app').info(`from redis, cache cur_hour: ${cache_data.cur_hour}`);
  } else {
    const api_url = ctx.state.api_url || ctx.originalUrl.split('?')[0];
    const cur_hour = await boardModel.getTodayHours({ tag: api_url });
    await setRedisByKey(cache_key, { cur_hour }, 60 * 10);
    ctx.request.body.cur_hour = cur_hour;
  }
  await next();
}

// 日志拦截中间件 记录当前参数 增删改使用
export const resolveLogger = async (ctx: Context, next: Next) => {
  const formData = { ...ctx.request.body };
  // 原始数据
  const ori_data = typeof formData.ori_data === 'string' ? formData.ori_data : JSON.stringify(formData.ori_data || {});
  // 提交的参数
  const params = { ...formData };
  // 删除原始数据 不重复记录
  params.ori_data && delete params.ori_data;

  const { account_name = '', admin_id = 0 } = ctx.session!;
  // 操作人
  const operator = `${account_name}(${admin_id})`;

  // 调用接口的页面
  const pageUrl = ctx.request.header.referer || '';
  // 参数信息
  const basic_msg = `resolveLogger,ua=[${ctx.request.headers['user-agent'] || ''}],pageUrl=${pageUrl}`;
  const params_msg = `params=[${JSON.stringify(params)}]`;
  const ori_msg = `ori_data=[${ori_data}]`;
  getLogger('app').info(basic_msg);
  getLogger('app').info(params_msg);
  getLogger('app').info(ori_msg);
  // 统一日志信息 错误日志那可以打印 不用切文件查看
  ctx.state.log_operator = operator;
  ctx.state.basic_msg = basic_msg;
  ctx.state.params_msg = params_msg;
  ctx.state.ori_msg = ori_msg;
  await next();
};
