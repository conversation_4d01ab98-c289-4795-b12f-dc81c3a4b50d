/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:50:47
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-18 12:01:26
 * @Description:
 */
import Router from 'koa-router';
import { commonCtrl } from '@/controllers';
const router = new Router({ prefix: '/common' });

const routers = router
  .post('/getBuyerIntegrationType', commonCtrl.getBuyerIntegrationType)
  .post('/getSellerIntegrationType', commonCtrl.getSellerIntegrationType)
  .get('/getDict', commonCtrl.getDict);

export default routers;
