/*
 * @Author: chen<PERSON><PERSON>@algorix
 * @Date: 2022-12-02 10:08:33
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-27 11:46:30
 * @Description:
 */

import Router from 'koa-router';
import { userController } from '@/controllers';

const router = new Router({ prefix: '/user' });

const routers = router
  .post('/resetPassword', userController.resetPasswordByUserId)
  .post('/getCurrentUser', userController.currentUser)
  .post('/logIn', userController.login)
  .post('/logOut', userController.logout)
  .post('/validPassword', userController.validPassword)
  .post('/confirmPassword', userController.confirmPassword)
  .post('/updateUserLink', userController.updateUserLink)
  .post('/getAllUserLinkList', userController.getAllUserLinkList)
  .post('/updateCompanyUser', userController.updateCompanyUser);

export default routers;
