/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-27 18:54:23
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 18:54:24
 * @Description:
 */
import Router from 'koa-router';
import { appController } from '@/controllers';

const router = new Router({ prefix: '/app' });
const routers = router.post('/getAppList', appController.getAppList);

export default routers;
