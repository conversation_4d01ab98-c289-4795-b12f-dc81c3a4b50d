/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:51:00
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-22 11:52:05
 * @Description:
 */

import Router from 'koa-router';
import { tntController, tntUserController } from '@/controllers';

const router = new Router({ prefix: '/tnt' });

const routers = router
  .post('/addOneUser', tntUserController.addOneUser)
  .post('/getUserList', tntUserController.getUserList)
  .post('/editUser', tntUserController.editUser)
  .post('/sendEmail', tntUserController.sendEmail)
  .post('/getTenantList', tntController.getTenantList)
  .post('/addTenant', tntController.addTenant)
  .post('/editTenant', tntController.editTenant)
  .post('/deleteTenant', tntController.deleteTenant);
export default routers;
