/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-09-19 10:52:17
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-09-19 15:24:33
 * @Description:
 */
import Router from 'koa-router';
import { stgController } from '@/controllers';

// const { addInterface, updateInterface, getAllInterface, deleteInterface } =
//   interfaceController;
const router = new Router({ prefix: '/stg' });

const routers = router
  .post('/addStgChain', stgController.addStgChain)
  .post('/getStgChainList', stgController.getStgChainList)
  .post('/updateStgChain', stgController.updateStgChain);

export default routers;
