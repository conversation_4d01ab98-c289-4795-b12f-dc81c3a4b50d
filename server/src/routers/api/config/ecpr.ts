/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-18 20:46:37
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-05-19 16:17:38
 * @Description:
 */
import Router from 'koa-router';
import { ecprController } from '@/controllers';

// const { addInterface, updateInterface, getAllInterface, deleteInterface } =
//   interfaceController;
const router = new Router({ prefix: '/ecpr' });

const routers = router
  .post('/addEcprConfig', ecprController.addEcprConfig)
  .post('/getEcprConfigList', ecprController.getEcprConfigList)
  .post('/editEcprConfig', ecprController.editEcprConfig)
  .post('/deleteEcprConfig', ecprController.deleteEcprConfig);
export default routers;
