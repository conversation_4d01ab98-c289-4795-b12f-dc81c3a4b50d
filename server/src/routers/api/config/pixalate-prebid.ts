/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-04-03 16:54:21
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-03 17:43:33
 * @Description:
 */
import Router from 'koa-router';
import { pixlPrebidController } from '@/controllers';
const router = new Router({ prefix: '/pixalate-prebid' });

const routers = router
  .post('/addPixlPrebid', pixlPrebidController.addPixlPrebid)
  .post('/getPixlPrebidList', pixlPrebidController.getPixlPrebidList)
  .post('/updatePixlPrebid', pixlPrebidController.updatePixlPrebid);

export default routers;
