/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-15 16:01:20
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-15 16:13:28
 * @Description:
 */

import Router from 'koa-router';
import { kwaCtrl } from '@/controllers';

const { addKwa, updateKwa, getKwaList } = kwaCtrl;
const router = new Router({ prefix: '/kwa' });

const routers = router
  .post('/addKwa', addKwa)
  .post('/updateKwa', updateKwa)
  .post('/getKwaList', getKwaList);

export default routers;
