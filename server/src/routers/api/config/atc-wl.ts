/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 17:22:33
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 17:22:34
 * @Description:
 */
import Router from 'koa-router';
import { atcCtrl } from '@/controllers';

const { addAtc, updateAtc, getActList, getSupplyList, getDemandList } = atcCtrl;
const router = new Router({ prefix: '/atc' });

const routers = router
  .post('/addAtc', addAtc)
  .post('/updateAtc', updateAtc)
  .post('/getActList', getActList)
  .post('/getSupplyList', getSupplyList)
  .post('/getDemandList', getDemandList);

export default routers;
