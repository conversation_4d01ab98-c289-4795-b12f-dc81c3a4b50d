/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-19 18:51:53
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-19 18:51:56
 * @Description:
 */

import Router from 'koa-router';
import { transparencyAppInfoCtrlCtrl } from '@/controllers';

const router = new Router({ prefix: '/transparency' });

const routers = router
  .post(
    '/getTransparencyAppInfo',
    transparencyAppInfoCtrlCtrl.getTransparencyAppInfo,
  )
  .post(
    '/updateTransparencyAppInfo',
    transparencyAppInfoCtrlCtrl.updateTransparencyAppInfo,
  )
  .post(
    '/updateTransparencyAppInfoTag',
    transparencyAppInfoCtrlCtrl.updateTransparencyAppInfoTag,
  );

export default routers;
