import Router from 'koa-router';
import { transparencyAppCrawlerCtrl } from '@/controllers';

const router = new Router({ prefix: '/transparency' });

const routers = router
  .post('/getAppCrawlerList', transparencyAppCrawlerCtrl.getAppCrawlerList)
  .post('/updateAppCrawler', transparencyAppCrawlerCtrl.updateAppCrawler)
  .post('/batchCreateAppCrawler', transparencyAppCrawlerCtrl.batchCreateAppCrawler);

export default routers;
