/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-27 17:55:08
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 14:49:06
 * @Description:
 */
/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-26 14:37:39
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-27 17:28:48
 * @Description:
 */
import Router from 'koa-router';
import { Context, Next } from 'koa';
import { fullReportCtrl } from '@/controllers';
import { setInterfaceTimeOut } from '@/utils';
import { parseDateParams, resolveFullReportDefaultData } from '@/utils/report';
const router = new Router({ prefix: '/dashboard' });
const setTimeOutMiddler = async (ctx: Context, next: Next) => {
  ctx.request.socket.setTimeout(60 * 60 * 1000);
  await next();
};

const routers = router
  .post(
    '/getDashboardList',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    fullReportCtrl.getDashboardList
  )
  .post(
    '/downloadDashboardReport',
    setTimeOutMiddler,
    setInterfaceTimeOut(60 * 60 * 1000),
    resolveFullReportDefaultData,
    parseDateParams,
    fullReportCtrl.downloadAllReport
  )

  .post(
    '/getConfigQps',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    fullReportCtrl.getConfigQps
  );

export default routers;
