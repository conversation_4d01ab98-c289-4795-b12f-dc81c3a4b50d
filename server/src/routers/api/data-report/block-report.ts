/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-02-27 17:55:18
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 16:29:59
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-28 15:09:17
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-11-27 17:29:50
 * @Description:
 */
import Router from 'koa-router';
import { Context, Next } from 'koa';
import { publisherBlockReportCtrl, advertiserBlockReportCtrl } from '@/controllers';
import { setInterfaceTimeOut } from '@/utils';
import { parseDateParams } from '@/utils/report';
const router = new Router({ prefix: '/block' });
const setTimeOutMiddler = async (ctx: Context, next: Next) => {
  ctx.request.socket.setTimeout(60 * 60 * 1000);
  await next();
};
const routers = router
  .post(
    '/getPublisherBlockList',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    publisherBlockReportCtrl.getPublisherBlockList
  )
  .post(
    '/downloadPublisherBlockList',
    setTimeOutMiddler,
    setInterfaceTimeOut(60 * 60 * 1000),
    parseDateParams,
    publisherBlockReportCtrl.downloadPublisherBlockList
  )
  .post(
    '/getAdvertiserBlockList',
    setTimeOutMiddler,
    setInterfaceTimeOut(15 * 60 * 1000),
    parseDateParams,
    advertiserBlockReportCtrl.getAdvertiserBlockList
  )
  .post(
    '/downloadAdvertiserBlockList',
    setTimeOutMiddler,
    setInterfaceTimeOut(60 * 60 * 1000),
    parseDateParams,
    advertiserBlockReportCtrl.downloadAdvertiserBlockList
  );

export default routers;
