/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-10-20 15:30:43
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-28 15:15:18
 * @Description:
 */
import Router from 'koa-router';
import { kwaiReportCtrl, humanController, pixalateController } from '@/controllers';

const { getKwaiReport, downloadKwaiReport } = kwaiReportCtrl;
const router = new Router({ prefix: '/report' });

const routers = router
  .post('/getKwaiReport', getKwaiReport)
  .post('/downloadKwaiReport', downloadKwaiReport)
  .post('/getPixalateReport', pixalateController.getPixalateReport)
  .post('/downloadPixalateReport', pixalateController.downloadPixalateReport)
  .post('/getHumanReport', humanController.getHumanReport)
  .post('/downloadHumanReport', humanController.downloadHumanReport);

export default routers;
