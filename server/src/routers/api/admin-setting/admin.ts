/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-21 16:44:24
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-21 16:44:26
 * @Description:
 */
import Router from 'koa-router';
import {
  adminUserCtrl, adminMenuCtrl, adminRoleCtrl
} from '@/controllers';

const {
  addUser, updateUser, getAllUserList, resetPassword
} = adminUserCtrl;

const {
  addMenu, updateMenu, getAllMenu, updateMenuSort, deleteMenu
} = adminMenuCtrl;

const {
  addRole, updateRole, getAllRole, updateRolePms
} = adminRoleCtrl;

const router = new Router({ prefix: '/admin' });

const routers = router
  .post('/getAllUserList', getAllUserList)
  .post('/addUser', addUser)
  .post('/updateUser', updateUser)
  .post('/resetPwd', resetPassword)
  .post('/addMenu', addMenu)
  .post('/updateMenu', updateMenu)
  .post('/getAllMenu', getAllMenu)
  .post('/deleteMenu', deleteMenu)
  .post('/updateMenuSort', updateMenuSort)
  .post('/addRole', addRole)
  .post('/updateRole', updateRole)
  .post('/getAllRole', getAllRole)
  .post('/updateRolePms', updateRolePms);

export default routers;

