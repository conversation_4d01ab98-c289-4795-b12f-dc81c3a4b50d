/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-12-13 18:47:58
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-12-25 14:22:37
 * @Description:
 */
import Router from 'koa-router';
import { boardController } from '@/controllers';
import { checkChartCache } from '@/middleware';

const router = new Router({ prefix: '/ai-board' });

const routers = router
  .post('/getOverview', checkChartCache, boardController.getOverview)
  .post('/getTopCountry', checkChartCache, boardController.getTopCountry)
  .post(
    '/getSevenDaysCountry',
    checkChartCache,
    boardController.getSevenDaysCountry
  )
  .post('/getTopAdFormat', checkChartCache, boardController.getTopAdFormat)
  .post(
    '/getAllTenantRevenue',
    checkChartCache,
    boardController.getAllTntRevenue
  )
  .post(
    '/getTopCountryEcpmAndEcpr',
    checkChartCache,
    boardController.getTopCountryEcpmAndEcpr
  );

export default routers;
