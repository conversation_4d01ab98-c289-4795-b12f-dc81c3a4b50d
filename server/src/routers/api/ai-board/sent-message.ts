/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-08 15:29:48
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-01-08 15:31:12
 * @Description:
 */
import Router from 'koa-router';
import { sentMsgCtrl } from '@/controllers';

const router = new Router({ prefix: '/ai-board' });

const routers = router.post(
  '/getAllSentMessage',
  sentMsgCtrl.getAllSentMessage
);

export default routers;
