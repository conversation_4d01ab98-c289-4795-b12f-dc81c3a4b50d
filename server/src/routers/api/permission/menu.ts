/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 15:56:56
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-12 11:41:04
 * @Description:
 */

import Router from 'koa-router';
import { menuController } from '@/controllers';

const { addMenu, updateMenu, getAllMenu, deleteMenu, updateMenuSort } = menuController;

const router = new Router({ prefix: '/menu' });

const routers = router
  .post('/addMenu', addMenu)
  .post('/updateMenu', updateMenu)
  .post('/getAllMenu', getAllMenu)
  .post('/deleteMenu', deleteMenu)
  .post('/updateMenuSort', updateMenuSort);

export default routers;
