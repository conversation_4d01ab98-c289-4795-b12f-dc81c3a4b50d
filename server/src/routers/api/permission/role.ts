/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON>@algorix.co
 * @Date: 2023-03-07 16:00:46
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-05-16 16:47:08
 * @Description:
 */
import Router from 'koa-router';
import { roleController } from '@/controllers';

const { addRole, editRole, getAllRoleAndPms, updateRole } =
  roleController;
const router = new Router({ prefix: '/role' });

const routers = router
  .post('/addRole', addRole)
  .post('/editRole', editRole)
  .post('/updateRole', updateRole)
  .post('/getPlatformRole', getAllRoleAndPms);

export default routers;
