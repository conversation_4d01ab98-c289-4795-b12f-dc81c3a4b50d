/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 17:05:21
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-04-06 10:50:49
 * @Description:
 */

import Router from 'koa-router';
import { interfaceController } from '@/controllers';

const { addInterface, updateInterface, getAllInterface, deleteInterface } =
  interfaceController;
const router = new Router({ prefix: '/interface' });

const routers = router
  .post('/addInterface', addInterface)
  .post('/updateInterface', updateInterface)
  .post('/getAllInterface', getAllInterface)
  .post('/deleteInterface', deleteInterface);
export default routers;
