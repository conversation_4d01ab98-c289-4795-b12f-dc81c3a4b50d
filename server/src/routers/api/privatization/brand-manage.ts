/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-29 19:56:38
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-30 19:10:37
 * @Description:
 */
import { brandCtrl, pixalateCtrl } from '@/controllers';
import Router from 'koa-router';

const router = new Router({ prefix: '/privatization' });
router
  .post('/getTenantBrandList', brandCtrl.getTenantBrandList)
  .post('/addTenantBrand', brandCtrl.addTenantBrand)
  .post('/editTenantBrand', brandCtrl.editTenantBrand)
  .post('/getTenantPixalateList', pixalateCtrl.getTenantPixalateList)
  .post('/addTenantPixalate', pixalateCtrl.addTenantPixalate)
  .post('/editTenantPixalate', pixalateCtrl.editTenantPixalate)
  .post('/upload', brandCtrl.upload)
  .get('/download', brandCtrl.download);

export default router;
