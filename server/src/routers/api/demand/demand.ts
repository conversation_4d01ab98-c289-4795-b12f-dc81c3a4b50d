/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:09:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-07 15:54:59
 * @Description:
 */

import Router from 'koa-router';
import { demandController } from '@/controllers';

const router = new Router({ prefix: '/demand' });

const routers = router
  .post('/updateDemand', demandController.updateDemand)
  .post('/getDemandList', demandController.getDemandList)
  .post('/getDemandListWithTesting', demandController.getDemandListTesting)
  .post('/getDemandEndpoint', demandController.getDemandEndpoint)
  // .post('/getPretargetCampaign', demandController.getPretargetCampaign)
  .post('/getDemandAuth', demandController.getDemandAuth);

export default routers;
