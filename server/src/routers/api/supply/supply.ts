/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix
 * @Date: 2022-12-05 14:09:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-07 17:02:45
 * @Description:
 */

import Router from 'koa-router';
import { supplyCtrl } from '@/controllers';

const router = new Router({ prefix: '/supply' });

const routers = router
  // .post('/addSupply', supplyCtrl.addSupply)
  .post('/updateSupply', supplyCtrl.updateSupply)
  .post('/getSupplyList', supplyCtrl.getSupplyList)
  // .post('/getSupplyListWithTesting', supplyCtrl.getSupplyListWithTesting)
  .post('/getSupplyAuth', supplyCtrl.getSupplyAuth);
// .post('/getSupplyAppPlacement', supplyCtrl.getSupplyAppPlacement)
// .post('/setSupplyAuth', validTesting, supplyCtrl.setSupplyAuth)
// .post('/getSupplyEndpoint', supplyCtrl.getSupplyEndpoint)
// .post('/getDashboardSupplyList', supplyCtrl.getDashboardSupplyList);

export default routers;
