import { QueryParamType } from '@/db/mysql';
import { sha256 } from '@/utils';

/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-10-07 18:06:01
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-10-17 10:49:06
 * @Description:
 */
export function getTestingBuyer(
  type: 'Test-Buyer-System' | 'Test-Buyer-Site-System',
  token: string,
  tnt_id: number
): QueryParamType {
  return {
    sql: 'insert into buyer (buyer_name, integration_type, status, tnt_id, profit_model, rev_share_ratio, imp_track_type, token, auction_type, user_id) values(?)',
    values: [[type, 1, 3, tnt_id, 1, 100, 1, token, 1, 0]]
  };
}

export function getTestingEndpoint(buyer_id: number, tnt_id: number, isSite: boolean = false): QueryParamType {
  return isSite
    ? `insert into buyer_endpoint(buyer_id, server_region, ad_format, url, connect_timeout,
          socket_timeout, gzip, tnt_id) values 
          (${buyer_id}, 1, 1, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 1, 2, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 1, 3, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 1, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 2, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 3, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 1, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 2, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 3, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id})`
    : `insert into buyer_endpoint(buyer_id, server_region, ad_format, url, connect_timeout,
          socket_timeout, gzip, tnt_id) values (${buyer_id}, 1, 1, 
          'https://use.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',
          0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 1, 2, 'https://use.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 1, 3, 'https://use.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 1, 'https://apac.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 2, 'https://apac.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 3, 'https://apac.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 1, 'https://euc.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 2, 'https://euc.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 3, 'https://euc.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id})`;
}

export function getTestingQPS(buyer_id: number, tnt_id: number): QueryParamType {
  return {
    sql: 'insert into qps (buyer_id, level, seller_id, qps, status, op_id, tnt_id, ots_id,server_region, uk_key) values ?',
    values: [
      [
        [buyer_id, 2, 0, 50, 1, 0, tnt_id, '', 1, sha256(`${tnt_id}2${buyer_id}01`)],
        [buyer_id, 2, 0, 50, 1, 0, tnt_id, '', 2, sha256(`${tnt_id}2${buyer_id}02`)]
      ]
    ]
  };
}

export function getTestingCap(buyer_id: number, tnt_id: number): QueryParamType {
  return {
    sql: 'insert into cap (seller_id, buyer_id, type, imp_cap, rev_cap, status, cap_status, op_id, sys_update_time, tnt_id) values ?',
    values: [[[0, buyer_id, 2, 500, 1, 1, 1, 0, '1970-01-01 00:00:01', tnt_id]]]
  };
}
