/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-11-23 10:25:21
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-27 16:11:37
 * @LastEditors: chen<PERSON>dan
 * @LastEditTime: 2023-12-21 18:47:35
 * @Description:
 */

import { Code } from '@/codes';

type Type = {
  [key: string | number]: string;
};

export const EmailParams = {
  from: 'Rix Engine<<EMAIL>>',
  // cc: process.env.NODE_ENV === 'prod' ? '<EMAIL>' : '<EMAIL>'
  cc:
    process.env.NODE_ENV === 'prod'
      ? 'Rix Engine<<EMAIL>>'
      : 'Rix Engine<<EMAIL>>'
};

export const StatusMap = {
  Active: 1,
  Paused: 2,
  Testing: 3
};

export const StatusDesc: { [key: number]: string } = {
  1: 'Active',
  2: 'Paused',
  3: 'Testing'
};

export const AdFormatMap: Type = {
  1: 'Banner',
  2: 'Native',
  3: 'Video',
  4: 'Reward Video'
};
export const StatusType = {
  Active: 1,
  Paused: 2,
  Delete: 3
};

export const RESULT = {
  code: Code.ERROR_SYS,
  message: 'system error',
  data: ''
};

export const QpsLevel = {
  supply: 1,
  demand: 2,
  'demand + supply': 3,
  'demand + supply-app': 4,
  'demand + supply-placement': 5,

  'demand + supply + ad_forma': 6,
  'demand + supply + country': 7,
  'demand + ad_format': 8,
  'demand + country': 9,
  'supply + bundle,': 51,
  'supply + country': 52,
  'supply + ad_format': 53
};

export const ProfitType = {
  Seller: 1,
  Demand: 2,
  'Seller-Demand': 3
};
