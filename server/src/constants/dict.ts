import { CommonAPI } from '@/types/common';

// mapping 映射查询维护
export const DictMap: CommonAPI.DictItem[] = [
  {
    dict_type: 'supply_tag',
    dict_content: 'select supply_tag from aat_supply_ep where status=1',
    aat_dict_type: true
  },
  {
    dict_type: 'special_users',
    dict_content:
      'select u.user_id, u.account_name, u.type, u.role from user u left join switch_account sa on u.user_id = sa.special_user_id where u.tnt_id = 1083'
  },
  {
    dict_type: 'account_list',
    // 排除掉 1083 的账号
    dict_content:
      'select u.user_id, u.account_name, u.tnt_id, u.status, t.tnt_name from user u left join tenant t on u.tnt_id = t.tnt_id where u.tnt_id != 1083 and u.type in (1, 4, 5)'
  },
  {
    dict_type: 'device_brand',
    dict_content: 'select LOWER(mapping_value) value, mapping_value label, sort_order from global_mapping where type = 2 order by sort_order asc'
  },
  {
    dict_type: 'ad_size',
    dict_content: 'select CAST(mapping_key AS SIGNED) value, mapping_value label from global_mapping where type = 1 order by value'
  }
];

/**
 * 字典类型列表
 */
export const DictTypeList = DictMap.map((item) => item.dict_type);
