type BlockStatusMap = Record<number, { code: string; reason: string }>;

/**
 * 需求方block status 状态码
 */
export const DemandBlockStatusMap: BlockStatusMap = {
  20001: {
    code: 'BUYER_FILTER_QPS_BUYER_BLOCK_ERROR',
    reason: 'Demand QPS block'
  },
  20002: {
    code: 'BUYER_FILTER_CAP_BLOCK_ERROR',
    reason: 'Demand CAP block'
  },
  21001: {
    code: 'BUYER_FILTER_WHITE_BLACK_LIST_BLOCK_ERROR',
    reason: '黑白名单 block'
  },
  22001: {
    code: 'BUYER_FILTER_CONTENT_ERROR',
    reason: 'Demand流量筛选配置block'
  },
  22002: {
    code: 'PIXALATE_IP_BLOCK_ERROR',
    reason: 'Pixalate IP block'
  },
  22003: {
    code: 'PIXALATE_DEVICE_ID_BLOCK_ERROR',
    reason: 'Pixalate device ID block'
  },
  22101: {
    code: 'BUYER_FILTER_PIXALATE_IVT_OVER',
    reason: 'Pixalate ivt block'
  },
  22102: {
    code: 'BUYER_FILTER_HUMAN_IVT_OVER',
    reason: 'Human ivt block'
  },
  50000: {
    code: 'BUYER_ATC_CONTROL_UNKNOWN',
    reason: 'Demand ATC block：未知'
  },
  50003: {
    code: 'BUYER_ATC_CONTROL_NEWER_PROTECTED',
    reason: 'Demand ATC block：新流量控制（对应BuyerTag=3）'
  },
  50007: {
    code: 'BUYER_ATC_CONTROL_TIER_LOWER_PROTECTED',
    reason: 'Demand ATC block：较差流量控制（对应BuyerTag=7）'
  },
  50008: {
    code: 'BUYER_ATC_CONTROL_TIER_NONE_PROTECTED',
    reason: 'Demand ATC block：极差流量控制（对应BuyerTag=8）'
  },
  50010: {
    code: 'BUYER_ATC_CONTROL_NO_FILL_PROTECTED',
    reason: 'Demand ATC block：无填充控制(对应BuyerTag=10）'
  },
  51000: {
    code: 'BUYER_ATC_CONTROL_QPS_CONTROL',
    reason: 'Demand ATC block: QPS Control控制'
  },
  52000: {
    code: 'BUYER_ATC_CONTROL_QPS_RATE_LIMITER',
    reason: 'Demand ATC block：流量限制'
  }
};

/**
 * 供应方block status 状态码
 */
export const SupplyBlockStatusMap: BlockStatusMap = {
  20000: {
    code: 'SUPPLY_INFO_ERROR',
    reason: '无法映射流量接入方式（主要是系统原因）'
  },
  21000: {
    code: 'REQUEST_BODY_ERROR',
    reason: '请求内容无法解析'
  },
  29001: {
    code: 'REQUEST_TRAFFIC_ROUTE_BECOME_CIRCLE_ERROR',
    reason: '内部转发形成链路block'
  },
  30000: {
    code: 'CONTENT_ERROR',
    reason:
      '流量请求内容信息有误，常见：1. iOS流量 bundle 为非全数字 2. 缺少国家、ua、ip/无效ip 3. 底价信息过大，banner/native > 10(1079 15$)， veideo > 100 4. 不符合法规。USA COPPA流量，EU流量GDPR不为1'
  },
  30200: {
    code: 'TESTING_REQUEST_NON_TESTING_SUPPLY_ERROR',
    reason: '测试流量（BidRequest.testing=1）但supply status不为Testing'
  },
  31000: {
    code: 'BID_REQUEST_FORMAT_ERROR',
    reason: 'BidRequest不符合OpenRTB规范'
  },
  40001: {
    code: 'SELLER_STRATEGY_QPS_BLOCK',
    reason: 'Seller QPS block'
  },
  40002: {
    code: 'SELLER_STRATEGY_CAP_BLOCK',
    reason: 'Seller Cap block'
  },
  40003: {
    code: 'NO_CONFIG_AUTH_BUYER',
    reason: '无授权有效demand'
  },
  40004: {
    code: 'NO_SUITABLE_BUYER',
    reason: '没有符合转发条件demand'
  },
  40005: {
    code: 'SELLER_STRATEGY_WHITE_BLACK_LIST_BLOCK',
    reason: '黑白名单block'
  },
  50000: {
    code: 'SELLER_ATC_CONTROL_UNKNOWN',
    reason: 'ATC控制：未知'
  },
  50003: {
    code: 'SELLER_ATC_CONTROL_NEWER_PROTECTED',
    reason: 'ATC控制：新流量控制（对应SellerTag=3）'
  },
  50007: {
    code: 'SELLER_ATC_CONTROL_TIER_LOWER_PROTECTED',
    reason: 'ATC控制：较差流量控制（对应SellerTag=7）'
  },
  50008: {
    code: 'SELLER_ATC_CONTROL_TIER_NONE_PROTECTED',
    reason: 'ATC控制：极差流量控制（对应sellerTag=8）'
  },
  50009: {
    code: 'SELLER_ATC_CONTROL_TIRE_LONG_TAIL_PROTECTED',
    reason: 'ATC控制：尾量控制'
  },
  52000: {
    code: 'SELLER_ATC_CONTROL_QPS_RATE_LIMITER',
    reason: 'ATC控制：流量限制'
  },
  9999: {
    code: 'SYSTEM_ERROR',
    reason: '系统错误'
  }
};

type BlockTagMap = Record<number, { code: string; reason: string }>;

/**
 * 供应方block tag 状态码
 */
export const SellerTagMap: BlockTagMap = {
  1: {
    code: 'REQUEST_WHITE',
    reason: '请求白名单流量'
  },
  2: {
    code: 'WHITE_TRAFFIC',
    reason: 'ATC白名单流量'
  },
  3: {
    code: 'NEWER_PROTECT',
    reason: '新流量'
  },
  5: {
    code: 'TIER_PERFECT',
    reason: '优质流量'
  },
  6: {
    code: 'TIER_NORMAL',
    reason: '正常流量'
  },
  7: {
    code: 'TIER_LOWER',
    reason: '低价值量'
  },
  8: {
    code: 'TIER_NONE',
    reason: '无价值量&长尾流量'
  }
};

/**
 * 需求方block tag 状态码
 */
export const BuyerTagMap: BlockTagMap = {
  1: {
    code: 'REQUEST_WHITE',
    reason: '请求白名单流量'
  },
  2: {
    code: 'WHITE_TRAFFIC',
    reason: 'ATC白名单流量'
  },
  3: {
    code: 'NEWER_PROTECT',
    reason: '新流量'
  },
  5: {
    code: 'TIER_PERFECT',
    reason: '优质流量'
  },
  6: {
    code: 'TIER_NORMAL',
    reason: '正常流量'
  },
  7: {
    code: 'TIER_LOWER',
    reason: '低价值量'
  },
  8: {
    code: 'TIER_NONE',
    reason: '无价值量&长尾流量'
  },
  10: {
    code: 'NO_FILL',
    reason: '低填充&无填充'
  }
};

// 用来生成下拉框的 options，label 为 code，value 为 key
export const BlockMapOptions = (map: BlockStatusMap | BlockTagMap) => {
  return Object.entries(map).map(([key, value]) => ({
    label: value.code,
    value: key
  }));
};
