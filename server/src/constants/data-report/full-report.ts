/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-27 16:22:23
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-09 15:41:36
 * @Description:
 */

// 特殊的维度,需要额外的处理,注意这里与std平台的表达的意思不同
export const SpecialDimension = ['http_code', 'internal_request'];
export const SpecialDimensionMap: { [key: string]: string } = {
  http_code: 'coalesce(http_code, 0) as http_code',
  internal_request: 'coalesce(internal_request, 0) as internal_request'
};

export const DateType = {
  Null: 0,
  Day: 1,
  Hour: 2,
  Month: 3
};

export const SchainMap: Record<number, string> = {
  0: 'Incomplete',
  1: 'Complete',
};

export const InventoryMapDesc: { [key: number]: string } = {
  1: 'Inapp',
  2: 'Website',
  0: 'UnKnown'
};

export const HttpCodeDesc: { [key: number]: string } = {
  0: 'Normal',
  408: 'Timeout',
  429: 'Too Many Requests',
  10: 'Connect Failed'
};

export const RequestTypeMapDesc: { [key: number]: string } = {
  1: 'Internal',
  0: 'External'
};

export const DeviceTypeMapDesc: { [key: number]: string } = {
  1: 'Mobile/Tablet',
  2: 'PC',
  3: 'CTV',
  4: 'Phone',
  5: 'Tablet',
  6: 'Connected Device',
  7: 'Set Top Box',
  0: 'Unknown',
};
