/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-28 15:07:49
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 15:40:21
 * @Description:
 */

export const PixalateDimensions = [
  'day',
  'month',
  'tnt_id',
  'fraud_type',
  'buyer_id',
  'seller_id',
  'publisher_id',
  'app_bundle_id',
  'country'
];

export const PixalateOrderKey = [
  'day',
  'month',
  'tnt_id',
  'buyer_id',
  'seller_id',
  'publisher_id',
  'gross_tracked_ads',
  'sivt_imp_rate',
  'givt_imp_rate',
  'ivt_rate'
];

export const PixalateMetricOrderKey = ['gross_tracked_ads', 'sivt_imp_rate', 'givt_imp_rate', 'ivt_rate'];
