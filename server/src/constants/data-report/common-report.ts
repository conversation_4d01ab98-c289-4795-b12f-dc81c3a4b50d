/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-02-27 16:19:31
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 11:58:29
 * @Description:
 */

import { TimeZoneMapByValue } from '@rixfe/rix-tools';

const DimensionsHeader = [
  { header: 'Date', key: 'date' },
  { header: 'Tenant', key: 'tnt' },
  { header: 'Tenant', key: 'tnt_id' },
  { header: 'Publisher', key: 'seller' },
  { header: 'Advertiser', key: 'buyer' },
  { header: 'Pub Partner', key: 'pub_partner' },
  { header: 'Adv Partner', key: 'adv_partner' },
  { header: 'Bundle', key: 'app_bundle_id' },
  { header: 'Unit ID', key: 'placement_id' },
  { header: 'Ad Format', key: 'ad_format' },
  { header: 'Ad Size', key: 'ad_size' },
  { header: 'Country', key: 'country' },
  { header: 'Platform', key: 'platform' },
  { header: 'Server Region', key: 'region' },
  { header: 'App Name', key: 'app_name' },
  { header: 'Ad Domain', key: 'ad_domain' },
  { header: 'Inventory', key: 'inventory' },
  { header: 'Http Code', key: 'http_code' },
  { header: 'Request Type', key: 'internal_request' },
  { header: 'Source Tenant', key: 'source_tenant' },
  { header: 'Source Deep', key: 'source_deep' },
  { header: 'Schain Hop', key: 'seller_schain_hop' },
  { header: 'Schain Complete', key: 'seller_schain_complete' },
  { header: 'Demand Schain Hop', key: 'buyer_schain_hop' },
  { header: 'Demand Schain Complete', key: 'buyer_schain_complete' },
  { header: 'Device Brand', key: 'make' },
  { header: 'Device Type', key: 'device_type' },
  { header: 'Bid Api', key: 'res_api' },
  { header: 'Status', key: 'status' },
  { header: 'Adv Tag', key: 'buyer_tag' },
  { header: 'Pub Tag', key: 'seller_tag' }
];
const AllMetricsHeader = [
  { header: 'Advertiser Net Revenue', key: 'buyer_net_revenue' },
  { header: 'Publisher Net Revenue', key: 'seller_net_revenue' },
  {
    header: 'Profit',
    key: 'profit'
  },
  { header: 'Request', key: 'request' },
  { header: 'Total Request', key: 'total_request' },
  { header: 'Block Request', key: 'block_request' },
  { header: 'Out Request', key: 'out_request' },
  { header: 'Response', key: 'response' },
  { header: 'Win', key: 'win' },
  { header: 'Impression(ADM)', key: 'impression' },
  { header: 'Click', key: 'click' },
  { header: 'Bid Floor', key: 'total_seller_bid_floor' },
  {
    header: 'Fill Rate',
    key: 'fill_rate'
  },
  { header: 'Advertiser Gross Revenue', key: 'buyer_gross_revenue' },
  { header: 'Publisher Gross Revenue', key: 'seller_gross_revenue' },
  {
    header: 'eCPR',
    key: 'ecpr'
  },
  {
    header: 'Advertiser eCPR',
    key: 'adv_ecpr'
  },
  {
    header: 'Click Rate',
    key: 'click_rate'
  },
  {
    header: 'CPC',
    key: 'cpc'
  },
  {
    header: 'Profit Rate',
    key: 'profit_rate'
  },
  {
    header: 'Win Rate',
    key: 'win_rate'
  },
  {
    header: 'Render Rate',
    key: 'impression_rate'
  },
  {
    header: 'Advertiser Gross eCpm',
    key: 'buyer_gross_ecpm'
  },
  {
    header: 'Advertiser Net eCpm',
    key: 'buyer_net_ecpm'
  },
  {
    header: 'Publisher Net eCpm',
    key: 'seller_net_ecpm'
  },
  {
    header: 'Bid Price',
    key: 'bid_price'
  },
  {
    header: 'QPS (Real)',
    key: 'real_qps'
  },
  {
    header: 'Advertiser QPS (Config)',
    key: 'adv_config_qps'
  },
  {
    header: 'Publisher QPS (Config)',
    key: 'pub_config_qps'
  },
  {
    header: 'Impression(Pay)',
    key: 'seller_payment_impression'
  },
  {
    header: 'Tmax',
    key: 'tmax'
  },
  {
    header: 'PX Prebid Pass Rate',
    key: 'pixalate_passed_filtered_rate'
  },
  {
    header: 'PX Prebid Scan Rate',
    key: 'pixalate_be_filtered_rate'
  },
  {
    header: 'Same IP Rate(ADM)',
    key: 'same_ip_rate'
  },
  {
    header: 'Same IP Rate(Pay)',
    key: 'seller_same_ip_rate'
  }
];

export const HeaderOptions = [...DimensionsHeader, ...AllMetricsHeader];

export const TimeZoneMap = TimeZoneMapByValue;

export const AdFormatToLabel: { [key: string]: string } = {
  1: 'Banner',
  2: 'Native',
  3: 'Video',
  4: 'Reward Video'
};

export const MoblieOS: { [key: string]: string } = {
  0: 'Undefined',
  1: 'iOS',
  2: 'Android',
  3: 'Other',
  4: 'Linux',
  5: 'MacOS',
  6: 'Windows',
  // OTT/CTV 预留11~29
  11: 'tvOS',
  12: 'Roku',
  13: 'Amazon',
  14: 'Microsoft',
  15: 'Samsung Smart TV',
  16: 'LG Smart TV',
  17: 'Sony Playstation',
  18: 'Vizio',
  19: 'Philips Smart TV',
  50: 'Tizen',
  51: 'KaiOS'
};

export const RegionLabelMap = {
  // 0: 'All Region',
  USE: 'USE',
  APAC: 'APAC'
};

export const QpsLevel = {
  supply: 1,
  demand: 2,
  'demand + supply': 3,
  'demand + supply-app': 4,
  'demand + supply-placement': 5,
  // ---
  'demand + supply + ad_forma': 6,
  'demand + supply + country': 7,
  'demand + ad_format': 8,
  'demand + country': 9,
  'supply + bundle,': 51,
  'supply + country': 52,
  'supply + ad_format': 53
};

export const RegionType = {
  ALL: 0,
  USE: 1,
  APAC: 2
};
