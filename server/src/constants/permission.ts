/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-03-27 16:14:15
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2023-07-10 11:41:24
 * @Description:
 */
/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-16 11:49:55
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-03-17 16:03:33
 * @Description:
 */

export const SelectType = {
  in: 1,
  'not in': 2
};

export const Type = {
  Menu: 2,
  Operation: 1
};

export const UserType = {
  Tenant: 1,
  Supply: 2,
  Demand: 3,
  Rix_Admin: 4,
  Rix_Data_Analyst: 5
};

export const LinkMenuType = {
  Ordinary: 1,
  Dashboard: 2
};

export const RoleType: any = {
  1: 'Super Administrator',
  2: 'Administrator',
  3: 'Operator',
  4: 'Data Analyst',
  5: 'Rix Administrator',
  6: 'Rix Data Analyst'
};

export const InterfaceType = {
  Normal: 1,
  'NO-Login': 2, // 不需要登录的开放接口
  'Login-Global': 3 // 需要登录的开放接口
};

export const StatusType = {
  Active: 1,
  Paused: 0
};

export const InterfaceOperationType: any = {
  1: 'Write',
  2: 'Read'
};

export const InterfaceTypeToString: any = {
  1: 'Normal',
  2: 'NO-Login',
  3: 'Login-Global'
};

export const RoleTypeToNumber = {
  'Super Administrator': 1,
  Administrator: 2,
  'General User': 3,
  'Data Analyst': 4,
  'Rix Administrator': 5,
  'Rix Data Analyst': 6,
  'Supply User': 7,
  'Demand User': 8
};

export const OperationType = {
  write: 1,
  read: 2
};

export const BLType = {
  Unblock: 1,
  Block: 2
};
