/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-21 17:13:33
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 17:13:33
 * @Description:
 */

export const MenuType = {
  Menu: 2,
  Operation: 1
};

export const InterfaceType = {
  Normal: 1,
  'NO-Login': 2, // 不需要登录的开放接口
  'Login-Global': 3 // 需要登录的开放接口
};

export const InterfaceTypeToString: any = {
  1: 'Normal',
  2: 'NO-Login',
  3: 'Login-Global'
};

// 角色创建者
export const RoleCreator = {
  System: 1, // 系统自带角色
  User: 2 // 用户自定义角色
};

// 角色类型
export const RoleType = {
  'Super Administer': 1, // 超管角色 新增租户自动生成
  'Normal Role': 2 // 普通角色
};

// 对外接口或者登出接口不需要刷新session有效期
export const IgnoreCookieApis = [
  /^\/api\/user\/logOut$/i
];

export const NoLoginApis = [
  /^\/api\/user\/logIn$/i,
  ...IgnoreCookieApis
];

export const TenantSuperAdmin = {
  super_account: 'super_admin',
  super_display_name: 'Super Administer'
};

// 两种角色类型
export const UserType = {
  System: 1, // 系统自带角色
  Custom: 2 // 用户自定义角色
};
