/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-31 18:55:13
 * @LastEditors: ch<PERSON><PERSON><PERSON>
 * @LastEditTime: 2024-01-04 16:36:59
 * @Description:
 */
const config = Object.seal({
  port: 3002,
  database: {
    host: '**********',
    port: 8306,
    user: 'test',
    password: '%xOakkb3',
    database: 'db_saas',
    // database: 'db_saas_prod',
    timezone: '+00:00'
  },
  aat_database: {
    host: '*********',
    port: 8306,
    user: 'test',
    password: '123456',
    database: 'db_aat',
    timezone: '+00:00'
  },
  domainConfig: {
    console: 'console.rixengine.com'
  },
  redisConfig: {
    is_cluster: false,
    admin_key: 'RIXENGINE_ADMIN_AUTHORIZATION_vXMGCVGJ8cLP',
    platform_key: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
    optArray: [
      {
        port: 8379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 8379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    }
  },
  /**
   * 获取服务端的 redis 数据配置
   */
  reportRedisConfig: {
    is_cluster: false,
    expire_time: 48 * 60 * 60, // 48h
    platform_key: '',
    optArray: [
      {
        port: 8379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 8379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    }
  },
  passwordConfig: {
    admin_prefix: 'VchBvMmZ-', // 加密密钥
    platform_prefix: '8rL5Mbp5-' // saas加密密钥
  },
  emailConfig: {
    host: 'smtp.feishu.cn',
    port: 587,
    user: '<EMAIL>',
    pass: 'aX2o1Uk8U1rxS89i'
  },
  Tnt_Host_Prefix_KEY: 'Tenant_Host_Prefixs',
  UpdateUserInfo_KEY: 'needUpdateInfo',
  RixEngineSessionKey: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
  NotifyFeishuUrl:
    'https://open.feishu.cn/open-apis/bot/v2/hook/585f4fff-8c22-42eb-9719-217ab4176e9b',
  BoardCaCheKeyPrefix: 'RIXADMIN_BOARD_CACHE_KEY_bdLhLhz4wfQC',
  BoardCaCheTime: 60 * 10 // seconds
});

export default config;
