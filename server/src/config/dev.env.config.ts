/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-13 14:30:12
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-02 11:49:52
 * @Description: 数据库配置
 */

const config = Object.seal({
  port: 5090,
  database: {
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '123456',
    database: 'db_saas',
    timezone: '+00:00'
  },
  aat_database: {
    host: '127.0.0.1',
    port: 3306,
    user: 'root',
    password: '123456',
    database: 'db_aat',
    timezone: '+00:00'
  },
  domainConfig: {
    console: 'console.rixengine.com'
  },
  redisConfig: {
    is_cluster: false,
    admin_key: 'RIXENGINE_ADMIN_AUTHORIZATION_vXMGCVGJ8cLP',
    platform_key: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
    optArray: [
      {
        port: 6379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    }
  },
  /**
   * 获取服务端的 redis 数据配置
   */
  reportRedisConfig: {
    is_cluster: false,
    expire_time: 48 * 60 * 60, // 48h
    platform_key: '',
    optArray: [
      {
        port: 6379,
        host: '127.0.0.1',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '127.0.0.1',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    }
  },
  passwordConfig: {
    admin_prefix: 'VchBvMmZ-', // 加密密钥
    platform_prefix: '8rL5Mbp5-' // saas加密密钥
  },
  emailConfig: {
    host: 'smtp.feishu.cn',
    port: 587,
    user: '<EMAIL>',
    pass: 'aX2o1Uk8U1rxS89i'
  },
  Tnt_Host_Prefix_KEY: 'Tenant_Host_Prefixs',
  UpdateUserInfo_KEY: 'needUpdateInfo',
  RixEngineSessionKey: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQC',
  NotifyFeishuUrl: 'https://open.feishu.cn/open-apis/bot/v2/hook/585f4fff-8c22-42eb-9719-217ab4176e9b',
  BoardCaCheKeyPrefix: 'RIXADMIN_BOARD_CACHE_KEY_bdLhLhz4wfQC',
  BoardCaCheTime: 60 * 10 // seconds
});

export default config;
