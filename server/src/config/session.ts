/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-18 14:12:50
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-08 16:00:23
 * @Description:
 */
// 过期1天
const timeOut = 1 * 24 * 60 * 60 * 1000;
export const sessionConfig = {
  key: 'XJCh3qHl', // cookie key (default is koa:sess)
  autoCommit: true, // (boolean) automatically commit headers (default true)
  maxAge: timeOut, // cookie的过期时间 maxAge in ms (default is 1 days)
  overwrite: true, // 是否可以overwrite    (默认default true)
  httpOnly: true, // cookie是否只有服务器端可以访问 httpOnly or not (default true)
  signed: false, // 签名默认true
  rolling: false, // 在每次请求时强行设置cookie，这将重置cookie过期时间（默认：false）
  renew: true // (boolean) renew session when session is nearly expired,
};

export default {};
