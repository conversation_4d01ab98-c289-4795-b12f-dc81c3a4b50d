/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-03-21 15:55:42
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-02 11:49:18
 * @Description:
 */
/* *
 * @Author: x<PERSON><PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @file: 数据库配置
 * @Date: 2023-01-03 10:42:17
 */

const config = Object.seal({
  port: 3002,
  database: {
    host: '*********',
    port: 3306,
    user: 'saas_user_w',
    password: 'afa28f72470948dc901aa1d48384d21c',
    database: 'db_saas',
    timezone: '+00:00'
  },
  aat_database: {
    host: '*********1',
    port: 3306,
    user: 'saas_user_w',
    password: 'd13c57a33be041689c515f8332011d32',
    database: 'db_aat',
    timezone: '+00:00'
  },
  domainConfig: {
    console: 'console.rixengine.com'
  },
  redisConfig: {
    platform_key: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQD',
    is_cluster: false,
    admin_key: 'RIXENGINE_ADMIN_AUTHORIZATION_FC5p0Gp5Dx',
    optArray: [
      {
        port: 6379,
        host: '**********',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '**********',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    }
  },
  /**
   * 获取服务端的 redis 数据配置
   */
  reportRedisConfig: {
    is_cluster: false,
    expire_time: 48 * 60 * 60, // 48h
    platform_key: '',
    optArray: [
      {
        port: 6379,
        host: '*********',
        password: '',
        db: 0,
        family: 4 // IPV4/6
      }
    ],
    opts: {
      port: 6379,
      host: '*********',
      password: '',
      db: 0,
      family: 4 // IPV4/6
    },
  },
  passwordConfig: {
    admin_prefix: 'VchBvMmZ-', // 加密密钥
    platform_prefix: '8rL5Mbp5-' // saas加密密钥
  },
  emailConfig: {
    host: 'smtp.feishu.cn',
    port: 587,
    user: '<EMAIL>',
    pass: 'aX2o1Uk8U1rxS89i'
  },
  Tnt_Host_Prefix_KEY: 'Tenant_Host_Prefixs',
  UpdateUserInfo_KEY: 'needUpdateInfo',
  RixEngineSessionKey: 'RIXENGINE_SESSION_AUTHORIZATION_bdLhLhz4wfQD',
  NotifyFeishuUrl:
    'https://open.feishu.cn/open-apis/bot/v2/hook/2842f829-b85d-4a43-9964-dc99c82f5dcc',
  BoardCaCheKeyPrefix: 'RIXADMIN_BOARD_CACHE_KEY_bdLhLhz4wfQC',
  BoardCaCheTime: 60 * 10 // seconds
});

export default config;
