/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-28 15:11:08
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-28 15:11:10
 * @Description:
 */

import Joi from 'joi';
import { validReportDateParams } from './util';
import { Pixalate<PERSON>rderK<PERSON>, PixalateDimensions } from '@/constants/data-report/pixalate-report';

const NumReg = /^\d{8}$/;

const ReportCommon = {
  start_date: Joi.string().pattern(NumReg).required(), // 8位的数字 默认当天
  end_date: Joi.string().pattern(NumReg).required(), // 8位的数字 默认当天
  start: Joi.number().integer().min(0).required(),
  end: Joi.number().integer().min(1).required()
    .when('start', { // 必须大于start
      is: Joi.number().integer(),
      then: Joi.number().integer().max(Joi.ref('start', {
        // 限制最大查询数量
        adjust: (relation) => relation + 500
      }))
        .message('end must be larger then start,and less then or equal start + 500')
    })
    .when('start', {
      is: Joi.number().integer(),
      then: Joi.number().integer().min(Joi.ref('start', {
        // 限制最大查询数量
        adjust: (relation) => relation + 1
      })).message('end must be larger then start,and less then or equal start + 500')
    }),
  order: Joi.string().valid('desc', 'asc')
};

export const PixalateReportScheme = Joi.object({
  ...ReportCommon,
  buyer_id: Joi.array().items(Joi.number().integer()),
  seller_id: Joi.array().items(Joi.number().integer()),
  dimension: Joi.array().items(Joi.string().valid(...PixalateDimensions)),
  order_key: Joi.array().items(Joi.string().valid(...PixalateOrderKey)),
  app_bundle_id: Joi.array().items(Joi.string()),
  fraud_type: Joi.array().items(Joi.string()),
  tnt_id: Joi.array().items(Joi.number().integer())
}).custom((value, helper) => validReportDateParams(value, helper, 93));

export const DownloadPixalateReportSchema = Joi.object({
  start_date: Joi.string().pattern(NumReg).required(), // 8位的数字 默认当天
  end_date: Joi.string().pattern(NumReg).required(), // 8位的数字 默认当天
  order: Joi.string().valid('desc', 'asc'),
  seller_id: Joi.array().items(Joi.number().integer()),
  tnt_id: Joi.array().items(Joi.number().integer()),
  buyer_id: Joi.array().items(Joi.number().integer()),
  dimension: Joi.array().items(Joi.string().valid(...PixalateDimensions)),
  order_key: Joi.array().items(Joi.string().valid(...PixalateOrderKey)),
  app_bundle_id: Joi.array().items(Joi.string()),
  fraud_type: Joi.array().items(Joi.string())
}).custom((value, helper) => validReportDateParams(value, helper, 93));
