/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-28 15:11:41
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 15:11:56
 * @Description:
 */
/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-07-12 11:43:58
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-10-10 10:33:16
 * @Description:
 */
import Joi from 'joi';
import moment from 'moment';

/**
 * @description: 校验日期格式和范围
 * @param start_date 开始日期
 * @param end_date 结束日期
 * @param limit 限制范围
 * @returns
 */
export const validDateParams = (
  start_date: string,
  end_date: string,
  limit: number
) => {
  const startDate = moment(start_date, 'YYYYMMDD');
  const endDate = moment(end_date, 'YYYYMMDD');
  const toDay = moment();
  let isVaild = true;
  let message = '';
  if (!startDate.isValid() || !endDate.isValid()) {
    return {
      isVaild: false,
      message: 'wrong date format'
    };
  }
  if (!startDate.isSameOrBefore(endDate)) {
    isVaild = false;
    message = 'The end_date must larger or equal then start_date.';
  }
  if (endDate.diff(startDate, 'days') > limit - 1) {
    isVaild = false;
    message = `The date range must less then ${limit} days.`;
  }
  if (endDate.isAfter(toDay)) {
    isVaild = false;
    message = 'The end_date must less or equal then today.';
  }
  return {
    isVaild,
    message
  };
};

const validDate = (value: string) => {
  const y = value.substring(0, 4);
  const m = value.substring(4, 6);
  const d = value.substring(6);
  const flag = Number.isNaN(Date.parse(`${y}-${m}-${d}`));
  if (flag) {
    return false;
  }
  return true;
};
// report报表校验使用的 limit限制多少天
export const validReportDateParams = (value: any, helper: Joi.CustomHelpers<any>, limit: number) => {
  const { start_date, end_date } = value;
  const message: any = 'Please input valid date string';
  const f1 = validDate(start_date);
  const f2 = validDate(end_date);
  if (!f1 || !f2) {
    return helper.message(message);
  }
  const y = start_date.substring(0, 4);
  const m = start_date.substring(4, 6);
  const d = start_date.substring(6);

  const end = new Date(+y, +m, +d);
  end.setDate(end.getDate() + limit);
  const str = moment(end).format('YYYYMMDD');

  if (+end_date > +str) {
    const m: any = `The date interval needs to be within ${limit} days.`;
    return helper.message(m);
  }
  if (+end_date < +start_date) {
    const t: any = 'The end_date must larger or equal then start_date.';
    return helper.message(t);
  }
  return true;
};
