/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-02-03 14:05:34
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-27 11:46:00
 * @Description:
 */

import { DictTypeList } from '@/constants/dict';
import Jo<PERSON> from 'joi';

const password_reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;

export const LoginPasswordScheme = Joi.object({
  account_name: Joi.string().required(),
  password: Joi.string().required()
});

export const ResetPasswordScheme = Joi.object({
  old_password: Joi.string().min(6).max(25).required(),
  new_password: Joi.string().min(6).max(25).pattern(password_reg).required()
});

export const ConfirmPasswordScheme = Joi.object({
  old_password: Joi.string().min(6).max(25).required()
});

export const AddUserScheme = Joi.object({
  account_name: Joi.string().required(),
  password: Joi.string().min(6).max(25).pattern(password_reg)
});

export const EditUserScheme = Joi.object({
  user_id: Joi.number().required(),
  status: Joi.number().required(),
  new_password: Joi.string().min(6).max(25).pattern(password_reg)
});

export const DeleteUserScheme = Joi.object({
  user_id: Joi.number().required()
});

export const UpdateCompanyUserSchema = Joi.object({
  display_name: Joi.string().required()
});

export const UpdateUserLinkSchema = Joi.object({
  id: Joi.number().optional(),
  special_user_id: Joi.number().required(),
  link_users_ids: Joi.array().items(Joi.number()).required(),
  status: Joi.number().optional()
});
