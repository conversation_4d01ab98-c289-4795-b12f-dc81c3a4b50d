/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-09 11:27:37
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-09 11:28:51
 * @Description:
 */

import <PERSON><PERSON> from 'joi';

export const validDate = (value: string, helper: Jo<PERSON>.CustomHelpers<any>) => {
  const flag = Number.isNaN(Date.parse(value));
  const message: any = 'invalid date';
  if (flag) {
    return helper.message(message);
  }
  return true;
};
