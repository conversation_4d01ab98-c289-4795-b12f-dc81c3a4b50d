/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chenmu<PERSON>@algorix.co
 * @Date: 2023-08-08 17:24:01
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-07 10:39:49
 * @Description:
 */

import { AdFormats, ServerRegion, StatusOptions } from '@/constants/config';
import { commonService } from '@/services';
import { CountryOptions } from '@rixfe/rix-tools';
import Joi from 'joi';
import { validDate } from './utils';

// country 缩写数组，CountryOptions 的 value
const Countries = CountryOptions.map((v) => v.value);

const AtcCommon = {
  ad_format: Joi.array().items(Joi.number().valid(...AdFormats)),
  ad_size: Joi.array().items(Joi.number()).external(async (value) => {
    if (!value) return value;
    const adSizeDict = await commonService.getDict('ad_size');
    const validSizes = adSizeDict.map(item => Number(item.value));

    for (const size of value) {
      if (!validSizes.includes(size)) {
        throw new Error(`Invalid ad_size: ${size}`);
      }
    }
    return value;
  }),
  country: Joi.array().items(Joi.number().valid(...Countries)),
  bundle: Joi.array().items(Joi.any()),
  tnt_id: Joi.number().integer().required(),
  expired: Joi.string().min(10).max(10).message('invalid date').custom(validDate)
};

export const AddAtcSchema = Joi.object({
  ...AtcCommon,

  // 数字型数组
  seller_id: Joi.array().items(Joi.number().integer().required()).required(),
  region: Joi.string()
    .valid(...ServerRegion)
    .required(),
  buyer_id: Joi.number().integer()
});
export const UpdateAtcSchema = Joi.object({
  ...AtcCommon,
  id: Joi.number().integer().required(),
  status: Joi.number()
    .integer()
    .valid(...StatusOptions)
    .required()
});

const KwaCommon = {
  tnt_id: Joi.number().integer().required(),
  app_id: Joi.string().required(),
  native_pid: Joi.any(),
  inters_pid: Joi.any(),
  reward_pid: Joi.any(),
  token: Joi.string().required(),
  mixed_status: Joi.number().integer().valid(1, 2).required()
};

export const AddKwaSchema = Joi.object({
  ...KwaCommon,
  buyer_id: Joi.number().integer().required(),
  bundle: Joi.string().required()
});

export const UpdateKwaSchema = Joi.object({
  ...KwaCommon,
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required()
});

const CommonCustomFields = {
  tnt_id: Joi.number().integer().required(),
  // app_id: Joi.string().required(),
  buyer_id: Joi.number().integer().required()
};

// 定义标签字段(至少存在一个)
const CustomTagFields = {
  banner_tag_id: Joi.string().allow(''),
  native_tag_id: Joi.string().allow(''),
  video_tag_id: Joi.string().allow(''),
  rewarded_video_tag_id: Joi.string().allow('')
};

// 自定义错误消息
const tagErrorMessage = {
  'object.missing':
    'At least one valid tag must be provided (banner_tag_id, native_tag_id, video_tag_id, or rewarded_video_tag_id).'
};

const createCustomSchema = (additionalFields = {}) => {
  return Joi.object({
    ...CommonCustomFields,
    ...CustomTagFields,
    ...additionalFields
  })
    .custom((value, helpers) => {
      if (!value.banner_tag_id && !value.native_tag_id && !value.video_tag_id && !value.rewarded_video_tag_id) {
        return helpers.error('object.missing');
      }
      return value;
    })
    .messages(tagErrorMessage);
};

// 添加 Schema
export const AddCustomSchema = createCustomSchema({
  // bundle: Joi.string().required()
});
// 更新 Schema
export const UpdateCustomSchema = createCustomSchema({
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required()
});

const CommonStgSchema = {
  developer_website_domain: Joi.string(),
  bundle: Joi.string(),

  publisher_id: Joi.string().required()
};

export const AddStgSchema = Joi.object({
  ...CommonStgSchema,
  tnt_id: Joi.array().items(Joi.number().integer().required())
});

export const UpdateStgSchema = Joi.object({
  ...CommonStgSchema,
  tnt_id: Joi.number().integer().required(),
  id: Joi.number().integer().required(),
  status: Joi.number().integer().valid(1, 2).required()
});

export const AddIvtConfigSchema = Joi.object({
  tnt_id: Joi.number().integer().required(),
  seller_id: Joi.array().items(Joi.number()),
  buyer_id: Joi.array().items(Joi.number()),
  bundle: Joi.string().allow(''),
  type: Joi.number().integer().valid(1, 2).required(),
  ratio: Joi.number().integer().required().min(1).max(100)
});

export const UpdateIvtConfigSchema = Joi.object({
  id: Joi.number().integer().required(),
  seller_id: Joi.array().items(Joi.number()),
  buyer_id: Joi.array().items(Joi.number()),
  status: Joi.number().integer().valid(1, 2).required(),
  bundle: Joi.string().allow(''),
  country: Joi.array().items(Joi.number().valid(...Countries)),
  type: Joi.number().integer().valid(1, 2).required(),
  ratio: Joi.number().integer().required().min(1).max(100).required()
});
