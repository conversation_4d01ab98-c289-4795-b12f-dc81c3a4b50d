/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-01-12 11:37:32
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2024-01-12 11:37:36
 * @Description:
 */
import Joi from 'joi';

export const UpdateMenuSortSchema = Joi.object({
  list: Joi.array().items(Joi.object({
    id: Joi.number().integer().required(),
    sort: Joi.number().integer().min(0).required(),
    pid: Joi.number().integer().required()
  }))
});
