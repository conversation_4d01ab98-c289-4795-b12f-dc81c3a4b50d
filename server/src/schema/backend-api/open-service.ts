import { OpenServiceAPI } from '@/types/open-service';
import <PERSON><PERSON> from 'joi';

export const BeOpenServiceScheme = Joi.object({
  account_name: Joi.string().required(),
  password: Joi.string().required(),
  cs_domain: Joi.string().required()
});

export const BeOpenServiceRegisterScheme = Joi.object<OpenServiceAPI.RegisterUserParams>({
  // 平台的唯一注册key，便于后续对平台的数据请求
  ext_uid: Joi.string().required(),
  email: Joi.string().email({ tlds: false }).required(),
  company: Joi.string().required(),
  contact: Joi.string().required(),
  phone: Joi.string().required()
});

export const BeOpenServiceGetSupplyScheme = Joi.object({
  cs_domain: Joi.string().required()
});
