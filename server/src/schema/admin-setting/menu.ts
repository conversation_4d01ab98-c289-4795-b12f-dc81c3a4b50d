/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-20 17:52:48
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-21 14:40:22
 * @Description:
 */

import Joi from 'joi';

export const DeleteMenuSchema = Joi.object({
  id: Joi.number().integer().required()
});

const CommonMenuSchema = {
  title: Joi.string().required(),
  remark: Joi.string().min(0),
  access: Joi.string().min(0),
  pid: Joi.number().integer(),
  type: Joi.valid(1, 2).required()
};

export const AddMenuSchema = Joi.object({
  ...CommonMenuSchema
});

export const EditMenuSchema = Joi.object({
  ...CommonMenuSchema,
  id: Joi.number().integer().required()
});

export const UpdateMenuSortSchema = Joi.object({
  list: Joi.array().items(Joi.object({
    id: Joi.number().integer().required(),
    sort: Joi.number().integer().min(0).required(),
    pid: Joi.number().integer().required()
  }))
});
