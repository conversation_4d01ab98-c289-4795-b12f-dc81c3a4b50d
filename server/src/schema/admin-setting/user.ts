/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-11 15:13:59
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 10:07:24
 * @Description:
 */

import Jo<PERSON> from 'joi';

const password_reg = /^(?![^a-zA-Z]+$)(?!\D+$)/;
const email_reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;

const UserCommon = {
  display_name: Joi.string().required(),
  account_name: Joi.string().required(),
  remark: Joi.string().min(0),
  role_id: Joi.number().integer().required()
};

export const AddUserSchema = Joi.object({
  ...UserCommon,
  send_email: Joi.array().items(Joi.string().pattern(email_reg)).min(1).required()
});

export const EditUserSchema = Joi.object({
  ...<PERSON><PERSON><PERSON><PERSON><PERSON>,
  admin_id: Joi.number().integer().required(),
  status: Joi.number().valid(1, 2).required()
});

export const ResetPasswordSchema = Joi.object({
  password: Joi.string().min(6).max(25).pattern(password_reg)
    .required(),
  admin_id: Joi.number().integer().required(),
  send_email: Joi.array().items(Joi.string().pattern(email_reg)).min(1).required(),
  account_name: Joi.string().required()
});
