/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-20 17:54:20
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-20 19:22:39
 * @Description:
 */

/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-11 15:13:45
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-11 15:38:31
 * @Description:
 */

import Joi from 'joi';

export const AddRoleSchema = Joi.object({
  role_name: Joi.string().required(),
  remark: Joi.string().min(0)
});

export const EditRoleSchema = Joi.object({
  id: Joi.number().integer().required(),
  role_name: Joi.string().required(),
  remark: Joi.string().min(0)
});

export const EditRolePms = Joi.object({
  id: Joi.number().integer().required(),
  menus: Joi.array().items(Joi.number().integer()).min(1).required()
});
