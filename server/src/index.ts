/* *
 * @Author: yuan<PERSON>@algorix.co
 * @file:
 * @Date: 2019-03-28 20:41:41
 * @Last Modified by: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Last Modified time: 2022-11-23 18:01:02
 */
process.env.TZ = 'UTC';

import fs from 'fs';
import Koa from 'koa';
import koaBody from 'koa-body';
import compress from 'koa-compress';
import convert from 'koa-convert';
import session from 'koa-session';
import koaStatic from 'koa-static';
import staticCache from 'koa-static-cache';
import { historyApiFallback } from 'koa2-connect-history-api-fallback';
import path from 'path';
// 注册文件别名
import moduleAlias from 'module-alias';
import zlib from 'zlib';
// 文件路径别名需要放置前面
moduleAlias.addAlias('@', path.join(__dirname, './'));

import { getLogger, httpLogger } from './config/log4js';
import { sessionConfig } from './config/session';
import storage, { resolveStorage } from './db/localStorage';
import routers from './routers';
import { getConfig } from './utils';

const { port } = getConfig();

const app = new Koa();

app.use(resolveStorage({ whiteList: ['/api', '/backend-api'] }));

app.use(
  koaBody({
    formLimit: '10mb',
    jsonLimit: '10mb',
    textLimit: '10mb',
    multipart: true, // 是否支持 multipart-formdate 的表单
    formidable: {
      uploadDir: path.join(__dirname, '../upload/'),
      multiples: true,
      // keepExtensions: true,    // 保持文件的后缀
      maxFieldsSize: 30 * 1024 * 1024, // 文件上传大小
      onFileBegin: (formName, file: any) => {
        // 存放文件上传的路径
        if (!fs.existsSync(path.join(__dirname, '../upload'))) {
          fs.mkdirSync(path.join(__dirname, '../upload'));
        }
        const dirPath = path.join(__dirname, '../upload/') + file.name;
        // eslint-disable-next-line no-param-reassign
        file.path = dirPath;
      }
    }
  })
);

// compress
app.use(
  compress({
    threshold: 2048, // 2kb
    gzip: {
      flush: zlib.constants.Z_SYNC_FLUSH
    },
    deflate: {
      flush: zlib.constants.Z_SYNC_FLUSH
    },
    br: false // disable brotli
  })
);

// handle fallback for HTML5 history API
app.use(
  historyApiFallback({
    whiteList: ['/api']
  })
);

// 配置session中间件
app.keys = ['c7d06befb6954a07b4333c472b3e7807']; // 加密的钥匙
app.use(session(sessionConfig, app));

// 挂载log
app.use(async (ctx, next) => {
  const start = Date.now();

  const user_id = ctx.session ? ctx.session.admin_id : null;
  const tnt_id = ctx.session ? ctx.session.tnt_id : null;
  const session_id = ctx.session ? ctx.session.session_id : null;

  ctx.log = getLogger('app');
  await next();
  const operator = storage.getItem('user') || '';
  const responseTime = ((Date.now() - start) / 1000).toFixed(4);
  const msg = `user_id:${user_id}, tnt_id=${tnt_id},operator:${operator}, session:${session_id}, responseTime:${responseTime}s, requestUrl:${ctx.request.url}, message:${ctx.message}`;
  if (ctx.response.status !== 200) {
    httpLogger.error(msg);
  } else {
    httpLogger.info(msg);
  }
});

const files: staticCache.Files = {};
const filePath = './../webroot';
// 静态文件缓存
app.use(
  convert(
    staticCache(
      path.join(__dirname, filePath),
      {
        maxAge: 8 * 24 * 60 * 60,
        buffer: true,
        gzip: true,
        usePrecompiledGzip: true // use disk gzip first
      },
      files
    )
  )
);

// @ts-ignore
files['/index.html'].maxAge = 0;

// 配置静态资源访问中间件
app.use(convert(koaStatic(path.join(__dirname, filePath))));

// 初始化路由中间件
app.use(routers.api.routes());

app.use(routers.backendAPI.routes());

// 监听启动端口
app.listen(port);
console.log(`the server is start at port ${port}`);
console.log(`current environment ${process.env.NODE_ENV}`);
getLogger('app').info(`the server is start at port ${port}`);
getLogger('app').info(`current environment ${process.env.NODE_ENV}`);
// 未捕获的错误
process.on('uncaughtException', (err) => {
  console.error(err);
  getLogger('error').error(`uncaughtException ${err.message}`);
});
// 未捕获的错误
process.on('unhandledRejection', (reason, p) => {
  console.error('Unhandled Rejection at: ', p);
  getLogger('error').error(`unhandledRejection ${reason}`);
});
