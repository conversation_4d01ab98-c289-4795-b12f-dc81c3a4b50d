/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:21:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-12-28 11:31:04
 * @Description:
 */

export declare namespace UserAPI {
  type AdminListItem = {
    admin_id: number;
    account_name: string;
    password: string;
    role: number;
    role_id: number;
    role_type: number;
    display_name: string;
  };

  type UpdateCompanyUserParams = {
    display_name: string;
    admin_id: number;
  };

  type LoginParams = {
    account_name: string;
    password: string;
    cs_domain: string;
  };

  type ResetPasswordParams = {
    admin_id: number;
    old_password: string;
    new_password: string;
  };

  type ConfirmPasswordParams = {
    admin_id: number;
    old_password: string;
  };
}
