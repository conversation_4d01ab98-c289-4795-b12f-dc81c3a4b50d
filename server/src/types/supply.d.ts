/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 16:37:25
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 16:41:52
 * @Description:
 */

import { Context } from 'koa';
import { AppListAPI } from './app';

declare namespace SupplyAPI {
  type addSupplySqls = {
    addSeller: string;
    addProfit: string;
    addUser: string;
    addUserRole: string;
    getPermission?: string;
    addPermission?: string;
  };

  type SupplyListItem = {
    seller_id: number;
    seller_name: string;
    integration_type: number;
    status: number;
    create_time: string;
    update_time: string;
    status_desc: string;
    integration_type_desc: string;
    channel_type: string;
    device_type: string;
    relationship: string;
    cus_status: number;
  };

  type AddSupplyParams = {
    seller_name: string;
    seller_account_name: string;
    integration_type: number;
    status: number;
    channel_type: string;
    device_type: string;
    relationship: string;
    tnt_id: number;
    profit_model: number;
    profit_ratio: number;
    rev_share_ratio: number;
    cus_status: number;
    op_id: number;
    tagid_status: number;
    pass_nurl: string;
    pass_burl: string;
    pass_lurl: string;
    rev_track_type: number;
    banner_multi_size: number;
    crid_filter?: number;
    sp_id: number;
  };

  type UpdateSupplyParams = AddSupplyParams & {
    seller_id: number;
    profit_id: number;
    pass_nurl?: string;
    pass_burl?: string;
    pass_lurl: string;
    rev_track_type?: number;
    profitRatioChange?: boolean;
    user_id: number;
    win_rate_profit_ratio?: number;
    developer_traffic?: number;
  };

  interface Supply {
    getSupplyList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<SupplyListItem[]>;
    updateSupply(params: SupplyAPI.UpdateSupplyParams): Promise<boolean>;
  }

  interface SupplyModel {
    getSupplyList(
      tnt_id: number,
      cur_time_zone: string
    ): Promise<SupplyListItem[]>;

    updateSupply(params: SupplyAPI.UpdateSupplyParams): Promise<boolean>;
  }

  interface SupplyCtrlInterface {
    getSupplyList(ctx: Context): void;
  }

  type SellerDemandAuth = {
    buyer_id: number;
    level: number;
    pub_id: number;
    buyer_name: string;
    status: number;
    integration_type: number;
  };

  type SellerPlacement = AppListAPI.PlacementListItem & {
    demand_list: SellerDemandAuth[];
  };

  type SellerAppItem = AppListAPI.AppListItem & {
    demand_list: SellerDemandAuth[];
  };

  type SetSupplyAuthParams = {
    level: number;
    buyer_ids: number[];
    pub_id: number; // seller_id/app_id/xxx
    op_id: number;
    status: number;
    app_id: number;
    plm_id: number;
    tnt_id: number;
  };
}
