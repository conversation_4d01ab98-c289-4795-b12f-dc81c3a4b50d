/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-27 17:18:28
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 17:18:29
 * @Description:
 */
/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-19 14:36:48
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-11-13 14:38:29
 * @Description:
 */

export declare namespace QpsAPI {
  type QpsListItem = {
    id: number;
    seller_id: number;
    buyer_id: number;
    level: number;
    qps: number;
    status: number;
    op_id: number;
    seller_name: string;
    buyer_name: string;
    app_name: string;
    plm_name: string;
    create_time: string;
    update_time: string;
    account_status: number;
    region: number;
    bundle: string;
  };

  interface Qps {
    getQpsList(tnt_id: number, cur_time_zone: string): Promise<QpsListItem[]>;
  }
}
