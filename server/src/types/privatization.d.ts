import { Context } from 'koa';
import type { File } from 'formidable';
import { CommonAPI } from './common';
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-01-29 19:44:39
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-03 16:17:10
 * @Description:
 */
export declare namespace PrivateAPI {
  type BrandList = {
    id: number;
    brand_name: string;
    sj_domain: string;
    brand_logo_path?: string;
    brand_favicon_path?: string;
    created_time: string;
  };
  type AddBrand = {
    tnt_id: number;
    sj_domain: string;
    brand_name: string;
    brand_logo_path?: string;
    brand_favicon_path?: string;
  };

  interface BrandModel {
    isBrandExist(tnt_id: number): Promise<boolean>;
    getTenantBrandList(): Promise<BrandList[]>;
    addTenantBrand(params: AddBrand, defaultPxConfigString: string): Promise<any>;
    editTenantBrand(params: AddBrand): Promise<any>;
    uploadLogo(params: CommonAPI.UploadParams): Promise<any>;
    uploadFavicon(params: CommonAPI.UploadParams): Promise<any>;
    download(params: CommonAPI.DownloadParams): Promise<any>;
  }

  interface BrandService {
    getTenantBrandList(): Promise<BrandList[]>;
    addTenantBrand(params: AddBrand): Promise<any>;
    editTenantBrand(params: AddBrand): Promise<any>;
    uploadLogo(file: File): Promise<any>;
    uploadFavicon(file: File): Promise<any>;
    downloadLogo(params: CommonAPI.DownloadParams): Promise<any>;
    downloadFavicon(params: CommonAPI.DownloadParams): Promise<any>;
  }

  interface BrandController {
    getTenantBrandList(ctx: Context): void;
    addTenantBrand(ctx: Context): void;
    editTenantBrand(ctx: Context): void;
    upload(ctx: Context): void;
    download(ctx: Context): void;
  }

  type PixalateConfig = {
    use_rix_common: number;
    report_api?: string;
    report_api_key?: string;
    display_web_tag?: string;
    display_app_tag?: string;
    display_web_js?: string;
    display_app_js?: string;
    native_web_tag?: string;
    native_app_tag?: string;
    video_web_tag?: string;
    video_app_tag?: string;
  };

  type PixalateItem = {
    id: number;
    tnt_id: number;
    tnt_name: string;
    op_id: number;
    op_name: string;
    use_rix_common: number;
    pixalate_config: PixalateConfig;
  };

  type AddPixalateItem = {
    tnt_id: number;
    cur_admin_id: number;
  } & PixalateConfig;

  type EditPixalateItem = AddPixalateItem & {
    id: number;
  };
}
