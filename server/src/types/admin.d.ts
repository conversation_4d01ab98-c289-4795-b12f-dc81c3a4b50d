/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-21 17:09:06
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 17:11:52
 * @Description:
 */

export declare namespace AdminSettingAPI {

  type AddMenuParams = {
    title: string;
    pid: number;
    remark: string;
    access: string;
    type: number;
    id: number;
  }

  type UpdateMenuParams = AddMenuParams & {
    id: number;
  }

  type UpdateMenuItem = {
    id: number;
    sort: number;
    pid: number;
  }
  type UpdateMenuSortParams = {
    list: UpdateMenuItem[];
  }

  type AddRole = {
    role_name: string;
    remark: string;
  }

  type UpdateRole = {
    id: number;
    remark: string;
    role_name: string;
  }

  type UpdateRolePMS = {
    menus: number[];
    id: number;
  }

  type UpdateUserPassword = {
    admin_id: number;
    password: string;
    account_name: string;
    send_email: string[];
  }

  type AddUserParams = {
    account_name: string;
    password: string;
    display_name: string;
    send_email: string[];
    role_id: number;
    remark: string;
  };

  type EditUserParams = {
    admin_id: number;
    role_id: number;
    account_name: string;
    password: string;
    display_name: string;
    status: number;
    remark: string;
  };
}
