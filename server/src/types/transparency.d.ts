/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-01-25 11:26:32
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-25 19:29:04
 * @Description:
 */
import { Context } from 'koa';
export declare namespace TransparencyAPI {
  type UploadParams = {};
  interface SellerJsonCtrl {
    upload(ctx: Context): Promise<void>;
  }
  interface SellerJsonService {
    upload(params: any): Promise<any>;
  }
  interface SellerJsonModel {
    upload(params: UploadParams): Promise<any>;
  }

  interface UpdateSellerIdByChainIdParams {
    chain_id: string;
    seller_id: string;
  }

  interface UpdateTransparencyAppInfoParams {
    multi_aat_domain: string;
    developer_id: string;
  }

  interface SchainTruncationController {
    getSchainTruncationList(ctx: Context): Promise<any>;
    updateSchainTruncationConfig(ctx: Context): Promise<any>;
  }

  interface BatchCreateAppCrawlerParams {
    bundles: {
      app_bundle_id: string;
      platform: number;
    }[];
    op_user: string;
  }
}
