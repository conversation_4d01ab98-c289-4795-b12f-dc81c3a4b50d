/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 15:44:19
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-29 16:44:24
 * @Description:
 */

export declare namespace BillingAPI {
  type PublisherBillingListItem = {
    id: number;
    tnt_id: number | string;
    date: string;
    seller_id: number;
    buyer_id: number;
    seller: string;
    buyer: string;
    seller_net_revenue: number;
    buyer_net_revenue?: number;
    seller_payment_impression: number;
    impression: number;
    seller_total_request: number;
    seller_request: number;
    request?: number;
    profit: number;
    profit_rate: number;
    total_request: number;
    // partner_id?: {
    //   seller_partner_id?: number[];
    //   buyer_partner_id?: number[];
    // }[];
    partner_id?: number;
    partner_name?: string;
    pub_partner?: string;
  };
  type BillingListItem = {
    id: number;
    tnt_id: number | string;
    seller_id: number;
    buyer_id: number | string;
    buyer: string;
    seller: string;
    buyer_gross_ecpm: string;
    buyer_gross_revenue: string;
    buyer_net_ecpm: string | number;
    buyer_net_revenue: number;
    date: string;
    fill_rate: string;
    render_rate: string;
    impression: string;
    impression_rate: string;
    request: string;
    response: string;
    seller_gross_ecpm: number;
    seller_gross_revenue: number;
    seller_net_ecpm: number;
    seller_net_revenue: number;
    seller_payment_impression: number;
    total_request: string;
    win: string;
    win_rate: string;
    block_request: string;
    profit: number;
    profit_rate: number;
    ad_format: string;
    ad_size: string;
    ad_width: number;
    ad_height: number;
    country: string;
    partner_id: number;
    partner_name: string;
  };
  type AdvertiserParams = {
    tnt_id: number[];
    buyer_id: number[];
    seller_id: number[];
    columns: string[];
    start: number;
    start_date: string;
    end: number;
    end_date: string;
    order: string;
    order_key: string[];
    isAll: boolean;
    tz_start_date: string;
    tz_end_date: string;
    cur_time_zone: string;
    cur_condition_str: string;
    partner_id?: {
      seller_partner_id?: number[];
      buyer_partner_id?: number[];
    }[];
    months?: string[];
  };

  type PublisherParams = AdvertiserParams;

  type sqlValue = {
    conditions: string;
    dimension: string;
    outerDimension?: string;
    outerConditions?: string;
    groupBy: string;
    order: string;
    limit: string;
    table?: string;
    time_condition_str?: string;
    changeBuyer?: boolean;
  };

  type MonthlyReportItem = {
    id: number;
    month: string;
    buyer_net_revenue: number;
    seller_net_revenue: number;
    tnt_id: string | number;
    tnt_status?: number;
    seller_total_request: number;
    seller_request: number;
    buyer_request: number;
    seller_id?: number;
    buyer_id?: number;
    profit?: number;
    profit_rate?: number;
  };

}
