export declare namespace OpenServiceAPI {
  type RegisterUserParams = {
    ext_uid: string;
    email: string;
    company: string;
    contact: string;
    phone: string;
  };

  type SupplyData = {
    seller_id: number;
    seller_name: string;
    integration_type: number;
    relationship: number;
    token: string;
    status: number;
    publisher_id: string;
    host_prefix: string;
  }

  type WhiteBoxSupply = {
    seller_id: number;
    seller_name: string;
    host_prefix: string;
    token: string;
    endpoints: {
      use: string;
      apac: string;
      euw: string;
      global: string;
    }
  };
}
