/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-02-27 17:10:43
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-27 18:50:58
 * @Description:
 */
import { Context } from 'koa';
export declare namespace AppListAPI {
  type AppListItem = {
    app_id: number;
    app_name: string;
    seller_id: number;
    bundle: string;
    platform: number;
    store_url: string;
    category: string;
    screen_orientation: number;
    status: number;
    create_time: string;
    update_time: string;
  };

  type PlacementListItem = {
    plm_id: number;
    plm_name: string;
    app_id: number;
    ad_format: number;
    placement_type: number;
    ad_width: number;
    ad_height: number;
    price: number;
    bid_floor: number;
    assets: string;
    support_html: number;
    support_video: number;
    video_api: string;
    companionad: number;
    skip: number;
    maxduration: number;
    minduration: number;
    protocols: string;
    banner_api: string;
    pos: string;
    status: number;
    create_time: string;
    update_time: string;
  };
  interface AppModel {
    getAppList(): Promise<AppListItem[]>;
  }

  interface AppService {
    getAppList(): Promise<AppListItem[]>;
  }

  interface AppController {
    getAppList(ctx: Context): Promise<void>;
  }
}
