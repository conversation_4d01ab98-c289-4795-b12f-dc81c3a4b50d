/*
 * @Author: ch<PERSON><PERSON><PERSON> chenmu<PERSON>@algorix.co
 * @Date: 2022-12-05 11:24:47
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-28 11:44:16
 * @FilePath: /saas.rix-platform/server-ts/src/types/demand.d.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

export declare namespace DemandAPI {
  type DemandListItem = {
    buyer_id: number;
    buyer_name: string;
    integration_type: number;
    status: number;
    create_time: string;
    update_time: string;
    status_desc: string;
    integration_type_desc: string;
  };

  type AddDemandParams = {
    buyer_name: string;
    demand_account_name: string;
    integration_type: number;
    status: number;
    tnt_id: number;
    profit_model: number;
    profit_ratio: number;
    rev_share_ratio: number;
    profit_status: number;
    op_id: number;
    auction_type: number;
    imp_track_type: number;
    schain_required: number;
    idfa_required: number;
    filter_mraid: number;
    multi_format: number;
    banner_multi_size: number;
    banner_transfer_format: number;
    max_pxl_ivt_ratio: number;
    max_hm_ivt_ratio: number;
    dp_id: number;
    pass_display_manager: number;
    burl_track_type: number;
  };

  type UpdateDemandParams = AddDemandParams & {
    buyer_id: number;
    profit_id: number;
    imp_track_type: number;
    user_id: number;
    skoverlay: number;
    autostore: number;
    block_off_store_app: number;
    auto_qps: number;
    native_format: number;
    native_root_key: number;
  };
}
