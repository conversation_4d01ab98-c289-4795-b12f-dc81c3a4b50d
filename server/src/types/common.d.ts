/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:30:45
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-30 18:28:22
 * @Description:
 */

import { Context } from 'koa';

export declare namespace CommonAPI {
  type IntegrationTypeItem = {
    id: number;
    itg_name: string;
    itg_key: string;
    create_time: string;
    update_time: string;
  };
  type UploadParams = {
    fileName: string;
    filePath: string;
    bucket?: string;
    clearLocal?: boolean; // 是否清除本地文件，默认清除
    metadata?: Record<string, any>;
  };
  type DownloadParams = {
    fileName: string;
    bucket?: string;
  };
  interface Common {
    getBuyerIntegrationType(): Promise<IntegrationTypeItem[]>;
    getSellerIntegrationType(): Promise<IntegrationTypeItem[]>;
  }

  interface CommonCtrl {
    getBuyerIntegrationType(ctx: Context): void;
    getSellerIntegrationType(ctx: Context): void;
  }

  type DictParams = {
    /**
     * 字典类型
     */
    dict_type: string;
  };

  type DictItem = {
    /**
     * 字典类型
     */
    dict_type: string;
    /**
     * sql 语句
     */
    dict_content: string;
    /**
     * 是否从 aat 数据库获取数据
     */
    aat_dict_type?: boolean;
  };

  interface Dict {
    getDict(options: DictParams): Promise<DictItem[]>;
  }
}
