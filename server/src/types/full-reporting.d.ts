/*
 * @Author: 袁跃钊 <EMAIL>
 * @Date: 2024-02-27 15:56:50
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-29 11:50:46
 * @Description:
 */

export declare namespace FullReportingAPI {
  type PropertyToString<T> = {
    [K in keyof T]: string;
  };
  type DashboardListItem = {
    buyer_gross_ecpm: string | number;
    buyer_gross_revenue: string | number;
    buyer_net_ecpm: string | number;
    buyer_net_revenue: string | number;
    date: string | number;
    fill_rate: string | number;
    render_rate: string | number;
    impression: string | number;
    impression_rate: string | number;
    request: string | number;
    response: string | number;
    seller_gross_ecpm: string | number;
    seller_gross_revenue: string | number;
    seller_net_ecpm: string | number;
    seller_net_revenue: string | number;
    total_request: string | number;
    win: string | number;
    win_rate: string | number;
    block_request: string | number;
    out_request: string | number;
    click: string | number;
    total_seller_bid_floor: string | number;
    total_res_price: string | number;
    ecpr: string | number;
    adv_ecpr: string | number;
  };
  type FullReportListItem = DashboardListItem & {
    tnt: number | string;
    buyer: string;
    seller: string;
    app_name: string;
    app_bundle_id: string;
    buyer_id: number | string;
    seller_id: number | string;
    country: string;
    ad_format: string;
    ad_size: string;
    ad_width: number;
    ad_height: number;
    platform: string;
    region: string;
    adv_config_qps: number;
    pub_config_qps: number;
    inventory: number | string;
    http_code: number | string;
    internal_request: number | string;
  };
  type DashboardParams = {
    ad_size?: any;
    app_bundle_id?: any;
    platform?: any;
    buyer_id?: any;
    seller_id?: any;
    split_time?: any;
    start_date?: any;
    end_date?: any;
    start?: any;
    end?: any;
    columns?: any;
    metrics?: string[];
    download?: boolean;
    order_key: string[];
    tnt_id: number;
    order: string;
    type: number;
  };
  type GetListParams = {
    http_code?: string[];
    ad_size?: string | string[];
    app_bundle_id?: string | string[];
    app_name?: string[];
    platform?: number[];
    buyer_id?: number[];
    seller_id?: number[];
    split_time: number;
    start_date: string;
    end_date: string;
    dates?: {
      start: string;
      end: string;
    };
    start?: number;
    end?: number;
    columns?: string[];
    metrics: string[];
    download?: boolean;
    order_key: string[];
    tnt_id: number[];
    order: string;
    type: number;
    isDemand: boolean;
    cur_time_zone: string;
    cur_condition_str: string;
    tz_start_date: string;
    tz_end_date: string;
    partner_id?: {
      seller_partner_id?: number[];
      buyer_partner_id?: number[];
    }[];
  };
  type GetPublisherListParams = {
    app_bundle_id?: string | string[];
    platform?: number[];
    buyer_id?: number;
    seller_id?: number[];
    split_time: number;
    start_date: string;
    end_date: string;
    start?: number;
    end?: number;
    columns?: string[];
    metrics: string[];
    download?: boolean;
    order_key: string[];
    tnt_id: number;
    order: string;
    type: number;
    isDemand: boolean;
  };
  type ParseParams = {
    dimension: string;
    metric: string;
    table: string;
    conditions: string;
    group_by: string;
    order_by: string;
    limit: string;
    isDemand: boolean;
    isAll?: boolean;
    isUnion?: boolean;
    union_table?: {
      daily: string;
      big: string;
    };
    originMetrics?: string[];
    originDimension?: string[];
    union_conditions?: string;
    formData?: GetListParams;
  };

  type QueryPixalateReportParams = {
    buyer_id: number[];
    seller_id: number[];
    start_date: string;
    end_date: string;
    start: number;
    end: number;
    order_key: string[];
    dimension: string[];
    order: string;
    app_bundle_id: string[];
    tnt_id: number;
  };
  type QueryHumanReportParams = {
    buyer_id: number[];
    seller_id: number[];
    start_date: string;
    end_date: string;
    start: number;
    end: number;
    order_key: string[];
    dimension: string[];
    order: string;
    bundle: string[];
    domain: string[];
    tnt_id: number;
    metrics: string[];
  };

  type ExportedReportItem = {
    id: number;
    name: string;
    type: number;
    status: number;
    query_condition: string;
    create_time: string;
    url: string;
    err_msg: string;
    type_desc: string;
    status_desc: string;
  };
}
