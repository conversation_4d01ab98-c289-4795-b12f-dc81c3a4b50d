/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-10-20 15:34:51
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 15:18:00
 * @Description:
 */

import { Context } from 'koa';
export declare namespace ReportAPI {

  type QueryKwaiReportParams = {
    buyer_id: number[];
    seller_id: number[];
    start_date: string;
    end_date: string;
    start: number;
    end: number;
    order_key: string[];
    dimension: string[];
    order: string;
    app_bundle_id: string[];
    tnt_id: number;
  };

  type KwaiReportItem = {
    id: number;
    day: string;
    buyer_id: number;
    bundle: string;
    app_id: string;
    buyer: string;
    seller: string;
    date: string;
    request: number;
    response: number;
    revenue: number;
    impressions: number;
    click: number;
  };

  type QueryPixalateReportParams = {
    buyer_id: number[];
    seller_id: number[];
    start_date: string;
    end_date: string;
    start: number;
    end: number;
    order_key: string[];
    dimension: string[];
    order: string;
    app_bundle_id: string[];
    tnt_id: number[];
    tnt: number[];
  };
  type QueryHumanReportParams = {
    buyer_id: number[];
    seller_id: number[];
    start_date: string;
    end_date: string;
    start: number;
    end: number;
    order_key: string[];
    dimension: string[];
    order: string;
    bundle: string[];
    domain: string[];
    tnt_id: number[];
    tnt: number[];
    metrics: string[];
  };
}
