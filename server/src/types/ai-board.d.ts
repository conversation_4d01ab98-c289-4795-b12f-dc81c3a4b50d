/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-13 18:49:00
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-07 11:45:14
 * @Description:
 */

import { Context } from 'koa';
export declare namespace BoardAPI {
  type MetricsItem = {
    date: string;
    revenue: number;
    profit: number;
    request: number;
    ecpr: number;
  };
  type OverviewItem = {
    revenue: number | string;
    revenue_increase: number;
    profit: number | string;
    profit_increase: number;
    request: number | string;
    request_increase: number;
    ecpm: number | string;
    ecpm_increase: number;

    hours_data?: MetricsItem[];
    update_time: string;
  };

  type TopCountryItem = {
    tnt: number;
    country: string;
    revenue: number;
    sl_revenue: number;
    top_tnts: {
      tnt: string;
      revenue: number;
      country?: string;
    }[];
  };

  type SevenDaysCountryItem = {
    country: string;
    revenue: number;
    date: string;
  };

  type TopAdFormatItem = {
    ad_format: string;
    revenue: number;
    ad_size: string;
    top_ad_sizes: {
      ad_size: string;
      revenue: number;
    }[];
  };
  type CTVData = {
    date: string;
    revenue: number;
  };
  type TenantRevenueItem = {
    tnt_id: string;
    revenue: number;
  };
  type TopCountryEcpmAndEcprItem = {
    date: string;
    country: string;
    ecpm: number;
    ecpr: number;
    request: number;
    impression: number;
    revenue: number;
    ad_format: string;
  };

  interface EcpmAndEcprData {
    barData: Omit<
      BoardAPI.TopCountryEcpmAndEcprItem,
      'date' | 'request' | 'impression' | 'revenue'
    >[];
    lineData: {
      [key: string]: Omit<
        BoardAPI.TopCountryEcpmAndEcprItem,
        'country' | 'request' | 'impression' | 'revenue' | 'ad_format'
      >[];
    };
  }

}

export declare namespace SentMsgAPI {
  type SentMessageItem = {
    tnt_id: number;
    tnt_name: string;
    content: string;
    ext_1: string;
    op_id: number;
    op_name: string;
    mixed_key: string;
    create_time: string;
  };
  interface SentMsgModel {
    getAllSentMessage(): Promise<SentMessageItem[]>;
  }
  interface SentMsgService {
    getAllSentMessage(): Promise<SentMessageItem[]>;
  }
  interface SentMsgCtrl {
    getAllSentMessage(ctx: Context): void;
  }
}
