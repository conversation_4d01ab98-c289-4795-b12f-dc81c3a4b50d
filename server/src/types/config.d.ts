/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-19 14:45:16
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-03 17:39:06
 * @Description:
 */
import { Context } from 'koa';

export declare namespace ConfigAPI {
  type AddEcprParams = {
    type: number;
    server_region: number;
    filter_qps: number;
    newer_qps: number;
    tier_ecpr: { tags: string[] };
    tier_qps: { tags: string[] };
    op_id: number;
    status: number;
    tnt_id: number;
  };

  type EditEcprParams = {
    id: number;
    type: number;
    server_region: number;
    filter_qps: number;
    newer_qps: number;
    tier_ecpr: { tags: string[] };
    tier_qps: { tags: string[] };
    op_id: number;
    status: number;
    tnt_id: number;
  };

  type checkEcprParams = {
    tnt_id: number;
    server_region: number;
  };
  type DeleteEcprParams = {
    id: number;
    tnt_id: number;
  };

  type AddAtcParams = {
    seller_id: number[];
    buyer_id: number;
    tnt_id: number;
    region: string;
    country: string[];
    bundle: string[];
    ad_format: number[];
    ad_size: number[];
    expired: string;
    op_id: number;
  };

  type UpdateAtcParams = {
    id: number;
    country: string[];
    bundle: string[];
    ad_format: number[];
    ad_size: number[];
    expired: string;
    status: number;
    op_id: number;
    tnt_id: number;
  };

  type AddKwaParams = {
    buyer_id: number;
    tnt_id: number;
    bundle: string;
    app_id: string;
    native_pid: string;
    inters_pid: string;
    reward_pid: string;
    token: string;
    mixed_status: number;
    op_id: number;
  };

  type UpdateKwaParams = {
    tnt_id: number;
    id: number;
    app_id: string;
    native_pid: string;
    inters_pid: string;
    reward_pid: string;
    token: string;
    mixed_status: number;
    status: number;
    op_id: number;
  };

  type AddCustomParams = {
    buyer_id: number;
    tnt_id: number;
    bundle: string;
    app_id: string;
    banner_tag_id: string;
    native_tag_id: string;
    video_tag_id: string;
    rewarded_video_tag_id: string;
    op_id: number;
  };

  type UpdateCustomParams = AddCustomParams & {
    id: number;
    status: number;
  };

  type AddStgParams = {
    tnt_id: number;
    type: number;
    publisher_id: number;
    developer_website_domain: string;
    bundle: string;
    op_id: number;
    status: number;
  };

  type CheckStgParams = {
    isEdit?: boolean;
    id: number;
    tnt_id: number;
    developer_website_domain: string;
    bundle: string;
    type: number;
  };

  type UpdateStgParams = {
    id: number;
    tnt_id: number;
    publisher_id: number;
    developer_website_domain: string;
    bundle: string;
    op_id: number;
    status: number;
    type: number;
  };

  type AddIvtParams = {
    tnt_id: number;
    seller_id: number[];
    buyer_id: number[];
    op_id: number;
    status: number;
    buyer_id_str?: string;
    seller_id_str?: string;
    type: number;
    ratio: number;
    bundle?: string;
    country?: string[];
  };

  type UpdateIvtParams = {
    id: number;
    tnt_id: number;
    seller_id: number[];
    buyer_id: number[];
    op_id: number;
    status: number;
    buyer_id_str?: string;
    seller_id_str?: string;
    type: number;
    ratio: number;
    bundle?: string;
    country?: string[];
  };

  type CheckIvtParams = {
    id: number;
    tnt_id: number;
    seller_id?: number[];
    buyer_id?: string[];
    buyer_id_str?: string;
    seller_id_str?: string;
    bundle?: string;
    type: number;
  };

  type CheckPixlParams = {
    id: number;
    tnt_id: number;
    seller_id: number[];
    buyer_id: number[];
    seller_id_str?: string;
    buyer_id_str?: string;
  };

  type PixlPrebidListItem = {
    id: number;
    tnt_id: number;
    tnt_name: string;
    seller_id: number;
    buyer_id: number;
    seller_name: string;
    buyer_name: string;
    status: number;
    op_id: number;
    op_name: string;
    update_time: string;
  };

  type AddPixlParams = {
    tnt_id: number;
    seller_id: number[];
    buyer_id: number[];
    status: number;
    op_id: number;
  };

  type UpdatePixlParams = {
    id: number;
    tnt_id: number;
    seller_id: number;
    buyer_id: number;
    status: number;
    op_id: number;
  };

  interface EcprServices {
    isEcprConfigExist(params: checkEcprParams): Promise<any>;
    getEcprConfigList(): Promise<any>;
    addEcprConfig(params: AddEcprParams): Promise<Boolean>;
    editEcprConfig(params: EditEcprParams): Promise<Boolean>;
  }

  interface EcprModel {
    isEcprConfigExist(params: checkEcprParams): Promise<any>;
    getEcprConfigList(): Promise<any>;
    addEcprConfig(params: AddEcprParams): Promise<Boolean>;
    editEcprConfig(params: EditEcprParams): Promise<Boolean>;
  }

  interface EcprController {
    addEcprConfig(ctx: Context): Promise<any>;
    getEcprConfigList(ctx: Context): Promise<any>;
    editEcprConfig(ctx: Context): Promise<any>;
  }

  interface StgChainController {
    getStgChainList(ctx: Context): Promise<any>;
    addStgChain(ctx: Context): Promise<any>;
    updateStgChain(ctx: Context): Promise<any>;
  }

  interface StgChainServices {
    isStgChainExist(params: CheckStgParams): Promise<any>;
    getStgChainList(): Promise<any>;
    addStgChain(params: AddStgParams): Promise<Boolean>;
    updateStgChain(params: UpdateStgParams): Promise<Boolean>;
  }

  interface StgChainModel {
    isStgChainExist(params: CheckStgParams): Promise<any>;
    getStgChainList(): Promise<any>;
    addStgChain(params: AddStgParams): Promise<Boolean>;
    updateStgChain(params: UpdateStgParams): Promise<Boolean>;
  }

  interface IvtController {
    getIvtList(ctx: Context): Promise<any>;
    addIvt(ctx: Context): Promise<any>;
    updateIvt(ctx: Context): Promise<any>;
  }

  interface IvtServices {
    isIvtExist(params: any): Promise<any>;
    getIvtList(): Promise<any>;
    addIvt(params: any): Promise<Boolean>;
    updateIvt(params: any): Promise<Boolean>;
  }

  interface IvtModel {
    isIvtExist(params: any): Promise<any>;
    getIvtList(): Promise<any>;
    addIvt(params: any, shouldUpdateOnDuplicate: boolean): Promise<Boolean>;
    updateIvt(params: any): Promise<Boolean>;
  }

  interface PixlPrebidController {
    getPixlPrebidList(ctx: Context): Promise<void>;
    addPixlPrebid(ctx: Context): Promise<void>;
    updatePixlPrebid(pctx: Context): Promise<void>;
  }

  interface PixlPrebidServices {
    isPixlPrebidExist(params: any): Promise<{ count: number }>;
    getPixlPrebidList(): Promise<PixlPrebidListItem>;
    addPixlPrebid(params: any): Promise<boolean>;
    updatePixlPrebid(params: any): Promise<boolean>;
  }

  interface PixlPrebidModel {
    isPixlPrebidExist(params: CheckPixlParams): Promise<{ count: number }>;
    getPixlPrebidList(): Promise<PixlPrebidListItem>;
    addPixlPrebid(params: any): Promise<boolean>;
    updatePixlPrebid(params: any): Promise<boolean>;
  }
}
