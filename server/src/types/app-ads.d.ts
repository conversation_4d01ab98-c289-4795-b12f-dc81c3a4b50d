/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-19 18:03:59
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-19 18:46:36
 * @Description:
 */

export declare namespace AppAdsAPI {
  type QueryAppInfoParams = {
    bundle: string[];
    lines: string[];
  };

  type GetAppInfoItem = {
    bundle?: string;
    lines: string[];
  };
  type GetAppInfoParams = {
    params: { bundle?: string; lines?: string[] }[];
  };

  type GetAppInfoResultItem = {
    bundle: string;
    status_code: number;
    status_msg: string;
    lines: string[];
  };

  type GetAppInfoResult = {
    code: number;
    msg: string;
    data: GetAppInfoResultItem[];
  };
}
