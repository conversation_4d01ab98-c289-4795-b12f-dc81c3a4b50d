/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:29:16
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-08 16:30:26
 * @Description:
 */

export declare namespace TenantAPI {
  type UserListItem = {
    user_id: number;
    account_name: string;
    password: string;
    tnt_id?: number;
    status?: number;
    create_time?: string;
    update_time?: string;
    disabled_auth_sign?: string[];
    disabled_menu_path?: string[];
    api_list?: string[];
    pwd_expire_time?: string;
    access?: string[];
    role_list?: string[];
  };

  type addUserParams = {
    account_name: string;
    password: string;
    tnt_id: number;
    type: number;
    user_id: number;
    role_id: number;
    email: string;
  };
  type addUserSQLs = {
    addUser: string;
    addUserRole: string;
    getPermissio?: string;
    addPermission?: string;
  };
  type EditUserParams = {
    user_id: number;
    status: number;
    tnt_id: number;
    new_password?: string;
    account_name: string;
    role_id: number;
    type: number;
    isChangeName: boolean;
    email: string;
  };
  type EditUserSQLs = {
    getUserRole: string;
    addUserRole: string;
    updateUser: string;
    updateUserRole: string;
  };
  type TenantListItem = {
    tnt_id: number;
    tnt_name: string;
    token: string;
    host_prefix: string;
    cs_domain: string;
    status: number;
    email: string;
    create_time: string;
    update_time: string;
    pl_status: number;
    hm_status: number;
    company: string;
    contact: string;
    phone: string;
    brand: string;
    pv_domain: string;
  };
  type AddTenantParams = {
    tnt_id: number;
    tnt_name: string;
    token: string;
    host_prefix: string;
    cs_domain: string;
    email: string;
    company: string;
    contact: string;
    phone: string;
    brand: string;
    pl_status: number;
    hm_status: number;
    op_id: number;
    pv_domain: string;
  };
  type EditTenantParams = {
    tnt_id: number;
    tnt_name: string;
    token: string;
    host_prefix: string;
    cs_domain: string;
    status: number;
    email: string;
    new_password?: string;
    pl_status: number;
    hm_status: number;
    company: string;
    contact: string;
    phone: string;
    brand: string;
    pv_domain: string;
  };
  type EditTenantSQLs = {
    getUser: string;
    updateUser?: string;
    updateTenant: string;
  };
  type DeleteTenantParams = {
    tnt_id: number;
    tnt_name: string;
    host_prefix: string;
    email: string;
    cs_domain: string;
  };
  type IsExitTenantParams = {
    tnt_id: number;
    tnt_name: string;
    email: string;
    host_prefix: string;
  };

  type IsExitEmailParams = {
    email: string;
  };
}
