/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-27 18:50:01
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 18:51:37
 * @Description:
 */

import { AppListAPI } from '@/types/app';
import { appModel } from '@/models/developer/app';
class AppService implements AppListAPI.AppService {
  async getAppList(): Promise<AppListAPI.AppListItem[]> {
    return await appModel.getAppList();
  }
}

export const appService = new AppService();
