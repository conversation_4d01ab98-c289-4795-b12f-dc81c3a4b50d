/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-08 17:06:34
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 19:07:22
 * @Description:
 */
import { actModel } from '@/models';
import { ConfigAPI } from '@/types/config';

class AtcService {
  async addAtc(options: ConfigAPI.AddAtcParams) {
    return actModel.addAtc(options);
  }

  async updateAtc(options: ConfigAPI.UpdateAtcParams) {
    return actModel.updateAtc(options);
  }

  async getActList() {
    return await actModel.getActList();
  }

  async getSupplyList() {
    return await actModel.getSupplyList();
  }

  async getDemandList() {
    return await actModel.getDemandList();
  }
}

export const atcService = new AtcService();
