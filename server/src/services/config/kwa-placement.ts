/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-15 15:48:01
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-15 15:49:06
 * @Description:
 */

import { kwaModel } from '@/models';
import { ConfigAPI } from '@/types/config';

class KwaService {
  async addKwa(options: ConfigAPI.AddKwaParams) {
    return kwaModel.addKwa(options);
  }

  async updateKwa(options: ConfigAPI.UpdateKwaParams) {
    return kwaModel.updateKwa(options);
  }

  async isExistKwa(options: ConfigAPI.AddKwaParams) {
    const data = await kwaModel.isExistKwa(options);
    return data.length > 0;
  }

  async getKwaList() {
    return await kwaModel.getKwaList();
  }
}

export const kwaService = new KwaService();
