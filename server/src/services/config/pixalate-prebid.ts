/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-04-03 17:13:06
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-04-03 17:34:03
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-09-19 10:56:21
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2023-09-19 15:23:18
 * @Description:
 */

import { actModel, pixlPrebidModel } from '@/models';
import { ConfigAPI } from '@/types/config';
import { generateMap } from '@/utils';
class PixlPrebidServices implements ConfigAPI.PixlPrebidServices {
  async isPixlPrebidExist(params: ConfigAPI.CheckPixlParams) {
    return await pixlPrebidModel.isPixlPrebidExist(params);
  }

  async getPixlPrebidList() {
    const [data, sList, bList] = await Promise.all([
      pixlPrebidModel.getPixlPrebidList(),
      actModel.getSupplyList(),
      actModel.getDemandList()
    ]);
    const sMap = generateMap(sList, 'seller_id', 'seller_name');
    sMap['0'] = 'All Publishers';
    const bMap = generateMap(bList, 'buyer_id', 'buyer_name');
    const result = data.map((item: ConfigAPI.PixlPrebidListItem) => ({
      ...item,
      seller_name: sMap[`${item.seller_id}`] || '',
      buyer_name: bMap[`${item.buyer_id}`] || ''
    }));
    return result;
  }

  async addPixlPrebid(params: ConfigAPI.AddPixlParams) {
    return await pixlPrebidModel.addPixlPrebid(params);
  }

  async updatePixlPrebid(params: ConfigAPI.UpdatePixlParams) {
    return await pixlPrebidModel.updatePixlPrebid(params);
  }
}
export const pixlPrebidService = new PixlPrebidServices();
