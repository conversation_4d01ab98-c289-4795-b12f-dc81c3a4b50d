/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-05-18 20:54:53
 * @LastEditors: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 17:05:16
 * @Description:
 */
import { ecprModel } from '@/models';
import { ConfigAPI } from '@/types/config';
class EcprServices implements ConfigAPI.EcprServices {
  async isEcprConfigExist(params: any) {
    return await ecprModel.isEcprConfigExist(params);
  }

  async getEcprConfigList() {
    return await ecprModel.getEcprConfigList();
  }

  async addEcprConfig(params: ConfigAPI.AddEcprParams): Promise<Boolean> {
    return await ecprModel.addEcprConfig(params);
  }

  async editEcprConfig(params: ConfigAPI.EditEcprParams): Promise<Boolean> {
    return await ecprModel.editEcprConfig(params);
  }

  async deleteEcprConfig(params: ConfigAPI.DeleteEcprParams): Promise<Boolean> {
    return await ecprModel.deleteEcprConfig(params);
  }
}
export const ecprService = new EcprServices();
