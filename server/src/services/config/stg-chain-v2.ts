/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-09-19 10:56:21
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-09-19 15:23:18
 * @Description:
 */

import { stgModelV2 } from '@/models';
import { ConfigAPI } from '@/types/config';
class StgChainServicesV2 implements ConfigAPI.StgChainServices {
  async isStgChainExist(params: any) {
    return await stgModelV2.isStgChainExist(params);
  }

  async getStgChainList() {
    return await stgModelV2.getStgChainList();
  }

  async addStgChain(params: ConfigAPI.AddStgParams): Promise<Boolean> {
    return await stgModelV2.addStgChain(params);
  }

  async updateStgChain(params: ConfigAPI.UpdateStgParams): Promise<Boolean> {
    return await stgModelV2.updateStgChain(params);
  }
}
export const stgChainServiceV2 = new StgChainServicesV2();
