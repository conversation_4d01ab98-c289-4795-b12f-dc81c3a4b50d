/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-10-16 18:43:26
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-12-04 15:22:43
 * @Description:
 */
import { ivtModel } from '@/models';
import { ConfigAPI } from '@/types/config';

class IvtServices implements ConfigAPI.IvtServices {
  async isIvtExist(params: ConfigAPI.CheckIvtParams) {
    return await ivtModel.isIvtExist(params);
  }

  async getIvtList() {
    return await ivtModel.getIvtList();
  }

  async addIvt(params: ConfigAPI.AddIvtParams): Promise<Boolean> {
    return ivtModel.addIvt(params);
  }

  async updateIvt(params: ConfigAPI.UpdateIvtParams, isDuplicated?: boolean): Promise<Boolean> {
    return isDuplicated ? ivtModel.addIvt(params, true) : ivtModel.updateIvt(params);
  }
}
export const ivtService = new IvtServices();
