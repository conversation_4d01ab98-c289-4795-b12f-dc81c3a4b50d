/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-09-19 10:56:21
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-09-19 15:23:18
 * @Description:
 */

import { stgModel } from '@/models';
import { ConfigAPI } from '@/types/config';
class StgChainServices implements ConfigAPI.StgChainServices {
  async isStgChainExist(params: any) {
    return await stgModel.isStgChainExist(params);
  }

  async getStgChainList() {
    return await stgModel.getStgChainList();
  }

  async addStgChain(params: ConfigAPI.AddStgParams): Promise<Boolean> {
    return await stgModel.addStgChain(params);
  }

  async updateStgChain(params: ConfigAPI.UpdateStgParams): Promise<Boolean> {
    return await stgModel.updateStgChain(params);
  }
}
export const stgChainService = new StgChainServices();
