import { customModel } from '@/models';
import { ConfigAPI } from '@/types/config';

class CustomService {
  async addCustom(options: ConfigAPI.AddCustomParams) {
    return customModel.addCustom(options);
  }

  async updateCustom(options: ConfigAPI.UpdateCustomParams) {
    return customModel.updateCustom(options);
  }

  async getAppAndBundleList(options: ConfigAPI.AddKwaParams) {
    return customModel.getAppAndBundleList(options);
  }

  async getCustomList() {
    return await customModel.getCustomList();
  }
}

export const customService = new CustomService();
