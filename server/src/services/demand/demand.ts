/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON><EMAIL>
 * @Date: 2022-12-05 11:44:06
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-27 19:43:18
 * @FilePath: /saas.rix-platform/server-ts/src/services/demand/demand.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEmpo
 */
import { demandModel } from '@/models';
import { DemandAPI } from '@/types/demand';

class DemandService {
  async getDemandList(isTesting: boolean = false) {
    return await demandModel.getDemandList(isTesting);
  }

  async updateDemand(params: DemandAPI.UpdateDemandParams): Promise<boolean> {
    return await demandModel.updateDemand(params);
  }

  async getDemandEndpoint() {
    return await demandModel.getDemandEndpoint();
  }

  async getDemandAuth(buyer_id: number, tnt_id: number) {
    return await demandModel.getDemandAuth(buyer_id, tnt_id);
  }
}

export const demandService = new DemandService();
