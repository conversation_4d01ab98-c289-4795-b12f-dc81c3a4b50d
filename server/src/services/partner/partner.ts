/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-01-11 15:26:06
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-28 15:19:56
 * @Description:
 */

import { PartnerType } from '@/constants/partner';
import { partnerModel, demandModel, supplyModel } from '@/models';

class PartnerService {
  async getPartnerList() {
    const result = await Promise.all([
      partnerModel.getPartnerList(),
      demandModel.getPartnerDemand(),
      supplyModel.getPartnerSupply()
    ]);
    const data = Array.isArray(result[0]) ? result[0] : [];
    const demandPartner = Array.isArray(result[1]) ? result[1] : [];
    const supplyPartner = Array.isArray(result[2]) ? result[2] : [];
    const buyerObj: any = {};
    const sellerObj: any = {};
    demandPartner.forEach((v) => {
      if (!buyerObj[v.dp_id]) {
        buyerObj[v.dp_id] = [v];
      } else {
        buyerObj[v.dp_id].push(v);
      }
    });
    supplyPartner.forEach((v) => {
      if (!sellerObj[v.sp_id]) {
        sellerObj[v.sp_id] = [v];
      } else {
        sellerObj[v.sp_id].push(v);
      }
    });
    if (Array.isArray(data) && data.length) {
      return data.map((v) => {
        // eslint-disable-next-line no-nested-ternary
        const type = v.dp_id
          ? v.sp_id
            ? PartnerType['Supply & Demand']
            : PartnerType.Demand
          : PartnerType.Supply;

        const buyers = v.dp_id > 0 ? buyerObj[v.dp_id] : [];
        const sellers = v.sp_id > 0 ? sellerObj[v.sp_id] : [];
        return {
          ...v,
          type,
          buyers,
          sellers
        };
      });
    }
    return data;
  }
}

export const partnerService = new PartnerService();
