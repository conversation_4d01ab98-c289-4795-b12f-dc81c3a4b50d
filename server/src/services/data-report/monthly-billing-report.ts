import { IgnoreTnt } from '@/constants/data-report/monthly-report';
import { monthlyBillingModel, tenantModel } from '@/models';
import { BillingAPI } from '@/types/billing';
import { TenantAPI } from '@/types/tenant';
import { get1046MonthlyBillingReportSql, getCommonTenantMonthlyReportSql } from '@/utils/report/monthly';
import { LabelGenerationParams } from '@rixfe/rix-tools';
import moment from 'moment-timezone';

// 获取 tenant map
const getTenantMap = async (tenantList: TenantAPI.TenantListItem[]) => {
  const tntMap = new Map<number | string, { tnt_name?: string; tnt_status: number }>(
    tenantList.map((tenant) => [tenant.tnt_id, { tnt_name: tenant.tnt_name, tnt_status: tenant.status }])
  );

  const defaultTenants = {
    'Baidu Direct': { tnt_status: 1 },
    'Baidu Reseller': { tnt_status: 1 }
  };

  Object.entries(defaultTenants).forEach(([key, value]) => tntMap.set(key, value));

  return tntMap;
};

interface MonthlyBillingParams {
  tnt_id?: number[] | string[];
  tnt_status?: number;
  months?: string | string[];
  [key: string]: any;
}

class MonthlyBillingService {
  /**
   * 处理请求参数，标准化格式
   */
  private processRequestParams(
    params: MonthlyBillingParams,
    tenantList: TenantAPI.TenantListItem[]
  ): {
    processedParams: any;
    months: string[];
  } {
    const { tnt_status, ...restParams } = params;
    const { tnt_id, months: rawMonths = [] } = restParams;

    // 如果没有指定租户ID，则根据状态过滤租户
    if (!tnt_id?.length) {
      restParams.tnt_id = tenantList
        .filter((tenant) => !IgnoreTnt.includes(`${tenant.tnt_id}`) && (!tnt_status || tenant.status === tnt_status))
        .map((tenant) => tenant.tnt_id);
    }

    // 标准化月份格式为 YYYYMM
    const months = Array.isArray(rawMonths) ? rawMonths : [rawMonths];
    restParams.month = months.map((month) => moment(month, 'YYYYMMDD').startOf('months').format('YYYYMM'));

    return { processedParams: restParams, months };
  }

  /**
   * 处理 TeraBox 数据，分离直销和代理商数据
   */
  private processTeraBoxData(
    teraBoxData: BillingAPI.MonthlyReportItem[],
    directSids: any[]
  ): {
    directMap: Record<string, BillingAPI.MonthlyReportItem>;
    resellerMap: Record<string, BillingAPI.MonthlyReportItem>;
  } {
    const directMap: Record<string, BillingAPI.MonthlyReportItem> = {};
    const resellerMap: Record<string, BillingAPI.MonthlyReportItem> = {};

    if (Array.isArray(teraBoxData) && teraBoxData.length) {
      teraBoxData.forEach((item: BillingAPI.MonthlyReportItem) => {
        const {
          month: date,
          buyer_net_revenue = 0,
          seller_net_revenue = 0,
          seller_id,
          seller_request = 0,
          seller_total_request = 0,
          buyer_request = 0
        } = item;

        const isDirect = directSids.includes(seller_id);
        const map = isDirect ? directMap : resellerMap;
        const ter_tnt_id = isDirect ? 'Baidu Direct' : 'Baidu Reseller';

        if (!map[date]) {
          map[date] = {
            month: date,
            tnt_id: ter_tnt_id,
            seller_net_revenue: seller_net_revenue,
            seller_request: seller_request,
            seller_total_request: seller_total_request,
            buyer_request: buyer_request,
            buyer_net_revenue: buyer_net_revenue,
            id: 0 // 添加初始 id，后续会在 formatResultData 中重新分配
          };
        } else {
          map[date].seller_net_revenue += seller_net_revenue;
          map[date].seller_request += seller_request;
          map[date].seller_total_request += seller_total_request;
          map[date].buyer_net_revenue += buyer_net_revenue;
          map[date].buyer_request += buyer_request;
        }
      });
    }

    return { directMap, resellerMap };
  }

  /**
   * 格式化结果数据
   */
  private formatResultData(
    data: BillingAPI.MonthlyReportItem[],
    tntMap: Map<number | string, { tnt_name?: string; tnt_status: number }>,
    forcedStatus?: number
  ): BillingAPI.MonthlyReportItem[] {
    return data.map((item, index) => {
      const tnt_info = tntMap.get(item.tnt_id);
      const seller_net_revenue = +item.seller_net_revenue.toFixed(2);
      const buyer_net_revenue = +item.buyer_net_revenue.toFixed(2);
      const profit = buyer_net_revenue - seller_net_revenue;
      const profit_rate = buyer_net_revenue === 0 ? 0 : ((profit / buyer_net_revenue) * 100).toFixed(2);

      return {
        ...item,
        tnt_id: tnt_info?.tnt_name ? `${tnt_info.tnt_name}(${item.tnt_id})` : item.tnt_id,
        tnt_status: forcedStatus !== undefined ? forcedStatus : tnt_info?.tnt_status,
        buyer_net_revenue,
        seller_net_revenue,
        profit: +profit.toFixed(2),
        profit_rate: +profit_rate,
        id: index
      };
    });
  }

  /**
   * 获取月度账单列表
   */
  async getMonthlyBillingList(params: MonthlyBillingParams, labels: LabelGenerationParams): Promise<any> {
    // 获取租户列表
    const tenantList = (await tenantModel.getTenantList()) as TenantAPI.TenantListItem[];
    const tntMap = await getTenantMap(tenantList);

    // 处理请求参数
    const { processedParams } = this.processRequestParams(params, tenantList);

    // 获取SQL查询语句
    const sql = await getCommonTenantMonthlyReportSql(processedParams, labels);
    const sql1046 = await get1046MonthlyBillingReportSql(processedParams, labels);

    // 并行获取数据
    const [stdMonthlyData, teraboxData, directSids] = await Promise.all([
      monthlyBillingModel.getMonthlyBillingList(sql, labels),
      processedParams.tnt_id.includes(1046) && monthlyBillingModel.get1046Data(sql1046, labels),
      monthlyBillingModel.getDirectSeller()
    ]);

    // 处理TeraBox数据
    const { directMap, resellerMap } = this.processTeraBoxData(teraboxData || [], directSids);

    // 合并结果数据
    const result: BillingAPI.MonthlyReportItem[] = [
      ...Object.values<BillingAPI.MonthlyReportItem>(directMap),
      ...Object.values<BillingAPI.MonthlyReportItem>(resellerMap),
      ...stdMonthlyData.filter((item) => item.tnt_id !== 1046)
    ];

    // 格式化结果数据
    const formattedResult = this.formatResultData(result, tntMap);

    return {
      total: formattedResult?.length || 0,
      data: formattedResult?.length ? formattedResult : []
    };
  }

  /**
   * 获取已暂停租户的月度账单列表
   */
  async getPausedTenantMonthlyBillingList(params: MonthlyBillingParams, labels: LabelGenerationParams): Promise<any> {
    // 获取租户列表
    const tenantList = (await tenantModel.getTenantList()) as TenantAPI.TenantListItem[];
    const tntMap = await getTenantMap(tenantList);

    // 强制设置状态为暂停状态(2)
    const pausedParams = { ...params, tnt_status: 2 };

    // 处理请求参数
    const { processedParams } = this.processRequestParams(pausedParams, tenantList);

    // 如果没有暂停的租户，则返回空数组
    if (processedParams.tnt_id.length === 0) {
      return {
        total: 0,
        data: []
      };
    }

    // 获取SQL查询语句
    const sql = await getCommonTenantMonthlyReportSql(processedParams, labels);

    // 获取数据
    const stdMonthlyData = await monthlyBillingModel.getMonthlyBillingList(sql, labels);
    const result: BillingAPI.MonthlyReportItem[] = stdMonthlyData.filter((item) => item.tnt_id !== 1046);

    // 格式化结果数据，强制设置状态为暂停(2)
    const formattedResult = this.formatResultData(result, tntMap, 2);

    return {
      total: formattedResult?.length || 0,
      data: formattedResult?.length ? formattedResult : []
    };
  }
}

export const monthlyBillingService = new MonthlyBillingService();
