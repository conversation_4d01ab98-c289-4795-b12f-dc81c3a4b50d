/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-26 14:29:21
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-04-09 15:42:12
 * @Description:
 */
import { Code } from '@/codes';
import { getLogger } from '@/config/log4js';
import { AdFormatToLabel, MoblieOS, QpsLevel, RegionLabelMap, RegionType } from '@/constants/data-report/common-report';
import { ExportTypeMap, StatusTypeMap } from '@/constants/data-report/exported-report';
import { DeviceTypeMapDesc, HttpCodeDesc, InventoryMapDesc, RequestTypeMapDesc, SchainMap } from '@/constants/data-report/full-report';
import { demandModel, exportLogModel, fullReportModel, supplyModel, tenantModel } from '@/models';
import { FullReportingAPI } from '@/types/full-reporting';
import { QpsAPI } from '@/types/qps';
import { TenantAPI } from '@/types/tenant';
import { createDirectory } from '@/utils';
import { formatDailyReportParams, getDownTaskInfo, includeToday } from '@/utils/report';
import { generateDeviceBrandDimensionAndFilter } from '@/utils/report/device_brand';
import { updateBQConfigAdapter } from '@/utils/report/slim';
import {
  adaptGenerateBigQuerySQL,
  APIFrameworksMapByValue,
  concatSQLFragments,
  CountryMapByValue,
  LabelGenerationParams,
  transformOneRowSQL,
  transformSQLResult
} from '@rixfe/rix-tools';
import { stringify } from 'csv-stringify';
import eventStream from 'event-stream';
import fs from 'fs';
import moment from 'moment-timezone';

class DashboardService {
  async getDashboardList(option: any, labels: LabelGenerationParams) {
    const formatOption = formatDailyReportParams(option) as any;
    const { hour, metrics, split_time, start_date, end_date, cur_time_zone, tnt_id } = formatOption;
    if (!tnt_id || !tnt_id?.length) {
      const tenantList = await tenantModel.getTenantList();
      formatOption.tnt_id = tenantList.map((v: TenantAPI.TenantListItem) => v.tnt_id);
    }

    await updateBQConfigAdapter();
    const { fragments, info } = await adaptGenerateBigQuerySQL(formatOption, {
      api_url: labels.tag
    });

    const formattedFragments = await generateDeviceBrandDimensionAndFilter(formatOption, fragments);

    const sql = concatSQLFragments({
      ...formattedFragments,
      select: [...formattedFragments.select, 'count(*) over() as total']
    });

    // 判断表类型
    const isDailyTable = info.isDaily;
    const isDemandTable = info.isDemand;

    const [data, today_hours] = await Promise.all([
      // 查询列表数据
      fullReportModel.getNewDashboardList(sql, labels),
      // 对于非daily表单独查询今日小时数
      !isDailyTable
        ? fullReportModel.getNewHoursToday(formatOption.tnt_id, formattedFragments.from, formatOption.cur_time_zone, labels)
        : 0
    ]);

    // 处理查询结果
    const result = transformSQLResult(data, {
      rowProcessingOptions: {
        metrics: metrics || [],
        context: {
          start_date: start_date,
          end_date: end_date,
          today_hours,
          isDemand: isDemandTable,
          hour: hour,
          isToday: includeToday(start_date, end_date),
          split_time: split_time,
          timezone: cur_time_zone,
          isDemandReport: false,
          isSupplyReport: false
        }
      }
    });

    const total = result.length > 0 ? data[0].total : 0;
    return {
      total,
      data: result.length > 0 ? result : []
    };
  }

  // 下载全部报表
  async downloadAllReport(
    options: FullReportingAPI.GetListParams,
    user_id: number,
    labels: LabelGenerationParams
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const { columns, metrics, split_time, start_date, end_date, cur_time_zone, tnt_id, cur_condition_str } = options;

      if (!tnt_id || !tnt_id?.length) {
        const tenantList = await tenantModel.getTenantList();
        options.tnt_id = tenantList.map((v: TenantAPI.TenantListItem) => v.tnt_id);
      }

      const { csvName, requestPath, outputCsvPath, csvPath, headerCloumns } = getDownTaskInfo(
        cur_time_zone,
        options.tnt_id,
        columns || [],
        metrics,
        'Full_Reporting'
      );

      const exportLogRow = await exportLogModel.createExportLog(
        user_id,
        cur_condition_str,
        ExportTypeMap['Full Reporting'],
        csvName,
        requestPath
      );
      if (exportLogRow?.insertId) {
        const result = {
          code: Code.SUCCESS,
          name: csvName,
          type: ExportTypeMap['Full Reporting']
        };
        resolve(result);
      }

      await updateBQConfigAdapter();
      // 生成SQL片段
      const { fragments, info } = await adaptGenerateBigQuerySQL(options as any, {
        api_url: labels.tag
      });

      const formattedFragments = await generateDeviceBrandDimensionAndFilter(options, fragments);

      const sql = concatSQLFragments({
        ...formattedFragments,
        limit: '10000'
      });

      // 判断表类型
      const isDailyTable = info.isDaily;
      const isDemandTable = info.isDemand;

      const [data, today_hours] = await Promise.all([
        // 查询列表数据
        fullReportModel.newDownloadAllReport(sql, labels),
        // 对于非daily表单独查询今日小时数
        !isDailyTable
          ? fullReportModel.getNewHoursToday(options.tnt_id, formattedFragments.from, options.cur_time_zone, labels)
          : 0
      ]);

      if (data) {
        const [supplyList, demandList, qpsList, tenantList] = await Promise.all([
          supplyModel.getSupplyList(),
          demandModel.getDemandList(),
          this.getSupplyAndDemandConfigQps(options.tnt_id),
          tenantModel.getTenantList()
        ]);

        await createDirectory(outputCsvPath);
        const writeStream = fs.createWriteStream(outputCsvPath, 'utf-8');
        writeStream.on('open', () => {
          console.log('开始写入', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Write into csv,Path=[${outputCsvPath}],Current Time=[${moment().format('YYYY-MM-DD HH:mm:ss')}]`
          );
        });

        writeStream.on('error', async (err) => {
          if (exportLogRow?.insertId) {
            const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
            await exportLogModel.updateExportLog(sql);
          }
          getLogger('error').error(`wrtie into csv error,err=[${err.message}]`);
          reject({
            code: Code.ERROR_SYS,
            errmsg: err
          });
        });
        writeStream.on('finish', async () => {
          console.log('写入完成', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Close write stream,Path=[${outputCsvPath}],Current Time=[${moment().format('YYYY-MM-DD HH:mm:ss')}]`
          );
          const sql = `update export_log set status=${StatusTypeMap.Created}, path="${csvPath}" where id=${exportLogRow.insertId}`;
          await exportLogModel.updateExportLog(sql);
        });

        data
          .on('error', async (err: any) => {
            if (exportLogRow?.insertId) {
              const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
              await exportLogModel.updateExportLog(sql);
            }
            getLogger('error').error(`bigquery stream error, error=[${err.message}]`);
            data.end();
            reject({
              code: Code.ERROR_SYS,
              errmsg: err
            });
          })
          .on('end', () => {
            console.log('请求end', moment().format('YYYY-MM-DD HH:mm:ss'));
            data.end();
          })
          .pipe(
            eventStream.mapSync((data: any, index: number) => {
              const item = transformOneRowSQL(data, {
                metrics: metrics || [],
                context: {
                  start_date,
                  end_date,
                  today_hours,
                  isDemand: isDemandTable,
                  hour: undefined,
                  isToday: includeToday(start_date, end_date),
                  split_time,
                  timezone: cur_time_zone
                }
              });

              // ecpr,adv_ecpr 转为数字（sql中计算了，结果被bq处理成了字符串）
              item.ecpr = +item.ecpr;
              item.adv_ecpr = +item.adv_ecpr;

              if (item.buyer_id && Array.isArray(demandList)) {
                item.buyer = `${item.buyer_id}`;
                const buyer = demandList.find((v: any) => v.buyer_id === +item.buyer_id);
                if (buyer) {
                  item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
                }
              }
              if (item.seller_id && Array.isArray(supplyList)) {
                item.seller = `${item.seller_id}`;
                const seller = supplyList.find((v: { seller_id: number }) => v.seller_id === +item.seller_id);
                if (seller) {
                  item.seller = `${seller.seller_name}(${seller.seller_id})`;
                }
              }
              // TODO: 后续啊将 tnt 统一成 tnt_id 的逻辑
              if (item.tnt_id) {
                const tnt = tenantList?.find((v: { tnt_id: number }) => v.tnt_id === +item.tnt_id);
                item.tnt = tnt ? `${tnt.tnt_name}(${tnt.tnt_id})` : `${item.tnt_id}` || '-';
              }
              if (item.source_tenant && tenantList) {
                const sourceTenant = tenantList.find(
                  (v: { tnt_id: number }) => v.tnt_id === +(item.source_tenant || 0),
                );
                item.source_tenant = sourceTenant
                  ? `${sourceTenant.tnt_name}(${sourceTenant.tnt_id})`
                  : item.source_tenant;
              }
              item.res_api = APIFrameworksMapByValue[item.res_api] || '-';
              item.seller_schain_complete = SchainMap[item.seller_schain_complete] || '-';
              item.buyer_schain_complete = SchainMap[item.buyer_schain_complete] || '-';
              item.country = CountryMapByValue[item.country] || 'Unknown';
              item.ad_format = AdFormatToLabel[`${item.ad_format}`];
              item.ad_size = `${item.ad_width} * ${item.ad_height}`;
              item.platform = MoblieOS[item.platform];
              item.inventory = InventoryMapDesc[item.inventory as number];
              item.http_code = `${item.http_code}(${HttpCodeDesc[item.http_code as number]})`;
              item.internal_request = RequestTypeMapDesc[item.internal_request as number];
              let adv_config_qps = 0;
              let pub_config_qps = 0;
              const advQps = qpsList.filter(
                (qps: QpsAPI.QpsListItem) => qps.level === QpsLevel.demand && item.buyer_id === qps.buyer_id
              );
              const pubQps = qpsList.filter(
                (qps: QpsAPI.QpsListItem) => qps.level === QpsLevel.supply && item.seller_id === qps.seller_id
              );
              advQps.forEach((qps: QpsAPI.QpsListItem) => (adv_config_qps += qps.qps));
              pubQps.forEach((qps: QpsAPI.QpsListItem) => (pub_config_qps += qps.qps));

              if (item.region === RegionLabelMap.USE) {
                adv_config_qps = 0;
                pub_config_qps = 0;
                const useAdvQps = advQps.filter((item: QpsAPI.QpsListItem) => item.region === RegionType.USE);
                const usePubQps = pubQps.filter((item: QpsAPI.QpsListItem) => item.region === RegionType.USE);
                useAdvQps.forEach((qps: QpsAPI.QpsListItem) => (adv_config_qps += qps.qps));
                usePubQps.forEach((qps: QpsAPI.QpsListItem) => (pub_config_qps += qps.qps));
              }
              if (item.region === RegionLabelMap.APAC) {
                adv_config_qps = 0;
                pub_config_qps = 0;
                const apacAdvQps = advQps.filter((item: QpsAPI.QpsListItem) => item.region === RegionType.APAC);
                const apacPubQps = pubQps.filter((item: QpsAPI.QpsListItem) => item.region === RegionType.APAC);
                apacAdvQps.forEach((qps: QpsAPI.QpsListItem) => (adv_config_qps += qps.qps));
                apacPubQps.forEach((qps: QpsAPI.QpsListItem) => (pub_config_qps += qps.qps));
              }

              if (item.device_type) {
                item.device_type = DeviceTypeMapDesc[item.device_type as number];
              }

              item.adv_config_qps = adv_config_qps;
              item.pub_config_qps = pub_config_qps;

              return item;
            })
          )
          .pipe(
            stringify({
              header: true,
              columns: headerCloumns,
              encoding: 'utf-8',
              bom: true
            })
          )
          .pipe(writeStream);
      }
    });
  }

  async getConfigQps(tnt_id: number) {
    return await fullReportModel.getConfigQps(tnt_id);
  }

  async getSupplyAndDemandConfigQps(tnt_id: number[]) {
    return await fullReportModel.getSupplyAndDemandConfigQps(tnt_id);
  }
}

export const dashboardService = new DashboardService();
