/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-28 15:08:06
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-04 10:01:23
 * @Description:
 */
import { demandModel, exportLogModel, pubBillingModel, supplyModel, tenantModel } from '@/models';
import { stringify } from 'csv-stringify';
import eventStream from 'event-stream';
import fs from 'fs';
import moment from 'moment-timezone';
// ?constants
import { Code } from '@/codes';
import { getLogger } from '@/config/log4js';
import { PublisherBillingMetrics } from '@/constants/data-report/billing-report';
import { ExportTypeMap, StatusTypeMap } from '@/constants/data-report/exported-report';
import { BillingAPI } from '@/types/billing';
import { DemandAPI } from '@/types/demand';
import { SupplyAPI } from '@/types/supply';
import { TenantAPI } from '@/types/tenant';
import { createDirectory } from '@/utils';
import { getDownTaskInfo } from '@/utils/report';
import { queryBQDataWithCache, updateBQConfigAdapter } from '@/utils/report/slim';
import {
  adaptGenerateBigQuerySQL,
  buildMultiTableQuery,
  concatSQLFragments,
  CountryMapByValue,
  LabelGenerationParams,
  SQLFragments,
  transformOneRowSQL,
  transformSQLResult
} from '@rixfe/rix-tools';
import { AdFormatToLabel } from '@/constants/data-report/common-report';

class PubBillingService {
  async getPublisherBillingList(params: any, labels: LabelGenerationParams): Promise<any> {
    const { tnt_id } = params;
    if (!tnt_id || !tnt_id?.length) {
      const tenantList = await tenantModel.getTenantList();
      params.tnt_id = tenantList.map((v: TenantAPI.TenantListItem) => v.tnt_id);
    }
    const sql = await this.initSQL(params, false, labels);
    const data = await queryBQDataWithCache(sql, labels);

    const result = transformSQLResult(data, {
      rowProcessingOptions: {
        metrics: ['profit', 'profit_rate'],
        context: {
          start_date: params.start_date,
          end_date: params.end_date
        }
      }
    });

    const total = result.length > 0 ? data[0].total : 0;
    return { total, data: result };
  }

  async initSQL(params: any, isAll: boolean, labels: LabelGenerationParams) {
    const { columns = [], partner_id = [], tnt_id = [] } = params;
    // 固定的指标，和 PublisherBillingMetrics 不同
    const FIXED_METRICS = [
      'request',
      'response',
      'impression',
      'seller_payment_impression',
      'total_request',
      'seller_net_revenue',
      'buyer_net_revenue',
      'profit',
      'profit_rate'
    ];
    await updateBQConfigAdapter();
    // 生成SQL片段
    const { fragments } = await adaptGenerateBigQuerySQL(
      {
        ...params,
        metrics: FIXED_METRICS
      },
      { api_url: labels.tag }
    );

    // 如果存在查询 ad_format, country, app_bundle_id 维度，则需要添加 IS NOT NULL 的判断条件
    const conditions = ['ad_format', 'country', 'app_bundle_id'];
    conditions.forEach((column) => {
      if (columns.includes(column)) {
        fragments.where?.push(`${column} IS NOT NULL`);
      }
    });

    let multiTableQuery: SQLFragments = fragments;
    const partner_id_columns = columns.includes('partner_id') ? ['partner_id', 'partner_name'] : [];
    const where_columns = partner_id.length > 0 ? [`partner_id in (${partner_id.join(',')})`] : [];
    const sellerPartnerTable = concatSQLFragments({
      select: ['seller_id', ...partner_id_columns],
      from: '`saas-373106.saas_others.seller_partner`',
      where: [...where_columns, `tnt in (${tnt_id.join(',')})`],
      groupBy: ['seller_id', ...partner_id_columns]
    });
    const sellerPartnerFragments: SQLFragments = {
      select: partner_id_columns,
      from: `(${sellerPartnerTable})`,
      where: [],
      groupBy: partner_id_columns
    };

    multiTableQuery = buildMultiTableQuery(
      {
        fragments,
        alias: 'billing_overview'
      },
      [
        {
          table: {
            fragments: sellerPartnerFragments,
            alias: 'seller_partner'
          },
          type: 'INNER JOIN',
          on: 'billing_overview.seller_id = seller_partner.seller_id'
        }
      ]
    );

    return concatSQLFragments({
      ...multiTableQuery,
      select: [...multiTableQuery.select, 'count(*) over() as total'],
      // 如果isAll为true，则limit为10000，否则为fragments.limit
      limit: isAll ? '10000' : multiTableQuery.limit
    });
  }

  async downloadAllReport(
    params: BillingAPI.PublisherParams,
    user_id: number,
    labels: LabelGenerationParams
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const { columns, cur_time_zone, cur_condition_str, tnt_id } = params;
      const tenantList = await tenantModel.getTenantList();
      if (!tnt_id || !tnt_id?.length) {
        params.tnt_id = tenantList?.map((v: TenantAPI.TenantListItem) => v.tnt_id);
      }

      // 如果存在查询 ad_format, country, app_bundle_id 维度，需要移除固定指标 request, response, total_request
      const needRemoveMetrics = ['ad_format', 'country', 'app_bundle_id'].some((column) => columns.includes(column));
      const filterMetrics = PublisherBillingMetrics.filter((metric) => {
        if (needRemoveMetrics) {
          return !['request', 'response', 'total_request'].includes(metric);
        }
        return true;
      });

      const ori_condition = JSON.parse(cur_condition_str);
      ori_condition.metrics = filterMetrics;
      const dimensions = columns.map((v) => (v === 'partner_id' ? 'pub_partner_id' : v));
      const { csvName, requestPath, outputCsvPath, csvPath, headerCloumns } = getDownTaskInfo(
        cur_time_zone,
        tnt_id,
        dimensions,
        filterMetrics,
        'Publisher_Billing_Reporting'
      );
      const sql = await this.initSQL(params, true, labels);
      const [data, exportLogRow] = await Promise.all([
        pubBillingModel.downloadAllReport(sql, labels),
        exportLogModel.createExportLog(
          user_id,
          JSON.stringify(ori_condition),
          ExportTypeMap['Publisher Billing Reporting'],
          csvName,
          requestPath
        )
      ]);
      if (data) {
        const [supplyList, demandList] = await Promise.all([supplyModel.getSupplyList(), demandModel.getDemandList()]);
        await createDirectory(outputCsvPath);
        const writeStream = fs.createWriteStream(outputCsvPath, 'utf-8');
        writeStream.on('open', () => {
          console.log('开始写入', outputCsvPath, moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Write into csv,Path=[${outputCsvPath}],Current Time=[${moment().format('YYYY-MM-DD HH:mm:ss')}]`
          );
        });
        writeStream.on('error', async (err) => {
          if (exportLogRow?.insertId) {
            const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
            await exportLogModel.updateExportLog(sql);
          }
          getLogger('error').error(`wrtie into csv error,err=[${err.message}]`);
          reject({
            code: Code.ERROR_SYS,
            errmsg: err
          });
        });
        writeStream.on('finish', async () => {
          console.log('写入完成', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Close write stream,Path=[${outputCsvPath}],Current Time=[${moment().format('YYYY-MM-DD HH:mm:ss')}]`
          );
          const sql = `update export_log set status=${StatusTypeMap.Created}, path="${csvPath}" where id=${exportLogRow.insertId}`;
          await exportLogModel.updateExportLog(sql);
        });
        writeStream.on('pipe', () => {
          const result = {
            code: Code.SUCCESS,
            name: csvName,
            // url: requestPath
            type: ExportTypeMap['Publisher Billing Reporting']
          };
          resolve(result);
        });
        data
          .on('error', async (err) => {
            if (exportLogRow?.insertId) {
              const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
              await exportLogModel.updateExportLog(sql);
            }
            getLogger('error').error(`bigquery stream error, error=[${err.message}]`);
            data.end();
            reject({
              code: Code.ERROR_SYS,
              errmsg: err
            });
          })
          .on('end', () => {
            console.log('请求end', moment().format('YYYY-MM-DD HH:mm:ss'));
            data.end();
          })
          .pipe(
            eventStream.mapSync((item: BillingAPI.PublisherBillingListItem, index: number) => {
              const result = transformOneRowSQL(item, {
                metrics: ['profit', 'profit_rate'],
                context: {
                  start_date: params.start_date,
                  end_date: params.end_date,
                  today_hours: 0,
                  isDemand: false,
                  isSupplyReport: true
                }
              });

              const { seller_id, buyer_id, partner_id, partner_name, tnt_id } = item;
              const seller = supplyList.find((supply: SupplyAPI.SupplyListItem) => supply.seller_id === seller_id);
              const buyer = demandList.find((demand: DemandAPI.DemandListItem) => demand.buyer_id === buyer_id);
              result.pub_partner = partner_id ? `${partner_name}(${partner_id})` : '-';
              const tnt = tenantList.find((v: { tnt_id: number }) => v.tnt_id === +tnt_id);

              if (result.country) {
                result.country = CountryMapByValue[result.country] || 'Unknown';
              }
              if (result.ad_format) {
                result.ad_format = AdFormatToLabel[`${result.ad_format}`];
              }
              if (result.ad_width && result.ad_height) {
                result.ad_size = `${result.ad_width} * ${result.ad_height}`;
              }

              result.seller = seller ? `${seller?.seller_name}(${seller_id})` : `${seller_id}`;
              result.buyer = buyer ? `${buyer?.buyer_name}(${buyer_id})` : `${buyer_id}`;
              result.tnt_id = tnt ? `${tnt?.tnt_name}(${tnt?.tnt_id})` : `${tnt_id}`;
              return result;
            })
          )
          .pipe(
            stringify({
              header: true,
              columns: headerCloumns,
              encoding: 'utf-8',
              bom: true
            })
          )
          .pipe(writeStream);
      }
    });
  }
}

export const pubBillingService = new PubBillingService();
