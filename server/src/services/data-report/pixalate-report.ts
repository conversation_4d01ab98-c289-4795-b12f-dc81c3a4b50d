/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-28 15:09:17
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 16:33:47
 * @Description:
 */
import { pixalateModel } from '@/models';
import { joinQueries } from '@/utils/params';
import { PixalateDimensions, PixalateMetricOrderKey } from '@/constants/data-report/pixalate-report';
import { ReportAPI } from '@/types/report';
import { getOrderBy } from '@/utils/report';

class PixalateReport {
  getReportParams(formData: ReportAPI.QueryPixalateReportParams) {
    const { start_date, end_date, start, end, order, dimension = [], tnt_id = [] } = formData;
    let { order_key = [] } = formData;
    // eslint-disable-next-line no-param-reassign
    formData.tnt = tnt_id || [];
    let normal_dimension = PixalateDimensions.filter((v) => dimension.includes(v));
    normal_dimension = normal_dimension.map((v) => (v === 'tnt_id' ? 'tnt' : v));
    if (Array.isArray(order_key)) {
      order_key = order_key.map((v) => (v === 'tnt_id' ? 'tnt' : v));
      order_key = order_key.filter((v) => normal_dimension.includes(v) || PixalateMetricOrderKey.includes(v));
    }
    const condition = joinQueries(
      {
        numberCols: ['seller_id', 'buyer_id', 'tnt'],
        arrayQueries: ['seller_id', 'publisher_id', 'buyer_id', 'app_bundle_id', 'fraud_type', 'country', 'tnt'],
        extra: `day >= '${start_date}' and day <= '${end_date}'`
      },
      formData
    );
    // const day_col = `format_date('%F', PARSE_DATE('%Y%m%d',day)) as day`;
    const limit = `limit ${end - start} offset ${start}`;
    const order_str = getOrderBy(order_key, order, {
      isEmptyStr: true,
      isDefault: false
    });
    // const dimension_arr = normal_dimension.map((v) => (v === 'day' ? day_col : v));
    const dimension_arr = normal_dimension.map((v) => {
      if (v === 'month') {
        return `FORMAT_DATETIME('%Y-%m', PARSE_DATE('%Y%m%d', day)) AS month`;
      } else if (v === 'day') {
        return `format_date('%F', PARSE_DATE('%Y%m%d',day)) as day`;
      }
      return v;
    });
    return {
      limit,
      order: order_str,
      condition,
      normal_dimension_str: dimension_arr.join(','),
      dimension: normal_dimension.join(',')
    };
  }

  async getPixalateReport(option: any, api_url: string) {
    const result = await pixalateModel.getPixalateReport(option, api_url);
    let totalNum = 0;
    const data = result.map(({ total, ...rest }, index: number) => {
      if (index === 0) {
        totalNum = total;
      }
      if (rest.publisher_id === null) {
        rest.publisher_id = '-';
      }
      return { ...rest, id: index };
    });

    return {
      data,
      total: totalNum
    };
  }

  async downloadPixalateReport(options: any, api_url: string) {
    return await pixalateModel.downloadPixalateReport(options, api_url);
  }
}

export const pixalateService = new PixalateReport();
