/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-10-16 18:43:26
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-10-25 17:07:26
 * @Description:
 */
import { kwaiReportModel } from '@/models';
import { ReportAPI } from '@/types/report';

import { KwaiDimensions, KwaiMetricOrderKey } from '@/constants/data-report';
import { joinQueries } from '@/utils/params';
import { getOrderBy } from '@/utils/report';
class KwaiServices {
  getReportParams(formData: ReportAPI.QueryKwaiReportParams) {
    const {
      start_date,
      end_date,
      start,
      end,
      order,
      dimension = [],
      tnt_id
    } = formData;

    let { order_key = [] } = formData;
    const normal_dimension = KwaiDimensions.filter((v) =>
      dimension.includes(v)
    );
    if (Array.isArray(order_key)) {
      order_key = order_key.filter(
        (v) => normal_dimension.includes(v) || KwaiMetricOrderKey.includes(v)
      );
    }
    const condition = joinQueries(
      {
        numberCols: ['buyer_id', 'tnt_id'],
        arrayQueries: ['buyer_id', 'app_id', 'bundle', 'tnt_id'],
        extra: `day >= '${start_date}' and day <= '${end_date}'`
      },
      formData
    );
    const day_col = `format_date('%F', PARSE_DATE('%Y-%m-%d',day)) as day`;
    const limit = `limit ${end - start} offset ${start}`;
    const order_str = getOrderBy(order_key, order, {
      isEmptyStr: true,
      defaultOrderKey: 'revenue'
    });

    const dimension_arr = normal_dimension.map((v) => {
      if (v === 'day') {
        return day_col;
      }
      return v;
    });

    const splitDemand = normal_dimension.includes('buyer_id');
    return {
      limit,
      order: order_str,
      condition,
      normal_dimension_str: dimension_arr.join(','),
      dimension: normal_dimension.join(','),
      splitDemand
    };
  }

  async getKwaiReport(params: any, api_url: string) {
    console.log(params);
    const result = await Promise.all([
      kwaiReportModel.getKwaiReport(params, api_url),
      kwaiReportModel.countKwaiReport(params, api_url)
    ]);
    const data = result[0] || [];
    const resultData = data.map(
      (item: ReportAPI.KwaiReportItem, index: number) => ({
        ...item,
        id: index
      })
    );
    const total = (result[1] && result[1][0] && result[1][0].total) || 0;
    return {
      data: resultData,
      total: +total
    };
  }

  async downloadKwaiReport(params: any, api_url: string) {
    return kwaiReportModel.downloadKwaiReport(params, api_url);
  }
}
export const kwaiReportService = new KwaiServices();
