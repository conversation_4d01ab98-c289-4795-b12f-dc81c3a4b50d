/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-06-28 15:08:06
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-04 10:01:23
 * @Description:
 */
import { demandModel, exportLogModel, fullReportModel, supplyModel, tenantModel } from '@/models';
import { stringify } from 'csv-stringify';
import eventStream from 'event-stream';
import fs from 'fs';
import moment from 'moment-timezone';
// ?constants
import { Code } from '@/codes';
import { getLogger } from '@/config/log4js';
import { BuyerTagMap, DemandBlockStatusMap, SellerTagMap } from '@/constants/data-report/block-report';
import { ExportTypeMap, StatusTypeMap } from '@/constants/data-report/exported-report';
import { FullReportingAPI } from '@/types/full-reporting';
import { TenantAPI } from '@/types/tenant';
import { createDirectory } from '@/utils';
import { formatDailyReportParams, getDownTaskInfo } from '@/utils/report';
import { updateBQConfigAdapter } from '@/utils/report/slim';
import { adaptGenerateBigQuerySQL, concatSQLFragments, type LabelGenerationParams } from '@rixfe/rix-tools';

class AdvBlockService {
  async getAdvertiserBlockList(option: any, labels: LabelGenerationParams): Promise<any> {
    const formatOption = formatDailyReportParams(option) as any;
    const { tnt_id } = formatOption;
    if (!tnt_id || !tnt_id?.length) {
      const tenantList = await tenantModel.getTenantList();
      formatOption.tnt_id = tenantList.map((v: TenantAPI.TenantListItem) => v.tnt_id);
    }

    const sql = await this.initSQL(formatOption, false, labels);

    const data = await fullReportModel.getNewDashboardList(sql, labels);

    const total = data.length > 0 ? data[0].total : 0;
    return { total, data };
  }

  async initSQL(params: any, isAll: boolean, labels: LabelGenerationParams) {
    const { columns = [] } = params;
    // status 是字符串数组，数据库为整型，需要过滤
    if (params.status && Array.isArray(params.status) && params.status.length > 0) {
      // 过滤出合法的整数状态值
      const validStatusValues = params.status.filter((status: string) => Number.isInteger(+status));

      // 如果存在合法状态值则更新,否则删除status参数
      if (validStatusValues.length > 0) {
        params.status = validStatusValues;
      } else {
        delete params.status;
      }
    }
    // 判断 columns 是否包含 buyer_id，如果包含则需要将 buyer_id 加入到 columns 中的
    // 这是为了指定查询的表 demand 表
    const hasBuyerIdDimension = columns.includes('buyer_id');
    const queryColumns = hasBuyerIdDimension ? columns : [...columns, 'buyer_id'];
    await updateBQConfigAdapter({
      ExtraDefaultMetrics: [],
      IsUseDefaultSort: false,
      AdvertiserMetrics: ['out_request']
    });
    const { fragments } = await adaptGenerateBigQuerySQL(
      {
        ...params,
        columns: queryColumns
      },
      {
        api_url: labels.tag
      }
    );

    // 仅在原始columns不包含buyer_id时移除相关字段
    if (!hasBuyerIdDimension) {
      const removeBuyerId = (item: string) => !item.includes('buyer_id');

      fragments.select = fragments.select.filter(removeBuyerId);
      fragments.groupBy = fragments.groupBy?.filter(removeBuyerId);
      fragments.orderBy = fragments.orderBy?.filter(removeBuyerId);
    }

    return concatSQLFragments({
      ...fragments,
      select: [...fragments.select, 'count(*) over() as total'],
      limit: isAll ? '10000' : fragments.limit
    });
  }

  // 下载全部报表
  async downloadAllReport(
    options: FullReportingAPI.GetListParams,
    user_id: number,
    labels: LabelGenerationParams
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const formatOption = formatDailyReportParams(options) as any;
      const { tnt_id, cur_time_zone, columns, metrics, cur_condition_str } = formatOption;
      if (!tnt_id || !tnt_id?.length) {
        const tenantList = await tenantModel.getTenantList();
        options.tnt_id = tenantList.map((v: TenantAPI.TenantListItem) => v.tnt_id);
      }

      const { csvName, requestPath, outputCsvPath, csvPath, headerCloumns } = getDownTaskInfo(
        cur_time_zone,
        options.tnt_id,
        columns || [],
        metrics,
        'Advertiser_Block_Reporting'
      );

      const exportLogRow = await exportLogModel.createExportLog(
        user_id,
        cur_condition_str,
        ExportTypeMap['Advertiser Block Reporting'],
        csvName,
        requestPath
      );
      if (exportLogRow?.insertId) {
        const result = {
          code: Code.SUCCESS,
          name: csvName,
          type: ExportTypeMap['Advertiser Block Reporting']
        };
        resolve(result);
      }

      const sql = await this.initSQL(formatOption, true, labels);
      const data = await fullReportModel.newDownloadAllReport(sql, labels);

      if (data) {
        const [supplyList, demandList, tenantList] = await Promise.all([
          supplyModel.getSupplyList(),
          demandModel.getDemandList(),
          tenantModel.getTenantList()
        ]);

        await createDirectory(outputCsvPath);
        const writeStream = fs.createWriteStream(outputCsvPath, 'utf-8');
        writeStream.on('open', () => {
          console.log('开始写入', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Write into csv,Path=[${outputCsvPath}],Current Time=[${moment().format('YYYY-MM-DD HH:mm:ss')}]`
          );
        });

        writeStream.on('error', async (err) => {
          if (exportLogRow?.insertId) {
            const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
            await exportLogModel.updateExportLog(sql);
          }
          getLogger('error').error(`wrtie into csv error,err=[${err.message}]`);
          reject({
            code: Code.ERROR_SYS,
            errmsg: err
          });
        });
        writeStream.on('finish', async () => {
          console.log('写入完成', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Close write stream,Path=[${outputCsvPath}],Current Time=[${moment().format('YYYY-MM-DD HH:mm:ss')}]`
          );
          const sql = `update export_log set status=${StatusTypeMap.Created}, path="${csvPath}" where id=${exportLogRow.insertId}`;
          await exportLogModel.updateExportLog(sql);
        });

        data
          .on('error', async (err: any) => {
            if (exportLogRow?.insertId) {
              const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
              await exportLogModel.updateExportLog(sql);
            }
            getLogger('error').error(`bigquery stream error, error=[${err.message}]`);
            data.end();
            reject({
              code: Code.ERROR_SYS,
              errmsg: err
            });
          })
          .on('end', () => {
            console.log('请求end', moment().format('YYYY-MM-DD HH:mm:ss'));
            data.end();
          })
          .pipe(
            eventStream.mapSync((item: any) => {
              if (item.buyer_id && Array.isArray(demandList)) {
                item.buyer = `${item.buyer_id}`;
                const buyer = demandList.find((v: any) => v.buyer_id === +item.buyer_id);
                if (buyer) {
                  item.buyer = `${buyer.buyer_name}(${buyer.buyer_id})`;
                }
              }
              if (item.seller_id && Array.isArray(supplyList)) {
                item.seller = `${item.seller_id}`;
                const seller = supplyList.find((v: { seller_id: number }) => v.seller_id === +item.seller_id);
                if (seller) {
                  item.seller = `${seller.seller_name}(${seller.seller_id})`;
                }
              }
              if (item.tnt_id) {
                const tnt = tenantList?.find((v: { tnt_id: number }) => v.tnt_id === +item.tnt_id);
                item.tnt = tnt ? `${tnt.tnt_name}(${tnt.tnt_id})` : `${item.tnt_id}` || '-';
              }
              if (item.source_tenant && tenantList) {
                const sourceTenant = tenantList.find(
                  (v: { tnt_id: number }) => v.tnt_id === +(item.source_tenant || 0)
                );
                item.source_tenant = sourceTenant
                  ? `${sourceTenant.tnt_name}(${sourceTenant.tnt_id})`
                  : item.source_tenant;
              }

              if (item.status) {
                item.status = DemandBlockStatusMap[item.status]?.code || item.status;
              }
              if (item.seller_tag) {
                item.seller_tag = SellerTagMap[item.seller_tag]?.code || item.seller_tag;
              }
              if (item.buyer_tag) {
                item.buyer_tag = BuyerTagMap[item.buyer_tag]?.code || item.buyer_tag;
              }

              return item;
            })
          )
          .pipe(
            stringify({
              header: true,
              columns: headerCloumns,
              encoding: 'utf-8',
              bom: true
            })
          )
          .pipe(writeStream);
      }
    });
  }
}

export const advBlockService = new AdvBlockService();
