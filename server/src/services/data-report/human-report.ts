/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-28 15:05:34
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 16:35:23
 * @Description:
 */
import { humanModel } from '@/models';
import { joinQueries } from '@/utils/params';
import {
  HumanDefaultMetrics,
  HumanDimensions,
  METRICS_2_SQL
} from '@/constants/data-report/human-report';
import { ReportAPI } from '@/types/report';
import { getOrderBy } from '@/utils/report';

class HumanReport {
  getReportParams(formData: ReportAPI.QueryHumanReportParams) {
    const {
      start_date,
      end_date,
      start,
      end,
      order,
      dimension = [],
      metrics = HumanDefaultMetrics,
      tnt_id = []
    } = formData;
    let { order_key = [] } = formData;
    formData.tnt = tnt_id || [];
    let normal_dimension = HumanDimensions.filter((v) => dimension.includes(v));
    normal_dimension = normal_dimension.map((v) => (v === 'tnt_id' ? 'tnt' : v));
    if (Array.isArray(order_key)) {
      order_key = order_key.map((v) => (v === 'tnt_id' ? 'tnt' : v));
      order_key = order_key.filter(
        (v) => normal_dimension.includes(v) || metrics.includes(v)
      );
    }
    const condition = joinQueries(
      {
        numberCols: ['seller_id', 'buyer_id', 'tnt'],
        arrayQueries: ['seller_id', 'buyer_id', 'bundle', 'domain', 'tnt'],
        extra: `day >= '${start_date}' and day <= '${end_date}'`
      },
      formData
    );
    const limit = `limit ${end - start} offset ${start}`;
    const order_str = getOrderBy(order_key, order, {
      isEmptyStr: true,
      isDefault: false,
    });
    const dimension_arr = normal_dimension.map((v) => {
      if (v === 'month') {
        return `FORMAT_DATETIME('%Y-%m', PARSE_DATE('%Y%m%d', day)) AS month`;
      } else if (v === 'day') {
        return `format_date('%F', PARSE_DATE('%Y%m%d',day)) as day`;
      }
      return v;
    });
    const metrics_arr = metrics.map((v) => (METRICS_2_SQL as any)[v]);
    return {
      limit,
      order: order_str,
      condition,
      normal_dimension_str: dimension_arr.join(','),
      metrics_str: metrics_arr.join(','),
      dimension: normal_dimension.join(',')
    };
  }

  async getHumanReport(option: any, api_url: string) {
    const result = await humanModel.getHumanReport(option, api_url);
    let totalNum = 0;
    const data = (result || []).map(({ total, ...rest }, index) => {
      if (index === 0) {
        totalNum = total;
      }
      return { ...rest, id: index };
    });

    return {
      data,
      total: totalNum
    };
  }

  async downloadHumanReport(options: any, api_url: string) {
    return await humanModel.downloadHumanReport(options, api_url);
  }
}

export const humanService = new HumanReport();
