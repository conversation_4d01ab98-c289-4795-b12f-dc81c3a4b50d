/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-27 16:02:10
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 16:26:01
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-06-28 15:08:06
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-04 11:47:22
 * @Description:
 */
import { Code } from '@/codes';
import { getLogger } from '@/config/log4js';
import { AdvertiserBillingMetrics } from '@/constants/data-report/billing-report';
import { AdFormatToLabel } from '@/constants/data-report/common-report';
import { ExportTypeMap, StatusTypeMap } from '@/constants/data-report/exported-report';
import { advBillingModel, demandModel, exportLogModel, supplyModel, tenantModel } from '@/models';
import { BillingAPI } from '@/types/billing';
import { DemandAPI } from '@/types/demand';
import { SupplyAPI } from '@/types/supply';
import { TenantAPI } from '@/types/tenant';
import { createDirectory } from '@/utils';
import { getDownTaskInfo } from '@/utils/report';
import { queryBQDataWithCache, updateBQConfigAdapter } from '@/utils/report/slim';
import {
  adaptGenerateBigQuerySQL,
  buildMultiTableQuery,
  concatSQLFragments,
  CountryMapByValue,
  LabelGenerationParams,
  SQLFragments,
  transformOneRowSQL,
  transformSQLResult
} from '@rixfe/rix-tools';
import { stringify } from 'csv-stringify';
import eventStream from 'event-stream';
import fs from 'fs';
import moment from 'moment-timezone';

class AdvBillingService {
  async getAdvertiserBillingList(params: any, labels: LabelGenerationParams): Promise<any> {
    const { tnt_id } = params;
    if (!tnt_id || !tnt_id?.length) {
      const tenantList = await tenantModel.getTenantList();
      params.tnt_id = tenantList.map((v: TenantAPI.TenantListItem) => v.tnt_id);
    }
    const sql = await this.initSQL(params, false, labels);
    const data = await queryBQDataWithCache(sql, labels);

    const result = transformSQLResult(data, {
      rowProcessingOptions: {
        // billing 报表没有单独计算的指标
        metrics: [],
        context: {
          start_date: params.start_date,
          end_date: params.end_date
        }
      }
    });

    const total = result.length > 0 ? data[0].total : 0;
    return { total, data: result };
  }

  async initSQL(params: any, isAll: boolean, labels: LabelGenerationParams) {
    const { columns = [], partner_id = [], tnt_id = [] } = params;
    // 固定的指标
    const FIXED_METRICS = [...AdvertiserBillingMetrics];
    // 判断 columns 是否包含 buyer_id，如果包含则需要将 buyer_id 加入到 columns 中的
    // 这是为了指定查询的表 billing_demand_report
    const hasBuyerIdDimension = columns.includes('buyer_id');
    const queryColumns = hasBuyerIdDimension ? columns : [...columns, 'buyer_id'];
    await updateBQConfigAdapter();
    // 生成SQL片段
    const { fragments } = await adaptGenerateBigQuerySQL(
      {
        ...params,
        // 没有 buyer_request
        metrics: FIXED_METRICS,
        columns: queryColumns
      },
      { api_url: labels.tag }
    );
    // 如果存在查询 ad_format, country, app_bundle_id 维度，则需要添加 IS NOT NULL 的判断条件
    const conditions = ['ad_format', 'country', 'app_bundle_id'];
    conditions.forEach((column) => {
      if (columns.includes(column)) {
        fragments.where?.push(`${column} IS NOT NULL`);
      }
    });

    // 仅在原始columns不包含buyer_id时移除相关字段
    if (!hasBuyerIdDimension) {
      const removeBuyerId = (item: string) => !item.includes('buyer_id');

      fragments.select = fragments.select.filter(removeBuyerId);
      fragments.groupBy = fragments.groupBy?.filter(removeBuyerId);
      fragments.orderBy = fragments.orderBy?.filter(removeBuyerId);
    }

    let multiTableQuery: SQLFragments = fragments;
    // 如何构建买家伙伴关系表 buyerPartnerFragments
    // 1. 判断params.columns 是否存在 partner_id 则 select partner_id,partner_name，否则传[]
    // 2. group 和 select 结果一致
    // 3. where条件 判断params里是否存在 partner_id数字数组，并拼接成 partner_id in (12106, 12104, 12100)
    const partner_id_columns = columns.includes('partner_id') ? ['partner_id', 'partner_name'] : [];
    const where_columns = partner_id.length > 0 ? [`partner_id in (${partner_id.join(',')})`] : [];
    const buyerPartnerTable = concatSQLFragments({
      select: ['buyer_id', ...partner_id_columns],
      from: '`saas-373106.saas_others.buyer_partner`',
      where: [...where_columns, `tnt in (${tnt_id.join(',')})`],
      groupBy: ['buyer_id', ...partner_id_columns]
    });
    const buyerPartnerFragments: SQLFragments = {
      select: partner_id_columns,
      from: `(${buyerPartnerTable})`,
      where: [],
      groupBy: partner_id_columns
    };
    multiTableQuery = buildMultiTableQuery(
      {
        fragments,
        alias: 'billing_overview'
      },
      [
        {
          table: {
            fragments: buyerPartnerFragments,
            alias: 'buyer_partner'
          },
          type: 'INNER JOIN',
          on: 'billing_overview.buyer_id = buyer_partner.buyer_id'
        }
      ]
    );

    return concatSQLFragments({
      ...multiTableQuery,
      select: [...multiTableQuery.select, 'count(*) over() as total'],
      // 下载时限制10000条
      limit: isAll ? '10000' : multiTableQuery.limit
    });
  }

  async downloadAllReport(
    params: BillingAPI.AdvertiserParams,
    user_id: number,
    labels: LabelGenerationParams
  ): Promise<any> {
    return new Promise(async (resolve, reject) => {
      const { columns, cur_time_zone, tnt_id, cur_condition_str } = params;
      const tenantList = await tenantModel.getTenantList();
      if (!tnt_id || !tnt_id?.length) {
        params.tnt_id = tenantList?.map((v: TenantAPI.TenantListItem) => v.tnt_id);
      }
      // 如果存在查询 ad_format, country, app_bundle_id 维度，需要移除固定指标 request 和 response
      const needRemoveMetrics = ['ad_format', 'country', 'app_bundle_id'].some((column) => columns.includes(column));
      const filterMetrics = AdvertiserBillingMetrics.filter((metric) => {
        if (needRemoveMetrics) {
          return !['request', 'response'].includes(metric);
        }
        return true;
      });

      const ori_condition = JSON.parse(cur_condition_str);
      ori_condition.metrics = filterMetrics;
      const dimensions = columns.map((v) => (v === 'partner_id' ? 'adv_partner_id' : v));
      const { csvName, requestPath, outputCsvPath, csvPath, headerCloumns } = getDownTaskInfo(
        cur_time_zone,
        tnt_id,
        dimensions,
        filterMetrics,
        'Advertiser_Billing_Reporting'
      );

      // 由于表义不明，修改 headerCloumns 中 key 为 request 的 header 为 Out Request
      headerCloumns.forEach((column) => {
        if (column.key === 'request') {
          column.header = 'Out Request';
        }
      });

      const exportLogRow = await exportLogModel.createExportLog(
        user_id,
        JSON.stringify(ori_condition),
        ExportTypeMap['Advertiser Billing Reporting'],
        csvName,
        requestPath
      );
      if (exportLogRow?.insertId) {
        const result = {
          code: Code.SUCCESS,
          name: csvName,
          type: ExportTypeMap['Advertiser Billing Reporting']
        };
        resolve(result);
      }
      const sql = await this.initSQL(params, true, labels);
      const data = await advBillingModel.downloadAllReport(sql, labels);

      if (data) {
        const [demandList, supplyList] = await Promise.all([demandModel.getDemandList(), supplyModel.getSupplyList()]);
        await createDirectory(outputCsvPath);
        const writeStream = fs.createWriteStream(outputCsvPath, 'utf-8');
        writeStream.on('open', () => {
          console.log('开始写入', outputCsvPath, moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Write into csv,Path=[${outputCsvPath}],Current Time=[${moment().format('YYYY-MM-DD HH:mm:ss')}]`
          );
        });
        writeStream.on('error', async (err) => {
          if (exportLogRow?.insertId) {
            const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
            await exportLogModel.updateExportLog(sql);
          }
          getLogger('error').error(`wrtie into csv error,err=[${err.message}]`);
          reject({
            code: Code.ERROR_SYS,
            errmsg: err
          });
        });

        writeStream.on('finish', async () => {
          console.log('写入完成', moment().format('YYYY-MM-DD HH:mm:ss'));
          getLogger('app').info(
            `Close write stream,Path=[${outputCsvPath}],Current Time=[${moment().format('YYYY-MM-DD HH:mm:ss')}]`
          );
          const sql = `update export_log set status=${StatusTypeMap.Created}, path="${csvPath}" where id=${exportLogRow.insertId}`;
          await exportLogModel.updateExportLog(sql);
        });

        data
          .on('error', async (err) => {
            if (exportLogRow?.insertId) {
              const sql = `update export_log set status=${StatusTypeMap.Failed}, err_msg="${err.message}" where id=${exportLogRow.insertId}`;
              await exportLogModel.updateExportLog(sql);
            }
            getLogger('error').error(`bigquery stream error, error=[${err.message}]`);
            data.end();
            reject({
              code: Code.ERROR_SYS,
              errmsg: err
            });
          })
          .on('end', () => {
            console.log('请求end', moment().format('YYYY-MM-DD HH:mm:ss'));
            data.end();
          })
          .pipe(
            eventStream.mapSync((item: BillingAPI.BillingListItem) => {
              const result = transformOneRowSQL(item, {
                metrics: [],
                context: {
                  start_date: params.start_date,
                  end_date: params.end_date
                }
              });

              const { buyer_id, seller_id, tnt_id, partner_id, partner_name } = item;

              const buyer = demandList.find((demand: DemandAPI.DemandListItem) => demand.buyer_id === buyer_id);
              const seller = supplyList.find((demand: SupplyAPI.SupplyListItem) => demand.seller_id === seller_id);
              result.buyer = buyer ? `${buyer?.buyer_name}(${buyer_id})` : `${buyer_id}`;
              result.seller = seller ? `${seller?.seller_name}(${seller_id})` : `${seller_id}`;
              result.adv_partner = partner_id ? `${partner_name}(${partner_id})` : '-';

              if (result.country) {
                result.country = CountryMapByValue[result.country] || 'Unknown';
              }
              if (result.ad_format) {
                result.ad_format = AdFormatToLabel[`${result.ad_format}`];
              }
              if (result.ad_width && result.ad_height) {
                result.ad_size = `${result.ad_width} * ${result.ad_height}`;
              }

              if (tnt_id && tenantList) {
                const tnt = tenantList.find((v: { tnt_id: number }) => v.tnt_id === +tnt_id);
                result.tnt_id = `${tnt_id}` || '-';
                if (tnt) {
                  result.tnt_id = `${tnt.tnt_name}(${tnt_id})`;
                }
              }
              return result;
            })
          )
          .pipe(
            stringify({
              header: true,
              columns: headerCloumns,
              encoding: 'utf8',
              bom: true
            })
          )
          .pipe(writeStream);
      }
    });
  }
}

export const advBillingService = new AdvBillingService();
