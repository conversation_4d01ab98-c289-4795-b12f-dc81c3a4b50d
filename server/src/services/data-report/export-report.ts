/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><EMAIL>
 * @Date: 2023-11-17 18:45:26
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 17:30:19
 * @Description:
 */
import moment from 'moment-timezone';
import { exportLogModel } from '@/models';
import {
  ExportType,
  StatusMap,
  StatusTypeMap
} from '@/constants/data-report/exported-report';
import { FullReportingAPI } from '@/types/full-reporting';

class ExportLogService {
  async getExportLog(user_id: number, cur_time_zone: string) {
    const data = await exportLogModel.getExportLog(user_id, cur_time_zone);
    return data.map((v: FullReportingAPI.ExportedReportItem) => {
      const item = { ...v };
      item.type_desc = ExportType[v.type || 1];
      item.status_desc =
        item.status === 3 ? 'Created' : StatusMap[item.status || 1];
      item.url =
        item.status !== 4 ? `/api/exported-report/download/${item.name}` : '';
      return item;
    });
  }

  async getExportLogByName(name: string, user_id: number) {
    const data = await exportLogModel.getExportLogByName(name, user_id);
    if (Array.isArray(data) && data.length) {
      const { path, name } = data[0];
      return `${path}/${name}`;
    }
    return false;
  }

  async getExportTaskStatus(name: string, type: number) {
    const data = await exportLogModel.getExportLogByName(name, type);
    if (Array.isArray(data) && data.length) {
      const { status, name, create_time } = data[0];
      const diff_time = moment().diff(moment(create_time), 'minutes');
      if (status === StatusTypeMap.Created || diff_time < 15) {
        return {
          status,
          url:
            status === StatusTypeMap.Created
              ? `/api/exported-report/download/${name}`
              : '',
          name
        };
      }
    }
    return {
      status: StatusTypeMap.Failed,
      url: '',
      name: ''
    };
  }
}

export const exportLogService = new ExportLogService();
