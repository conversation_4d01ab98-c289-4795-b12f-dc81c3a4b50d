/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-08 15:22:05
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-08 15:24:44
 * @Description:
 */
import { SentMsgAPI } from '@/types/ai-board';
import { sentMsgModel } from '@/models';
class SentMsgService implements SentMsgAPI.SentMsgService {
  async getAllSentMessage(): Promise<SentMsgAPI.SentMessageItem[]> {
    return sentMsgModel.getAllSentMessage();
  }
}

export const sentMsgService = new SentMsgService();
