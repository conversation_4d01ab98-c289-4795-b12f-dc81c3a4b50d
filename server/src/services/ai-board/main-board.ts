/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-13 19:04:15
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-07 14:29:12
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { AdFormatMap } from '@/constants';
import { AdFormats } from '@/constants/config';
import { DateType } from '@/constants/data-report/full-report';
import { boardModel, tenantModel } from '@/models';
import { BoardAPI } from '@/types/ai-board';
import { TenantAPI } from '@/types/tenant';
import { formatNumberToUnit } from '@/utils';
import { updateBQConfigAdapter } from '@/utils/report/slim';
import {
  adaptGenerateBigQuerySQL,
  concatSQLFragments,
  LabelGenerationParams,
  transformSQLResult
} from '@rixfe/rix-tools';
import moment from 'moment';
// 过滤 tnt
export const FILTER_TNTS = [1047, 1048, 1057, 1066];

class BoardService {
  async getOverview(formData: any, label: LabelGenerationParams): Promise<BoardAPI.OverviewItem[]> {
    const { cache_cur_hour, cur_hour, cur_time_zone = 'Etc/UTC', ...restParams } = formData;

    const cur_new_hour = cur_hour || cache_cur_hour || '';

    const t_start_hour = moment(cur_new_hour).format('YYYY-MM-DD 00:00:00');
    const t_end_hour = cur_new_hour;

    const y_start_hour = moment(t_start_hour).subtract(1, 'days').format('YYYY-MM-DD HH:00:00');
    const y_end_hour = moment(t_end_hour).subtract(1, 'days').format('YYYY-MM-DD HH:00:00');

    const getQuerySql = async (
      start_date: string,
      end_date: string,
      params: {
        restParams: any;
        label: LabelGenerationParams;
      }
    ) => {
      const { restParams, label } = params;
      await updateBQConfigAdapter();
      const { fragments } = await adaptGenerateBigQuerySQL(
        {
          ...restParams,
          start_date,
          end_date,
          tz_start_date: start_date,
          tz_end_date: end_date,
          cur_time_zone,
          split_time: DateType.Hour,
          columns: ['day_hour'],
          metrics: ['profit', 'request', 'ecpr', 'impression'],
          order: 'asc',
          order_key: ['date']
        },
        { api_url: label.tag }
      );
      return concatSQLFragments({
        ...fragments,
        where: [...(fragments.where || []), `tnt_id not in (${FILTER_TNTS.join(',')})`],
        limit: ''
      });
    };

    // 获取今日和昨日的 SQL
    const today_sql = await getQuerySql(t_start_hour, t_end_hour, {
      restParams,
      label
    });
    const yesterday_sql = await getQuerySql(y_start_hour, y_end_hour, {
      restParams,
      label
    });

    const datas = await boardModel.getOverview({ today: today_sql, yesterday: yesterday_sql }, label);

    // 获取 SQL 结果 并处理
    const [today_data, yesterday_data] = datas.map((data) => {
      return transformSQLResult(data, {
        rowProcessingOptions: {
          metrics: ['profit', 'request', 'ecpr', 'impression'],
          context: {
            start_date: t_start_hour,
            end_date: t_end_hour,
            split_time: DateType.Hour,
            timezone: cur_time_zone
          }
        }
      });
    });

    const todayMap = today_data.reduce((prev: any, next: any) => {
      if (!prev) {
        return {
          request: +next.request || 0,
          revenue: +next.buyer_net_revenue || 0,
          profit: +next.profit || 0,
          impression: +next.impression || 0
        };
      }
      return {
        request: prev.request + (+next.request || 0),
        revenue: prev.revenue + (+next.buyer_net_revenue || 0),
        profit: prev.profit + (+next.profit || 0),
        impression: prev.impression + (+next.impression || 0)
      };
    }, null);

    const yesterdayMap = yesterday_data.reduce((prev: any, next: any) => {
      if (!prev) {
        return {
          request: +next.request || 0,
          revenue: +next.buyer_net_revenue || 0,
          profit: +next.profit || 0,
          impression: +next.impression || 0
        };
      }
      return {
        request: prev.request + (+next.request || 0),
        revenue: prev.revenue + (+next.buyer_net_revenue || 0),
        profit: prev.profit + (+next.profit || 0),
        impression: prev.impression + (+next.impression || 0)
      };
    }, null);

    const result: BoardAPI.OverviewItem[] = [];
    if (todayMap && yesterdayMap) {
      const todayRev: number = todayMap.revenue || 0;
      const todayProfit: number = todayMap.profit || 0;
      const todayReq: number = todayMap.request || 0;
      const todayImp: number = todayMap.impression || 0;
      // const todayEcpm: number = today.ecpm / (today_data?.length || 1) || 0;
      const todayEcpm = todayImp > 0 ? (todayRev / todayImp) * 1000 : 0;

      const yesRev: number = yesterdayMap.revenue || 0;
      const yesProfit: number = yesterdayMap.profit || 0;
      const yesReq: number = yesterdayMap.request || 0;
      const yesImp: number = yesterdayMap.impression || 0;
      // const yesEcpm: number =
      //   yesterday.ecpm / (yesterday_data?.length || 1) || 0;
      const yesEcpm = yesImp > 0 ? (yesRev / yesImp) * 1000 : 0;
      const revenue_increase: number = yesRev > 0 ? +(((todayRev - yesRev) / yesRev) * 100).toFixed(2) : 0;
      const profit_increase: number = yesProfit > 0 ? +(((todayProfit - yesProfit) / yesProfit) * 100).toFixed(2) : 0;
      const request_increase: number = yesReq > 0 ? +(((todayReq - yesReq) / yesReq) * 100).toFixed(2) : 0;

      const ecpm_increase: number = yesEcpm > 0 ? +(((todayEcpm - yesEcpm) / yesEcpm) * 100).toFixed(2) : 0;
      result.push({
        revenue: todayRev,
        revenue_increase,
        profit: todayProfit,
        profit_increase,
        request: formatNumberToUnit(todayReq),
        request_increase,
        ecpm: todayEcpm,
        ecpm_increase,
        hours_data: today_data,
        update_time: cur_hour
      });
    }
    return Promise.resolve(result);
  }

  async getTopCountry(
    cur_hour: string,
    label: LabelGenerationParams
  ): Promise<{
    data: BoardAPI.TopCountryItem[];
    profit: number;
  }> {
    const [topCountryData, tenantList] = await Promise.all([
      boardModel.getTopCountry(cur_hour, label),
      tenantModel.getTenantList()
    ]);

    const tenantMap = new Map();
    tenantList.forEach((item: TenantAPI.TenantListItem) => {
      tenantMap.set(item.tnt_id, `${item.tnt_name}(${item.tnt_id})`);
    });

    if (topCountryData?.length) {
      const topCtyObj: any = {}; // 按国家维度分组
      const topCtyTntObj: any = {}; // 国家下的top 5 tnt
      const topTntInAllCountry: any = {}; // 所有国家下的top 5 tnt
      topCountryData.forEach((v) => {
        if (!topCtyObj[v.country]) {
          topCtyObj[v.country] = {
            country: v.country,
            revenue: v.revenue,
            sl_revenue: v.sl_revenue
          };
        } else {
          topCtyObj[v.country] = {
            country: v.country,
            revenue: topCtyObj[v.country].revenue + v.revenue,
            sl_revenue: topCtyObj[v.country].sl_revenue + v.sl_revenue
          };
        }

        //  每个国家下的top 5 tnt
        if (!topCtyTntObj[v.country]) {
          topCtyTntObj[v.country] = [
            {
              ...v,
              tnt: tenantMap.get(v.tnt) || `(${v.tnt})`
            }
          ];
        } else if (topCtyTntObj[v.country].length < 5) {
          topCtyTntObj[v.country].push({
            ...v,
            tnt: tenantMap.get(v.tnt) || `(${v.tnt})`
          });
        }

        // 所有国家下的top 5 tnt
        if (!topTntInAllCountry[v.tnt]) {
          topTntInAllCountry[v.tnt] = {
            tnt: tenantMap.get(v.tnt) || `(${v.tnt})`,
            revenue: v.revenue
          };
        } else {
          topTntInAllCountry[v.tnt] = {
            tnt: tenantMap.get(v.tnt) || `(${v.tnt})`,
            revenue: topTntInAllCountry[v.tnt].revenue + v.revenue
          };
        }
      });

      // rev前五国家单独列出，其他国家合并为其他
      const topCountryResult: BoardAPI.TopCountryItem[] = Object.values<BoardAPI.TopCountryItem>(topCtyObj).reduce(
        (prev: any, next: any) => {
          if (prev.length < 5) {
            prev.push({
              country: next.country,
              revenue: +next.revenue,
              sl_revenue: +next.sl_revenue,
              top_tnts: topCtyTntObj[next.country]
            });
          } else if (!prev[5]) {
            prev[5] = next;
          } else {
            prev[5] = {
              country: 'Other',
              revenue: +prev[5].revenue + +next.revenue,
              sl_revenue: +prev[5].sl_revenue + +next.sl_revenue,
              // 合并其他国家的top 5 tnt,并将相同tnt合并
              top_tnts: [...(prev[5].top_tnts || []), ...topCtyTntObj[next.country]].reduce((prev: any, next: any) => {
                if (prev.length < 5) {
                  prev.push(next);
                } else {
                  const index = prev.findIndex((item: any) => item.tnt === next.tnt);
                  if (index > -1) {
                    prev[index].revenue += next.revenue;
                  } else {
                    prev.sort((a: any, b: any) => b.revenue - a.revenue);
                    prev.pop();
                    prev.push(next);
                  }
                }
                return prev;
              }, [])
            };
          }
          return prev;
        },
        []
      );

      // 第一次请求返回数据
      const profit = topCountryResult.reduce((prev: any, next: any) => {
        const p = next.revenue - next.sl_revenue;
        return prev + p;
      }, 0);

      const result = {
        data: topCountryResult,
        topTntInAllCountry: Object.values<BoardAPI.TopCountryItem>(topTntInAllCountry)
          .sort((a, b) => b.revenue - a.revenue)
          .splice(0, 5),
        profit: +profit
      };
      return Promise.resolve(result);
    }
    return Promise.resolve({ data: [], profit: 0 });
  }

  async getSevenDaysCountry(cur_hour: string, label: LabelGenerationParams) {
    const start_date = moment(cur_hour).subtract(7, 'days').format('YYYY-MM-DD');
    const end_date = moment().subtract(1, 'days').format('YYYY-MM-DD');
    const sevenDaysCountryData = await boardModel.getSevenDaysCountry(start_date, end_date, label);
    if (sevenDaysCountryData) {
      const topCountryData = sevenDaysCountryData.reduce((prev: any, next: any) => {
        if (!prev[next.country]) {
          prev[next.country] = {
            country: next.country,
            revenue: next.revenue
          };
        } else {
          prev[next.country] = {
            country: next.country,
            revenue: prev[next.country].revenue + next.revenue
          };
        }
        return prev;
      }, {});
      const top_countries_str = Object.keys(topCountryData).splice(0, 5);
      const result: {
        data: BoardAPI.SevenDaysCountryItem[];
        start_date: string;
        end_date: string;
        update_time: string;
      } = {
        data: [],
        start_date,
        end_date,
        update_time: cur_hour
      };
      if (sevenDaysCountryData) {
        result.data = sevenDaysCountryData.filter((item) => top_countries_str.includes(item.country));
      }
      // 第一次请求返回数据
      return Promise.resolve(result);
    }
    return Promise.resolve(false);
  }

  async getTopAdFormat(cur_hour: string, label: LabelGenerationParams) {
    const supplyList = await boardModel.getCTVSellers();
    const seller_ids_str = supplyList.map((item: any) => `${item.seller_id}`).join(',');
    const [topAdFormatData, ctvData] = await Promise.all([
      boardModel.getTopAdFormat(label),
      boardModel.getCTV(cur_hour, seller_ids_str, label)
    ]);
    const topAdFormatObj: any = {}; // 按ad_format维度分组
    topAdFormatData.forEach((item) => {
      if (!topAdFormatObj[item.ad_format]) {
        topAdFormatObj[item.ad_format] = {
          ad_format: item.ad_format,
          revenue: item.revenue,
          top_ad_sizes: [
            {
              ad_size: item.ad_size,
              revenue: item.revenue
            }
          ]
        };
      } else {
        topAdFormatObj[item.ad_format] = {
          ad_format: item.ad_format,
          revenue: topAdFormatObj[item.ad_format].revenue + item.revenue,
          top_ad_sizes: topAdFormatObj[item.ad_format].top_ad_sizes.concat([
            {
              ad_size: item.ad_size,
              revenue: item.revenue
            }
          ])
        };
      }
    });

    if (topAdFormatData) {
      const adFormatData = Object.values<BoardAPI.TopAdFormatItem>(topAdFormatObj)
        .sort((a, b) => b.revenue - a.revenue)
        .map((item) => ({
          ...item,
          revenue: +item.revenue.toFixed(2),
          ad_format: AdFormatMap[item.ad_format],
          top_ad_sizes: item.top_ad_sizes.splice(0, 3).map((item) => ({
            ...item,
            revenue: +item.revenue.toFixed(2)
          }))
        }));

      const result = {
        adFormatData,
        ctvData
      };

      // 第一次请求返回数据
      return Promise.resolve(result);
    }
    return Promise.resolve({ adFormatData: [], ctvData: [] });
  }

  async getAllTntRevenue(cur_hour: string, label: LabelGenerationParams): Promise<BoardAPI.TenantRevenueItem[]> {
    getLogger('app').info(`update getAllTntRevenue cache from gcp, cur_hour: ${cur_hour}`);
    const [allTntRevenueData, tenantList] = await Promise.all([
      boardModel.getAllTntRevenue(label),
      tenantModel.getTenantList()
    ]);
    if (tenantList) {
      const tenantMap = new Map();
      tenantList.forEach((item: TenantAPI.TenantListItem) => {
        tenantMap.set(item.tnt_id, `${item.tnt_name}(${item.tnt_id})`);
      });
      if (allTntRevenueData) {
        const result = allTntRevenueData.map((item) => ({
          ...item,
          tnt_id: tenantMap.get(item.tnt_id) || `(${item.tnt_id})`
        }));

        return Promise.resolve(result);
      }
    }
    return Promise.resolve([]);
  }

  async getTopCountryEcpmAndEcpr(cur_hour: string, label: LabelGenerationParams): Promise<BoardAPI.EcpmAndEcprData> {
    const [topCountryData, supplyList] = await Promise.all([
      boardModel.getTopCountry(cur_hour, label, true, true),
      boardModel.getCTVSellers()
    ]);
    const seller_ids_str = supplyList?.map((item: any) => `${item.seller_id}`).join(',') || '';
    if (topCountryData) {
      const top_countries_str = topCountryData.map((item) => `'${item.country}'`).join(',');

      const [topCountryEcpmAndEcprData, topCountryCTVData] = await Promise.all([
        boardModel.getTopCountryEcpmAndEcpr(cur_hour, top_countries_str, label),
        boardModel.getCTVEcpmAndEcpr(cur_hour, seller_ids_str, top_countries_str, label)
      ]);

      if (topCountryEcpmAndEcprData) {
        // 分组条形图的数据
        const barData: BoardAPI.EcpmAndEcprData['barData'] = [];
        // 每个ad_format七天的数据
        const lineData: BoardAPI.EcpmAndEcprData['lineData'] = {};

        AdFormats.forEach((ad_format) => {
          const data = topCountryEcpmAndEcprData.filter((item) => +item.ad_format === ad_format);
          if (data.length) {
            // 处理分组条形图的数据
            const mergedBarData = data.reduce((prev: any, entry) => {
              // 生成唯一的键，由 ad_format 和 country 组成
              const key = `${entry.ad_format}-${entry.country}`;
              const result = { ...prev };
              // ，累加 request, impression, revenue
              if (result[key]) {
                result[key].request += +entry.request;
                result[key].impression += +entry.impression;
                result[key].revenue += +entry.revenue;
              } else {
                // 如果键不存在，添加新的条目
                result[key] = {
                  ...entry,
                  request: +entry.request,
                  impression: +entry.impression,
                  revenue: +entry.revenue
                };
              }

              return result;
            }, {});
            // 处理折线图的数据
            const mergeLineData = data.reduce((prev: any, entry) => {
              const key = `${entry.ad_format}-${entry.date}-${entry.country}`;
              const result = { ...prev };
              // ，累加 request, impression, revenue
              if (result[key]) {
                result[key].request += +entry.request;
                result[key].impression += +entry.impression;
                result[key].revenue += +entry.revenue;
              } else {
                // 如果键不存在，添加新的条目
                result[key] = {
                  ...entry,
                  request: +entry.request,
                  impression: +entry.impression,
                  revenue: +entry.revenue
                };
              }
              return result;
            }, {});

            const mergedBarArray = Object.values<BoardAPI.TopCountryEcpmAndEcprItem>(mergedBarData);
            const barItems = mergedBarArray.map((item, index) => {
              const ecpr = +((item.revenue * 1000000) / item.request).toFixed(2);
              const ecpm = +((item.revenue * 1000) / item.impression).toFixed(2);
              return {
                country: item.country,
                ad_format: AdFormatMap[item.ad_format],
                ecpr: ecpr || 0,
                ecpm: ecpm || 0
              };
            });
            barData.push(...barItems);
            const mergedLineArray = Object.values<BoardAPI.TopCountryEcpmAndEcprItem>(mergeLineData);
            const lineItems = mergedLineArray.map((item, index) => {
              const ecpr = +((item.revenue * 1000000) / item.request).toFixed(2);
              const ecpm = +((item.revenue * 1000) / item.impression).toFixed(2);
              return {
                date: item.date,
                ecpr,
                ecpm,
                country: item.country
              };
            });
            lineData[AdFormatMap[ad_format]] = lineItems;
          }
        });
        const ctvBarData = topCountryCTVData.reduce((prev: any, entry) => {
          // 生成唯一的键，由 country 组成
          const key = `${entry.country}`;
          const result = { ...prev };
          // ，累加 request, impression, revenue
          if (result[key]) {
            result[key].request += +entry.request;
            result[key].impression += +entry.impression;
            result[key].revenue += +entry.revenue;
          } else {
            // 如果键不存在，添加新的条目
            result[key] = {
              ...entry,
              request: +entry.request,
              impression: +entry.impression,
              revenue: +entry.revenue
            };
          }

          return result;
        }, {});

        const ctvBarArray = Object.values<BoardAPI.TopCountryEcpmAndEcprItem>(ctvBarData);
        const ctvBarItems = ctvBarArray.map((item, index) => {
          const ecpr = +((item.revenue * 1000000) / item.request).toFixed(2);
          const ecpm = +((item.revenue * 1000) / item.impression).toFixed(2);
          return {
            country: item.country,
            ad_format: 'CTV',
            ecpr: ecpr || 0,
            ecpm: ecpm || 0
          };
        });

        const ctvLineData = topCountryCTVData.reduce((prev: any, entry) => {
          const key = `${entry.date}-${entry.country}`;
          const result = { ...prev };
          // ，累加 request, impression, revenue
          if (result[key]) {
            result[key].request += +entry.request;
            result[key].impression += +entry.impression;
            result[key].revenue += +entry.revenue;
          } else {
            // 如果键不存在，添加新的条目
            result[key] = {
              ...entry,
              request: +entry.request,
              impression: +entry.impression,
              revenue: +entry.revenue
            };
          }
          return result;
        }, {});

        const ctvLineArray = Object.values<BoardAPI.TopCountryEcpmAndEcprItem>(ctvLineData);
        const ctvLineItems = ctvLineArray.map((item, index) => {
          const ecpr = +((item.revenue * 1000000) / item.request).toFixed(2);
          const ecpm = +((item.revenue * 1000) / item.impression).toFixed(2);
          return {
            date: item.date,
            ecpr: ecpr || 0,
            ecpm: ecpm || 0,
            country: item.country
          };
        });
        lineData.CTV = ctvLineItems;
        const result = {
          barData,
          lineData,
          ctvBarData: ctvBarItems
        };

        // 第一次请求返回数据
        return Promise.resolve(result);
      }
    }
    return Promise.resolve({ barData: [], lineData: {} });
  }
}

export const boardService = new BoardService();
