/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:33:41
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-08 16:30:56
 * @Description:
 */

import { tntUserModel } from '@/models';
import { TenantAPI } from '@/types/tenant';

class UserService {
  async addOneUser(options: TenantAPI.addUserParams): Promise<any> {
    return await tntUserModel.addOneUser(options);
  }

  async getUserList(params: TenantAPI.UserListItem): Promise<TenantAPI.UserListItem[]> {
    return await tntUserModel.getUserList();
  }

  async getUserListCount(tnt_id: number): Promise<number> {
    return tntUserModel.getUserListCount();
  }

  async editUser(params: TenantAPI.EditUserParams): Promise<any> {
    return await tntUserModel.editUser(params);
  }

  async sendEmailToUser(params: any) {
    return await tntUserModel.sendEmailToUser(params);
  }

  async isAccountNameExists(params: TenantAPI.addUserParams, isEdit: boolean): Promise<any[]> {
    return await tntUserModel.isAccountNameExists(params, isEdit);
  }
}

export const tntUserService = new UserService();
