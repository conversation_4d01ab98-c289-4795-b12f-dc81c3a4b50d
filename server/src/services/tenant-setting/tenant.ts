/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:33:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-18 15:45:16
 * @Description:
 */

import { tenantModel } from '@/models';
import { TenantAPI } from '@/types/tenant';

class TenantService {
  async getTenantList() {
    return await tenantModel.getTenantList();
  }

  async addTenant(params: TenantAPI.AddTenantParams) {
    return tenantModel.addTenant(params);
  }

  async editTenant(params: TenantAPI.EditTenantParams) {
    return tenantModel.editTenant(params);
  }

  async deleteTenant(params: TenantAPI.DeleteTenantParams) {
    return await tenantModel.deleteTenant(params);
  }

  async isExitTenant(params: any, tnt_id?: number) {
    return await tenantModel.isExitTenant(params, tnt_id);
  }

  async isExitEmail(params: TenantAPI.IsExitEmailParams) {
    return tenantModel.isExitEmail(params);
  }
}

export const tenantService = new TenantService();
