/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 14:48:50
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-12 11:40:14
 * @Description:
 */
import { menuModel, roleModel } from '@/models';

import { LinkMenuType } from '@/constants/permission';
import { PermissionAPI } from '@/types/permission';
import { roleService } from './role';
import { buildSQLSetClause } from '@rixfe/rix-tools';
import { QueryParamType } from '@/db/mysql';

class MenuServices {
  async addMenu(params: any) {
    return await menuModel.addMenu(params);
  }

  async updateMenu(params: any) {
    const {
      title,
      path = '',
      pid = 0,
      remark = '',
      id,
      icon = '',
      is_hide = 1,
      component = '',
      access = '',
      menu_render = 2,
      interfaces = [],
      itf_change,
      type,
      menu_type = LinkMenuType.Ordinary,
      node_type
    } = params;

    const updateObj = buildSQLSetClause([
      ['title', title],
      ['type', type],
      ['path', path],
      ['pid', pid],
      ['remark', remark],
      ['menu_render', menu_render],
      ['icon', icon],
      ['is_hide', is_hide],
      ['component', component],
      ['access', access || ''],
      ['menu_type', menu_type],
      ['node_type', node_type || 1]
    ]);

    const sqls: QueryParamType[] = [
      {
        sql: 'update menu set ? where id=?',
        values: [updateObj, id]
      }
    ];

    if (itf_change) {
      // 删除所有对应interface
      sqls.push({
        sql: 'delete from interface_rl where rsc_id=?',
        values: [id]
      });

      if (interfaces && interfaces.length) {
        const values = interfaces.map((itf_id: any) => [type, itf_id, id]);
        sqls.push({
          sql: 'insert into interface_rl (type, itf_id, rsc_id) values ?',
          values: [values]
        });
      }
    }

    const data = await menuModel.updateMenu(sqls);
    if (data) {
      const roles: any[] = await roleModel.getRoleByMenuIds([params.id]);
      roleService.updateRolePmsByIds(roles);
    }
    return data;
  }

  async getAllMenu() {
    const menus = await menuModel.getAllMenuAndInterface();
    const data = menus.map((item: any) => {
      const interfaces = item.interfaces
        .split(',')
        .filter((v: string) => v.trim())
        .map((v: string) => +v);
      return {
        ...item,
        interfaces
      };
    });
    return data;
  }

  async isMenuExist(options: any) {
    return await menuModel.isMenuExist(options);
  }

  async deleteMenu(ids: number[]) {
    const data = await menuModel.deleteMenu(ids);
    if (data) {
      const roles: any[] = await roleModel.getRoleByMenuIds(ids);
      roleService.updateRolePmsByIds(roles);
    }
    return data;
  }

  async updateMenuSort(options: PermissionAPI.UpdateMenuParams) {
    const { list } = options;

    if (list.length === 0) {
      return false;
    }

    const caseWhen = [];
    const menuIds = [];
    const pidValues: any[] = [];
    const sortValues: any[] = [];

    for (const { id, pid, sort } of list) {
      pidValues.push(id, pid);
      sortValues.push(id, sort);
      caseWhen.push('WHEN ? THEN ?');
      menuIds.push(id);
    }

    const caseWhenStr = caseWhen.join(' ');

    const sql = `UPDATE menu SET pid = CASE id ${caseWhenStr} END, sort = CASE id ${caseWhenStr} END WHERE id IN (?)`;
    const values = [...pidValues, ...sortValues, menuIds];

    const flag = await menuModel.updateMenuSort({ sql, values });
    return flag;
  }
}

export const menuService = new MenuServices();
