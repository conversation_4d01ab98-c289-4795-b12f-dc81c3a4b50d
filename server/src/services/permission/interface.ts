/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chen<PERSON><PERSON>@algorix.co
 * @Date: 2023-03-07 16:53:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-06-14 11:01:45
 * @Description:
 */

import { interfaceModel, menuModel, roleModel } from '@/models';
import {
  InterfaceTypeToString,
  InterfaceOperationType,
  InterfaceType
} from '@/constants/permission';
import { getConfig } from '@/utils';
import { setRedisByKey } from '@/db/redis';
import { roleService } from './role';

const { redisConfig } = getConfig();

class InterfaceModel {
  async addInterface(options: any) {
    const data = await interfaceModel.addInterface(options);
    if (data && options.type !== InterfaceType.Normal) {
      this.handleRefreshGlobalApi();
    }
    return data;
  }

  // 设计到的菜单对应用户需要更改下
  async updateInterface(options: any) {
    const flag = await interfaceModel.updateInterface(options);
    if (flag) {
      // 刷新开放api权限
      this.handleRefreshGlobalApi();
      const menus: any[] = await menuModel.getMenuByInterface(options.id);
      const menu_ids = [...new Set(menus.map((v) => v.menu_id))];
      if (menu_ids.length) {
        // 通过菜单获取角色
        const roles: any[] = await roleModel.getRoleByMenuIds(menu_ids);
        // 更新角色的权限
        roleService.updateRolePmsByIds(roles);
      }
    }
    return flag;
  }

  async getAllInterface() {
    const data = await interfaceModel.getAllInterface();
    if (data && Array.isArray(data)) {
      return data.map((item) => ({
        ...item,
        type_desc: InterfaceTypeToString[item.type],
        op_type_desc: InterfaceOperationType[item.op_type]
      }));
    }
    return null;
  }

  async isRoutePathExist(options: any) {
    return await interfaceModel.isRoutePathExist(options);
  }

  // 删除没同步
  async deleteInterface(id: number) {
    return await interfaceModel.deleteInterface(id);
  }

  // 刷新全局api记录
  async handleRefreshGlobalApi() {
    const key = `${redisConfig.platform_key}_GLOBAL_API`;
    let apis = await interfaceModel.getGlobalInterface();
    apis = apis.filter((v: any) => v.path && v.path.trim());
    // 保存到库中
    await setRedisByKey(key, apis);
    return true;
  }
}

export const interfaceService = new InterfaceModel();
