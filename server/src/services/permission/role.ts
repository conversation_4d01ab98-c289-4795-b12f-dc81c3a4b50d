/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 14:55:25
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-10-20 19:00:05
 * @Description:
 */
import { interfaceModel, roleModel } from '@/models';
import { setRedisByKey } from '@/db/redis';
import { getConfig } from '@/utils';
import { MenuType } from '@/constants/platform-setting';
import { QueryParamType } from '@/db/mysql';
const { redisConfig } = getConfig();
class RoleServices {
  async addRole(options: any, operator: string) {
    return await roleModel.addRole(options, operator);
  }

  async isRoleExist(role_name: string) {
    return await roleModel.isRoleExist(role_name);
  }

  async getAllRoleAndPms() {
    let result = await roleModel.getAllRoleAndPms();
    if (Array.isArray(result) && result.length) {
      result = result.map((v) => {
        let pms_list = [];
        if (v.permissions) {
          pms_list = v.permissions.split(',').filter((t: string) => t.trim());
        }
        const obj = {
          ...v,
          pms_list
        };
        delete obj.permissions;
        return obj;
      });
    }
    return result;
  }

  async editRole(options: any) {
    const { id, permissions, pms_change } = options;

    if (!pms_change) {
      return true;
    }

    const sqls: QueryParamType[] = [];

    if (Array.isArray(permissions)) {
      sqls.push({
        sql: 'delete from role_permission_rl where role_id=? and tnt_id=0',
        values: [id]
      });
      if (permissions.length) {
        sqls.push({
          sql: 'insert into role_permission_rl (role_id, rsc_id, type,tnt_id) values ?',
          values: [permissions.map(({ rsc_id, type }: any) => [id, rsc_id, type, 0])]
        });
      }
    }
    const data = await roleModel.editRole(sqls);
    if (data) {
      const key = `${redisConfig.platform_key}_ROLE_PMS_${id}`;
      const menus = permissions.map((v: any) => v.rsc_id);
      const result = await Promise.all([
        menus.length ? interfaceModel.getInterfaceByMenu(menus) : [],
        roleModel.getMenuByRole(id)
      ]);
      const apis = Array.isArray(result[0]) ? result[0] : [];
      const pms = Array.isArray(result[1]) ? result[1] : [];

      const api_list = new Set<string>();
      for (const v of apis) {
        if (v.path && v.path.trim()) {
          api_list.add(v.path.trim());
        }
      }

      const menu_access = new Set<string>();
      const btn_access = new Set<string>();
      for (const { access, type } of pms) {
        if (access && access.trim()) {
          if (type === MenuType.Menu) {
            menu_access.add(access);
          } else if (type === MenuType.Operation) {
            btn_access.add(access);
          }
        }
      }

      const value = {
        btn_access: Array.from(btn_access),
        menu_access: Array.from(menu_access),
        api_list: Array.from(api_list)
      };
      await setRedisByKey(key, value);
    }
    return data;
  }

  async updateRole(options: any) {
    const { role_name, id } = options;

    const data = await roleModel.updateRole({
      sql: 'update role set role_name=? where id=? and tnt_id=0',
      values: [role_name, id]
    });
    return data;
  }

  // 批量更新角色的权限
  async updateRolePmsByIds(roles: { role_id: number; tnt_id: number }[]) {
    const role_ids = [...roles.map((v) => v.role_id)];
    const pms: any[] = await roleModel.getPmsByRoleId(role_ids);
    const all_menu_access = pms.filter((v) => v.type === MenuType.Menu).filter((v) => v.access && v.access.trim());
    const all_btn_access = pms.filter((v) => v.type === MenuType.Operation).filter((v) => v.access && v.access.trim());
    const all_menu_ids = [...new Set(pms.map((v) => v.menu_id))];
    let all_api_list: any[] = [];
    if (all_menu_ids.length) {
      const paths: any[] = await interfaceModel.getInterfaceByMenu(all_menu_ids);
      all_api_list = paths.filter((v) => v.path && v.path.trim());
    }
    // 批量更新角色的权限
    roles.forEach(({ role_id }) => {
      // 不需要tnt_id 因为还有系统自带的角色
      const key = `${redisConfig.platform_key}_ROLE_PMS_${role_id}`;
      const menu_ids = pms.filter((e) => e.role_id === role_id).map((e) => e.menu_id);
      const menu_access = all_menu_access.filter((e) => e.role_id === role_id).map((e) => e.access);
      const btn_access = all_btn_access.filter((e) => e.role_id === role_id).map((e) => e.access);
      const api_list = all_api_list.filter((e) => menu_ids.includes(e.menu_id)).map((e) => e.path);
      const data = { menu_access, api_list, btn_access };
      setRedisByKey(key, data);
    });
  }
}
export const roleService = new RoleServices();
