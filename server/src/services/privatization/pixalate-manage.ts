import { tenantPixalateModel } from '@/models';
import { PrivateAPI } from '@/types/privatization';
import { getPixalateConfig } from './share-utils';

class TenantPixalateService {
  async getTenantPixalateList(): Promise<PrivateAPI.PixalateItem[]> {
    const list = await tenantPixalateModel.getTenantPixalateList();

    const filteredList: PrivateAPI.PixalateItem[] = [];

    for (const item of list) {
      const { pixalate_config, ...rest } = item;

      // 和 brand 共用一张表，所以 pixalate_config 可能为空
      if (!pixalate_config) {
        continue;
      }

      // use_rix_common 为 1 表示使用rixengine的pixalate，2 表示自定义pixalate
      try {
        const parsed = JSON.parse(pixalate_config) as unknown;
        const useRixCommon = (parsed as PrivateAPI.PixalateConfig).use_rix_common;

        if (!parsed || typeof parsed !== 'object' || useRixCommon === 1) {
          continue;
        }
        filteredList.push({
          ...rest,
          pixalate_config: parsed as PrivateAPI.PixalateConfig,
          use_rix_common: useRixCommon
        });
      } catch {
        // 非法 JSON，跳过该项
        continue;
      }
    }

    return filteredList;
  }

  async addTenantPixalate(formData: PrivateAPI.AddPixalateItem): Promise<boolean> {
    const { tnt_id, cur_admin_id, ...restData } = formData;

    // 如果 restData 为空，设置默认值 default '{"use_rix_common":1}'
    const pixalateConfig = JSON.stringify(getPixalateConfig(restData));

    const res = await tenantPixalateModel.addTenantPixalate(tnt_id, cur_admin_id, pixalateConfig);
    return res.affectedRows > 0;
  }

  async editTenantPixalate(formData: PrivateAPI.EditPixalateItem): Promise<boolean> {
    const { id, cur_admin_id, ...restData } = formData;

    const pixalateConfig = JSON.stringify(getPixalateConfig(restData));
    const res = await tenantPixalateModel.editTenantPixalate(id, cur_admin_id, pixalateConfig);
    return res.affectedRows > 0;
  }
}

export const tenantPixalateService = new TenantPixalateService();
