/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-29 19:56:54
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-18 10:56:05
 * @Description:
 */
/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-25 11:41:32
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-25 19:37:41
 * @Description:
 */

import type { File } from 'formidable';
import { PrivateAPI } from '@/types/privatization';
import { branModel } from '@/models';
import { CommonAPI } from '@/types/common';
import { getBrandStorageOptions } from '@/utils/gcs-download';
import { getPixalateConfig } from './share-utils';

class BrandService implements PrivateAPI.BrandService {
  async getTenantBrandList(): Promise<PrivateAPI.BrandList[]> {
    return branModel.getTenantBrandList();
  }

  async addTenantBrand(params: PrivateAPI.AddBrand): Promise<any> {
    // 添加代码的逻辑，需要添加 默认的 pixalate 的数据
    const defaultPixalateConfig = getPixalateConfig();
    return branModel.addTenantBrand(params, JSON.stringify(defaultPixalateConfig));
  }

  async editTenantBrand(params: PrivateAPI.AddBrand): Promise<any> {
    return !!(await branModel.editTenantBrand(params));
  }

  async uploadLogo(file: File): Promise<any> {
    const { bucket, pathPrefix } = getBrandStorageOptions('logo');
    const fileExt = file.originalFilename!.split('.').pop()! || '.png';
    const newFilename = `${file.newFilename || Date.now()}.${fileExt}`;
    const filePath = file.filepath; // 本地文件路径
    const fileName = `${pathPrefix}${newFilename}`;
    const options: CommonAPI.UploadParams = {
      bucket,
      filePath,
      fileName
    };
    return await branModel.uploadLogo(options);
  }

  async uploadFavicon(file: File): Promise<any> {
    const { bucket, pathPrefix } = getBrandStorageOptions('favicon');
    const fileExt = file.originalFilename!.split('.').pop()! || '.png';
    const newFilename = `${file.newFilename || Date.now()}.${fileExt}`;
    const filePath = file.filepath; // 本地文件路径
    const fileName = `${pathPrefix}${newFilename}`;
    const options: CommonAPI.UploadParams = {
      bucket,
      filePath,
      fileName
    };
    return await branModel.uploadLogo(options);
  }

  async downloadLogo(params: CommonAPI.DownloadParams): Promise<any> {
    const { bucket } = getBrandStorageOptions('logo');
    const fileName = `${params.fileName}`;
    const options: CommonAPI.DownloadParams = {
      bucket,
      fileName
    };
    return await branModel.download(options);
  }

  async downloadFavicon(params: CommonAPI.DownloadParams): Promise<any> {
    const { bucket } = getBrandStorageOptions('favicon');
    const fileName = `${params.fileName}`;
    const options: CommonAPI.DownloadParams = {
      bucket,
      fileName
    };
    return await branModel.download(options);
  }
}

export const brandService = new BrandService();
