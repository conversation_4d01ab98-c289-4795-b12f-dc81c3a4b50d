import { PrivateAPI } from '@/types/privatization';

type ConfigKey = keyof PrivateAPI.PixalateConfig;

/**
 * 获取 pixalate 配置
 *
 * 如果 use_rix_common 为 2，则表示使用自定义 pixalate，否则使用 rixengine 的 pixalate
 * 
 * 如果 data 为空，则返回默认配置
 *
 * @param data 表单数据
 * @returns pixalate 配置
 */
export function getPixalateConfig(data?: Partial<PrivateAPI.PixalateConfig>): PrivateAPI.PixalateConfig {
  const pixalateConfigKeys: (keyof PrivateAPI.PixalateConfig)[] = [
    'use_rix_common',
    'report_api',
    'report_api_key',
    'display_web_tag',
    'display_app_tag',
    'display_web_js',
    'display_app_js',
    'native_web_tag',
    'native_app_tag',
    'video_web_tag',
    'video_app_tag'
  ];

  // 默认使用 rixengine 的 pixalate
  const pixalateConfig: PrivateAPI.PixalateConfig = { use_rix_common: 1 };
  const input = data || {};

  if (Object.keys(input).length === 0) {
    return pixalateConfig;
  }

  const out = pixalateConfig as Record<ConfigKey, unknown>;
  for (const key of pixalateConfigKeys) {
    const val = input[key as ConfigKey];
    if (val !== undefined) {
      if (key === 'use_rix_common') {
        const coerced = Number(val);
        out[key as ConfigKey] = coerced === 2 ? 2 : 1;
      } else {
        out[key as ConfigKey] = val;
      }
    }
  }

  return pixalateConfig;
}
