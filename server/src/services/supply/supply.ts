/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-12-05 11:44:06
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-03-22 14:24:15
 * @FilePath: /saas.rix-platform/server-ts/src/services/supply/supply.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AEmpo
 */
import { supplyModel } from '@/models';
import { SupplyAPI } from '@/types/supply';

class SupplyService implements SupplyAPI.Supply {
  async getSupplyList() {
    return await supplyModel.getSupplyList();
  }

  async updateSupply(params: SupplyAPI.UpdateSupplyParams): Promise<boolean> {
    return await supplyModel.updateSupply(params);
  }

  async getSupplyAuth(seller_id: number, tnt_id: number) {
    return await supplyModel.getSupplyAuth(seller_id, tnt_id);
  }

  async getSupplyEndpoint(tnt_id: number, seller_id?: number) {
    return await supplyModel.getSupplyEndpoint(tnt_id, seller_id);
  }
}

export const supplyService = new SupplyService();
