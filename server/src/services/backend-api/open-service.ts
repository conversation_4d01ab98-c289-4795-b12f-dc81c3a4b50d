import dbUtil from '@/db/mysql';
import { openServiceModel, RegisterUser } from '@/models/backend-api/open-service';
import { OpenServiceAPI } from '@/types/open-service';

class OpenService {
  async registerUser(params: OpenServiceAPI.RegisterUserParams): Promise<RegisterUser> {
    return dbUtil.execTransaction(async (tools) => {
      // 创建租户
      const tenantResult = await openServiceModel.addTenant(params,tools);

      const tntId = tenantResult.tntId;

      const [user] = await Promise.all([
        // 创建租户特定的账号（用于权限管理）
        openServiceModel.addOneUser(tenantResult, params, tools),
        // 默认配置开通：stg_atc_config，buyer（endpoint，qps_config，cap_config），seller
        openServiceModel.addDefaultConfig(tntId, tools)
      ]);

      return user;
    });
  }
}

export const openService = new OpenService();
