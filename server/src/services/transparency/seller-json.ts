/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-01-25 11:41:32
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-01-25 19:37:41
 * @Description:
 */

import type { File } from 'formidable';
import { TransparencyAPI } from '@/types/transparency';
import { transparencyModel } from '@/models';
import { CommonAPI } from '@/types/common';

class TransparencyService implements TransparencyAPI.SellerJsonService {
  async upload(file: File) {
    const bucket =
      process.env.NODE_ENV === 'prod'
        ? 'static-rix-engine'
        : 'console-rix-engine';
    const pathPrefix =
      process.env.NODE_ENV === 'prod' ? '' : 'saas_others/test/';

    const filePath = file.filepath;
    const fileName = `${pathPrefix}${file.originalFilename!}`;
    const options: CommonAPI.UploadParams = {
      bucket,
      filePath,
      fileName,
      metadata: {
        cacheControl: 'public, max-age=60'
      }
    };
    return await transparencyModel.upload(options);
  }
}

export const transparencyService = new TransparencyService();
