import { StatusMap } from '@/constants';
import { schainTruncationModel } from '@/models';

class SchainTruncationService {
  async updateSchainTruncationConfig(formData: any) {
    if (formData.id) {
      return schainTruncationModel.updateSchainTruncationConfig(formData);
    } else {
      // 如果 tnt_id + buyer_id 在数据库里面存在时，走 update 逻辑
      const [exist] = await schainTruncationModel.getSchainTruncationConfig(formData);
      if (exist) {
        return schainTruncationModel.updateSchainTruncationConfig({
          ...formData,
          id: exist.id,
          status: StatusMap.Active
        });
      }
      return schainTruncationModel.addSchainTruncationConfig(formData);
    }
  }
  getSchainTruncationList(formData: any) {
    return schainTruncationModel.getSchainTruncationList(formData);
  }
}

export const schainTruncationService = new SchainTruncationService();
