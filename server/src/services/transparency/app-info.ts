/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-19 18:36:53
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-20 15:42:07
 * @Description:
 */
import { transparencyAppInfoModel } from '@/models';
import { TransparencyAPI } from '@/types/transparency';

class TransparencyAppInfoService {
  async updateTransparencyAppInfoTag(options: any) {
    const data = await transparencyAppInfoModel.updateTransparencyAppInfoTag(options);

    return data.affectedRows === 1;
  }

  async getTransparencyAppInfo(options: any) {
    const { supply_tag } = options;
    // 需要处理数据，按照 app_bundle_id 聚合
    const appInfo = await transparencyAppInfoModel.getTransparencyAppInfo(options);

    if (!!supply_tag && appInfo.length > 0) {
      const tmp_domain = appInfo.map((item: any) => item.tmp_domain);
      const sellerInfo = await transparencyAppInfoModel.getSellerInfo({
        tmp_domain,
        supply_tag
      });

      // 使用 Map 来加速查找操作
      const sellerMap = new Map();
      sellerInfo?.forEach((item: any) => {
        if (!sellerMap.has(item.domain)) {
          sellerMap.set(item.domain, []);
        }
        sellerMap.get(item.domain).push(item);
      });

      // 遍历 appInfo 并为每个应用添加 seller 信息
      appInfo.forEach((app: any) => {
        const sellers = sellerMap.get(app.tmp_domain) || [];
        app.sellers = sellers;
        delete app.tmp_domain;
      });
    }

    return appInfo;
  }

  async updateTransparencyAppInfo(options: TransparencyAPI.UpdateTransparencyAppInfoParams) {
    const data = await transparencyAppInfoModel.updateTransparencyAppInfo(options);
    return data;
  }
}

export const transparencyAppInfoService = new TransparencyAppInfoService();
