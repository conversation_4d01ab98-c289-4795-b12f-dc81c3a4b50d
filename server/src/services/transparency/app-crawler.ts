import { Message } from '@/codes';
import { transparencyAppCrawlerModel } from '@/models';
import { TransparencyAPI } from '@/types/transparency';

class TransparencyAppCrawlerService {
  async createAppCrawler(options: any) {
    const isExist = await this.isExistAppCrawler(options.app_bundle_id, options.platform);
    if (isExist) {
      throw new Error(Message['APP_CRAWLER_EXIST']);
    }
    const data = await transparencyAppCrawlerModel.createAppCrawler(options);
    return data.affectedRows === 1 && data.insertId !== 0;
  }

  async batchCreateAppCrawler(options: TransparencyAPI.BatchCreateAppCrawlerParams) {
    try {
      const { bundles, op_user } = options;
      const result = await transparencyAppCrawlerModel.batchCreateAppCrawler(bundles, op_user);

      return result.affectedRows >= 0;
    } catch (error) {
      throw new Error(Message['ERROR_SYS']);
    }
  }

  async updateAppCrawler(options: any) {
    try {
      const data = await transparencyAppCrawlerModel.updateAppCrawler(options);
      return data.affectedRows === 1;
    } catch (error: any) {
      if (error.code === 'ER_DUP_ENTRY') {
        throw new Error(Message['APP_CRAWLER_EXIST']);
      }
      throw new Error(Message['ERROR_SYS']);
    }
  }

  async getAppCrawlerList(options: any) {
    const data = await transparencyAppCrawlerModel.getAppCrawlerList(options);
    return data;
  }

  async isExistAppCrawler(app_bundle_id: string, platform: number) {
    return await transparencyAppCrawlerModel.isExistAppCrawler(app_bundle_id, platform);
  }
}

export const transparencyAppCrawlerService = new TransparencyAppCrawlerService();
