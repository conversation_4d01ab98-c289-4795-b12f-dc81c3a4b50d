/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:41:24
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-18 15:08:41
 * @Description:
 */

import { getLogger } from '@/config/log4js';
import { DictMap } from '@/constants/dict';
import { commonModel } from '@/models';
import { CommonAPI } from '@/types/common';

class CommonService implements CommonAPI.Common {
  async getBuyerIntegrationType(): Promise<CommonAPI.IntegrationTypeItem[]> {
    return await commonModel.getBuyerIntegrationType();
  }

  async getSellerIntegrationType(): Promise<CommonAPI.IntegrationTypeItem[]> {
    return await commonModel.getSellerIntegrationType();
  }

  async getDict(key: string): Promise<any[]> {
    const dictItem = DictMap.find((item) => item.dict_type === key);
    if (!dictItem) {
      return [];
    }

    const { aat_dict_type } = dictItem;
    let data = [];
    try {
      const getCommonDict = aat_dict_type ? commonModel.getAatDict : commonModel.getSaasDict;
      data = await getCommonDict(dictItem);
    } catch (error: any) {
      getLogger('error').error(`getDict error ${error.message}`);
      return [];
    }
    return data;
  }
}

export const commonService = new CommonService();
