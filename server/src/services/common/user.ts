/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:45:32
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-26 18:03:42
 * @Description:ion.tnt_id
 */

import { delRedisByKey, getRedisByKey } from '@/db/redis';
import { userModel } from '@/models';
import { UserAPI } from '@/types/user';
import { getConfig, md5 } from '@/utils';

const { redisConfig } = getConfig();

class UserService {
  private generateRedisKey(userId: number, tntId: number): string {
    return md5(`${redisConfig.platform_key}_${userId}_${tntId}`);
  }

  private async clearRedisCache(specialUserId: number, userKeys: string[]): Promise<void> {
    for (const key of userKeys) {
      const cachedSpecialAccount = await getRedisByKey(key);
      // @ts-ignore
      if (cachedSpecialAccount?.special_user_id === specialUserId) {
        await delRedisByKey(key);
      }
    }
  }

  async updateUserLink(formData: any) {
    // 1. 如果没有 ID，则直接添加新的用户关联
    if (!formData.id) return userModel.addUserLink(formData);

    // 2. 获取当前特殊账号信息
    const [specialAccount] = (await userModel.getSpecialAccountById(formData.id)) || [];
    if (!specialAccount) return; // 如果没有找到特殊账号，直接返回

    const { special_user_id, linked_user_ids = '' } = specialAccount || {};
    const currentLinkedUserIds = linked_user_ids.split(',').filter(Boolean).map(Number);

    // 3. 获取当前特殊账号关联的所有用户
    const linkedUsers = (await userModel.getLinkedUsers(currentLinkedUserIds)) || [];

    // 4. 生成 Redis keys
    const userKeyList = linkedUsers.map((user: any) => this.generateRedisKey(user.user_id, user.tnt_id));

    // 5. 清理 Redis 缓存
    await this.clearRedisCache(special_user_id, userKeyList);

    // 6. 更新数据库
    return userModel.updateUserLink(formData);
  }

  async getAllUserLinkList() {
    // 获取所有特殊账号信息
    const specialAccounts = await userModel.getSpecialAccounts();
    if (!specialAccounts || specialAccounts.length === 0) {
      return [];
    }
    // 提取所有特殊账号的 linked_user_ids，并去重
    const allLinkedUserIds: any[] = [
      ...new Set(
        specialAccounts.flatMap(({ linked_user_ids }: any) => linked_user_ids.split(',').filter(Boolean).map(Number))
      )
    ];
    // 获取所有关联用户信息
    let linkUsers = [];
    if (allLinkedUserIds.length > 0) {
      linkUsers = await userModel.getLinkedUsers(allLinkedUserIds);
    }
    // 将关联用户信息按 user_id 分类，方便后续匹配
    const linkUsersMap = linkUsers.reduce((acc: any, user: any) => {
      acc[user.user_id] = user;
      return acc;
    }, {});

    // 返回最终的数据结构，关联用户信息通过 map 映射到每个特殊账号上
    const result = specialAccounts.map((account: any) => {
      const { special_user_id, linked_user_ids, ...rest } = account;

      const linkedUserIds = linked_user_ids ? linked_user_ids.split(',').map(Number) : [];

      const accountLinkUsers = linkedUserIds.map((userId: any) => linkUsersMap[userId]).filter(Boolean);

      return {
        ...rest,
        user_id: special_user_id,
        link_users: accountLinkUsers
      };
    });

    return result;
  }

  async confirmPassword(option: UserAPI.ConfirmPasswordParams): Promise<UserAPI.AdminListItem[]> {
    return await userModel.confirmPassword(option);
  }

  async login(options: UserAPI.LoginParams): Promise<UserAPI.AdminListItem[]> {
    const resultData = await userModel.getOneByUserNameAndPassword(options);
    return resultData;
  }

  async resetPasswordByUserId(option: UserAPI.ResetPasswordParams): Promise<boolean> {
    return await userModel.resetPasswordByUserId(option);
  }

  async validPassword(formData: any) {
    return await userModel.validPassword(formData);
  }

  async updateCompanyUser(params: UserAPI.UpdateCompanyUserParams) {
    return await userModel.updateCompanyUser(params);
  }
}

export const userService = new UserService();
