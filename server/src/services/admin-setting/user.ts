/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-31 23:26:28
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-27 11:09:16
 * @Description:
 */

import { adminUserModel } from '@/models';
import {
  genEnCode, md5, sendEmail, getConfig
} from '@/utils';
import { AdminSettingAPI } from '@/types/admin';
import { delRedisByKey, getRedisBy<PERSON>ey, isRedisMulKeyExists, setRedisByKey } from '@/db/redis';
import { StatusType } from '@/constants';

const { passwordConfig, redisConfig } = getConfig();

class UserService {
  async getAllUserList() {
    const data = await adminUserModel.getAllUserList();
    if (Array.isArray(data) && data.length) {
      const ids = data.map((v) => md5(`${redisConfig.admin_key}_${v.admin_id}`));
      const keys = await isRedisMulKeyExists(ids);
      return data.map((v) => {
        const key = md5(`${redisConfig.admin_key}_${v.admin_id}`);
        const is_login = keys.includes(key);
        return { ...v, is_login: is_login ? 1 : 2 };
      });
    }
    return data;
  }

  async addOneUser(platform: string, options: AdminSettingAPI.AddUserParams) {
    const ori_pwd = genEnCode(8);
    const password = md5(passwordConfig.admin_prefix + ori_pwd);
    const formData = { ...options, password };
    const resultData = await adminUserModel.addOneUser(formData);
    if (resultData) {
      const to = formData.send_email.join(',');
      await sendEmail({
        to,
        subject: 'RixEngine Admin Account',
        from: 'RixEngine<<EMAIL>>',
        html: `
                <p>Congratulations, RixEngine Admin Account had been successfully registered!</p>
                <p>RixEngine Admin Platform: ${platform}</p>
                <p>Username: ${formData.account_name}</p>
                <p>Password: ${ori_pwd}</p>
              `
      });
    }
    return resultData;
  }

  async updateOneUser(formData: AdminSettingAPI.EditUserParams) {
    const flag = await adminUserModel.updateOneUser(formData);
    if (flag) {
      const {
        admin_id, display_name, account_name, role_id, status
      } = formData;
      const key = md5(`${redisConfig.admin_key}_${admin_id}`);
      if (status === StatusType.Active) {
        const data = await getRedisByKey(key);
        // 同步更新信息
        if (data) {
          const tmp = {
            ...data, display_name, account_name, role_id
          };
          await setRedisByKey(key, tmp);
        }
      } else {
        // 删除用户
        await delRedisByKey(key);
      }
    }
    return flag;
  }

  async isUserExist(account_name: string) {
    return await adminUserModel.isUserExist(account_name);
  }

  async resetPassword(platform: string, params: AdminSettingAPI.UpdateUserPassword) {
    const password = md5(passwordConfig.admin_prefix + params.password);
    const data = await adminUserModel.resetPassword({ ...params, password });
    if (data && params.send_email?.length) {
      const key = md5(`${redisConfig.admin_key}_${params.admin_id}`);
      await delRedisByKey(key);
      const to = params.send_email.join(',');
      await sendEmail({
        to,
        subject: 'Reset Password Successfully!',
        from: 'RixEngine Admin<<EMAIL>>',
        html: `
                <p>Congratulations, RixEngine Admin reset password successfully!</p>
                <p>RixEngine Admin: ${platform}</p>
                <p>Username: ${params.account_name}</p>
                <p>Password: ${params.password}</p>
              `
      });
    }
    return data;
  }
}

export const adminUserService = new UserService();
