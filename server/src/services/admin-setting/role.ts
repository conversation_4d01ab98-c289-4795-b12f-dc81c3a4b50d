/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-20 17:43:00
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-21 11:26:48
 * @Description:
 */

import { RoleType, MenuType } from '@/constants/platform-setting';
import { getRedisByKey, setRedisByKey } from '@/db/redis';
import { adminRoleModel } from '@/models';
import { AdminSettingAPI } from '@/types/admin';
import { getConfig, md5 } from '@/utils';

const { redisConfig } = getConfig();

class RoleService {
  async addRole(options: AdminSettingAPI.AddRole) {
    const data = await adminRoleModel.addRole(options);
    if (data) {
      const id = data.insertId;
      const key = md5(`${redisConfig.admin_key}_ROLE_${id}`);
      await setRedisByKey(key, {
        type: RoleType['Normal Role'],
        menu_access: [],
        id,
        btn_access: []
      });
    }
    return !!data;
  }

  // 更新用户角色 运营不支持该状态
  async updateRole(options: AdminSettingAPI.UpdateRole) {
    return await adminRoleModel.updateRole(options);
  }

  async getAllRole() {
    const result = await adminRoleModel.getAllRole();
    if (!Array.isArray(result) || !result.length) {
      return result;
    }

    return result.map(({ permissions, ...rest }) => {
      const pms_list = permissions ? permissions.split(',').filter((t: string) => !!t.trim()) : [];

      return {
        ...rest,
        pms_list
      };
    });
  }

  async isRoleExist(role_name: string, id?: number) {
    const data = await adminRoleModel.isRoleExist(role_name);
    if (Array.isArray(data) && data.length) {
      if (!id || data[0].id !== id) {
        return true;
      }
    }
    return false;
  }

  // 更新角色权限
  async updateRolePms(options: AdminSettingAPI.UpdateRolePMS) {
    const data = await adminRoleModel.updateRolePms(options);
    if (!data) return false;

    const { menus, id } = options;
    const all_access = await adminRoleModel.getPmsByMenuId(menus);

    const { menuAccess, btnAccess } = all_access.reduce(
      (
        acc: {
          menuAccess: Set<string>;
          btnAccess: Set<string>;
        },
        v: { type: number; access: string }
      ) => {
        const access = v.access?.trim();
        if (access) {
          if (v.type === MenuType.Menu) {
            acc.menuAccess.add(access);
          } else if (v.type === MenuType.Operation) {
            acc.btnAccess.add(access);
          }
        }
        return acc;
      },
      {
        menuAccess: new Set<string>(),
        btnAccess: new Set<string>()
      }
    );

    // 更新redis缓存
    const key = md5(`${redisConfig.admin_key}_ROLE_${id}`);
    const existingData = (await getRedisByKey(key)) || {};
    await setRedisByKey(key, {
      ...existingData,
      id,
      menu_access: Array.from(menuAccess),
      btn_access: Array.from(btnAccess)
    });

    return true;
  }
}
export const adminRoleService = new RoleService();
