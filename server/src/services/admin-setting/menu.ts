/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-20 17:39:36
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-20 17:41:42
 * @Description:
 */

import { adminMenuModel } from '@/models';
import { AdminSettingAPI } from '@/types/admin';

class MenuService {
  async addMenu(params: AdminSettingAPI.AddMenuParams) {
    return await adminMenuModel.addMenu(params);
  }

  async updateMenu(params: AdminSettingAPI.UpdateMenuParams) {
    return await adminMenuModel.updateMenu(params);
  }

  async getAllMenu() {
    return await adminMenuModel.getAllMenu();
  }

  async isMenuExist(options: AdminSettingAPI.AddMenuParams) {
    const { id } = options;
    const data = await adminMenuModel.isMenuExist(options);
    if (Array.isArray(data) && data.length) {
      if (!id || data[0].id !== id) {
        return true;
      }
    }
    return false;
  }

  async updateMenuSort(options: AdminSettingAPI.UpdateMenuSortParams) {
    return await adminMenuModel.updateMenuSort(options);
  }

  async deleteMenu(id: number) {
    return await adminMenuModel.deleteMenu(id);
  }
}

export const adminMenuService = new MenuService();
