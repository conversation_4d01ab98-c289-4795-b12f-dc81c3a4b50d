/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2022-11-22 17:12:20
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-29 17:13:43
 * @FilePath: /saas.rix-platform/server-ts/src/codes/index.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

const CodeSystem = {
  ERROR_SYS: -1,
  SUCCESS: 0,
  PARAMS_INVALID: -2,
  DOWNLOAD_ERROR: -6
};

const MessageSystem = {
  ERROR_SYS: 'system error',
  SUCCESS: 'success',
  PARAMS_INVALID: 'params invalid',
  DOWNLOAD_ERROR: 'download faild, please try again or go to the exported report page to get the reason'
};

const CodeUser = {
  FAIL_USER_NAME_OR_PASSWORD_ERROR: 1101,
  FAIL_USER_NO_LOGIN: 1102,
  FAIL_USER_NO_PERMISSION: 1103,
  FAIL_USER_REST_PASSWORD_ERROR: 1104,
  CURRENT_PASSWORD_ERROR: 1105,
  ACCOUNT_NAME_EXISTS: 1106,
  USER_COUNT_LIMIT: 1107,
  FAIL_USER_NO_AUTH: 1108,
  PERMISSION_USER_CHANGE: 1109,
  TENANT_NAME_EXISTS: 1110,
  USER_NAME_EXISTS: 1111,
  UPDATE_USER_FAILED: 1112,
  // 接口开放 校验ip的错误状态
  REQUEST_IP_INVALID: 1115,
  EXT_UID_EXISTS: 1116,
  EMAIL_EXISTS: 1117,
};
const MessageUser = {
  FAIL_USER_NAME_OR_PASSWORD_ERROR: 'Wrong credentials. Try again.',
  FAIL_USER_NO_LOGIN: 'User is not logIn',
  FAIL_USER_REST_PASSWORD_ERROR: 'Reset password fail',
  FAIL_USER_NO_PERMISSION: 'You do not have the permissions to access it.',
  CURRENT_PASSWORD_ERROR: 'Current password invalid',
  ACCOUNT_NAME_EXISTS: 'Account Name has already exist',
  USER_COUNT_LIMIT: 'Allow adding up to 1000 users',
  FAIL_USER_NO_AUTH: 'You do not have the permissions to access it.',
  PERMISSION_USER_CHANGE: 'You permission has change, please log in again',
  TENANT_NAME_EXISTS: 'Tenant Name or Email or Host Prefix has already exist',
  USER_NAME_EXISTS: 'User Name has already exists, please change another one',
  UPDATE_USER_FAILED: 'Update user failed',
  EXT_UID_EXISTS: 'ext_uid already exists',
  EMAIL_EXISTS: 'email already exists',
  REQUEST_IP_INVALID: 'Your IP is not allowed to access, please check your IP'
};

const CodePermission = {
  MENU_PATH_EXISTS: 2101,
  OPERATION_CODE_EXISTS: 2102,
  ROLE_NAME_EXISTS: 2103,
  PERMISSION_NAME_EXISTS: 2104,
  ROUTE_PATH_EXISTS: 2105,
  MENU_TITLE_EXISTS: 2106
};
const MessagePermission = {
  MENU_PATH_EXISTS: 'Path has already exists, please change another one',
  OPERATION_CODE_EXISTS: 'Authorization Sign has already exists, please change another one',
  ROLE_NAME_EXISTS: 'Role Name has already exists, please change another one',
  PERMISSION_NAME_EXISTS: 'Permission Name has already exists, please change another one',
  ROUTE_PATH_EXISTS: 'Route Path has already exists, please change another one',
  MENU_TITLE_EXISTS: 'Menu title has already exists'
};

const CodeEcpr = {
  ECPR_CONFIG_EXISTS: 2501
};
const MessageEcpr = {
  ECPR_CONFIG_EXISTS: 'The Ecpr Config you input is already exists'
};

const CodeConfig = {
  /**
   * @deprecated 2025-08-21 已废弃，不再校验重复，插入 sql 处直接 on duplicate key update
   */
  ATC_EXISTS: 2601,
  KWA_EXISTS: 2602,
  STG_CHAIN_EXISTS: 2603,
  PIXALATE_EXISTS: 2604,
  UPDATE_PIXALATE: 2605,
  KWA_BUNDLE_EXISTS: 2606,
  KWA_APPID_EMPTY: 2607,
  KWA_BUNDLE_EMPTY: 2608
};
const MessageConfig = {
  ATC_EXISTS: 'The combination of Tenant, Server Region, Supply and Demand is already Exists, please edit it',
  KWA_EXISTS: 'The combination of Tenant, Demand and Bundle is already Exists, please edit it',
  KWA_BUNDLE_EXISTS: 'The combination of Demand and Bundle is already Exists, please edit it',
  KWA_APPID_EMPTY: 'The empty value of AppId already exists, please edit it',
  KWA_BUNDLE_EMPTY: 'The empty value of Bundle already exists, please edit it',
  STG_CHAIN_EXISTS: 'The combination of Tenant, Bundle, Type is already Exists, please edit it',
  PIXALATE_EXISTS: 'The config is already Exists, please edit it',
  UPDATE_PIXALATE: 'On duplicated pixalate config update'
};

const CodePrivatization = {
  BRAND_EXIST: 2701,
  APP_CRAWLER_EXIST: 2702
};
const MessagePrivatization = {
  BRAND_EXIST: 'Brand already exists',
  APP_CRAWLER_EXIST: 'The combination of app_bundle_id and platform already exists'
};
export const Code = {
  ...CodeSystem,
  ...CodeUser,
  ...CodePermission,
  ...CodeEcpr,
  ...CodeConfig,
  ...CodePrivatization
};
export const Message = {
  ...MessageSystem,
  ...MessageUser,
  ...MessagePermission,
  ...MessageEcpr,
  ...MessageConfig,
  ...MessagePrivatization
};
export type CodeProps = keyof typeof Code;
export default {};
