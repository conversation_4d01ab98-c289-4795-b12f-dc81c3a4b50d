/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 14:23:23
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-28 15:12:08
 * @Description:
 */

import dbUtils, { PoolConnection, QueryParamType } from '@/db/mysql';
import { SelectType, LinkMenuType } from '@/constants/permission';
import { getLogger } from '@/config/log4js';
import { buildSQLWhereClause } from '@rixfe/rix-tools';

class MenuModel {
  async addMenu(params: any) {
    const {
      title,
      path = '',
      pid = 0,
      remark = '',
      icon = '',
      is_hide = 1,
      component = '',
      access = '',
      menu_render = 2,
      interfaces = [],
      type,
      sort,
      menu_type = LinkMenuType.Ordinary,
      node_type
    } = params;

    const tmpSort = 0;
    let conn: PoolConnection | null = null;
    try {
      const { connection, commit, beginTransaction, query } = await dbUtils.getConnectionParams();
      await beginTransaction();
      conn = connection;

      const addOneRow = [
        [
          title,
          path,
          type,
          pid || 0,
          remark,
          icon,
          is_hide,
          component,
          sort || tmpSort,
          access || '',
          menu_render,
          menu_type,
          node_type || 1
        ]
      ];

      const row = await query({
        sql: 'insert into menu (title, path,type, pid, remark, icon, is_hide, component, sort, access, menu_render,menu_type, node_type) values ?',
        values: [addOneRow]
      });

      if (interfaces?.length) {
        const rscId = row.insertId;
        await query({
          sql: 'insert into interface_rl (type, itf_id, rsc_id) values ?',
          values: [interfaces.map((itf_id: any) => [type, itf_id, rscId])]
        });
      }

      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(row);
    } catch (error: any) {
      getLogger('error').error(`addMenu failed sql=[${error.message}]`);
      if (conn) {
        conn.rollback(() => {
          conn && conn.release();
          conn = null;
        });
      }
      return Promise.reject(error);
    }
  }

  async updateMenu(sqls: QueryParamType[]) {
    return !!(await dbUtils.execWaterfallTransaction(sqls));
  }

  // 1 为in, 2 为not in
  async getAllMenu(ids?: any, type?: any) {
    const { clause, params } = buildSQLWhereClause([
      [`id ${type === SelectType.in ? 'in' : 'not in'} (?)`, ids?.length ? ids : null]
    ]);

    const sql = `select *,
      DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time,
      DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time
    from menu ${clause}`;
    return dbUtils.query(sql, [...params]);
  }

  async getAllMenuAndInterface() {
    const sql = `
      select
        m.id, 
        m.title,
        m.path, 
        m.pid,
        m.component,
        m.is_hide,
        m.icon,
        m.pid,
        m.remark as remark,
        m.sort,
        m.access,
        m.menu_render,
        m.status,
        m.menu_type,
        m.type,
        m.node_type,
        group_concat(coalesce(il.itf_id, '')) as interfaces
      from menu as m
      left join interface_rl as il on il.rsc_id = m.id
      group by m.id
      order by m.path asc, m.sort asc;
    `;
    return await dbUtils.query(sql);
  }

  async isMenuExist(options: any) {
    const { title, pid } = options;
    const sql = `select id from menu where title = ? and pid=?`;
    return dbUtils.query(sql, [title, pid]);
  }

  async deleteMenu(ids: number[]) {
    const data = dbUtils.execWaterfallTransaction([
      {
        sql: 'delete from menu where id in (?)',
        values: [ids]
      },
      {
        sql: 'delete from interface_rl where rsc_id in (?)',
        values: [ids]
      }
    ]);
    return !!data;
  }

  // 菜单排序
  async updateMenuSort(sql: Exclude<QueryParamType, string>) {
    return !!(await dbUtils.query(sql.sql, sql.values));
  }

  // 通过接口获取关联菜单
  async getMenuByInterface(id: number) {
    const sql = `select distinct rsc_id as menu_id from interface_rl where itf_id = ?`;
    return await dbUtils.query(sql, [id]);
  }
}

export const menuModel = new MenuModel();
