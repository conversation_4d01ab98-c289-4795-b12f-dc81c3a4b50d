/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 14:40:31
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-28 15:13:20
 * @Description:
 */
import dbUtils, { PoolConnection, QueryParamType } from '@/db/mysql';
import { getLogger } from '@/config/log4js';
import { StatusType } from '@/constants';

class RoleModel {
  async getRoleIdByName(role_name: string, tnt_id: number) {
    const res = await dbUtils.query('select id from role where role_name = ? and tnt_id = ?', [role_name, tnt_id]);

    return res[0]?.id || 0;
  }

  async addRole(options: any, operator: string) {
    let conn: PoolConnection | null = null;
    try {
      const { role_name, type = 3, permissions = [] } = options;
      const { commit, connection, beginTransaction, query } = await dbUtils.getConnectionParams();
      conn = connection;
      await beginTransaction();
      const sql1 = `insert into role (role_name, type) values ("${role_name}", ${type})`;
      const rows = await query(sql1);
      const row_id = rows.insertId;
      if (permissions && permissions.length) {
        const arr = permissions.map((item: any) => `(${row_id}, ${item}, ${operator})`);
        const sql = `insert into role_permission_rl (role_id, pms_id, op_user_id) values ${arr.join(',')}`;
        await query(sql);
      }
      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      console.log(e);
      getLogger('error').error(`addRole failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          (conn as PoolConnection).release();
          conn = null;
        });
      }
      return Promise.reject(e);
    }
  }

  async updateRole(sql: Exclude<QueryParamType, string>) {
    return await dbUtils.query(sql.sql, sql.values);
  }

  async editRole(sqls: QueryParamType[]) {
    return !!(await dbUtils.execWaterfallTransaction(sqls));
  }

  async getAllRoleAndPms() {
    const sql = `
      select
        r.id as id,
        r.role_name as role_name,
        group_concat(coalesce(rpl.rsc_id, '')) as permissions
      from role as r
      left join role_permission_rl as rpl on rpl.role_id = r.id
      where r.tnt_id=0
      group by r.id
      order by r.id
    `;
    return await dbUtils.query(sql);
  }

  async isRoleExist(role_name: string) {
    const sql = `select id from role where role_name = "${role_name}"`;
    return dbUtils.query(sql);
  }

  async getAllUserByRole(role_id: number) {
    const sql = `select user_id from user_role_rl where role_id=${role_id}`;
    return await dbUtils.query(sql);
  }

  async getAllUserByRoleList(roles: string[]) {
    const sql = `select user_id from user_role_rl where role_id in (${roles.join(',')})`;
    return await dbUtils.query(sql);
  }

  async getRoleByMenuIds(ids: number[]) {
    const sql = `
      select
        role_id,
        tnt_id
      from role_permission_rl
      where rsc_id in (${ids.join(',')})
    `;
    return await dbUtils.query(sql);
  }

  // 通过角色获取权限
  async getPmsByRoleId(ids: number[]) {
    const sql = `
        select
          rp.role_id,
          rp.rsc_id as menu_id,
          m.type,
          m.access as access
        from role_permission_rl as rp
        left join menu as m on m.id=rp.rsc_id
        where rp.role_id in (${ids.join(',')}) and m.status=${StatusType.Active}
      `;
    return await dbUtils.query(sql);
  }

  async getMenuByRole(role_id: number) {
    const sql = `
      select
        m.id as menu_id,
        m.access as access,
        m.type as type
      from role_permission_rl as rpl
      left join menu as m on m.id=rpl.rsc_id
      where rpl.role_id=${role_id} and rpl.tnt_id=0 and m.status=${StatusType.Active}
    `;
    return await dbUtils.query(sql);
  }
}

export const roleModel = new RoleModel();
