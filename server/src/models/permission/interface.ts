/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2023-03-07 16:03:23
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 16:23:51
 * @Description:
 */
import dbUtils from '@/db/mysql';
import { InterfaceType, Type, RoleTypeToNumber, StatusType } from '@/constants/permission';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

class InterfaceModel {
  async addInterface(options: any) {
    const { itf_name, path, type, op_type } = options;
    const sql = `insert into interface (itf_name, path, type, op_type) values ?`;
    return !!(await dbUtils.query(sql, [[[itf_name, path, type, op_type]]]));
  }

  async updateInterface(options: any) {
    const { id, itf_name, type, path } = options;

    const updateObj = buildSQLSetClause([
      ['itf_name', itf_name],
      ['type', type],
      ['path', path]
    ]);

    if (!updateObj) {
      return false;
    }

    const sql = `update interface set ? where id=?`;
    return !!(await dbUtils.query(sql, [updateObj, id]));
  }

  async getAllInterface() {
    const sql = `select *,  
      DATE_FORMAT(update_time, '%Y-%m-%d %H:%i:%s') as update_time,
      DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as create_time
    from interface order by update_time desc`;
    return await dbUtils.query(sql);
  }

  async isRoutePathExist(options: any) {
    const { path } = options;
    const sql = `select id from interface where path = ?`;
    return await dbUtils.query(sql, [path]);
  }

  async getInterfacesByMenuOrOperation(ids?: any) {
    const { clause, params } = buildSQLWhereClause([['rsc_id in (?)', ids?.length ? ids : null]]);

    const sql = `
        select 
          il.type as type, 
          il.itf_id as itf_id, 
          il.rsc_id as rsc_id,
          it.path as path,
          it.type as type,
          it.op_type as op_type
        from (select * from interface_rl ${clause}) as il
        join interface as it on il.itf_id = it.id and it.status=?
      `;
    return await dbUtils.query(sql, [...params, StatusType.Active]);
  }

  //  menu跟operation 获取active的
  async getPermissionByInterfaceMenu(id: any) {
    const sql = `select 
                  p.id as pms_id
                from interface_rl as il
                join menu as m on m.id=il.rsc_id and m.status=1
                join permission_rl as pl on pl.type=? and pl.rsc_id=il.rsc_id
                join permission as p on p.id=pl.pms_id and p.status=1
                where il.itf_id=? and il.type=?
              `;
    return await dbUtils.query(sql, [Type.Menu, id, Type.Menu]);
  }

  // 获取active的
  async getPermissionByInterfaceOperation(id: any) {
    const sql = `select 
                  p.id as pms_id
                from interface_rl as il
                join operation as o on o.id=il.rsc_id and o.status=1
                join permission_rl as pl on pl.type=? and pl.rsc_id=il.rsc_id
                join permission as p on p.id=pl.pms_id and p.status=1
                where il.itf_id=? and il.type=?
              `;
    return await dbUtils.query(sql, [Type.Operation, id, Type.Operation]);
  }

  async getUserByPms(pms_ids: any[]) {
    const sql = `select user_id from user_permission_rl where pms_id in (?)`;
    return await dbUtils.query(sql, [pms_ids]);
  }

  async getUserByRole(pms_ids: any[]) {
    const sql = `select 
                    url.user_id as user_id
                 from role_permission_rl as rpl
                 join role as r on r.id=rpl.role_id 
                    and r.type!=? 
                    and r.status=?
                 join user_role_rl as url on url.role_id=rpl.role_id
                 where rpl.pms_id in (?)
                `;
    return await dbUtils.query(sql, [RoleTypeToNumber['Super Administrator'], StatusType.Active, pms_ids]);
  }

  // 获取开放接口
  async getGlobalInterface() {
    const sql = `select path,type from interface where type != ?`;
    return await dbUtils.query(sql, [InterfaceType.Normal]);
  }

  async deleteInterface(id: number) {
    const sql = `delete from interface where id=?`;
    return await dbUtils.query(sql, [id]);
  }

  async getInterfaceByMenu(ids: number[]) {
    const sql = `
      select
        i.path as path,
        il.rsc_id as menu_id
      from interface_rl as il
      left join interface as i on i.id=il.itf_id
      where i.status=? and il.rsc_id in (?)
    `;
    return await dbUtils.query(sql, [StatusType.Active, ids]);
  }
}
export const interfaceModel = new InterfaceModel();
