/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-27 16:46:51
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 17:15:31
 * @Description:
 */

import { AppListAPI } from '@/types/app';
import dbUtil from '@/db/mysql';
class AppModel implements AppListAPI.AppModel {
  async getAppList(): Promise<AppListAPI.AppListItem[]> {
    const sql = `select app_id,app_name,bundle from seller_app `;
    return await dbUtil.query(sql);
  }
}

export const appModel = new AppModel();
