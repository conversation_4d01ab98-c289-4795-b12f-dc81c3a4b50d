/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-29 19:45:09
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-04-03 16:23:38
 * @Description:
 */
import { downloadFileStream, uploadFile } from '@/db/bigquery';
import dbUtils from '@/db/mysql';
import { CommonAPI } from '@/types/common';
import { PrivateAPI } from '@/types/privatization';
import { buildSQLSetClause } from '@rixfe/rix-tools';
class BrandModel implements PrivateAPI.BrandModel {
  async isBrandExist(tnt_id: number): Promise<boolean> {
    // pixalate 的数据会影响到当前的判断，需要过滤掉
    const sql = `select count(*) as count from privatization p where tnt_id = ? and (p.brand_name != '' or p.sj_domain != '' or p.brand_logo_path != '' or p.brand_favicon_path != '')`;
    const res = await dbUtils.query(sql, [tnt_id]);
    return res[0].count > 0;
  }

  async getTenantBrandList(): Promise<PrivateAPI.BrandList[]> {
    const sql = `
      select 
        p.id, 
        p.brand_name, 
        p.sj_domain,
        p.brand_logo_path, 
        p.brand_favicon_path,
        p.create_time,
        t.tnt_name as tnt_name,
        t.tnt_id as tnt_id
      from privatization p
      left join tenant t on p.tnt_id = t.tnt_id
      where p.brand_name != '' or p.sj_domain != '' or p.brand_logo_path != '' or p.brand_favicon_path != ''
    `;
    return await dbUtils.query(sql);
  }

  async addTenantBrand(params: PrivateAPI.AddBrand, defaultPxConfigString: string): Promise<any> {
    const { brand_name, brand_logo_path, brand_favicon_path, tnt_id, sj_domain } = params;
    const sql = `
      insert into privatization(brand_name, brand_logo_path, brand_favicon_path, sj_domain, tnt_id, pixalate_config)
      values(?, ?, ?, ?, ?, ?)
      on duplicate key update brand_name = values(brand_name), brand_logo_path = values(brand_logo_path), brand_favicon_path = values(brand_favicon_path), sj_domain = values(sj_domain)
    `;

    return dbUtils.query(sql, [
      brand_name,
      brand_logo_path,
      brand_favicon_path,
      sj_domain || 'rixengine.com',
      tnt_id,
      defaultPxConfigString
    ]);
  }

  async editTenantBrand(params: PrivateAPI.AddBrand): Promise<any> {
    const { brand_name, brand_logo_path, brand_favicon_path, tnt_id, sj_domain } = params;

    const updateObj = buildSQLSetClause([
      ['brand_name', brand_name],
      ['brand_logo_path', brand_logo_path],
      ['brand_favicon_path', brand_favicon_path],
      ['sj_domain', sj_domain || 'rixengine.com']
    ]);

    if (!updateObj) {
      return false;
    }

    const sql = `update privatization set ? where tnt_id = ?`;
    return !!(await dbUtils.query(sql, [updateObj, tnt_id]));
  }

  async uploadLogo(params: CommonAPI.UploadParams): Promise<any> {
    return await uploadFile(params);
  }

  async uploadFavicon(params: CommonAPI.UploadParams): Promise<any> {
    return await uploadFile(params);
  }

  async download(params: CommonAPI.DownloadParams): Promise<any> {
    return await downloadFileStream(params);
  }
}
export const branModel = new BrandModel();
