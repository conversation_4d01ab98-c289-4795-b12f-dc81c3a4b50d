import dbUtils from '@/db/mysql';
import { PrivateAPI } from '@/types/privatization';

class TenantPixalateModel {
  async getTenantPixalateList(): Promise<
    (Omit<PrivateAPI.PixalateItem, 'pixalate_config'> & { pixalate_config: string | null })[]
  > {
    const sql =
      'select p.id, p.tnt_id, t.tnt_name, p.op_id, a.account_name as op_name, p.pixalate_config from privatization as p left join admin as a on p.op_id = a.admin_id left join tenant as t on p.tnt_id = t.tnt_id';
    return dbUtils.query(sql);
  }

  addTenantPixalate(tnt_id: number, cur_admin_id: number, pixalateConfig: string) {
    const sql =
      'insert into privatization (tnt_id, op_id, pixalate_config) values (?, ?, ?) on duplicate key update pixalate_config = values(pixalate_config), op_id = values(op_id)';
    return dbUtils.query(sql, [tnt_id, cur_admin_id, pixalateConfig]);
  }

  editTenantPixalate(id: number, cur_admin_id: number, pixalateConfig: string) {
    const sql = 'update privatization set pixalate_config = ?, op_id = ? where id = ?';
    return dbUtils.query(sql, [pixalateConfig, cur_admin_id, id]);
  }
}
export const tenantPixalateModel = new TenantPixalateModel();
