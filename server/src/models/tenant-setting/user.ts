/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:18:28
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-01-09 18:38:03
 * @Description:
 */

import { getLogger } from '@/config/log4js';
import dbUtils, { PoolConnection } from '@/db/mysql';
import { delRedisByKey } from '@/db/redis';
import { getCsDomain } from '@/models/tenant-setting/tenant';
import { TenantAPI } from '@/types/tenant';
import { genEnCode, getConfig, md5, sendEmail } from '@/utils';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';
const { RixEngineSessionKey } = getConfig();
class UserModel {
  async addOneUser(options: TenantAPI.addUserParams) {
    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } = await dbUtils.getConnectionParams();
      conn = connection;
      await beginTransaction();
      const { account_name, password, tnt_id, type = 1, role_id, email = '' } = options;

      // TODO type 的逻辑是什么，和 role_id 的区别在哪里
      const token = md5(`${new Date().getTime()}${account_name}${tnt_id}${genEnCode(6)}`);
      const addUserSql = 'insert into user (account_name, password,role,tnt_id,type,email,token) values ?';
      const oneUser = [[account_name, password, 1, tnt_id, type, email, token]];

      const user = await query({ sql: addUserSql, values: [oneUser] });

      const addUserRoleSql = 'insert into user_role_rl (role_id, user_id,op_user_id,tnt_id) values ?';
      const oneUserRole = [[role_id, user.insertId, 0, tnt_id]];
      await query({ sql: addUserRoleSql, values: [oneUserRole] });
      // 代码不删除，后续可能会用到
      // if (sqls.addPermission && sqls.getPermission) {
      //   await query({ sql: sqls.addUserRole, values: user_id });
      //   const permissions = await query(sqls.getPermission);
      //   if (Array.isArray(permissions) && permissions.length > 0) {
      //     const arr = permissions.map((item: any) => [
      //       user_id,
      //       item.type,
      //       item.rsc_id,
      //       tnt_id,
      //     ]);
      //     console.log(arr);
      //     await query({ sql: sqls.addPermission, values: [arr] });
      //   }
      // }
      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      getLogger('error').error(`addOneUser failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          conn!.release();
          conn = null;
        });
      }
      return Promise.reject(e);
    }
  }

  async getUserList() {
    const sql = `
        select
          u.account_name as account_name,
          u.type as type,
          u.email as email,
          u.user_id as user_id,
          u.tnt_id as tnt_id,
          u.status as status,
          u.update_time as update_time,
          t.tnt_name as tnt_name,
          url.role_id as role_id
          from (select 
            account_name,
            user_id,type,
            email,
            tnt_id,
            status,
            update_time
          from user where tnt_id != 0 and role = 1 and status != 3
          ) as u
          left join (select tnt_id,tnt_name from tenant) as t on u.tnt_id = t.tnt_id
          left join (select role_id,user_id from user_role_rl) as url on url.user_id = u.user_id
          order by u.update_time desc;
    `;
    return dbUtils.query(sql);
  }

  async getAllSaasUserList() {
    const sql = `select * from user where status = 1`;
    return await dbUtils.query(sql);
  }

  async getRixUsersByRoleId(role_id?: number) {
    const sql = `
      select
        u.user_id, u.tnt_id
      from user_role_rl ur
      join user u on ur.user_id = u.user_id
      where ur.role_id = ?;
    `;
    return await dbUtils.query(sql, [role_id]);
  }

  async getUserListCount() {
    const sql = `select count(1) from user where role = 1 and status != 3`;
    const data = await dbUtils.query(sql);
    return data[0]['count(1)'];
  }

  async editUser(params: TenantAPI.EditUserParams) {
    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } = await dbUtils.getConnectionParams();
      conn = connection;
      await beginTransaction();
      // 从参数中解构需要的用户信息字段
      const { user_id, status, tnt_id, new_password, account_name, role_id, type, isChangeName, email } = params;

      // 兼容老版本
      // 查询用户角色关系表,检查用户是否已有角色
      const userRole = await dbUtils.query('select * from user_role_rl where user_id = ? and tnt_id=?', [
        user_id,
        tnt_id
      ]);

      if (Array.isArray(userRole)) {
        if (userRole.length) {
          // 更新用户角色关系的SQL
          await dbUtils.query('update user_role_rl set role_id = ? where user_id = ? and tnt_id=?', [
            role_id,
            user_id,
            tnt_id
          ]);

          const keys = userRole.map((item: any) => md5(`${RixEngineSessionKey}_${item.user_id}_${tnt_id}`));
          await delRedisByKey(keys);
        } else {
          // 为用户添加新角色的SQL
          await dbUtils.query('insert into user_role_rl (role_id,user_id,tnt_id,op_user_id) values (?, ?, ?, 0)', [
            role_id,
            user_id,
            tnt_id
          ]);
        }
      }

      const updateUserObj = buildSQLSetClause([
        ['type', type],
        ['status', status],
        ['email', email],
        ['account_name', isChangeName ? account_name : null],
        ['password', new_password ? new_password : null]
      ]);

      const updateUserSql = 'update user set ? where user_id=? and tnt_id=?';
      await query({ sql: updateUserSql, values: [updateUserObj, user_id, tnt_id] });

      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      getLogger('error').error(`editUser failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          conn!.release();
          conn = null;
        });
      }
      return Promise.reject(e);
    }
  }

  async sendEmailToUser(params: any) {
    const { account_name, password, tnt_id, isResetPwd, email } = params;

    let host_prefix = '';
    let pv_domain = '';
    let brand = '';
    const subject_str = isResetPwd ? 'Reset Password' : 'New Account';
    const sql = 'select host_prefix,pv_domain,brand from tenant where tnt_id = ?';

    const data = await dbUtils.query(sql, [tnt_id]);
    if (Array.isArray(data) && data.length) {
      host_prefix = data[0].host_prefix;
      pv_domain = data[0].pv_domain;
      brand = data[0].brand;
    }
    const cs_domain = getCsDomain(pv_domain, host_prefix);
    const domain = process.env.NODE_ENV === 'prod' ? `https://${cs_domain}` : `http://${cs_domain}`;
    if (email) {
      await sendEmail({
        from: `${brand}<<EMAIL>>`,
        to: `${email}`,
        subject: `${brand} ${subject_str} (${account_name})`,
        html: `
        <p>Congratulations, ${brand} Super Administrator Account had been successfully ${
          isResetPwd ? 'reset password!' : 'registered!'
        }</p>
        <p>Platform: ${domain}</p>
        <p>Username: ${account_name}</p>
        <p>Password: ${password}</p>
        ${process.env.NODE_ENV !== 'prod' ? '<p>Development</p>' : ''}
        `
      });
    }
  }

  async isAccountNameExists(params: TenantAPI.addUserParams, isEdit: boolean) {
    const { clause, params: values } = buildSQLWhereClause([
      ['account_name = ?', params.account_name],
      ['tnt_id = ?', params.tnt_id],
      ['user_id != ?', isEdit ? params.user_id : null]
    ]);

    const sql = `select account_name from user ${clause}`;

    return await dbUtils.query(sql, values);
  }
}

export const tntUserModel = new UserModel();
