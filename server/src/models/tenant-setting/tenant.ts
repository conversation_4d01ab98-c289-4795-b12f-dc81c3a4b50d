/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-22 11:18:48
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-28 15:13:14
 * @Description:
 */
import { getLogger } from '@/config/log4js';
import { getTestingBuyer, getTestingCap, getTestingEndpoint, getTestingQPS } from '@/constants/default-data';
import dbUtils, { PoolConnection, QueryParamType } from '@/db/mysql';
import { delRedisByKey, delRedisSetCountByValue, getRedisSetCountByValue, setRedisSetCountByValue } from '@/db/redis';
import { TenantAPI } from '@/types/tenant';
import { genEnCode, getConfig, md5 } from '@/utils';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';
const { Tnt_Host_Prefix_KEY, RixEngineSessionKey } = getConfig();

export const getCsDomain = (pv_domain: string, host_prefix: string) => {
  const isProd = process.env.NODE_ENV === 'prod';
  const isDev = process.env.NODE_ENV === 'development';

  // 开发环境固定域名
  if (isDev) {
    return 'allowed.console.rixengine.com';
  }

  // 有自定义域名时使用自定义域名
  if (pv_domain) {
    return isProd ? `console.${pv_domain}` : `console-t.${pv_domain}`;
  }

  // 默认使用主机前缀 + 基础域名
  const baseDomain = isProd ? 'console.rixengine.com' : 'console-t.rixengine.com';
  const cs_domain = `${host_prefix}.${baseDomain}`;
  console.log('cs_domain', cs_domain);
  return cs_domain;
};

class TenantModel {
  async getTenantList() {
    const { clause, params } = buildSQLWhereClause([
      ['t.tnt_id != ?', process.env.NODE_ENV === 'prod' ? 0 : null],
      ['t.status != ?', 3]
    ]);

    const sql = `select 
        t.tnt_name,
        t.email,
        t.phone,
        t.company,
        t.contact,
        t.token,
        t.host_prefix,
        t.cs_domain,
        t.pv_domain,
        t.status,
        t.pl_status,
        t.hm_status,
        t.tnt_id
      from tenant t
      ${clause} order by t.tnt_id desc`;
    return await dbUtils.query(sql, [...params]);
  }

  async addTenant(params: TenantAPI.AddTenantParams) {
    const token = genEnCode(32);
    const {
      tnt_name,
      email,
      host_prefix,
      company,
      contact,
      phone,
      // brand,
      pl_status = 2,
      hm_status = 2,
      pv_domain = ''
    } = params;
    const cs_domain = getCsDomain(pv_domain, host_prefix);

    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } = await dbUtils.getConnectionParams();
      conn = connection;
      await beginTransaction();
      // 新增一条 tenant 数据
      const oneTenantRow = [
        [tnt_name, host_prefix, cs_domain, email, token, company, contact, phone, pl_status, hm_status, pv_domain]
      ];
      const tenant = await query({
        sql: 'insert into tenant (tnt_name,host_prefix, cs_domain,email ,token,company, contact, phone,pl_status,hm_status,pv_domain) values ?',
        values: [oneTenantRow]
      });
      const host_key = md5(`${RixEngineSessionKey}_${Tnt_Host_Prefix_KEY}`);
      await setRedisSetCountByValue(host_key, [host_prefix, cs_domain]);

      const tnt_id = tenant.insertId;
      // 新增默认的stg atc model
      await query({ sql: 'insert into stg_atc_config (tnt_id,model) values(?,?)', values: [tnt_id, 3] });
      // 新增默认的testing buyer 和 testing site buyer
      const addTestingBuyer = getTestingBuyer(
        'Test-Buyer-System',
        md5(`${new Date().getTime()}Z8P5-BY${genEnCode(4)}`),
        tnt_id
      );
      // !暂时去除site
      // const addTestingSiteBuyer = getTestingBuyer(
      //   'Test-Buyer-Site-System',
      //   md5(`${new Date().getTime() + 1}Z8P5-BY${genEnCode(4)}`),
      //   tnt_id
      // );
      // const [buyer, site_buyer] = await Promise.all(
      //   [addTestingBuyer, addTestingSiteBuyer].map((sql) => query(sql))
      // );
      const [buyer] = await Promise.all([addTestingBuyer].map((sql) => query(sql)));
      // 新增默认的testing endpoint 和 testing site endpoint
      const buyer_id = buyer.insertId;
      // const site_buyer_id = site_buyer.insertId;
      const endpointSql = getTestingEndpoint(buyer_id, tnt_id);
      // const siteEndpointSql = getTestingEndpoint(site_buyer_id, tnt_id, true);
      // await Promise.all(
      //   [endpointSql, siteEndpointSql].map((sql) => query(sql))
      // );
      await Promise.all([endpointSql].map((sql) => query(sql)));
      // 新增默认的testing qps
      const qpsSqls = [
        getTestingQPS(buyer_id, tnt_id)
        // getTestingQPS(site_buyer_id, tnt_id)
      ];
      await Promise.all(qpsSqls.map((sql) => query(sql)));

      // 新增默认的testing cap
      const capSqls = [
        getTestingCap(buyer_id, tnt_id)
        // getTestingCap(site_buyer_id, tnt_id)
      ];
      await Promise.all(capSqls.map((sql) => query(sql)));

      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      getLogger('error').error(`addTenant failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          conn!.release();
          conn = null;
        });
      }
      return Promise.reject(e);
    }
  }

  async editTenant(params: TenantAPI.EditTenantParams) {
    const {
      tnt_id,
      tnt_name,
      host_prefix,
      email,
      status,
      company,
      contact,
      phone,
      pl_status = 2,
      hm_status = 2,
      pv_domain = ''
    } = params;
    const cs_domain = getCsDomain(pv_domain, host_prefix);
    let conn: PoolConnection | null = null;
    try {
      const { commit, connection, beginTransaction, query } = await dbUtils.getConnectionParams();
      conn = connection;
      await beginTransaction();

      const users = await query({
        sql: 'select user_id from user where status != 3 and tnt_id=?',
        values: [tnt_id]
      });
      const host_key = md5(`${RixEngineSessionKey}_${Tnt_Host_Prefix_KEY}`);
      const pv_domain_exist = await getRedisSetCountByValue(host_key, cs_domain);
      if (pv_domain_exist !== 1) {
        await setRedisSetCountByValue(host_key, [cs_domain]);
      }
      const keys = users.map(({ user_id }: { user_id: number }) => md5(`${RixEngineSessionKey}_${user_id}_${tnt_id}`));
      await delRedisByKey(keys);

      const updateTenantObj = buildSQLSetClause([
        ['tnt_name', tnt_name ? tnt_name : null],
        ['host_prefix', host_prefix ? host_prefix : null],
        ['email', email ? email : null],
        ['cs_domain', cs_domain],
        ['pv_domain', pv_domain],
        ['company', company],
        ['contact', contact],
        ['phone', phone],
        ['status', status],
        ['pl_status', pl_status],
        ['hm_status', hm_status]
      ]);

      if (!updateTenantObj) {
        return false;
      }

      await query({
        sql: 'update tenant set ? where tnt_id=?',
        values: [updateTenantObj, tnt_id]
      });

      await commit();
      if (conn) {
        conn.release();
        conn = null;
      }
      return Promise.resolve(true);
    } catch (e: any) {
      getLogger('error').error(`editTenant failed ${e.message}`);
      if (conn) {
        conn.rollback(() => {
          conn!.release();
          conn = null;
        });
      }
      return Promise.reject(e);
    }
  }

  async deleteTenant(params: TenantAPI.DeleteTenantParams) {
    const { tnt_id, tnt_name, host_prefix, cs_domain, email } = params;

    const bakTenantObj = {
      status: 3,
      tnt_name: `bak_name_${new Date().getTime()}_${tnt_name}`,
      host_prefix: `bak_hf_${new Date().getTime()}_${host_prefix}`,
      cs_domain: `bak_cd_${new Date().getTime()}_${cs_domain}`,
      email: `bak_email_${new Date().getTime()}_${email}`
    };

    const sqls: QueryParamType[] = [
      {
        sql: 'update tenant set ? where tnt_id=?',
        values: [bakTenantObj, tnt_id]
      },
      {
        sql: 'delete from user_role_rl where tnt_id=?',
        values: [tnt_id]
      }
    ];

    const users = await dbUtils.query('select user_id,account_name from user where tnt_id=?', [tnt_id]);

    if (Array.isArray(users) && users.length) {
      for (const user of users) {
        sqls.push({
          sql: 'update user set status = 3 ,account_name = ? where user_id=? and tnt_id=?',
          values: [`bak_name_${new Date().getTime()}_${user.account_name}_${tnt_name}`, user.user_id, tnt_id]
        });
      }
    }

    const host_key = md5(`${RixEngineSessionKey}_${Tnt_Host_Prefix_KEY}`);
    // TODO: 只删除了 tenant 的，并没有删除该租户下的所有用户状态
    await delRedisSetCountByValue(host_key, host_prefix);
    return !!(await dbUtils.execWaterfallTransaction(sqls));
  }

  async isExitTenant(params: TenantAPI.IsExitTenantParams, id?: number) {
    const { clause, params: values } = buildSQLWhereClause([
      ['(host_prefix=? or tnt_name=? or email=?)', [params.host_prefix, params.tnt_name, params.email]],
      ['tnt_id != ?', id ? id : null]
    ]);

    const flatValues = values.flat();

    const [{ count }] = await dbUtils.query(`select count(*) as count from tenant ${clause}`, flatValues);
    return count > 0;
  }

  async isExitEmail(params: TenantAPI.IsExitEmailParams) {
    const { clause, params: values } = buildSQLWhereClause([
      ['email=?', [params.email]]
    ]);
    const [{ count }] = await dbUtils.query(`select count(*) as count from tenant ${clause}`, values);
    return count > 0;
  }
}

export const tenantModel = new TenantModel();
