/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-12-13 18:48:29
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-03-07 14:29:09
 * @Description:
 */

import { queryStackOverflow } from '@/db/bigquery';
import dbUtils from '@/db/mysql';
import { FILTER_TNTS } from '@/services/ai-board/main-board';
import { BoardAPI } from '@/types/ai-board';
import { getConfig } from '@/utils';
import { updateBQConfigAdapter } from '@/utils/report/slim';
import { LabelGenerationParams, adaptGetBQTableName, concatSQLFragments } from '@rixfe/rix-tools';
import moment from 'moment';

const { BoardCaCheTime } = getConfig();

export const getNewBQTable = async (start_date: string, end_date: string, cur_time_zone: string, columns: string[] = []) => {
  await updateBQConfigAdapter();
  return await adaptGetBQTableName(
    {
      split_time: 1,
      start_date,
      end_date,
      tz_start_date: start_date,
      tz_end_date: end_date,
      tnt_id: 0,
      order: '',
      order_key: [],
      cur_user_id: 0,
      cur_role_id: 0,
      cur_time_zone,
      columns
    },
    { api_url: '' }
  );
};

class BoardModel {
  async getOverview(sql: { today: string; yesterday: string }, label: LabelGenerationParams) {
    const { today, yesterday } = sql;
    const [today_data, yesterday_data] = await Promise.all([
      queryStackOverflow(today, {
        cacheTime: BoardCaCheTime * 3,
        ...label
      }),
      queryStackOverflow(yesterday, {
        cacheTime: BoardCaCheTime * 3,
        ...label
      })
    ]);
    return [today_data, yesterday_data];
  }

  async getTodayHours(label: LabelGenerationParams): Promise<string> {
    const date = moment().subtract(1, 'days').format('YYYY-MM-DD');
    // 不能使用 billing 表
    const tableName = await getNewBQTable(date, date, 'Etc/UTC', ['hour', 'ad_format']);

    const sql = concatSQLFragments({
      select: [`FORMAT_TIMESTAMP('%Y-%m-%d %H:%M:%S', max(day_hour)) as last_hour`],
      from: tableName,
      where: [`date >= '${date}'`, `tnt_id not in (${FILTER_TNTS.join(',')})`],
      limit: '1'
    });
    const data = await queryStackOverflow(sql, { ...label });

    let last_hour = '';
    if (Array.isArray(data) && data.length > 0) {
      last_hour = data[0].last_hour;
    }
    return Promise.resolve(last_hour);
  }

  async getTopCountry(
    cur_hour: string,
    label: LabelGenerationParams,
    isLimit?: boolean,
    isSevenDays?: boolean
  ): Promise<BoardAPI.TopCountryItem[]> {
    const curUtcMoment = moment.tz(cur_hour, 'Etc/UTC');
    const date = curUtcMoment.clone().tz('Etc/UTC').subtract(1, 'days').format('YYYY-MM-DD');

    const start_date = curUtcMoment.clone().tz('Etc/UTC').subtract(7, 'days').format('YYYY-MM-DD');
    const end_date = curUtcMoment.clone().tz('Etc/UTC').subtract(1, 'days').format('YYYY-MM-DD');

    const tableName = await getNewBQTable(date, date, 'Etc/UTC', [
      // 不涉及 request，可以直接查 billing 表
      // 'country',
      // 'ad_format',
      // 'ad_size'
    ]);

    const sql = concatSQLFragments({
      select: [
        `country`,
        isLimit ? '' : 'tnt_id as tnt',
        `sum(buyer_net_revenue) as revenue`,
        `sum(seller_net_revenue) as sl_revenue`
      ],
      from: tableName,
      where: [
        isSevenDays ? `date >= '${start_date}' and date <= '${end_date}'` : `date = '${date}'`,
        `tnt_id not in (${FILTER_TNTS.join(',')})`
      ],
      groupBy: [isLimit ? '' : 'tnt_id', 'country'],
      having: [`revenue > 0`],
      orderBy: [`revenue desc`],
      limit: isLimit ? `5` : ''
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime * 6 * 6,
      ...label
    });
  }

  async getSevenDaysCountry(
    start_date: string,
    end_date: string,
    label: LabelGenerationParams
  ): Promise<BoardAPI.SevenDaysCountryItem[]> {
    const tableName = await getNewBQTable(start_date, end_date, 'Etc/UTC', [
      // 不涉及 request，可以直接查 billing 表
      // 'country',
      // 'ad_format',
      // 'ad_size'
    ]);

    const sql = concatSQLFragments({
      select: [`country`, `round(sum(buyer_net_revenue), 2) as revenue`, `FORMAT_TIMESTAMP('%Y-%m-%d', date) as date`],
      from: tableName,
      where: [`date >= '${start_date}' and date <= '${end_date}'`, `tnt_id not in (${FILTER_TNTS.join(',')})`],
      groupBy: [`country`, `date`],
      having: [`revenue > 0`],
      orderBy: [`date asc`, `revenue desc`]
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime * 6 * 6,
      ...label
    });
  }

  async getTopAdFormat(label: LabelGenerationParams): Promise<BoardAPI.TopAdFormatItem[]> {
    const date = moment().subtract(1, 'days').format('YYYY-MM-DD');
    const tableName = await getNewBQTable(date, date, 'Etc/UTC', [
      // 不涉及 request，可以直接查 billing 表
      // 'country',
      // 'ad_format',
      // 'ad_size'
    ]);

    const sql = concatSQLFragments({
      select: [`ad_format`, `concat(ad_width,'*',ad_height) as ad_size`, `sum(buyer_net_revenue) as revenue`],
      from: tableName,
      where: [`date = '${date}'`, `ad_format != 0`, `tnt_id not in (${FILTER_TNTS.join(',')})`],
      groupBy: [`ad_format`, `ad_size`],
      having: [`revenue > 0`],
      orderBy: [`revenue desc`]
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime * 6 * 6,
      ...label
    });
  }

  async getCTVSellers(): Promise<{ seller_id: string }[]> {
    const sql = `SELECT seller_id FROM seller where device_type = 3 and tnt_id not in (${FILTER_TNTS.join(',')})`;
    const seller_ids = `36088,36125,36126,36127,36131,36132,36147,36152,36155,36161,36162,36164,36165,36166,36169,36171,36175,36183,36190,36192,36193,36194,36197,36198,36201,36202,36205,36208,36210,36212,36219,36228,36237,36239,36240,36242,36246,36247,36248,36249,36252,36273,36297,36299,36305,36307,36315,36316,36320,36321,36324,36325,36342,36343,36345,36347,36350,36353,36356,36366,36368,36390,36397,36399,36400,36407,36422,36429,36432,36434,36443,36445,36449,36456,36467,36476,36495,36512,36542,36543,36548,36551,36554,36572,36582,36591,36598,36604,36610,36614,36625,36629,36630,36641,36642,36647,36661,36664,36667,36682,36693,36714,36718,36722,36724,36731,36735,36744,36748,36755,36762,36766,36769,36774,36777,36787,36791,36807,36816,36838,36841,36853,36856,36867,36880,36899,36929,36931,36933,36940,36949,36963,36974,36980,36998,37019,37027,37030,37040,37043,37050,37053,37057,37071,37089,37108,37123,37154,37160,37162,37169,37176,37180,37182,37192,37207,37208,37224,37237,37245,37250,37259,37262,37321,37327,37334,37343,37347,37365,37373,37375,37378,37381,37386,37394,37400,37404,37405,37409,37416,37418,37419,37420,37423,37429,37431,37432,37433,37434,37440,37442,37575,37578,37596,37603,37617,37623,37627,37638,37664,37667,37671,37682,37685,37706`;
    if (process.env.NODE_ENV !== 'prod') {
      const data = seller_ids.split(',').map((item) => ({ seller_id: item }));
      return Promise.resolve(data);
    }
    return await dbUtils.query(sql);
  }

  async getCTV(cur_hour: string, seller_ids_str: string, label: LabelGenerationParams): Promise<BoardAPI.CTVData[]> {
    if (!seller_ids_str) return Promise.resolve([]);
    const start_date = moment(cur_hour).subtract(7, 'days').format('YYYY-MM-DD');
    const end_date = moment(cur_hour).subtract(1, 'days').format('YYYY-MM-DD');

    const tableName = await getNewBQTable(start_date, end_date, 'Etc/UTC', [
      // 不涉及 request，可以直接查 billing 表
      // 'country',
      // 'ad_format',
      // 'ad_size'
    ]);

    const sql = concatSQLFragments({
      select: [`FORMAT_TIMESTAMP('%Y-%m-%d', date) as date`, `round(sum(buyer_net_revenue), 2) as revenue`],
      from: tableName,
      where: [
        `date >= '${start_date}' and date <= '${end_date}'`,
        `seller_id in (${seller_ids_str})`,
        `tnt_id not in (${FILTER_TNTS.join(',')})`
      ],
      groupBy: [`date`],
      orderBy: [`date asc`]
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime * 6 * 6,
      ...label
    });
  }

  async getAllTntRevenue(label: LabelGenerationParams): Promise<BoardAPI.TenantRevenueItem[]> {
    const date = moment().subtract(1, 'days').format('YYYY-MM-DD');

    const tableName = await getNewBQTable(date, date, 'Etc/UTC', [
      // 不涉及 request，可以直接查 billing 表
      // 'country',
      // 'ad_format',
      // 'ad_size'
    ]);

    const sql = concatSQLFragments({
      select: [`tnt_id`, `round(sum(buyer_net_revenue), 2) as revenue`],
      from: tableName,
      where: [`date = '${date}'`, `tnt_id not in (${FILTER_TNTS.join(',')})`],
      groupBy: [`tnt_id`],
      orderBy: [`revenue desc`]
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime,
      ...label
    });
  }

  async getTopCountryEcpmAndEcpr(
    cur_hour: string,
    top_countries_str: string,
    label: LabelGenerationParams
  ): Promise<BoardAPI.TopCountryEcpmAndEcprItem[]> {
    const start_date = moment(cur_hour).subtract(7, 'days').format('YYYY-MM-DD');
    const end_date = moment(cur_hour).subtract(1, 'days').format('YYYY-MM-DD');

    const tableName = await getNewBQTable(start_date, end_date, 'Etc/UTC', ['country', 'ad_format']);

    const sql = concatSQLFragments({
      select: [
        `FORMAT_TIMESTAMP('%Y-%m-%d', date) as date`,
        `country`,
        `ad_format`,
        `sum(request) request`,
        `sum(impression) impression`,
        `sum(buyer_net_revenue) revenue`
      ],
      from: tableName,
      where: [
        `date >= '${start_date}' and date <= '${end_date}'`,
        `country in (${top_countries_str})`,
        `ad_format != 0`,
        `tnt_id not in (${FILTER_TNTS.join(',')})`
      ],
      groupBy: [`country`, `ad_format`, `date`],
      orderBy: [`date asc`, `revenue desc`]
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime,
      ...label
    });
  }

  async getCTVEcpmAndEcpr(
    cur_hour: string,
    seller_ids_str: string,
    top_countries_str: string,
    label: LabelGenerationParams
  ): Promise<BoardAPI.TopCountryEcpmAndEcprItem[]> {
    if (!seller_ids_str) return Promise.resolve([]);
    const start_date = moment(cur_hour).subtract(7, 'days').format('YYYY-MM-DD');
    const end_date = moment(cur_hour).subtract(1, 'days').format('YYYY-MM-DD');

    const tableName = await getNewBQTable(start_date, end_date, 'Etc/UTC', ['country']);

    const sql = concatSQLFragments({
      select: [
        `FORMAT_TIMESTAMP('%Y-%m-%d', date) as date`,
        `country`,
        `sum(request) request`,
        `sum(impression) impression`,
        `sum(buyer_net_revenue) revenue`
      ],
      from: tableName,
      where: [
        `date >= '${start_date}' and date <= '${end_date}'`,
        `seller_id in (${seller_ids_str})`,
        `country in (${top_countries_str})`,
        `tnt_id not in (${FILTER_TNTS.join(',')})`
      ],
      groupBy: [`date`, `country`],
      orderBy: [`date asc`]
    });

    return await queryStackOverflow(sql, {
      cacheTime: BoardCaCheTime,
      ...label
    });
  }
}

export const boardModel = new BoardModel();
