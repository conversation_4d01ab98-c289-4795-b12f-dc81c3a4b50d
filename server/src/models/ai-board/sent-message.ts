/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-01-08 15:15:59
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-03-12 14:05:54
 * @Description:
 */

import { SentMsgAPI } from '@/types/ai-board';
import dbUtils from '@/db/mysql';
class SentMsgModel implements SentMsgAPI.SentMsgModel {
  async getAllSentMessage(): Promise<SentMsgAPI.SentMessageItem[]> {
    const sql = `select
        smt.id,
        smt.content,
        smt.ext_1,
        smt.mixed_key,
        smt.op_id,
        DATE_FORMAT(smt.create_time, '%Y-%m-%d %H:%i:%s') as create_time,
        u.account_name as op_name,
        t.tnt_id,
        t.tnt_name
      from (select * from sent_msg_log where type=1 and DATE(create_time) >= DATE(NOW()) - INTERVAL 30 DAY) as smt
      left join user as u on smt.op_id=u.user_id
      left join tenant as t on smt.tnt_id=t.tnt_id
      order by smt.create_time desc;`;

    return await dbUtils.query(sql);
  }
}

export const sentMsgModel = new SentMsgModel();
