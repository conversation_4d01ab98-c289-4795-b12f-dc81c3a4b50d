/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-10-20 15:40:24
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-03-07 11:03:27
 * @Description:
 */

import { queryStackOverflow, bigqueryStream } from '@/db/bigquery';

function joinSql(options: any) {
  const sql = `
    select
      ${options.normal_dimension_str ? `${options.normal_dimension_str}, ` : ''}
      coalesce(sum(request), 0) as request,
      coalesce(sum(response), 0) as response,
      coalesce(sum(click), 0) as click,
      coalesce(sum(impression), 0) as impression,
      coalesce(round(sum(revenue),2), 0) as revenue,
      coalesce(case when sum(request) = 0 then 0 else round((sum(response) * 100) / sum(request),2) end, 0) as fill_rate,
      coalesce(case when sum(response) = 0 then 0 else round(sum(impression) * 100 *1.0 / sum(response), 2) end, 0) as impression_rate,
      coalesce(case when sum(impression) = 0 then 0 else round(sum(click) / sum(impression),2) end, 0) as click_rate,
      coalesce(case when sum(impression) = 0 then 0 else round(cast (sum(revenue) * 1000*1.0 / sum(impression) as numeric), 2) end,0) as ecpm,
      coalesce(case when sum(request) = 0 then 0 else round(cast (sum(revenue) * 1000000 * 1.0/sum(request) as numeric), 2) end, 0) as ecpr
    from saas-373106.saas_others.kwai_buyer_report where ${options.condition}
    ${options.dimension ? `group by ${options.dimension}` : ''}
  `;
  return sql;
}
class KwaiModel {
  async getKwaiReport(options: any, tag: string) {
    const sql = `${joinSql(options)} ${options.order} ${options.limit}`;
    return queryStackOverflow(sql, { tag });
  }

  async countKwaiReport(options: any, tag: string) {
    const sql = `select count(*) as total from (${joinSql(options)}) as d`;
    return await queryStackOverflow(sql, { tag });
  }

  async downloadKwaiReport(options: any, tag: string) {
    const sql = `${joinSql(options)} ${options.order}`;
    return bigqueryStream(sql, { tag });
  }
}

export const kwaiReportModel = new KwaiModel();
