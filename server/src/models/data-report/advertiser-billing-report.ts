/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-27 15:43:07
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 15:48:42
 * @Description:
 */

import { bigqueryStream } from '@/db/bigquery';
import { LabelGenerationParams } from '@rixfe/rix-tools';

class AdvBillingModel {
  async downloadAllReport(sql: string, labels: LabelGenerationParams) {
    return await bigqueryStream(sql, labels);
  }
}

export const advBillingModel = new AdvBillingModel();
