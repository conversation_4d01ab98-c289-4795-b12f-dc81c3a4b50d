// ?types
import { BillingAPI } from '@/types/billing';
// ?utils
import { queryStackOverflow } from '@/db/bigquery';
import dbUtils from '@/db/mysql';
import { LabelGenerationParams } from '@rixfe/rix-tools';

class MonthlyBillingModel {
  async getMonthlyBillingList(sql: string, labels: LabelGenerationParams) {
    return queryStackOverflow(sql, {
      cacheTime: 60 * 60 * 6,
      ...labels
    }) as Promise<BillingAPI.MonthlyReportItem[]>;
  }

  async getDirectSeller() {
    const sql = 'select seller_id from seller where tnt_id = 1046 and relationship = 1';
    const result = await dbUtils.query(sql);
    return result?.map((item: any) => item.seller_id) || [];
  }

  async get1046Data(sql: string, labels: LabelGenerationParams) {
    return queryStackOverflow(sql, {
      cacheTime: 60 * 60 * 6,
      ...labels
    });
  }
}

export const monthlyBillingModel = new MonthlyBillingModel();
