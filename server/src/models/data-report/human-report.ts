/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-28 15:04:41
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 15:04:43
 * @Description:
 */
import { bigqueryStream, queryStackOverflow } from '@/db/bigquery';

// 直接查询正式数据
function joinSql(options: any) {
  const table = 'saas-373106.saas_others.human_report';
  const sql = `
    select
      ${options.normal_dimension_str ? `${options.normal_dimension_str}, ` : ''}
      ${options.metrics_str ? `${options.metrics_str}, ` : ''} 
      COUNT(*) OVER() AS total
    from ${table} as i where ${options.condition}
    ${options.dimension ? `group by ${options.dimension}` : ''}
  `;
  return sql;
}

class HumanReport {
  async getHumanReport(options: any, tag: string) {
    const sql = `${joinSql(options)} ${options.order} ${options.limit}`;
    return await queryStackOverflow(sql, { tag });
  }

  async downloadHumanReport(options: any, tag: string) {
    const sql = `${joinSql(options)} ${options.order}`;
    return await bigqueryStream(sql, { tag });
  }
}

export const humanModel = new HumanReport();
