/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-06-28 15:06:38
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-27 15:49:03
 * @Description:
 */

import { bigqueryStream } from '@/db/bigquery';
import { LabelGenerationParams } from '@rixfe/rix-tools';

class PubBillingModel {
  async downloadAllReport(sql: string, labels: LabelGenerationParams) {
    return await bigqueryStream(sql, labels);
  }
}

export const pubBillingModel = new PubBillingModel();
