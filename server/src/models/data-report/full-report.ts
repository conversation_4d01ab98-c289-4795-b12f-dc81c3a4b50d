/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2022-12-26 14:16:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-29 12:02:35
 * @Description:
 */

import { QpsLevel } from '@/constants';
import { bigqueryStream, queryStackOverflow } from '@/db/bigquery';
import dbUtils from '@/db/mysql';
import { buildSQLWhereClause, concatSQLFragments, LabelGenerationParams } from '@rixfe/rix-tools';
import moment from 'moment-timezone';

class FullReportModel {
  async getNewHoursToday(
    tnt_id: number[],
    table: string,
    timeZone: string,
    labels: LabelGenerationParams
  ): Promise<any> {
    const utc0_today = moment().tz('Etc/UTC').format('YYYY-MM-DD');
    const tnt_str = tnt_id?.length ? `tnt_id in (${tnt_id})` : '';

    const sql = concatSQLFragments({
      select: [
        `EXTRACT(HOUR FROM TIMESTAMP(FORMAT_TIMESTAMP("%Y-%m-%d %H:%M:%S", max(day_hour), '${timeZone}'))) + 1 as hours`
      ],
      from: table,
      where: [tnt_str, `date = '${utc0_today}'`],
      limit: '1'
    });
    const data = await queryStackOverflow(sql, {
      cacheTime: 60 * 5,
      ...labels
    });
    return data[0]?.hours ?? 0;
  }

  async getNewDashboardList(sql: string, labels: LabelGenerationParams): Promise<any> {
    return await queryStackOverflow(sql, {
      cacheTime: 60 * 5,
      ...labels
    });
  }

  async newDownloadAllReport(sql: string, labels: LabelGenerationParams): Promise<any> {
    return await bigqueryStream(sql, { ...labels });
  }

  async getConfigQps(tnt_id: number) {
    const sql = `
      select
        q.server_region as region,
        q.ots_id as ots_id,
        q.seller_id as pub_id,
        q.buyer_id as buyer_id,
        q.id as id,
        q.level as level,
        q.qps as qps,
        b.buyer_name as buyer_name,
        s.seller_name as seller_name,
        s.seller_id as seller_id,
        a.account_name as account_name,
        a.status as account_status,
        sa.app_name as app_name,
        sa.app_id as app_id,
        sp.plm_name as plm_name,
        sp.plm_id as plm_id
      from (select * from qps where  level not in (${QpsLevel['supply + ad_format']},${QpsLevel['supply + bundle,']},${QpsLevel['supply + country']})) as q
      left join (select buyer_name, buyer_id from buyer) as b on b.buyer_id=q.buyer_id and q.level != ${QpsLevel.supply}
      left join (select plm_name, plm_id, app_id from seller_placement) as sp on sp.plm_id = q.seller_id and q.level = ${QpsLevel['demand + supply-placement']}
      left join (select app_name, app_id, seller_id from seller_app) as sa on ((sa.app_id = q.seller_id and q.level = ${QpsLevel['demand + supply-app']}) or 
      (q.level = ${QpsLevel['demand + supply-placement']} and sa.app_id = sp.app_id))
      left join (select seller_name, seller_id from seller) as s on ((s.seller_id=q.seller_id and 
        (q.level != ${QpsLevel.demand} or
        q.level != ${QpsLevel['demand + ad_format']} or
        q.level != ${QpsLevel['demand + country']})) or 
      (q.level = ${QpsLevel['demand + supply-app']} and (sa.seller_id = s.seller_id)) or 
      (q.level = ${QpsLevel['demand + supply-placement']} and sa.seller_id = s.seller_id))
      left join user as a on a.user_id=q.op_id 
      order by q.update_time desc;
    `;
    return await dbUtils.query(sql);
  }

  async getSupplyAndDemandConfigQps(tnt_id: number[]): Promise<any> {
    const { clause, params: values } = buildSQLWhereClause([
      ['level in (?)', [QpsLevel.demand, QpsLevel.supply]],
      ['tnt_id in (?)', tnt_id ? tnt_id : null]
    ]);

    const sql = `select qps,server_region region,seller_id,buyer_id,level from qps ${clause}`;
    return await dbUtils.query(sql, values);
  }
}

export const fullReportModel = new FullReportModel();
