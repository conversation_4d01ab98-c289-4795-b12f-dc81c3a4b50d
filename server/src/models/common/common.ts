/*
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-09 19:30:28
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-27 11:58:51
 * @Description:
 */
import dbUtils from '@/db/mysql';
import dbUtilsAat from '@/db/aat_mysql';
import { CommonAPI } from '@/types/common';

class CommonModel {
  async getBuyerIntegrationType(): Promise<CommonAPI.IntegrationTypeItem[]> {
    const sql = `select itg_name, id from buyer_integration_type order by id`;
    return await dbUtils.query(sql);
  }

  async getSellerIntegrationType(): Promise<CommonAPI.IntegrationTypeItem[]> {
    const sql = `select itg_name, id from seller_integration_type order by id`;
    return await dbUtils.query(sql);
  }

  /**
   * 从 aat 数据库获取数据
   */
  getAatDict(params: CommonAPI.DictItem) {
    return dbUtilsAat.query(params.dict_content);
  }

  /**
   * 从 saas 数据库获取数据
   */
  getSaasDict(params: CommonAPI.DictItem) {
    return dbUtils.query(params.dict_content);
  }
}

export const commonModel = new CommonModel();
