/*
 * @Author: chen<PERSON><PERSON>@algorix.co
 * @Date: 2022-12-02 10:09:49
 * @LastEditors: chenmudan
 * @LastEditTime: 2024-01-04 16:38:47
 * @Description:
 */

import { StatusType } from '@/constants';
import dbUtils from '@/db/mysql';
import { UserAPI } from '@/types/user';
import { buildSQLSetClause } from '@rixfe/rix-tools';

class UserModel {
  async getOneByUserNameAndPassword(options: UserAPI.LoginParams) {
    const sql = `
      select
        admin_id,
        account_name,
        display_name,
        password,
        ad.status,
        ad.role,
        ad.role_id,
        r.type as role_type
      from admin as ad
      left join admin_role as r on r.id=ad.role_id
      where account_name=? and password=? and ad.status=? limit 1
    `;
    const values = [options.account_name, options.password, StatusType.Active];
    return await dbUtils.query(sql, values);
  }

  async resetPasswordByUserId(options: UserAPI.ResetPasswordParams) {
    const sql = `update admin set password=? where admin_id=?`;
    return !!(await dbUtils.query(sql, [options.new_password, options.admin_id]));
  }

  async confirmPassword(params: UserAPI.ConfirmPasswordParams) {
    const { old_password, admin_id } = params;
    const sql = `select admin_id from admin where admin_id = ?
    and status=? and password=? limit 1`;
    return await dbUtils.query(sql, [admin_id, StatusType.Active, old_password]);
  }

  async validPassword(params: any) {
    const { password, admin_id } = params;
    const sql = `select admin_id from admin where admin_id=? and password=? and status=? limit 1`;
    return await dbUtils.query(sql, [admin_id, password, StatusType.Active]);
  }

  async updateCompanyUser(params: UserAPI.UpdateCompanyUserParams) {
    const { display_name, admin_id } = params;
    const sql = `update admin set display_name=? where admin_id=?`;
    return !!(await dbUtils.query(sql, [display_name, admin_id]));
  }

  async getSpecialAccounts() {
    const query = `
      SELECT
        sa.id,
        sa.special_user_id,
        sa.linked_user_ids,
        sa.status as link_status,
        sa.op_id,
        u.type,
        u.account_name,
        u.tnt_id,
        t.tnt_name
      FROM
        switch_account sa
        INNER JOIN user u ON sa.special_user_id = u.user_id
        LEFT JOIN tenant t ON u.tnt_id = t.tnt_id
      ORDER BY sa.update_time DESC
    `;
    return dbUtils.query(query);
  }

  async getSpecialAccountById(id: number) {
    const query = `
      SELECT
        sa.special_user_id,
        sa.linked_user_ids,
        sa.status as link_status
      FROM
        switch_account sa
      WHERE
        sa.id = ?
    `;
    return dbUtils.query(query, [id]);
  }

  async getLinkedUsers(linkedUserIds: number[]) {
    const query = `
      SELECT u.user_id, u.account_name, u.type, u.tnt_id, u.role AS role_id, t.tnt_name, u.status
      FROM user u
          LEFT JOIN tenant t ON u.tnt_id = t.tnt_id
      WHERE
          u.user_id IN (?)
    `;
    return dbUtils.query(query, [linkedUserIds]);
  }

  // 判断是否是特殊租户账号(1083, allTenant)
  async isSpecialAccount(special_user_id: number) {
    const query = `select user_id from user where user_id = ? and status = 1 and tnt_id = 1083 limit 1`;
    return await dbUtils.query(query, [special_user_id]);
  }

  async addUserLink(formData: any) {
    const { special_user_id, link_users_ids = [], link_status = 1, admin_id } = formData;
    const sql = `insert into switch_account (special_user_id, linked_user_ids, status, op_id) values (?, ?, ?, ?)`;
    return !!(await dbUtils.query(sql, [special_user_id, link_users_ids.join(','), link_status, admin_id]));
  }

  async updateUserLink(formData: any) {
    const { id, link_users_ids = [], link_status, admin_id } = formData;

    const updateObj = buildSQLSetClause([
      ['linked_user_ids', link_users_ids.join(',')],
      ['status', link_status],
      ['op_id', admin_id]
    ]);

    if (!updateObj) {
      return false;
    }

    const sql = `update switch_account set ? where id=?`;
    const values = [updateObj, id];
    return !!(await dbUtils.query(sql, values));
  }
}

export const userModel = new UserModel();
