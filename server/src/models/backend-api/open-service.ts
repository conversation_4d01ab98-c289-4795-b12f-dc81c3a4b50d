import { ProfitType } from '@/constants';
import { RoleTypeToNumber, UserType } from '@/constants/permission';
import { QueryParamType, TransactionReturnType } from '@/db/mysql';
import { setRedisSetCountByValue } from '@/db/redis';
import { roleModel } from '@/models/permission/role';
import { OpenServiceAPI } from '@/types/open-service';
import { genEnCode, getConfig, md5 } from '@/utils';
import {
  generateHostPrefix,
  getCsDomain,
  getTestingBuyer,
  getTestingCap,
  getTestingEndpoint,
  getTestingQPS
} from '@/utils/open-service';

const { passwordConfig, RixEngineSessionKey, Tnt_Host_Prefix_KEY } = getConfig();

const encryptionStr = passwordConfig.platform_prefix;

export type RegisterUser = {
  account_name: string;
  password: string;
  cs_domain: string;
};

export type Tenant = {
  tntId: number;
  hostPrefix: string;
  csDomain: string;
};

class OpenServiceModel {
  async addTenant(params: OpenServiceAPI.RegisterUserParams, { query }: TransactionReturnType): Promise<Tenant> {
    // 模糊查询 host_prefix 为 topon- 开头的所有数据，并确保生成的 hostPrefix 不重复
    let hostPrefixList = await this.getHostPrefixList(
      2, // 2 表示 topon 的租户
      query
    );
    let hostPrefix = generateHostPrefix();
    // 如果查询到数据，则需要判断是否存在重复，如果存在重复，则需要重新生成
    while (hostPrefixList.includes(hostPrefix)) {
      hostPrefix = generateHostPrefix();
      // 重新获取一次最新的 hostPrefixList，防止并发下重复
      hostPrefixList = await this.getHostPrefixList(2, query);
    }

    const tntName = hostPrefix;
    const email = params.email;
    const company = params.company;
    const contact = params.contact;
    const phone = params.phone;
    const csDomain = getCsDomain('', hostPrefix);
    const token = genEnCode(32);

    // 新增一条 tenant 数据
    // tnt_type 2 表示 topon 的租户
    // tnt_type 1 (默认值)表示 saas 的租户(我们自己的品牌的租户)
    const oneTenantRow = [[tntName, hostPrefix, csDomain, email, token, 2, company, contact, phone]];
    const tenant = await query({
      sql: 'insert into tenant (tnt_name, host_prefix, cs_domain, email ,token, tnt_type, company, contact, phone) values ?',
      values: [oneTenantRow]
    });

    // 后续 admin 平台的租户操作需要用到 这个 redis 数据
    const host_key = md5(`${RixEngineSessionKey}_${Tnt_Host_Prefix_KEY}`);

    await setRedisSetCountByValue(host_key, [hostPrefix, csDomain]);

    return {
      tntId: tenant.insertId,
      hostPrefix,
      csDomain
    };
  }

  async addOneUser(
    tenant: Tenant,
    params: OpenServiceAPI.RegisterUserParams,
    { query }: TransactionReturnType
  ): Promise<RegisterUser> {
    const { tntId, csDomain } = tenant;
    const { ext_uid } = params;
    // 校验 ext_uid 唯一性
    const extUidRows = await query({
      sql: 'select user_id from user where extra_provider_id = ? limit 1',
      values: [String(ext_uid)]
    });
    if (extUidRows && extUidRows.length > 0) {
      throw new Error('ext_uid already exists');
    }
    const accountName = `topon-${genEnCode(4, false)}`;
    const password = genEnCode(24);
    const md5Password = md5(encryptionStr + password);
    const token = md5(`${new Date().getTime()}${accountName}${tntId}${genEnCode(6)}`);
    const email = ''; // 可以为空
    const role = 1; // 表示系统创建的角色
    const type = 1; // 用户类型, 为租户用户
    // 需要从数据库获取 系统角色（租户id 为 0） ToponUser 的 roleId
    // 默认值 1 是表示的是 租户管理员的权限
    const roleId = (await roleModel.getRoleIdByName('ToponUser', 0)) || 1;

    const oneUserRow = [[accountName, md5Password, role, tntId, type, email, token, ext_uid]];

    // extra_provider_id 是外部服务的用户唯一标识，便于后续的数据请求
    const user = await query({
      sql: 'insert into user (account_name, password,role,tnt_id,type,email,token, extra_provider_id) values ?',
      values: [oneUserRow]
    });

    // 将用户与角色关联
    const addUserRoleSql = 'insert into user_role_rl (role_id, user_id,op_user_id,tnt_id) values ?';
    const oneUserRoleRow = [[roleId, user.insertId, 0, tntId]];
    await query({
      sql: addUserRoleSql,
      values: [oneUserRoleRow]
    });

    return {
      account_name: accountName,
      password,
      cs_domain: csDomain
    };
  }

  async addDefaultConfig(tntId: number, tools: TransactionReturnType): Promise<boolean> {
    // 不同的配置，可以使用 promise all 来处理，互不影响

    // act config 的默认配置
    const actConfigSQL: QueryParamType = {
      sql: 'INSERT INTO stg_atc_config (tnt_id, model) VALUES (?, ?)',
      // auto-control model: 3
      values: [tntId, 3]
    };

    await Promise.all([
      tools.query(actConfigSQL),
      // buyer 相关的配置
      this.addBuyerConfig(tntId, tools),
      // seller 相关的配置
      this.addSellerConfig(tntId, tools)
    ]);

    return true;
  }

  private async addBuyerConfig(tntId: number, tools: TransactionReturnType): Promise<boolean> {
    // 新增默认的testing buyer
    const testingBuyerConfigSQL: QueryParamType = getTestingBuyer(
      'Test-Buyer-System',
      md5(`${new Date().getTime()}Z8P5-BY${genEnCode(4)}`),
      tntId
    );
    // testing buyer 相关的配置: endpoint, qps_config, cap_config
    const buyer = await tools.query(testingBuyerConfigSQL);
    const buyerId = buyer.insertId;

    const endpointSql = getTestingEndpoint(buyerId, tntId);
    const qpsSql = getTestingQPS(buyerId, tntId);
    const capSql = getTestingCap(buyerId, tntId);
    await Promise.all([tools.query(endpointSql), tools.query(qpsSql), tools.query(capSql)]);

    return true;
  }

  private async addSellerConfig(tntId: number, tools: TransactionReturnType): Promise<boolean> {
    //  新增一个 seller 账号
    const sellerName = 'topon-white-seller';
    const token = md5(`${new Date().getTime()}QT1T-SL${genEnCode(4)}`);
    const userToken = md5(`${new Date().getTime()}${sellerName}${tntId}${genEnCode(6)}`);
    // 自定义默认值：relationship = 2, profit_ratio = 30
    const relationship = 2;
    const profitRatio = 30;
    const opId = 0;

    // 创建租户一个默认的 seller 账号
    const userRow = await tools.query({
      sql: 'insert into user (account_name,type,role,status,tnt_id,token) values ?',
      values: [[[`${sellerName}`, UserType.Supply, 2, 1, tntId, userToken]]]
    });
    const userInsertId = userRow.insertId;

    const sellerRow = [[`${sellerName}`, 1, relationship, tntId, userInsertId, token]];

    const seller = await tools.query({
      sql: 'insert into seller (seller_name, integration_type, relationship, tnt_id, user_id, token) values ?',
      values: [sellerRow]
    });
    const sellerId = seller.insertId;

    // 新增 seller 账号的权限和 profit
    await Promise.all([
      await tools.query({
        sql: 'insert into user_role_rl (user_id,role_id,op_user_id,tnt_id) values ?',
        values: [[[userInsertId, RoleTypeToNumber['Supply User'], 0, tntId]]]
      }),
      await tools.query({
        sql: 'insert into profit(type, seller_id, profit_ratio, op_id, tnt_id, status) values ?',
        values: [[[ProfitType.Seller, sellerId, profitRatio, opId, tntId, 1]]]
      })
    ]);

    return true;
  }

  private async getHostPrefixList(type: number, query: TransactionReturnType['query']): Promise<string[]> {
    const sql = `select host_prefix from tenant where tnt_type = ?`;
    return query({
      sql,
      values: [type]
    });
  }
}

export const openServiceModel = new OpenServiceModel();
