import dbUtils from '@/db/mysql';
import { buildSQLSetClause } from '@rixfe/rix-tools';

class SchainTruncationModel {
  getSchainTruncationList(formData: any) {
    const sql = `
      select
        stg.id,
        stg.level,
        stg.tnt_id,
        stg.status,
        t.tnt_name,
        stg.buyer_id,
        b.buyer_name,
        stg.schain_hops,
        stg.op_id,
        stg.create_time,
        stg.update_time,
        (case when ad.account_name is null then u.account_name else ad.account_name end) as op_name
      from
        stg_schain_truncation_config stg
        left join tenant t on stg.tnt_id = t.tnt_id
        left join buyer b on stg.buyer_id = b.buyer_id
        left join admin ad on stg.op_id = ad.admin_id
        left join user u on stg.op_id = u.user_id
      order by stg.update_time desc
    `;

    return dbUtils.query(sql);
  }

  async getSchainTruncationConfig(formData: any) {
    const { tnt_id, buyer_id = 0 } = formData;
    const sql = `select id from stg_schain_truncation_config where tnt_id = ? and buyer_id = ? limit 1`;
    return dbUtils.query(sql, [tnt_id, buyer_id]);
  }

  async updateSchainTruncationConfig(formData: any) {
    const { id, level, tnt_id, buyer_id, schain_hops, cur_admin_id, status } = formData;

    const updateObj = buildSQLSetClause([
      ['level', level],
      ['tnt_id', tnt_id],
      ['buyer_id', buyer_id],
      ['schain_hops', schain_hops],
      ['op_id', cur_admin_id],
      ['status', status]
    ]);

    if (!updateObj) {
      return false;
    }

    const sql = `update stg_schain_truncation_config set ? where id=?`;
    return !!(await dbUtils.query(sql, [updateObj, id]));
  }

  async addSchainTruncationConfig(formData: any) {
    const { level, tnt_id, buyer_id = 0, schain_hops = 0, cur_admin_id = 0, status = 1 } = formData;
    const sql = `insert into stg_schain_truncation_config (level, tnt_id, buyer_id, schain_hops, op_id, status) values (?, ?, ?, ?, ?, ?)`;
    return !!(await dbUtils.query(sql, [level, tnt_id, buyer_id, schain_hops, cur_admin_id, status]));
  }
}

export const schainTruncationModel = new SchainTruncationModel();
