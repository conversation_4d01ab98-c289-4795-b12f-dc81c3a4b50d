/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-01-25 11:44:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-01-25 19:29:06
 * @Description:
 */

import { TransparencyAPI } from '@/types/transparency';
import { uploadFile } from '@/db/bigquery';
import { CommonAPI } from '@/types/common';
class TransparencyModel implements TransparencyAPI.SellerJsonModel {
  async upload(params: CommonAPI.UploadParams) {
    return await uploadFile(params);
  }
}
export const transparencyModel = new TransparencyModel();
