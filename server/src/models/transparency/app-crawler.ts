import dbUtils from '@/db/aat_mysql';
import { buildSQLWhereClause } from '@rixfe/rix-tools';
import moment from 'moment';

class TransparencyAppCrawlerModel {
  async createAppCrawler(options: any) {
    const { app_bundle_id, platform, op_user } = options;

    // create_ts 需要传入零时区的秒级时间戳
    const sql = `INSERT INTO aat_newer_app (app_bundle_id, platform, status, op_user, create_ts) VALUES (?, ?, ?, ?, ?)`;
    const currentTimestamp = moment.utc().unix(); // 获取零时区的秒级时间戳
    const values = [app_bundle_id, platform, 0, op_user, currentTimestamp];
    return await dbUtils.query({ sql, values });
  }

  async batchCreateAppCrawler(bundles: Array<{ app_bundle_id: string; platform: number }>, op_user: string) {
    if (!bundles.length) {
      return { affectedRows: 0 };
    }

    const currentTimestamp = moment.utc().unix(); // 获取零时区的秒级时间戳
    // insert ignore 避免重复插入
    const sql = `INSERT IGNORE INTO aat_newer_app (app_bundle_id, platform, status, op_user, create_ts) VALUES ?`;

    const values = bundles.map((bundle) => [bundle.app_bundle_id, bundle.platform, 0, op_user, currentTimestamp]);

    return await dbUtils.query({ sql, values: [values] });
  }

  async updateAppCrawler(options: any) {
    const { app_id, app_bundle_id, platform, op_user } = options;

    const sql = `UPDATE aat_newer_app SET app_bundle_id = ?, platform = ?, op_user = ? WHERE app_id = ?`;
    const values = [app_bundle_id, platform, op_user, app_id];
    return await dbUtils.query({ sql, values });
  }

  async getAppCrawlerList(options: any) {
    const { app_bundle_id = [], platform, status } = options;

    const { clause, params: values } = buildSQLWhereClause([
      ['app_bundle_id IN (?)', app_bundle_id.length ? app_bundle_id : null],
      ['platform IN (?)', platform ? platform : null],
      ['status IN (?)', status ?? null]
    ]);

    const sql = `SELECT app_id, app_bundle_id, platform, status, (case when op_user = '' OR op_user IS NULL then 'system' else op_user end) as op_user, create_ts FROM aat_newer_app ${clause} ORDER BY create_ts DESC`;

    return await dbUtils.query({ sql, values });
  }

  // async getAppCrawlerByAppId(app_id: number) {
  //   const sql = 'SELECT app_id, app_bundle_id, platform, status, op_user, create_ts FROM aat_newer_app WHERE app_id = ?';
  //   const values = [app_id];
  //   return await dbUtils.query({ sql, values });
  // }

  async isExistAppCrawler(app_bundle_id: string, platform: number) {
    const sql = 'SELECT COUNT(*) AS count FROM aat_newer_app WHERE app_bundle_id = ? AND platform = ?';
    const values = [app_bundle_id, platform];
    const result = await dbUtils.query({ sql, values });
    return result[0].count > 0;
  }
}

export const transparencyAppCrawlerModel = new TransparencyAppCrawlerModel();
