/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-19 18:03:48
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-20 11:01:06
 * @Description:
 */

import dbUtils from '@/db/aat_mysql';
import { TransparencyAPI } from '@/types/transparency';
import { buildSQLWhereClause } from '@rixfe/rix-tools';

class TransparencyAppInfoModel {
  updateTransparencyAppInfoTag(options: any) {
    const { app_id, update_tag } = options;
    const sql = 'update aat_app set update_tag = ? where app_id = ?';
    return dbUtils.query({ sql, values: [update_tag, app_id] });
  }

  getTransparencyAppInfo(options: any) {
    const { app_bundle_id = [], aat_domain = [] } = options;

    const { clause, params: values } = buildSQLWhereClause([
      ['app.app_bundle_id IN (?)', app_bundle_id.length ? app_bundle_id : null],
      [
        '(developer.multi_aat_domain IN (?) OR developer.aat_domain IN (?))',
        aat_domain.length ? [aat_domain, aat_domain] : null
      ]
    ]);

    if (values.length === 0) {
      return [];
    }

    const newValues = values.reduce((acc: any, curr: any) => {
      if (Array.isArray(curr) && Array.isArray(curr[0])) {
        acc.push(...curr);
      } else {
        acc.push(curr);
      }
      return acc;
    }, []);

    const sql = `
      SELECT 
        app.app_id,
        app.app_bundle_id,
        app.app_name,
        app.developer_name,
        app.developer_website,
        app.developer_id,
        app.store_url,
        app.update_tag,
        app.category,
        app.rating_score,
        app.downloads_info,
        developer.aat_domain,
        developer.multi_aat_domain,
        developer.support_aat,
        developer.aat_url,
        IF(developer.multi_aat_domain = '', developer.aat_domain, developer.multi_aat_domain) as tmp_domain
      FROM 
        aat_app AS app 
        LEFT JOIN aat_developer AS developer 
        ON (app.developer_id=developer.developer_id)
        ${clause}
      `;

    return dbUtils.query({ sql, values: newValues });
  }

  getSellerInfo(options: any) {
    const { tmp_domain = [], supply_tag = '' } = options;

    const { clause, params: values } = buildSQLWhereClause([
      ['(supply_tag = "" OR supply_tag = ?)', supply_tag ? supply_tag : null],
      ['domain IN (?)', tmp_domain.length ? tmp_domain : null]
    ]);

    const sql = `
      SELECT domain, supply_tag, seller_id, seller_type, name FROM aat_supply_chain ${clause}
    `;

    return dbUtils.query({ sql, values });
  }

  updateTransparencyAppInfo(options: TransparencyAPI.UpdateTransparencyAppInfoParams) {
    const { multi_aat_domain = '', developer_id } = options;

    const sql = `update aat_developer set multi_aat_domain = ? where developer_id = ?`;
    return dbUtils.query({ sql, values: [multi_aat_domain, developer_id] });
  }
}

export const transparencyAppInfoModel = new TransparencyAppInfoModel();
