/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-05-18 20:52:13
 * @LastEditors: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 17:06:14
 * @Description:
 */
import dbUtils from '@/db/mysql';
import { ConfigAPI } from '@/types/config';
import { buildSQLSetClause } from '@rixfe/rix-tools';

class EcprModel implements ConfigAPI.EcprModel {
  async isEcprConfigExist(params: any) {
    const { tnt_id, server_region } = params;
    const sql = `select count(id) as count from ecpr_level_config where tnt_id = ? and server_region = ?`;
    const res = await dbUtils.query(sql, [tnt_id, server_region]);
    if (Array.isArray(res) && res.length > 0) {
      return { count: res[0].count };
    }
    return { count: 0 };
  }

  async getEcprConfigList() {
    const sql = `select id,type,server_region,filter_qps,newer_qps,tier_ecpr,tier_qps,op_id,status,tnt_id from ecpr_level_config order by update_time desc`;
    const uSql = `select admin_id,account_name from admin`;
    const [ecprList, userList] = await Promise.all([dbUtils.query(sql), dbUtils.query(uSql)]);
    const res = ecprList.map((item: any) => {
      const user = userList.find((user: any) => user.admin_id === item.op_id);
      return {
        ...item,
        op_name: user ? user.account_name : ''
      };
    });
    return res;
  }

  async addEcprConfig(params: ConfigAPI.AddEcprParams) {
    const { type, server_region, filter_qps, newer_qps, tier_ecpr, tier_qps, op_id, status = 1, tnt_id } = params;
    const sql = `insert into ecpr_level_config (type, server_region,filter_qps,newer_qps,tier_ecpr,tier_qps,op_id,status,tnt_id ) values ?`;

    return !!(await dbUtils.query(sql, [
      [
        [
          type,
          server_region,
          filter_qps,
          newer_qps,
          tier_ecpr.tags.join(','),
          tier_qps.tags.join(','),
          op_id,
          status,
          tnt_id
        ]
      ]
    ]));
  }

  async editEcprConfig(params: ConfigAPI.EditEcprParams) {
    const { id, type, server_region, filter_qps, newer_qps, tier_ecpr, tier_qps, op_id, status, tnt_id } = params;

    const ecprObj = buildSQLSetClause([
      ['type', type],
      ['server_region', server_region],
      ['filter_qps', filter_qps],
      ['newer_qps', newer_qps],
      ['tier_ecpr', tier_ecpr.tags.join(',')],
      ['tier_qps', tier_qps.tags.join(',')],
      ['op_id', op_id],
      ['status', status],
      ['tnt_id', tnt_id]
    ]);

    if (!ecprObj) {
      return false;
    }

    const sql = `update ecpr_level_config set ? where id = ? and tnt_id = ?`;
    return !!(await dbUtils.query(sql, [ecprObj, id, tnt_id]));
  }

  async deleteEcprConfig(params: ConfigAPI.DeleteEcprParams) {
    const { id, tnt_id } = params;
    const sql = `delete from ecpr_level_config where id = ? and tnt_id = ?`;
    return !!(await dbUtils.query(sql, [id, tnt_id]));
  }
}

export const ecprModel = new EcprModel();
