/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-19 12:22:20
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-02-26 18:27:21
 * @Description:
 */

import dbUtils from '@/db/mysql';
import { ConfigAPI } from '@/types/config';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

class StgModelV2 implements ConfigAPI.StgChainModel {
  async isStgChainExist(params: ConfigAPI.CheckStgParams) {
    const { tnt_id, developer_website_domain, id, type } = params;
    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id in (?)', tnt_id],
      ['developer_website_domain = ?', developer_website_domain],
      ['type = ?', type],
      ['id != ?', id ? id : null]
    ]);

    const sql = `select count(id) as count from stg_chain_map_v2 ${clause} limit 1`;
    const res = await dbUtils.query(sql, values);

    if (Array.isArray(res) && res.length > 0) {
      return { count: res[0].count };
    }
    return { count: 0 };
  }

  async getStgChainList() {
    const sql = `
    select 
      stg.id,
      stg.developer_website_domain,
      stg.publisher_id,
      stg.op_id,
      stg.status,
      stg.tnt_id,
      stg.type,
      stg.update_time,
      stg.type,
      tnt.tnt_name,
      (case when ad.account_name is null then concat(u.account_name,'(',u.user_id,')') else ad.account_name end) as op_name
      from stg_chain_map_v2 stg
      left join tenant tnt on stg.tnt_id = tnt.tnt_id
      left join admin ad on stg.op_id = ad.admin_id
      left join user u on stg.op_id = u.user_id
      order by stg.update_time desc`;
    return dbUtils.query(sql);
  }

  async addStgChain(params: ConfigAPI.AddStgParams) {
    const { op_id, status = 1, tnt_id, developer_website_domain, publisher_id, type = 1 } = params;
    const tnts = Array.isArray(tnt_id) ? tnt_id : [tnt_id];

    const values = tnts.map((tnt) => [developer_website_domain, publisher_id, op_id, status, tnt, type]);

    const sql = `insert into stg_chain_map_v2 (developer_website_domain,publisher_id,op_id,status,tnt_id,type) values ?`;
    return !!(await dbUtils.query(sql, [values]));
  }

  async updateStgChain(params: ConfigAPI.UpdateStgParams) {
    const { id, op_id, status, tnt_id, developer_website_domain, publisher_id, type = 1 } = params;

    const stgObj = buildSQLSetClause([
      ['op_id', op_id],
      ['status', status],
      ['tnt_id', tnt_id],
      ['developer_website_domain', developer_website_domain],
      ['publisher_id', publisher_id],
      ['type', type]
    ]);

    const sql = `update stg_chain_map_v2 set ? where id = ?`;
    return !!(await dbUtils.query(sql, [stgObj, id]));
  }
}

export const stgModelV2 = new StgModelV2();
