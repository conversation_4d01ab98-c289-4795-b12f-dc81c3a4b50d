import dbUtils from '@/db/mysql';
import { ConfigAPI } from '@/types/config';
import { buildSQLSetClause } from '@rixfe/rix-tools';

class CustomPlacementModel {
  async addCustom(options: ConfigAPI.AddCustomParams) {
    const {
      buyer_id,
      tnt_id,
      bundle = '',
      app_id = '',
      banner_tag_id = '',
      native_tag_id = '',
      video_tag_id = '',
      rewarded_video_tag_id = '',
      op_id
    } = options;
    const sql =
      'insert into custom_demand_placement (buyer_id, tnt_id, bundle, app_id, banner_tag_id, native_tag_id, video_tag_id, rewarded_video_tag_id, op_id) values (?, ?, ?, ?, ?, ?, ?, ?, ?)';

    return !!(await dbUtils.query(sql, [
      buyer_id,
      tnt_id,
      bundle,
      app_id,
      banner_tag_id,
      native_tag_id,
      video_tag_id,
      rewarded_video_tag_id,
      op_id
    ]));
  }

  async updateCustom(options: ConfigAPI.UpdateCustomParams) {
    const { id, tnt_id, app_id, banner_tag_id, native_tag_id, video_tag_id, rewarded_video_tag_id, status, op_id } =
      options;

    const customObj = buildSQLSetClause([
      ['app_id', app_id],
      ['banner_tag_id', banner_tag_id],
      ['native_tag_id', native_tag_id],
      ['video_tag_id', video_tag_id],
      ['rewarded_video_tag_id', rewarded_video_tag_id],
      ['status', status],
      ['op_id', op_id]
    ]);

    if (!customObj) {
      return false;
    }

    const sql = `UPDATE custom_demand_placement SET ? WHERE id=? AND tnt_id=?`;

    const result = await dbUtils.query(sql, [customObj, id, tnt_id]);
    return !!result;
  }

  async getAppAndBundleList(options: ConfigAPI.AddKwaParams) {
    const { buyer_id, tnt_id } = options;
    const sql = `select id, bundle, app_id from custom_demand_placement where buyer_id=? and tnt_id=?`;
    return await dbUtils.query(sql, [buyer_id, tnt_id]);
  }

  async getCustomList() {
    const sql = `
      select
        cs.id as id,
        cs.buyer_id as buyer_id,
        cs.tnt_id as tnt_id,
        cs.bundle as bundle,
        cs.app_id as app_id,
        cs.banner_tag_id as banner_tag_id,
        cs.native_tag_id as native_tag_id,
        cs.video_tag_id as video_tag_id,
        cs.rewarded_video_tag_id as rewarded_video_tag_id,
        cs.status as status,
        cs.op_id as op_id,
        DATE_FORMAT(cs.update_time, '%Y-%m-%d %H:%i:%s') as update_time,
        ad.account_name as op_name,
        b.buyer_name as buyer_name,
        t.tnt_name as tnt_name
      from custom_demand_placement as cs
      left join admin as ad on ad.admin_id=cs.op_id
      left join buyer as b on b.buyer_id=cs.buyer_id
      left join tenant as t on t.tnt_id=cs.tnt_id
      order by cs.update_time desc
    `;
    return await dbUtils.query(sql);
  }
}

export const customModel = new CustomPlacementModel();
