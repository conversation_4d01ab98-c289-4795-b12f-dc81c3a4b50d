/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2024-04-03 16:54:53
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-04-03 19:13:54
 * @Description:
 */

import dbUtils from '@/db/mysql';
import { ConfigAPI } from '@/types/config';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

class PixlPrebidModel implements ConfigAPI.PixlPrebidModel {
  async isPixlPrebidExist(params: ConfigAPI.CheckPixlParams) {
    const { tnt_id, id } = params;

    const seller_id = params.seller_id?.length ? params.seller_id : [0];
    const buyer_id = params.buyer_id?.length ? params.buyer_id : [0];

    const { clause, params: values } = buildSQLWhereClause([
      ['seller_id in (?)', seller_id],
      ['buyer_id in (?)', buyer_id],
      ['tnt_id = ?', tnt_id],
      ['id != ?', id ? id : null]
    ]);

    const sql = `select count(id) as count from stg_pixalate_prebid ${clause} limit 1`;

    const res = await dbUtils.query(sql, values);
    if (Array.isArray(res) && res.length > 0) {
      return { count: res[0].count };
    }
    return { count: 0 };
  }

  async getPixlPrebidList() {
    const sql = `
    select 
      pixl.id,
      pixl.seller_id,
      pixl.buyer_id,
      pixl.op_id,
      pixl.status,
      pixl.tnt_id,
      pixl.update_time,
      tnt.tnt_name,
      ad.account_name as op_name
      from stg_pixalate_prebid pixl
      left join tenant tnt on pixl.tnt_id = tnt.tnt_id
      left join admin ad on pixl.op_id = ad.admin_id
      order by pixl.update_time desc`;
    return dbUtils.query(sql);
  }

  async addPixlPrebid(params: ConfigAPI.AddPixlParams) {
    const { op_id, status = 1, tnt_id, seller_id = [0], buyer_id = [] } = params;
    const values: any[][] = [];

    for (const bid of buyer_id) {
      for (const sid of seller_id) {
        values.push([op_id, status, tnt_id, sid || 0, bid]);
      }
    }

    if (values.length === 0) {
      return false;
    }

    const sql = `insert into stg_pixalate_prebid (op_id,status,tnt_id,seller_id,buyer_id) values ?`;
    return !!(await dbUtils.query(sql, [values]));
  }

  async updatePixlPrebid(params: ConfigAPI.UpdatePixlParams) {
    const { id, op_id, status, tnt_id, seller_id, buyer_id } = params;

    const pixlObj = buildSQLSetClause([
      ['op_id', op_id],
      ['status', status],
      ['tnt_id', tnt_id]
    ]);

    if (!pixlObj) {
      return false;
    }

    const { clause, params: values } = buildSQLWhereClause([
      ['id = ?', id],
      ['seller_id = ?', seller_id],
      ['buyer_id = ?', buyer_id]
    ]);

    const sql = `update stg_pixalate_prebid set ? ${clause}`;
    return !!(await dbUtils.query(sql, [pixlObj, ...values]));
  }
}

export const pixlPrebidModel = new PixlPrebidModel();
