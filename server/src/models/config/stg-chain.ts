/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-19 12:22:20
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-26 18:27:21
 * @Description:
 */
/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-05-18 20:52:13
 * @LastEditors: chen<PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2023-08-08 17:06:14
 * @Description:
 */
import dbUtils from '@/db/mysql';
import { ConfigAPI } from '@/types/config';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

class StgModel implements ConfigAPI.StgChainModel {
  async isStgChainExist(params: ConfigAPI.CheckStgParams) {
    const { tnt_id, bundle, id, type } = params;

    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id in (?)', tnt_id],
      ['bundle = ?', bundle],
      ['type = ?', type],
      ['id != ?', id ? id : null]
    ]);

    const sql = `select count(id) as count from stg_chain_map ${clause} limit 1`;
    const res = await dbUtils.query(sql, values);
    if (Array.isArray(res) && res.length > 0) {
      return { count: res[0].count };
    }
    return { count: 0 };
  }

  async getStgChainList() {
    const sql = `
    select 
      stg.id,
      stg.bundle,
      stg.publisher_id,
      stg.op_id,
      stg.status,
      stg.tnt_id,
      stg.type,
      stg.update_time,
      stg.type,
      tnt.tnt_name,
      (case when ad.account_name is null then u.account_name else ad.account_name end) as op_name
      from stg_chain_map stg
      left join tenant tnt on stg.tnt_id = tnt.tnt_id
      left join admin ad on stg.op_id = ad.admin_id
      left join user u on stg.op_id = u.user_id
      order by stg.update_time desc`;
    return dbUtils.query(sql);
  }

  async addStgChain(params: ConfigAPI.AddStgParams) {
    const { op_id, status = 1, tnt_id, bundle, publisher_id, type = 1 } = params;
    const tnts = Array.isArray(tnt_id) ? tnt_id : [tnt_id];
    const values = tnts.map((tnt) => [bundle, publisher_id, op_id, status, tnt, type]);

    const sql = `insert into stg_chain_map (bundle,publisher_id,op_id,status,tnt_id,type) values ?`;
    return !!(await dbUtils.query(sql, [values]));
  }

  async updateStgChain(params: ConfigAPI.UpdateStgParams) {
    const { id, op_id, status, tnt_id, bundle, publisher_id, type = 1 } = params;

    const stgObj = buildSQLSetClause([
      ['op_id', op_id],
      ['status', status],
      ['tnt_id', tnt_id],
      ['bundle', bundle],
      ['publisher_id', publisher_id],
      ['type', type]
    ]);

    const sql = `update stg_chain_map set ? where id = ?`;
    return !!(await dbUtils.query(sql, [stgObj, id]));
  }
}

export const stgModel = new StgModel();
