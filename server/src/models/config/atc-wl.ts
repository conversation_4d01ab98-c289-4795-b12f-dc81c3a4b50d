/*
 * @Author: <PERSON><PERSON><PERSON><PERSON> chenmu<PERSON>@algorix.co
 * @Date: 2023-08-08 16:25:16
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-11-28 16:47:51
 * @Description:
 */
import { StatusMap } from '@/constants';
import dbUtils from '@/db/mysql';
import { ConfigAPI } from '@/types/config';
import { DemandAPI } from '@/types/demand';
import { SupplyAPI } from '@/types/supply';
import { buildSQLSetClause } from '@rixfe/rix-tools';

class AtcWLModel {
  async addAtc(options: ConfigAPI.AddAtcParams) {
    const {
      seller_id,
      bundle = [],
      buyer_id = 0,
      ad_format = [],
      ad_size = [],
      tnt_id,
      region,
      country = [],
      expired,
      op_id
    } = options;

    const country_str = country.join(',');
    const bundle_str = bundle.join(',');
    const ad_format_str = ad_format.join(',');
    const ad_size_str = ad_size.join(',');
    const time = new Date(`${expired} 23:59:59`).getTime();
    const expired_time = Math.floor(time / 1000);

    // 根据seller_id数组生成多个sql语句
    const values = seller_id.map((sid: number) => [
      sid,
      buyer_id || 0,
      tnt_id,
      region,
      country_str,
      bundle_str,
      ad_format_str,
      ad_size_str,
      expired_time,
      StatusMap.Active,
      op_id
    ]);

    const sql = `insert into stg_atc_white_list(seller_id, buyer_id, tnt_id, region, country, bundle, ad_format, ad_size, expired, status, op_id) values ? on duplicate key update country=values(country), bundle=values(bundle), ad_format=values(ad_format), ad_size=values(ad_size), expired=values(expired), status=values(status), op_id=values(op_id)`;
    return !!(await dbUtils.query(sql, [values]));
  }

  async updateAtc(options: ConfigAPI.UpdateAtcParams) {
    const { id, country = [], ad_size = [], ad_format = [], expired, status, bundle = [], op_id, tnt_id } = options;
    const country_str = country.join(',');
    const bundle_str = bundle.join(',');
    const ad_format_str = ad_format.join(',');
    const ad_size_str = ad_size.join(',');
    const time = new Date(`${expired} 23:59:59`).getTime();
    const expired_time = Math.floor(time / 1000);

    const atcObj = buildSQLSetClause([
      ['country', country_str],
      ['ad_size', ad_size_str],
      ['ad_format', ad_format_str],
      ['bundle', bundle_str],
      ['expired', expired_time],
      ['status', status],
      ['op_id', op_id]
    ]);

    if (!atcObj) {
      return false;
    }

    const sql = `update stg_atc_white_list set ? where id=? and tnt_id=?`;
    return !!(await dbUtils.query(sql, [atcObj, id, tnt_id]));
  }

  // 获取所有的列表
  async getActList() {
    const sql = `
    select
      st.id as id,
      st.expired as expired,
      st.status as status,
      st.update_time as update_time,
      st.tnt_id as tnt_id,
      st.seller_id as seller_id,
      st.buyer_id as buyer_id,
      st.region as region,
      st.country as country,
      st.bundle as bundle,
      st.ad_format as ad_format,
      st.ad_size as ad_size,
      ad.account_name as op_name,
      s.seller_name as seller_name,
      b.buyer_name as buyer_name,
      t.tnt_name as tnt_name
    from stg_atc_white_list as st
    left join admin as ad on ad.admin_id=st.op_id
    left join seller as s on s.seller_id=st.seller_id
    left join buyer as b on b.buyer_id=st.buyer_id
    left join tenant as t on t.tnt_id=st.tnt_id
    order by st.update_time desc
    `;
    return await dbUtils.query(sql);
  }

  async getSupplyList(): Promise<SupplyAPI.SupplyListItem[]> {
    const sql = `select seller_id, seller_name,tnt_id from seller`;
    return await dbUtils.query(sql);
  }

  async getDemandList(): Promise<DemandAPI.DemandListItem[]> {
    const sql = `select buyer_id, buyer_name,tnt_id from buyer`;
    return await dbUtils.query(sql);
  }
}

export const actModel = new AtcWLModel();
