/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-08-15 15:10:37
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-20 18:59:33
 * @Description:
 */
import dbUtils from '@/db/mysql';
import { ConfigAPI } from '@/types/config';
import { StatusMap } from '@/constants';
import { buildSQLSetClause } from '@rixfe/rix-tools';

class KwaPlacementModel {
  async addKwa(options: ConfigAPI.AddKwaParams) {
    const { bundle, buyer_id, tnt_id, op_id, app_id, native_pid, inters_pid, reward_pid, token, mixed_status } =
      options;
    const sql = `
      insert into kwai_placement (buyer_id, tnt_id, bundle, app_id, native_pid, inters_pid, reward_pid, token, mixed_status, status, op_id) values ?`;

    return !!(await dbUtils.query(sql, [
      [
        [
          buyer_id,
          tnt_id,
          bundle,
          app_id,
          native_pid || '',
          inters_pid || '',
          reward_pid || '',
          token,
          mixed_status,
          StatusMap.Active,
          op_id
        ]
      ]
    ]));
  }

  async updateKwa(options: ConfigAPI.UpdateKwaParams) {
    const { id, tnt_id, op_id, app_id, native_pid, inters_pid, reward_pid, token, mixed_status, status } = options;

    const kwaObj = buildSQLSetClause([
      ['app_id', app_id],
      ['native_pid', native_pid || ''],
      ['inters_pid', inters_pid || ''],
      ['reward_pid', reward_pid || ''],
      ['token', token],
      ['mixed_status', mixed_status],
      ['status', status],
      ['op_id', op_id]
    ]);

    const sql = `update kwai_placement set ? where id=? and tnt_id=?`;

    return !!(await dbUtils.query(sql, [kwaObj, id, tnt_id]));
  }

  // 只有新增才需要判断存不存在
  async isExistKwa(options: ConfigAPI.AddKwaParams) {
    const { buyer_id, tnt_id, bundle } = options;
    const sql = `select id from kwai_placement where bundle=? and buyer_id=? and tnt_id=? limit 1`;
    return await dbUtils.query(sql, [bundle, buyer_id, tnt_id]);
  }

  // 获取所有的列表
  async getKwaList() {
    const sql = `
      select
        kp.id as id,
        kp.buyer_id as buyer_id,
        kp.tnt_id as tnt_id,
        kp.bundle as bundle,
        kp.app_id as app_id,
        kp.native_pid as native_pid,
        kp.inters_pid as inters_pid,
        kp.reward_pid as reward_pid,
        kp.token as token,
        kp.mixed_status as mixed_status,
        kp.status as status,
        kp.op_id as op_id,
        DATE_FORMAT(kp.update_time, '%Y-%m-%d %H:%i:%s') as update_time,
        ad.account_name as op_name,
        b.buyer_name as buyer_name,
        t.tnt_name as tnt_name
      from kwai_placement as kp
      left join admin as ad on ad.admin_id=kp.op_id
      left join buyer as b on b.buyer_id=kp.buyer_id
      left join tenant as t on t.tnt_id=kp.tnt_id
      order by kp.update_time desc
    `;
    return await dbUtils.query(sql);
  }
}

export const kwaModel = new KwaPlacementModel();
