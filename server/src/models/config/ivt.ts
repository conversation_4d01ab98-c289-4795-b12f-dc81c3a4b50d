/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2023-09-19 12:22:20
 * @LastEditors: 袁跃钊 <EMAIL>
 * @LastEditTime: 2024-01-10 10:51:17
 * @Description:
 */

import dbUtils from '@/db/mysql';
import { ConfigAPI } from '@/types/config';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';

const getValues = (params: ConfigAPI.AddIvtParams) => {
  const { op_id, status = 1, tnt_id, buyer_id = [], seller_id = [], type, ratio } = params;

  const bundle = params?.bundle ? params.bundle.split(',').sort().join(',') : '';
  const country = params?.country ? params.country.join(',') : '';

  const values: any[][] = [];
  const b_ids = buyer_id.length ? buyer_id : [0];
  const s_ids = seller_id.length ? seller_id : [0];

  for (const b_id of b_ids) {
    for (const s_id of s_ids) {
      values.push([b_id, s_id, op_id, status, tnt_id, type, ratio, bundle, country]);
    }
  }
  return values;
};

class IvtModel implements ConfigAPI.IvtModel {
  async isIvtExist(params: ConfigAPI.CheckIvtParams) {
    const { tnt_id, bundle = '', id, type, buyer_id, seller_id } = params;

    const keys = 'id,status,tnt_id,buyer_id,seller_id,type,ratio,bundle';

    const { clause, params: values } = buildSQLWhereClause([
      ['tnt_id = ?', tnt_id],
      ['type = ?', type],
      ['id != ?', id ? id : null],
      ['buyer_id in (?)', buyer_id?.length ? buyer_id : 0],
      ['seller_id in (?)', seller_id?.length ? seller_id : 0],
      ['bundle = ?', bundle]
    ]);

    const sql = `select ${keys} from stg_traffic_quality ${clause} limit 1`;
    // const level_tnt = tnt_id && !buyer_id && !seller_id && !bundle;

    // const level_tnt_s_or_b = tnt_id && (buyer_id || seller_id) && !bundle;
    // const level_tnt_s_or_b_condition = `(${
    //   buyer_id ? `buyer_id in (${buyer_id})` : ''
    // } ${buyer_id && seller_id ? 'or' : ''} ${
    //   seller_id ? `seller_id in (${seller_id})` : ''
    // })`;

    // const level_tnt_s_and_b = tnt_id && buyer_id && seller_id && !bundle;
    // const level_tnt_s_and_b_condition = `(buyer_id in (${buyer_id}) and seller_id in (${seller_id}))`;

    // const level_tnt_bundle = tnt_id && bundle && !buyer_id && !seller_id;
    // const level_tnt_bundle_condition = `bundle = '${bundle}'`;

    // const level_tnt_s_or_b_bundle = tnt_id && bundle && (buyer_id || seller_id);
    // const level_tnt_s_or_b_bundle_condition = `(buyer_id in (${
    //   buyer_id || 0
    // }) or seller_id in (${seller_id || 0})) and bundle = '${bundle}'`;

    // const level_tnt_s_and_b_bundle = tnt_id && bundle && buyer_id && seller_id;
    // const level_tnt_s_and_b_bundle_condition = `buyer_id in (${buyer_id}) and seller_id in (${seller_id}) and bundle = '${bundle}'`;
    // if (level_tnt) {
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${id_str} limit 1`;
    // }
    // if (level_tnt_s_or_b) {
    //   const condition = `and (${level_tnt_s_or_b_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }
    // if (level_tnt_s_and_b) {
    //   const condition = `and (${level_tnt_s_and_b_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }
    // if (level_tnt_bundle) {
    //   const condition = `and (${level_tnt_bundle_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }
    // if (level_tnt_s_or_b_bundle) {
    //   const condition = `and (${level_tnt_s_or_b_bundle_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }
    // if (level_tnt_s_and_b_bundle) {
    //   const condition = `and (${level_tnt_s_and_b_bundle_condition})`;
    //   sql = `select ${keys} from stg_traffic_quality where tnt_id = ${tnt_id} and type=${type} ${condition} ${id_str} limit 1`;
    // }

    return await dbUtils.query(sql, values);
  }

  async getIvtList() {
    const sql = `
    select 
      ivt.id,
      ivt.tnt_id,
      ivt.buyer_id,
      ivt.seller_id,
      ivt.bundle,
      ivt.ratio,
      ivt.op_id,
      ivt.type,
      ivt.status,
      ivt.update_time,
      ivt.country,
      tnt.tnt_name,
      case when ad.account_name is not null then ad.account_name else concat(u.account_name,'(Std)') end as op_name,
      sl.seller_name,
      byr.buyer_name
      from stg_traffic_quality ivt
      left join tenant tnt on ivt.tnt_id = tnt.tnt_id
      left join admin ad on ivt.op_id = ad.admin_id
      left join user u on ivt.op_id = u.user_id
      left join seller sl on ivt.seller_id = sl.seller_id
      left join buyer byr on ivt.buyer_id = byr.buyer_id 
      order by ivt.update_time desc`;
    return dbUtils.query(sql);
  }

  async addIvt(params: ConfigAPI.AddIvtParams, shouldUpdateOnDuplicate: boolean = false) {
    const values = getValues(params);
    let sql =
      'insert into stg_traffic_quality (buyer_id,seller_id,op_id,status,tnt_id,type,ratio,bundle,country) values ?';

    if (shouldUpdateOnDuplicate) {
      sql +=
        ' on duplicate key update buyer_id = values(buyer_id),seller_id=values(seller_id),op_id=values(op_id),status=values(status),type=values(type),ratio=values(ratio),bundle=values(bundle),country=values(country)';
    }

    return !!(await dbUtils.query(sql, [values]));
  }

  async updateIvt(params: ConfigAPI.UpdateIvtParams) {
    const {
      id,
      op_id,
      status,
      tnt_id,
      buyer_id = 0,
      seller_id = 0,
      bundle = '',
      country = [],
      ratio = 1,
      type = 1
    } = params;

    const ivtObj = buildSQLSetClause([
      ['op_id', op_id],
      ['status', status],
      ['tnt_id', tnt_id],
      ['buyer_id', buyer_id],
      ['seller_id', seller_id],
      ['type', type],
      ['ratio', ratio],
      ['bundle', bundle],
      ['country', country.join(',')]
    ]);

    const sql = 'update stg_traffic_quality set ? where id = ? and tnt_id = ?';

    return !!(await dbUtils.query(sql, [ivtObj, id, tnt_id]));
  }
}

export const ivtModel = new IvtModel();
