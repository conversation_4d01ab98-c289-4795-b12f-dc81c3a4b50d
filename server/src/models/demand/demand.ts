/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 16:35:48
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-28 11:43:00
 * @Description:
 */

import { DemandAPI } from '@/types/demand';
import dbUtils from '@/db/mysql';
import { ProfitType, StatusMap } from '@/constants';
import { AuthLevel } from '@/constants/demand';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';
class DemandModel {
  async getDemandList(isTesting: boolean = false) {
    const { clause, params } = buildSQLWhereClause([['status != ?', isTesting ? StatusMap.Testing : null]]);

    const sql = `
        select
            byr.buyer_id as buyer_id,
            byr.buyer_name as buyer_name,
            byr.integration_type as integration_type,
            byr.status as status,
            byr.profit_model as profit_model,
            byr.rev_share_ratio as rev_share_ratio,
            byr.auction_type as auction_type,
            byr.imp_track_type as imp_track_type,
            byr.user_id as user_id,
            byr.schain_required as schain_required,
            byr.filter_mraid as filter_mraid,
            byr.max_hm_ivt_ratio as max_hm_ivt_ratio,
            byr.max_pxl_ivt_ratio as max_pxl_ivt_ratio,
            byr.idfa_required as idfa_required,
            byr.multi_format as multi_format,
            byr.banner_multi_size as banner_multi_size,
            byr.banner_transfer_format as banner_transfer_format,
            byr.burl_track_type as burl_track_type,
            byr.pass_display_manager as pass_display_manager,
            byr.token as token,
            byr.skoverlay,
            byr.autostore,
            byr.block_off_store_app,
            byr.auto_qps,
            byr.tnt_id as tnt_id,
            byr.native_format as native_format,
            byr.native_root_key as native_root_key,
            bit.itg_name as integration_type_desc,
            t.tnt_name as tnt_name,
            p.id as profit_id,
            p.profit_ratio as profit_ratio,
            p.status as profit_status,
            u.account_name as demand_account_name,
            u.status as demand_account_status,
            coalesce(bpt.dp_name, '') as partner_name,
            coalesce(bpt.partner_id, 0) as partner_id,
            coalesce(bpt.dp_id, 0) as dp_id
        from (select * from buyer ${clause}) as byr
        left join buyer_parent as bpt on bpt.dp_id=byr.dp_id
        left join buyer_integration_type as bit on byr.integration_type = bit.id
        left join profit as p on byr.buyer_id = p.buyer_id and p.type = ?
        left join user as u on byr.user_id = u.user_id
        left join tenant as t on byr.tnt_id = t.tnt_id
        order by byr.update_time desc
    `;
    return await dbUtils.query(sql, [...params, ProfitType.Demand]);
  }

  async updateDemand(params: DemandAPI.UpdateDemandParams) {
    const {
      buyer_id,
      tnt_id,
      banner_multi_size,
      banner_transfer_format,
      burl_track_type,
      pass_display_manager,
      autostore,
      skoverlay,
      block_off_store_app,
      auto_qps,
      max_hm_ivt_ratio,
      max_pxl_ivt_ratio,
      native_format,
      native_root_key
    } = params;

    const updateObj = buildSQLSetClause([
      ['banner_multi_size', banner_multi_size],
      ['banner_transfer_format', banner_transfer_format],
      ['burl_track_type', burl_track_type],
      ['pass_display_manager', pass_display_manager],
      ['skoverlay', skoverlay],
      ['autostore', autostore],
      ['block_off_store_app', block_off_store_app],
      ['auto_qps', auto_qps],
      ['max_hm_ivt_ratio', max_hm_ivt_ratio],
      ['max_pxl_ivt_ratio', max_pxl_ivt_ratio],
      ['native_format', native_format],
      ['native_root_key', native_root_key]
    ]);

    if (!updateObj) {
      // 没有需要更新的字段，提前返回
      return false;
    }

    const sql = `update buyer set ? where buyer_id=? and tnt_id=?`;
    const values = [updateObj, buyer_id, tnt_id];

    return !!(await dbUtils.query(sql, values));
  }

  // partner获取关联的上游
  async getPartnerDemand() {
    const sql = `select buyer_name, buyer_id, dp_id from buyer where  dp_id > 0`;
    return await dbUtils.query(sql);
  }

  async getDemandEndpoint() {
    const sql = `select * from buyer_endpoint`;
    return await dbUtils.query(sql);
  }

  async getDemandAuth(buyer_id: number, tnt_id: number) {
    const sql = `
            select
                ca.id as id,
                ca.level as level,
                ca.pub_id as seller_id,
                ca.buyer_id as buyer_id,
                ssp.seller_name as seller_name,
                ssp.status as seller_status,
                ssp.integration_type as integration_type
            from (select * from config_auth where tnt_id=?) as ca
            left join seller as ssp on ca.pub_id = ssp.seller_id
            where level = ? and buyer_id = ?
        `;
    return await dbUtils.query(sql, [tnt_id, AuthLevel.Supply, buyer_id]);
  }
}

export const demandModel = new DemandModel();
