/*
 * @Author: ch<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2023-07-31 23:22:35
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-22 11:02:50
 * @Description:
 */

import dbUtils from '@/db/mysql';
import { StatusType } from '@/constants';
import { AdminSettingAPI } from '@/types/admin';
import { buildSQLSetClause } from '@rixfe/rix-tools';

class UserModel {
  async addOneUser(options: AdminSettingAPI.AddUserParams) {
    const { account_name, display_name, password, role_id, remark = '' } = options;
    const sql = `insert into admin(account_name, display_name, password, status, role_id, remark) values(?,?,?,?,?,?)`;
    const values = [account_name, display_name, password, StatusType.Active, role_id, remark];
    return !!(await dbUtils.query(sql, values));
  }

  async updateOneUser(options: AdminSettingAPI.EditUserParams) {
    const { display_name, admin_id, status, remark = '', role_id } = options;

    const useObj = buildSQLSetClause([
      ['display_name', display_name],
      ['status', status],
      ['remark', remark],
      ['role_id', role_id]
    ]);

    if (!useObj) {
      return false;
    }

    const sql = `update admin set ? where admin_id=?`;
    const values = [useObj, admin_id];
    return !!(await dbUtils.query(sql, values));
  }

  // 判断用户是否存在 同一个租户的account_name不能重复
  async isUserExist(account_name: string) {
    const sql = `select admin_id from admin where account_name=?`;
    return await dbUtils.query(sql, [account_name]);
  }

  async getAllUserList() {
    const sql = `
      select 
        admin_id,
        account_name,
        display_name,
        ad.status,
        ad.remark,
        ad.role_id,
        ad.role,
        r.role_name,
        DATE_FORMAT(ad.update_time, '%Y-%m-%d %H:%i:%s') as update_time 
      from admin as ad
      left join admin_role as r on r.id=ad.role_id
      where ad.status != ?
    `;
    return await dbUtils.query(sql, [StatusType.Delete]);
  }

  async resetPassword(params: AdminSettingAPI.UpdateUserPassword) {
    const { admin_id, password, account_name } = params;
    const sql = `update admin set password=? where admin_id=? and account_name=?`;
    return !!(await dbUtils.query(sql, [password, admin_id, account_name]));
  }
}

export const adminUserModel = new UserModel();
