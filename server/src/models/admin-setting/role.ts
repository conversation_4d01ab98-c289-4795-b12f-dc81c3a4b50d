/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-12-20 17:27:17
 * @LastEditors: 袁跃钊 <PERSON><PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-28 15:11:34
 * @Description:
 */

import { StatusType } from '@/constants';
import { RoleType } from '@/constants/platform-setting';
import dbUtils, { QueryParamType } from '@/db/mysql';
import { AdminSettingAPI } from '@/types/admin';

class RoleModel {
  async addRole(options: AdminSettingAPI.AddRole) {
    const { role_name, remark } = options;
    const sql = `insert into admin_role (role_name, remark, type) values (?, ?, ?)`;
    // 新增角色只能是普通角色
    const values = [role_name, remark || '', RoleType['Normal Role']];
    return await dbUtils.query(sql, values);
  }

  // 系统角色不支持更改状态 名称
  async updateRole(options: AdminSettingAPI.UpdateRole) {
    const { id, remark, role_name } = options;
    const sql = `update admin_role set remark=?, role_name=? where id=?`;
    return !!(await dbUtils.query(sql, [remark || '', role_name, id]));
  }

  async getAllRole() {
    const sql = `
      select
        adr.id as id,
        adr.role_name as role_name,
        adr.status as status,
        adr.remark as remark,
        adr.type,
        DATE_FORMAT(adr.update_time, '%Y-%m-%d %H:%i:%s') as update_time,
        group_concat(coalesce(arp.menu_id, '')) as permissions
      from admin_role as adr
      left join admin_role_pms as arp on arp.role_id = adr.id and arp.status=?
      group by adr.id
      order by adr.update_time desc
    `;
    return await dbUtils.query(sql, [StatusType.Active]);
  }

  // 系统角色名称也不允许重复
  async isRoleExist(role_name: string) {
    const sql = `select id from admin_role where role_name = ?`;
    return await dbUtils.query(sql, [role_name]);
  }

  // 更新角色权限
  async updateRolePms(options: AdminSettingAPI.UpdateRolePMS) {
    const { menus, id } = options;

    const sqls: QueryParamType[] = [
      {
        // 构造SQL语句将该角色所有权限状态设置为暂停
        sql: `update admin_role_pms set status=? where role_id=?`,
        values: [StatusType.Paused, id]
      }
    ];

    if (Array.isArray(menus) && menus.length) {
      sqls.push({
        // 构造插入SQL,如果记录已存在则更新状态为激活
        sql: 'insert into admin_role_pms(role_id, menu_id) values ? on duplicate key update status=?',
        values: [menus.map((m) => [id, m]), StatusType.Active]
      });
    }
    return !!(await dbUtils.execWaterfallTransaction(sqls));
  }

  async getPmsByMenuId(menu_ids: number[]) {
    if (menu_ids.length) {
      const sql = `select access, type from admin_menu where id in (?)`;
      return await dbUtils.query(sql, [menu_ids]);
    }
    return [];
  }
}

export const adminRoleModel = new RoleModel();
