/*
 * @Author: chen<PERSON><PERSON>
 * @Date: 2023-12-20 17:08:59
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-22 10:42:35
 * @Description:
 */
import dbUtils from '@/db/mysql';
import { AdminSettingAPI } from '@/types/admin';

class MenuModel {
  async addMenu(params: AdminSettingAPI.AddMenuParams) {
    const { title, pid = 0, remark = '', access = '', type } = params;
    const sql = `insert into admin_menu(title, pid, remark, access, type) values(?,?,?,?,?)`;
    const values = [title, pid, remark, access, type];
    return !!(await dbUtils.query(sql, values));
  }

  async updateMenu(params: AdminSettingAPI.UpdateMenuParams) {
    const { title, pid = 0, remark = '', access = '', id } = params;
    const sql = `
      update admin_menu set
        title=?,
        pid=?,
        remark=?,
        access=?
      where id=?
    `;
    const values = [title, pid, remark, access, id];
    return !!(await dbUtils.query(sql, values));
  }

  async updateMenuSort(params: AdminSettingAPI.UpdateMenuSortParams) {
    const { list } = params;

    if (list.length === 0) {
      return false;
    }

    const menuIds = [];
    const parentIdValues: number[] = [];
    const sortOrderValues: number[] = [];
    const caseWhenClauses: string[] = [];

    for (const { id, pid, sort } of list) {
      menuIds.push(id);
      parentIdValues.push(id, pid);
      sortOrderValues.push(id, sort);
      caseWhenClauses.push('WHEN ? THEN ?');
    }

    const caseWhenString = list.map(() => 'WHEN ? THEN ?').join(' ');

    const sql = `UPDATE admin_menu SET pid = CASE id ${caseWhenString} END, sort = CASE id ${caseWhenString} END WHERE id IN (?)`;

    const queryValues = [...parentIdValues, ...sortOrderValues, menuIds];

    return !!(await dbUtils.query(sql, queryValues));
  }

  async isMenuExist(options: AdminSettingAPI.AddMenuParams) {
    const { pid, title } = options;
    const sql = `select id from admin_menu where title = ? and pid=?`;
    return await dbUtils.query(sql, [title, pid]);
  }

  async deleteMenu(id: number) {
    const sql = `delete from admin_menu where id=?`;
    return !!(await dbUtils.query(sql, [id]));
  }

  async getAllMenu() {
    const sql = `select * from admin_menu`;
    return await dbUtils.query(sql);
  }
}

export const adminMenuModel = new MenuModel();
