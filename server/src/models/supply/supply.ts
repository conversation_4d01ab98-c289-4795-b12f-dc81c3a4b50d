/*
 * @Author: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @Date: 2024-02-27 16:35:59
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-28 11:44:53
 * @Description:
 */
import { SupplyAPI } from '@/types/supply';
import dbUtils from '@/db/mysql';
import { ProfitType } from '@/constants';
import { AuthLevel } from '@/constants/demand';
import { buildSQLSetClause, buildSQLWhereClause } from '@rixfe/rix-tools';
class SupplyModel implements SupplyAPI.SupplyModel {
  async getSupplyList() {
    const sql = `
        select
            sl.seller_id as seller_id,
            sl.seller_name as seller_name,
            sl.integration_type as integration_type,
            sl.channel_type as channel_type,
            sl.device_type as device_type,
            sl.relationship as relationship,
            sl.token as token,
            sl.pass_burl as pass_burl,
            sl.pass_nurl as pass_nurl,
            sl.pass_lurl as pass_lurl,
            sl.status as status,
            sl.crid_filter as crid_filter,
            sl.create_time as create_time,
            sl.update_time as update_time,
            sl.profit_model as profit_model,
            sl.rev_share_ratio as rev_share_ratio,
            sl.cus_status as cus_status,
            sl.tagid_status as tagid_status,
            sl.user_id as user_id,
            sl.rev_track_type as rev_track_type,
            sl.banner_multi_size as banner_multi_size,
            sl.win_rate_profit_ratio as win_rate_profit_ratio,
            sl.developer_traffic as developer_traffic,
            sit.itg_name as integration_type_desc,
            p.id as profit_id,
            p.profit_ratio as profit_ratio,
            p.status as profit_status,
            t.host_prefix as host_prefix,
            t.pv_domain as pv_domain,
            t.tnt_id as tnt_id,
            t.tnt_name as tnt_name,
            u.account_name as seller_account_name,
            u.status as seller_account_status,
            coalesce(spt.sp_name, '') as partner_name,
            coalesce(spt.partner_id, 0) as partner_id,
            coalesce(spt.sp_id, 0) as sp_id
        from (select * from seller) as sl
        left join seller_integration_type as sit on sl.integration_type = sit.id
        left join profit as p on sl.seller_id = p.seller_id and p.type = ? 
        left join user as u on sl.user_id = u.user_id
        left join tenant as t on sl.tnt_id = t.tnt_id
        left join seller_parent as spt on spt.sp_id=sl.sp_id
        order by sl.update_time desc
    `;
    return await dbUtils.query(sql, [ProfitType.Seller]);
  }

  async updateSupply(params: SupplyAPI.UpdateSupplyParams): Promise<boolean> {
    const {
      banner_multi_size = 2,
      crid_filter = 1,
      win_rate_profit_ratio,
      developer_traffic,
      tnt_id,
      seller_id
    } = params;

    const supplyObj = buildSQLSetClause([
      ['banner_multi_size', banner_multi_size],
      ['crid_filter', crid_filter],
      ['win_rate_profit_ratio', win_rate_profit_ratio],
      ['developer_traffic', developer_traffic]
    ]);

    const sql = `update seller set ? where seller_id=? and tnt_id=?`;
    return !!(await dbUtils.query(sql, [supplyObj, seller_id, tnt_id]));
  }

  async getPartnerSupply() {
    const sql = `
      select
        seller_name,
        seller_id,
        sp_id
      from seller where sp_id > 0
    `;
    return await dbUtils.query(sql);
  }

  async getSupplyAuth(seller_id: number, tnt_id: number) {
    const sql = `
      select 
        ca.buyer_id as buyer_id, 
        level, 
        pub_id,
        buyer_name,
        b.status as status,
        b.integration_type as integration_type
      from (select buyer_id, level, pub_id from config_auth where pub_id = ? and level=? and tnt_id=?) as ca
      left join buyer as b on b.buyer_id = ca.buyer_id
    `;
    return await dbUtils.query(sql, [seller_id, AuthLevel.Supply, tnt_id]);
  }

  async getSupplyEndpoint(tnt_id: number, seller_id?: number) {
    const { clause, params } = buildSQLWhereClause([
      ['seller_id = ?', seller_id ? seller_id : null],
      ['t.tnt_id = ?', tnt_id]
    ]);

    const sql = `select t.host_prefix,t.pv_domain ,s.token from tenant t ,seller s ${clause}`;
    return await dbUtils.query(sql, params);
  }
}
export const supplyModel = new SupplyModel();
