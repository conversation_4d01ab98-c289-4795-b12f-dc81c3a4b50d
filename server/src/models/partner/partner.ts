/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-01-11 14:29:44
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2024-02-29 16:12:10
 * @Description:
 */

import dbUtils from '@/db/mysql';

class PartnerModel {
  async getPartnerList() {
    const sql = `
      select
        pt.tnt_id,
        pt.partner_id,
        pt.partner_name,
        pt.update_time,
        coalesce(spt.sp_id, 0) as sp_id,
        coalesce(spt.publisher_id, '') as sp_publisher_id,
        coalesce(spt.sp_name, '') as sp_name,
        coalesce(spt.email, '') as sp_email,
        coalesce(bpt.dp_id, 0) as dp_id,
        coalesce(bpt.dp_name, '') as dp_name,
        coalesce(bpt.email, '') as dp_email
      from partner as pt
      left join seller_parent as spt on pt.partner_id=spt.partner_id
      left join buyer_parent as bpt on pt.partner_id=bpt.partner_id
      order by pt.update_time desc
    `;
    return await dbUtils.query(sql);
  }

  async isPartnerExists(tnt_id: number, partner_name: string) {
    const sql = `select partner_id from partner where tnt_id=? and partner_name=? limit 1`;
    return await dbUtils.query(sql, [tnt_id, partner_name]);
  }
}

export const partnerModel = new PartnerModel();
