/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-12-19 18:03:48
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-20 11:01:06
 * @Description:
 */
import { AppAdsAPI } from '@/types/app-ads';
import request from '@/utils/request';

class AppInfo {
  async getAppInfo(
    options: AppAdsAPI.GetAppInfoParams
  ): Promise<AppAdsAPI.GetAppInfoResult> {
    const { params } = options;

    const result = await request.post(
      'http://**********:8080/bundle/search/bundle',
      JSON.stringify(params)
    );
    return result.data as AppAdsAPI.GetAppInfoResult;
  }
}

export const appInfoModel = new AppInfo();
