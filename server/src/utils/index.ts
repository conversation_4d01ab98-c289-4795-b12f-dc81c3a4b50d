/* *
 * @Author: <EMAIL>
 * @file:
 * @Date: 2023-01-01 21:08:00
 * @Last Modified by: chen<PERSON><EMAIL>
 * @Last Modified time: 2022-11-23 10:27:45
 */
import crypto from 'crypto';
import fs from 'fs';
import converter from 'json-2-csv';
import { Context, Next } from 'koa';
import nodemailer from 'nodemailer';
import path from 'path';
import { post } from './request';

import { getLogger } from '@/config/log4js';

import devConfig from '../config/dev.env.config';
import prodConfig from '../config/prod.env.config';
import testConfig from '../config/test.env.config';
import { EmailParams } from '../constants';

export const getConfig = () => {
  if (!process.env.NODE_ENV) {
    throw Error(`environment variable 'NODE_ENV' is required.`);
  }
  let result = prodConfig;
  switch (process.env.NODE_ENV) {
    case 'prod':
      result = prodConfig;
      break;
    case 'test':
      result = testConfig;
      break;
    default:
      result = devConfig;
      break;
  }
  return result;
};

const { emailConfig } = getConfig();
const mailParams: any = {
  host: emailConfig.host,
  port: emailConfig.port,
  secure: false,
  debug: true,
  auth: {
    user: emailConfig.user,
    pass: emailConfig.pass
  },
  tls: {
    ciphers: 'SSLv3',
    requireTLS: true
  }
};
const transporter = nodemailer.createTransport(mailParams);
export const sendEmail = async function sendEmail(obj: any) {
  // send mail with defined transport object
  const { from } = EmailParams;
  await transporter.sendMail({ from, ...obj });
};
export const genEnCode = function (pasLen: number, isSpecial?: boolean) {
  let pasArr = [
    'a',
    'b',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'j',
    'k',
    'l',
    'm',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
    'A',
    'B',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'J',
    'K',
    'L',
    'M',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9'
  ];
  const specialCode = ['_', '-', '$', '%', '&', '@', '+', '!'];
  if (isSpecial) {
    pasArr = pasArr.concat(specialCode);
  }
  let password = '';
  const pasArrLen = pasArr.length;
  for (let i = 0; i < pasLen; i++) {
    password += pasArr[Math.floor(Math.random() * pasArrLen)];
  }
  return password;
};

export const md5 = function (content: crypto.BinaryLike) {
  const hash = crypto.createHash('md5');
  return hash.update(content).digest('hex');
};

export const sha256 = function (content: crypto.BinaryLike) {
  const hash = crypto.createHash('sha256');
  return hash.update(content).digest('hex');
};

export const formatDateTime = function (value: Date, format: string) {
  const year = value.getFullYear();
  const month = value.getMonth() + 1;
  const day = value.getDate();
  const hour = value.getHours();
  const min = value.getMinutes();
  const secs = value.getSeconds();
  const str = format
    .replace(/dd/, `0${day}`.slice(-2))
    .replace(/yyyy/, `${year}`)
    .replace(/MM/, `0${month}`.slice(-2))
    .replace(/hh/, `0${hour}`.slice(-2))
    .replace(/mm/, `0${min}`.slice(-2))
    .replace(/ss/, `0${secs}`.slice(-2));
  return str;
};

export const readFileByPromise = function (path: string) {
  return new Promise((resolve, reject) => {
    fs.readFile(path, 'utf-8', (err, data) => {
      if (err) {
        reject(err);
      } else {
        resolve(data);
      }
    });
  });
};

export function formatMoney(money: number) {
  const str = money.toFixed(2); // 只取2位小数
  const l = str.split('.')[0]; // 获取整数位
  const r = str.split('.')[1]; // 获取小数位
  const arr = []; // 用于保存结果
  const len = Math.ceil(l.length / 3); // 3位数一个 `,`
  for (let i = 0; i < len; i++) {
    // 如果传(-3,0)获取不到参数，将0换成undefined相当于没传
    arr.unshift(l.slice(-3 * (i + 1), -3 * i || undefined));
    if (i !== len - 1) {
      // 最后一次截取不加 `,`了
      arr.unshift(',');
    }
  }
  return `${arr.join('')}.${r}`;
}

/**
 * @deprecated 此方案是为了兼容ie，但后续优化可使用 trim 方法替换
 * @param str
 * @returns 去除字符串两端空格，返回字符串，如果传入的不是字符串，返回空字符串
 */
export const trim = function (str: any) {
  // TODO 直接使用字符串转换，不需要额外的布尔检查
  // return !!str ? String(str).trim() : '';
  return Boolean(str) === true ? `${str}`.replace(/(^\s*)|(\s*$)/g, '') : '';
};
// 是否空白字符串
export const isNotEmptyStr = function (obj: any) {
  return !/^\s*$/.test(trim(obj));
};

export const download = async function (data: object[]) {
  const csv = await converter.json2csvAsync(data);
  return csv;
};

export const setInterfaceTimeOut = function (time: number) {
  return async function (ctx: Context, next: Next) {
    await Promise.race([
      new Promise((resolve, reject) => {
        const timer = setTimeout(() => {
          clearTimeout(timer);
          const e: any = new Error('Request timeout');
          e.status = 408;
          reject(e);
          getLogger('error').error(`${ctx.session!.session_id} Request timeout`);
        }, time);
      }),
      // eslint-disable-next-line no-async-promise-executor
      new Promise(async (resolve, reject) => {
        try {
          await next();
          resolve(1);
        } catch (error) {
          getLogger('error').error(`${ctx.session!.session_id} error ${JSON.stringify(error)}`);
          reject(error);
        }
      })
    ]);
  };
};

export const notifyFeishu = (title: string, text: string) => {
  const { NotifyFeishuUrl } = getConfig();
  const url = NotifyFeishuUrl;
  const body = {
    msg_type: 'post',
    content: {
      post: {
        zh_cn: {
          title,
          content: [
            [
              {
                tag: 'text',
                text
              }
            ]
          ]
        }
      }
    }
  };
  const headers = {
    'Content-Type': 'application/json'
  };
  post({ url, data: body, headers });
};

/**
 * description: 数字格式化加上单位B,M,K
 * @param {number} originFixed 小于一千时保留的位数，默认2
 * @param {number} formatFixed 格式化之后保留的位数，默认2
 */
export const formatNumberToUnit = (num: number, originFixed = 2, formatFixed = 2) => {
  if (num >= 1000000000) {
    return `${(num / 1000000000).toFixed(formatFixed)}B`;
  }
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(formatFixed)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(formatFixed)}K`;
  }
  return num.toFixed(originFixed);
};

export async function createDirectory(filePath: string) {
  const directory = path.dirname(filePath);

  try {
    await fs.promises.mkdir(directory, { recursive: true });
  } catch (err) {
    console.log('createDirectory error', err);
    getLogger('error').error(`createDirectory error: ${err}`);
    throw err;
  }
}

export const str2num = <T>(str: T): number => {
  if (!str) return 0;

  return Number.isNaN(+str) ? 0 : +str;
};
/**
 * @description: Generate key value map
 * @param {array} data - An array of objects
 * @param {keyof T} key - A key of an object within the data array item as the return map key
 * @param {keyof T} value - A key of an object within the data array item as the return map value
 * @returns {object} - An object with the value of key as the key and the value of value as the value of the
 *                     corresponding object.eg: { data.item[key]: data.item[value] }
 */
export const generateMap = <T, K extends keyof T, P extends keyof T>(data: T[], key: K, value: P) => {
  if (!Array.isArray(data)) throw new Error('data must be an array');
  const result = {} as Record<string, T[P]>;
  data.forEach((item) => {
    result[item[key] as string] = item[value];
  });
  return result;
};

/**
 * @description: 获取用户ip
 */
export const getUserIp = (ctx: Context) => {
  const { req } = ctx;

  if (req.headers['x-forwarded-for']) {
    return req.headers['x-forwarded-for'].toString().split(',')[0].trim();
  }

  if (req.headers['x-real-ip']) {
    return req.headers['x-real-ip'].toString().trim();
  }

  if (req.socket.remoteAddress) {
    return req.socket.remoteAddress.replaceAll('::ffff:', '');
  }

  return '';
};
