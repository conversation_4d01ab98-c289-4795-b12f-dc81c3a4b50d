import {
  adaptGenerateBigQuerySQL,
  buildMultiTableQuery,
  concatSQLFragments,
  DEMAND_BILLING_TABLE_NAME,
  LabelGenerationParams,
  REPORT_SCHEMA,
  SUPPLY_BILLING_TABLE_NAME
} from '@rixfe/rix-tools';
import { updateBQConfigAdapter } from './slim';

/**
 * 获取报表sql
 * @param params
 * @param labels
 * @returns
 */
export async function getCommonTenantMonthlyReportSql(params: any, labels: LabelGenerationParams) {
  const { columns = [], order_key = [], order = 'desc' } = params;

  // Update BQ config before generating SQL
  await updateBQConfigAdapter();

  // Generate base SQL fragments
  const { fragments } = await adaptGenerateBigQuerySQL(
    {
      ...params,
      metrics: []
    },
    { api_url: labels.tag }
  );

  const { where } = fragments;

  // Build demand side query
  const demandSQL = concatSQLFragments({
    select: [
      ...columns,
      'sum(buyer_net_revenue) as buyer_net_revenue',
      'sum(seller_net_revenue) as seller_net_revenue',
      'sum(request) as buyer_request'
    ],
    from: `${REPORT_SCHEMA}.${DEMAND_BILLING_TABLE_NAME}`,
    where,
    groupBy: [...columns]
  });

  // Build supply side query
  const supplyColumns = columns.filter((v: string) => v !== 'buyer_id');
  const supplySQL = concatSQLFragments({
    select: [
      ...supplyColumns,
      '(case when sum(block_request) is null then sum(request) else (sum(request) + sum(block_request)) end) as seller_total_request',
      'sum(request) as seller_request'
    ],
    from: `${REPORT_SCHEMA}.${SUPPLY_BILLING_TABLE_NAME}`,
    where,
    groupBy: [...supplyColumns]
  });

  // Join demand and supply queries
  const multiTableQuery = buildMultiTableQuery(
    {
      fragments: {
        select: [...columns, 'buyer_net_revenue', 'seller_net_revenue', 'buyer_request'],
        from: `(${demandSQL})`,
        where: []
      },
      alias: 'demand'
    },
    [
      {
        table: {
          fragments: {
            select: ['seller_total_request', 'seller_request'],
            from: `(${supplySQL})`,
            where: [],
            groupBy: []
          },
          alias: 'supply'
        },
        type: 'INNER JOIN',
        // on: 'demand.tnt_id = supply.tnt_id and demand.month = supply.month'
        on: supplyColumns.map((v: any) => `demand.${v} = supply.${v}`).join(' and ')
      }
    ]
  );

  // 处理排序，用户排序为第一优先级，否则使用默认排序 buyer_net_revenue desc
  const orderBy: string[] = [
    order_key.length ? `${order_key.join(',')} ${order}` : 'buyer_net_revenue desc'
  ]

  // Return final SQL without limit
  return concatSQLFragments({
    ...multiTableQuery,
    orderBy,
    limit: ''
  });
}

export async function get1046MonthlyBillingReportSql(params: any, labels: LabelGenerationParams) {
  const sql = await getCommonTenantMonthlyReportSql(
    {
      ...params,
      tnt_id: [1046],
      columns: ['seller_id', 'month', 'tnt_id']
    },
    labels
  );
  return sql;
}
