import { commonService } from '@/services';
import { SQLFragments } from '@rixfe/rix-tools';

/**
 * 生成 device_brand 的维度、过滤条件
 * @param params
 * @param fragments
 * @returns
 */
export async function generateDeviceBrandDimensionAndFilter(params: any, fragments: SQLFragments) {
  // 如果 fragments.from 包含 'billing'，直接返回 fragments
  if (fragments.from.includes('billing')) {
    return fragments;
  }

  const { make = [], columns = [] } = params;

  // 如果没有选择 'make' 且 make 为空，直接返回 fragments
  if (!columns.includes('make') && make.length === 0) {
    return fragments;
  }

  // 过滤用户选择的设备品牌
  const userSelectedDevices = make.filter((device: string) => device !== 'others') as string[];
  const hasSelectedOthers = make.includes('others');

  const Top20DeviceBrands: string[] = [];
  const OtherDeviceBrands: string[] = [];
  const shouldFetchDeviceBrands =
    (columns.includes('make') && (userSelectedDevices.length > 0 || !hasSelectedOthers)) ||
    (make.length > 0 && hasSelectedOthers);

  if (shouldFetchDeviceBrands) {
    const deviceBrandDict = await commonService.getDict('device_brand');

    for (const item of deviceBrandDict) {
      if (item.sort_order === 0) {
        OtherDeviceBrands.push(item.value);
      } else if (item.sort_order <= 20) {
        Top20DeviceBrands.push(item.value);
      }
    }
    OtherDeviceBrands.push('others');
  }

  // 如果需要修改select
  let newSelect = [...fragments.select];
  if (columns.includes('make')) {
    let deviceBrandDimension: string;

    if (userSelectedDevices.length === 0 && hasSelectedOthers) {
      deviceBrandDimension = `'others' as make`;
    } else {
      // userSelectedDevices 如果为空，说明没有选择任何设备品牌（除 others 外），直接使用 Top20DeviceBrands
      const selectDevice = userSelectedDevices.length > 0 ? userSelectedDevices : Array.from(Top20DeviceBrands);

      deviceBrandDimension = `(CASE WHEN make IS NULL THEN NULL WHEN make IN (${selectDevice.map((device) => `'${device}'`).join(',')}) THEN make ELSE 'others' END) as make`;
    }

    newSelect = newSelect.filter((item) => !item.includes('make')).concat(deviceBrandDimension);
  }

  // 如果需要修改where
  let newWhere = [...(fragments.where || [])];
  if (make.length > 0) {
    const conditions: string[] = [];
    if (userSelectedDevices.length > 0) {
      conditions.push(userSelectedDevices.map((device) => `'${device}'`).join(','));
    }

    if (hasSelectedOthers && OtherDeviceBrands.length > 0) {
      conditions.push(OtherDeviceBrands.map((device) => `'${device}'`).join(','));
    }

    if (conditions.length > 0) {
      const deviceBrandFilter = `make IN (${conditions.join(',')})`;
      newWhere = newWhere.filter((item) => !item.includes('make')).concat(deviceBrandFilter);
    }
  }

  return {
    ...fragments,
    select: newSelect,
    where: newWhere
  };
}
