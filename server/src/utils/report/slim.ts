import { getLogger } from '@/config/log4js';
import { queryStackOverflow } from '@/db/bigquery';
import { getRedisByKey } from '@/db/redisServer';
/**
 * @/services 简写会导致提前初始化错误，需要全路径导入
 */
import { commonService } from '@/services/common/common';
import { LabelGenerationParams, updateBQConfig } from '@rixfe/rix-tools';

/**
 * 判断 daily 表是否在更新
 * @description redis中SaaS_Lock::daily_report 大于0时，daily表在更新
 * @returns boolean
 */
export async function isDailyTableUpdating() {
  const value = (await getRedisByKey('SaaS_Lock::daily_report')) as number;
  return !!value && value > 0;
}

/**
 * update BQConfig, IsDailyTableUpdating and AdSize
 *
 */
export async function updateBQConfigAdapter(options?: {
  ExtraDefaultMetrics?: string[];
  IsUseDefaultSort?: boolean;
  AdvertiserMetrics?: string[];
}) {
  const isUpdating = await isDailyTableUpdating();

  getLogger('app').info(`isDailyTableUpdating，SaaS_Lock::daily_report: [${isUpdating}]`);

  const adSizeDict = await commonService.getDict('ad_size');
  // 从 [{label, value} ...] -> {value:key ...}
  const adSizeMap: Record<string, string> = adSizeDict.reduce((acc, item) => {
    acc[item.value] = item.label;
    return acc;
  }, {});

  updateBQConfig({
    IsDailyTableUpdating: isUpdating,
    AdSize: adSizeMap,
    ...(options || {}),
    WhereConfig: {
      numberCols: [
        'seller_id',
        'buyer_id',
        'ad_format',
        'platform',
        'hour',
        'instl',
        'inventory',
        'tnt_id',
        'http_code',
        'internal_request',
        'device_type',
        'status',
        'seller_tag',
        'buyer_tag'
      ],
      exactQueries: ['placement_id', 'ad_width', 'ad_height', 'test_tag_a'],
      arrayQueries: [
        'internal_request',
        'http_code',
        'instl',
        'seller_id',
        'buyer_id',
        'country',
        'ad_format',
        'app_bundle_id',
        'region',
        'platform',
        'hour',
        'ad_domain',
        'inventory',
        'res_crid',
        'res_cid',
        'tnt_id',
        'month',
        'make',
        'device_type',
        'status',
        'seller_tag',
        'buyer_tag'
      ]
    }
  });
}

/**
 * 获取限制 limit_str
 * @param tnt_id
 * @returns
 */
export function transformLimitStr(tnt_id: number, limit?: string, isAll?: boolean) {
  const allow_5w_Rows = tnt_id && [1075, 1047].includes(tnt_id);
  const limit_str = allow_5w_Rows ? '50000' : '10000';
  return isAll ? `${limit_str}` : limit || '';
}

/**
 * 抽离出通用的查询逻辑
 * @param sql 查询sql
 * @param tag 标签
 * @returns
 */
export function queryBQDataWithCache(sql: string, tag: LabelGenerationParams) {
  return queryStackOverflow(sql, {
    cacheTime: 60 * 10,
    ...tag
  });
}
