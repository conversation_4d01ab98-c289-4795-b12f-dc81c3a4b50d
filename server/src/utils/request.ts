/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-09-19 16:12:51
 * @LastEditors: chen<PERSON><PERSON>
 * @LastEditTime: 2023-12-20 10:24:27
 * @Description:
 */
import axios, { AxiosResponse, AxiosRequestConfig } from 'axios';
import { getLogger } from '@/config/log4js';

export const post = async function (
  config: AxiosRequestConfig
): Promise<AxiosResponse> {
  if (!config.url) throw new Error('url is required');
  return await axios.post(config.url, config.data, {
    headers: config.headers || { 'Content-Type': 'application/json' }
  });
};

const request = axios.create({
  timeout: 120000,
  headers: {
    'Content-Type': 'application/json'
  }
});
request.interceptors.response.use((resp) => resp, (err) => {
  getLogger('error').error(`request failed, err=[${JSON.stringify(err)}, ${err.message}]`);
  return Promise.reject(err);
});

request.interceptors.request.use((req) => req);

export default request;

