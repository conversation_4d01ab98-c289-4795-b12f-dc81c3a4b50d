/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-11-08 14:47:32
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2024-02-29 16:20:42
 * @Description:
 */
import { Context, Next } from 'koa';
import path from 'path';
import moment from 'moment-timezone';
import { FullReportingAPI } from '@/types/full-reporting';
import { DateType } from '@/constants/data-report/full-report';
import { HeaderOptions, TimeZoneMap } from '@/constants/data-report/common-report';

import { genEnCode } from '.';
// serivce层里面的计算结果
export const getFullReportCalculationFormula = (
  metric: string,
  item: FullReportingAPI.DashboardListItem,
  isDemand: boolean,
  split_time?: number,
  start_date?: string,
  end_date?: string,
  today_hours?: number, // 今天的小时数
  isToday?: boolean, // 是否包含今天
  isDemandReport?: boolean,
  isSupplyReport?: boolean
) => {
  const days = moment(end_date).diff(moment(start_date), 'days') + 1;
  const todayAecs = (today_hours || 0) * 60 * 60;
  const {
    buyer_net_revenue,
    seller_net_revenue,
    request,
    block_request,
    response,
    out_request,
    click,
    impression,
    win,
    buyer_gross_revenue,
    total_seller_bid_floor,
    total_res_price,
    total_request
  } = item;
  const fill_rate_request = isDemandReport || isSupplyReport ? total_request : request;
  const formula: any = {
    profit: +(+buyer_net_revenue - +seller_net_revenue).toFixed(2),
    profit_rate: +buyer_net_revenue
      ? +(((+buyer_net_revenue - +seller_net_revenue) * 100) / +buyer_net_revenue).toFixed(2)
      : 0.0,
    total_request: isDemand ? (+request ? +request : 0) : +request + +block_request,
    fill_rate: +response ? +((+response * 100) / +fill_rate_request).toFixed(2) : 0.0,
    // ecpr: +request
    //   ? +((+buyer_net_revenue * 1000000) / +request).toFixed(2)
    //   : 0.0,
    // adv_ecpr: +out_request
    //   ? +((+buyer_net_revenue * 1000000) / +out_request).toFixed(2)
    //   : 0.0,
    click_rate: +impression ? +((+click * 100) / +impression).toFixed(2) : 0.0,
    win_rate: +response ? ((+win * 100) / +response).toFixed(2) : 0.0,
    impression_rate: +response ? +((+impression * 100) / +response).toFixed(2) : 0.0,
    buyer_gross_ecpm: +impression ? +((+buyer_gross_revenue * 1000) / +impression).toFixed(2) : 0.0,
    buyer_net_ecpm: +impression ? +((+buyer_net_revenue * 1000) / +impression).toFixed(2) : 0.0,
    seller_net_ecpm: +impression ? +((+seller_net_revenue * 1000) / +impression).toFixed(2) : 0.0,
    total_seller_bid_floor: +request ? +(+total_seller_bid_floor / +request).toFixed(4) : 0.0,
    bid_price: +response ? +(+total_res_price / +response).toFixed(4) : 0.0
  };

  let real_qps = 0;
  if (split_time === DateType.Hour) {
    real_qps = +(+formula.total_request / (60 * 60)).toFixed(0);
  } else if (split_time === DateType.Day) {
    const currentTime = moment().endOf('day').format('YYYY-MM-DD');
    const itemIsToday = item.date === currentTime;

    real_qps = +(+formula.total_request / (itemIsToday ? todayAecs : 60 * 60 * 24)).toFixed(0);
  } else {
    real_qps = +(
      +formula.total_request / (isToday ? 60 * 60 * 24 * (days - 1) + todayAecs : 60 * 60 * 24 * days)
    ).toFixed(0);
  }

  formula.real_qps = real_qps;
  return formula[metric as keyof typeof formula];
};

// model层里面的计算sql
export const getFullReportCalculationSQL = (metric: string, isDemand: boolean) => {
  const sql = {
    total_request: isDemand
      ? 'sum(request) as total_request'
      : '(case when sum(block_request) is null then sum(request) else (sum(request) + sum(block_request)) end) as total_request',
    ecpr: '(case when sum(request) = 0 then 0 else round(cast (sum(buyer_net_revenue) * 1000000 / sum(request) as numeric), 2) end) as ecpr',
    adv_ecpr: isDemand
      ? '(case when sum(request) = 0 then 0 else round(cast (sum(buyer_net_revenue) * 1000000 /sum(request) as numeric), 2) end) as adv_ecpr'
      : '(case when sum(out_request) = 0 then 0 else round(cast (sum(buyer_net_revenue) * 1000000 /sum(out_request) as numeric), 2) end) as adv_ecpr',
    win_rate: '(case when sum(response) = 0 then 0 else round(sum(win) *100 *1.0/ sum(response), 2) end) as win_rate',
    avg_dsp_cost_time:
      '(CASE WHEN SUM(request) = 0 THEN 0 ELSE ROUND(SUM(total_cost_time) / SUM(request),2)END) AS avg_dsp_cost_time',
    avg_response_cost_time:
      '(CASE WHEN SUM(response) = 0 THEN 0 ELSE ROUND(SUM(total_response_cost_time) / SUM(response), 2) END) AS avg_response_cost_time'
  };

  return sql[metric as keyof typeof sql];
};

// 判断搜索日期是否是包含今天
export const includeToday = (start_date: string, end_date: string) => {
  const today = moment().format('YYYY-MM-DD');
  // 当天0-1点之间的数据还没有完全统计出来，所以不包含在今天
  const exsitedData = moment().startOf('day').add(1, 'hours') < moment();
  const isToday = today === start_date || today === end_date;
  return exsitedData && isToday;
};

// 设置接口超时
export const setTimeOutMiddler = async (ctx: Context, next: Next) => {
  ctx.request.socket.setTimeout(5 * 60 * 1000);
  await next();
};

// 将时段对应的小时数转换为时区对应的小时数（bq默认hour是UTC0的hour）
function convertHourToTimezone(hour: number, timezone: string): number {
  // 获取 UTC 与目标时区的偏移量（以分钟为单位）
  const offset = moment.tz(timezone).utcOffset();

  // 将偏移量转换为小时
  const offsetHours = offset / 60;

  // 调整小时数
  let adjustedHour = hour - offsetHours;

  // 如果结果小于 0，将其转换为 24 小时制
  if (adjustedHour < 0) {
    adjustedHour += 24;
  }

  // 如果结果大于等于 24，将其转换为 24 小时制
  if (adjustedHour >= 24) {
    adjustedHour -= 24;
  }

  return adjustedHour;
}

// 根据时区转化日期
const getDateFromTZ = (formData: any, timeZone: string) => {
  const { start_date, end_date } = formData;
  const options = { ...formData };
  moment.tz.setDefault(timeZone);

  if (Object.hasOwn(options, 'start_date')) {
    const start = moment(start_date).startOf('day').tz('Etc/UTC');
    options.tz_start_date = start.format('YYYY-MM-DD HH:00:00');
  }
  if (Object.hasOwn(options, 'end_date')) {
    const end = moment(end_date).endOf('day').tz('Etc/UTC');
    options.tz_end_date = end.format('YYYY-MM-DD HH:00:00');
  }
  if (Object.hasOwn(options, 'hour')) {
    options.hour = options.hour.map((item: number) => {
      const hour = convertHourToTimezone(item, timeZone);
      return hour;
    });
  }
  if (Object.hasOwn(options, 'dates')) {
    options.dates = options.dates.map((item: string) => {
      // const date = moment(item).startOf('day').tz('Etc/UTC').format('YYYYMMDD');
      const curStartDate = moment(item).startOf('day').tz('Etc/UTC').format('YYYY-MM-DD HH:00:00');
      const curEndDate = moment(item).endOf('day').tz('Etc/UTC').format('YYYY-MM-DD HH:00:00');
      return {
        start: curStartDate,
        end: curEndDate
      };
      // return date;
    });
  }
  return options;
};

// 解析时区参数中间件
export const parseDateParams = async (ctx: Context, next: Next) => {
  const formData = { ...ctx.request.body };
  const params = getDateFromTZ(formData, formData.cur_time_zone);
  params.cur_time_zone = formData.cur_time_zone || 'Etc/UTC';
  ctx.request.body = params;
  return await next();
};

export async function resolveFullReportDefaultData(ctx: Context, next: Next) {
  const timeZone = ctx.headers['x-time-zone'] || 'Etc/UTC';
  // 原始条件
  const original = { ...ctx.request.body };
  const formData = {
    ...ctx.request.body,
    cur_time_zone: timeZone,
    cur_condition_str: JSON.stringify(original)
  };
  ctx.request.body = formData;
  return await next();
}

// 获取下载csv的文件头部
export const getCsvHeaders = (columns: string[], metrics: string[]) => {
  const headerCloumns = [...HeaderOptions].filter((item) => {
    if (item.key === 'date') {
      return (
        columns?.includes('day') ||
        columns?.includes('day_hour') ||
        columns?.includes('month') ||
        columns?.includes('date')
      );
    }
    if (item.key === 'seller' || item.key === 'buyer' || item.key === 'adv_partner' || item.key === 'pub_partner') {
      return columns?.includes(`${item.key}_id`);
    }
    if (item.key === 'ad_size') {
      return columns?.includes('ad_width, ad_height');
    }
    return columns?.includes(item.key) || metrics?.includes(item.key);
  });
  return headerCloumns;
};

export const getDownTaskInfo = (
  cur_time_zone: string,
  tnt_id: number[],
  columns: string[] = [],
  metrics: string[] = [],
  type_name: string
) => {
  // const tnt_td_str = tnt_id.join('_');
  const time = moment().format('YYYYMMDD_HH_mm_ss');
  const day = moment().format('YYYYMMDD');
  const csvName = `${type_name || 'Report'}_${time}_${genEnCode(2, false)}_(${TimeZoneMap[cur_time_zone]}).csv`;
  const csvPath = `/files/report/${day}/`;
  const outputCsvPath = path.join(__dirname, `../../${csvPath}/${csvName}`);
  const requestPath = `/api/exported-report/download/${csvName}`;
  const headerCloumns = getCsvHeaders(columns || [], metrics || []);

  return {
    csvName,
    csvPath,
    outputCsvPath,
    requestPath,
    headerCloumns
  };
};

interface ExtraInfo {
  /**
   * 兜底排序字段，默认为 buyer_net_revenue
   */
  defaultOrderKey?: string;
  /**
   * 兜底字段排序方式，默认为 desc
   */
  defaultOrder?: string;
  /**
   * 是否启用与兜底排序比较，默认为 true，即启用
   */
  isDefault?: boolean;
  /**
   * 校验orderKeys失败时是否返回空字符串，默认为 false，返回兜底排序字段
   */
  isEmptyStr?: boolean;
}
/**
 * 携带兜底排序字段的sql排序
 *
 * @param orderKeys string[]
 * @param order string
 * @param extra ExtraInfo
 */
export function getOrderBy(orderKeys: string[], order: string, extra?: ExtraInfo) {
  const {
    defaultOrderKey = 'buyer_net_revenue',
    defaultOrder = 'desc',
    isDefault = true,
    isEmptyStr = false
  } = extra || {};
  let prefix = 'order by';

  if (!orderKeys || !orderKeys.length) {
    return isEmptyStr ? '' : `${prefix} ${defaultOrderKey} ${order || defaultOrder}`;
  }

  let orderDetails = orderKeys.map((item) => `${item} ${order || 'desc'}`).join(',');

  // 如果 orderKey 包含兜底的排序字段，则直接返回
  if (!isDefault || orderKeys.includes(defaultOrderKey)) {
    return `${prefix} ${orderDetails}`;
  }

  return `${prefix} ${orderDetails}, ${defaultOrderKey} ${defaultOrder}`;
}

// 格式化 daily report 参数
export function formatDailyReportParams(options: FullReportingAPI.GetListParams) {
  // 修改 start_date，end_date 格式化
  // 去除 其他不必要的
  const { start_date, end_date, ...rest } = options;

  return {
    ...rest,
    start_date: moment(start_date).format('YYYY-MM-DD'),
    end_date: moment(end_date).format('YYYY-MM-DD')
  };
}
