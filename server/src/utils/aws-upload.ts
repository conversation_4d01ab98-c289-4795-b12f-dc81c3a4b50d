/* *
 * @Author: ch<PERSON><PERSON><PERSON>@algorix.co
 * @file:
 * @Date: 2022-02-23 10:26:05
 */

import path from 'path';
import AWS from 'aws-sdk';

AWS.config.loadFromPath(path.join(__dirname, '../config/aws.config.json'));

export function s3Upload(params: AWS.S3.PutObjectRequest) {
  return new Promise<void>((resolve, reject) => {
    const s3 = new AWS.S3();
    s3.putObject(params, (resp) => {
      if (!resp) {
        // s3.gene
        resolve();
      } else {
        reject(resp);
      }
    });
  });
}
