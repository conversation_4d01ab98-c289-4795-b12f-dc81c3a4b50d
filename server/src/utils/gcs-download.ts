/*
 * @Author: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2024-02-18 10:54:35
 * @LastEditors: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @LastEditTime: 2024-02-18 10:54:36
 * @Description:
 */
import {
  BrandBucketName,
  UploadFaviconPath,
  UploadLogoPath
} from '@/constants/privatization';
export const getBrandStorageOptions = (type: 'logo' | 'favicon') => {
  let bucket = BrandBucketName.default;
  let pathPrefix =
    type === 'logo'
      ? `${UploadLogoPath.default}logos/`
      : `${UploadFaviconPath.default}favicons/`;
  if (process.env.NODE_ENV === 'prod') {
    bucket = BrandBucketName.prod;
    pathPrefix = type === 'logo' ? UploadLogoPath.prod : UploadFaviconPath.prod;
  }
  return {
    bucket,
    pathPrefix
  };
};
