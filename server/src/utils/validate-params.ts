/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @Date: 2023-01-19 10:37:11
 * @LastEditors: 袁跃钊 y<PERSON><PERSON><PERSON><PERSON>@algorix.co
 * @LastEditTime: 2023-10-30 11:45:33
 * @Description:
 */
import { Code } from '@/codes';
import <PERSON><PERSON> from 'joi';
import { Context, Next } from 'koa';

// 校验参数，支持post跟get两个方法
export const validateParams =
  (schema: Joi.Schema, isAsync = false) =>
  (target: any, methodName: string, desc: PropertyDescriptor) => {
    const curMethod = desc.value; // 原方法
    // eslint-disable-next-line no-param-reassign
    desc.value = async (ctx: Context, next: Next) => {
      const params = ctx.request.method.toLowerCase() === 'post' ? ctx.request.body : ctx.request.query;

      try {
        const result = isAsync
          ? await schema.validateAsync(params || {}, { allowUnknown: true })
          : schema.validate(params || {}, { allowUnknown: true });
        if (result.error) {
          throw new Error(result.error.message);
        }
        // 执行原方法
        await curMethod.call(target, ctx, next);
      } catch (error: any) {
        ctx.body = {
          code: Code.PARAMS_INVALID,
          message: error.message,
          data: ''
        };
      }
    };
  };

// api接口 验证参数
export const validateBeApiBody =
  (schema: Joi.Schema, allowUnknown = true) =>
  (target: any, methodName: string, desc: PropertyDescriptor) => {
    const curMethod = desc.value; // 原方法
    // eslint-disable-next-line no-param-reassign
    desc.value = async (ctx: Context, next: Next) => {
      const method = ctx.request.method.toLowerCase();
      const params = method === 'post' ? ctx.request.body : ctx.request.query;

      try {
        // 允许多余字段
        const result = schema.validate(params || {}, { allowUnknown });

        if (result.error) {
          throw new Error(result.error.message);
        }
        await curMethod.call(target, ctx, next);
      } catch (error: any) {
        ctx.body = {
          code: Code.PARAMS_INVALID,
          message: error.message,
          data: ''
        };
      }
    };
  };
