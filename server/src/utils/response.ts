/*
 * @Author: ch<PERSON><PERSON><PERSON> chenmu<PERSON>@algorix.co
 * @Date: 2022-11-22 17:12:20
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2023-01-16 16:14:01
 * @FilePath: /saas.rix-platform/server-ts/src/utils/response.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {
  Code, Message, CodeProps
} from '@/codes';

// 获取通用的返回结果
export const getCtxResult = function(err_key: CodeProps, data?: any, errmsg?: string) {
  return {
    code: Code[err_key],
    message: errmsg || Message[err_key],
    data: data || {}
  };
};

export const getCtxBackResult = function(
  err_key: CodeProps,
  data: any,
  errmsg?: string
) {
  return {
    code: Code[err_key],
    message: errmsg || Message[err_key],
    data: {
      data: data.data || {},
      total: data.total || 0
    }
  };
};

