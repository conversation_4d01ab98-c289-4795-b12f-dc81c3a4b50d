import { QueryParamType } from '@/db/mysql';
import { genEnCode, sha256 } from '.';

export const getCsDomain = (pv_domain: string, host_prefix: string) => {
  const domain =
    process.env.NODE_ENV === 'prod'
      ? 'console.rixengine.com'
      : 'console-t.rixengine.com';
  let cs_domain = '';
  if (process.env.NODE_ENV === 'development') {
    cs_domain = 'allowed.console-t.rixengine.com';
  } else if (pv_domain) {
    cs_domain =
      process.env.NODE_ENV === 'prod'
        ? `console.${pv_domain}`
        : `console-t.${pv_domain}`;
  } else {
    cs_domain = `${host_prefix}.${domain}`;
  }
  console.log('cs_domain', cs_domain);
  return cs_domain;
};

export function generateHostPrefix(): string {
  // 生成逻辑
  // 返回格式：topon-xxx
  // 首先需要 模糊查询 topon- 开头的数据
  // xxx 的数据来源，随机生成12位字符串（小写字母+数字）
  // 如果查询到数据，则需要判断是否存在重复，如果存在重复，则需要重新生成
  return `topon-${genEnCode(8, false).toLowerCase()}`;
}

export function generateEmail(hostPrefix: string): string {
  // 生成逻辑（初始邮箱是无效的，但数据库需要存入，而且是唯一字段）
  // 返回的格式：一定要唯一，同时表明这个邮箱是无效的
  // 邮箱格式：<EMAIL>
  return `invalid-email-only-for-initialization@${hostPrefix}.com`;
}

export function getTestingBuyer(
  type: 'Test-Buyer-System' | 'Test-Buyer-Site-System',
  token: string,
  tnt_id: number
): QueryParamType {
  return {
    sql: 'insert into buyer (buyer_name, integration_type, status, tnt_id, profit_model, rev_share_ratio, imp_track_type, token, auction_type, user_id) values(?)',
    values: [[type, 1, 3, tnt_id, 1, 100, 1, token, 1, 0]]
  };
}

export function getTestingEndpoint(
  buyer_id: number,
  tnt_id: number,
  isSite: boolean = false
): QueryParamType {
  return isSite
    ? `insert into buyer_endpoint(buyer_id, server_region, ad_format, url, connect_timeout,
          socket_timeout, gzip, tnt_id) values 
          (${buyer_id}, 1, 1, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 1, 2, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 1, 3, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 1, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 2, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 3, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 1, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 2, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 3, 'http://rixdsp.apse.bid.rixbeedesk.com/dsp/rtb/bidRequest?seller_id=60027&seller_token=08f99358a3f0490eaa90a2321e5ffd13',0, 1000, 1, ${tnt_id})`
    : `insert into buyer_endpoint(buyer_id, server_region, ad_format, url, connect_timeout,
          socket_timeout, gzip, tnt_id) values (${buyer_id}, 1, 1, 
          'https://use.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',
          0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 1, 2, 'https://use.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 1, 3, 'https://use.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 1, 'https://apac.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 2, 'https://apac.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 2, 3, 'https://apac.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 1, 'https://euc.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 2, 'https://euc.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id}),
          (${buyer_id}, 3, 3, 'https://euc.xyz.svr-algorix.com/rtb/sa?sid=160017&token=734f1df31208fa84090c5b37ea6839ab',0, 1000, 1, ${tnt_id})`;
}

export function getTestingQPS(
  buyer_id: number,
  tnt_id: number
): QueryParamType {
  return {
    sql: 'insert into qps (buyer_id, level, seller_id, qps, status, op_id, tnt_id, ots_id,server_region, uk_key) values ?',
    values: [
      [
        [
          buyer_id,
          2,
          0,
          50,
          1,
          0,
          tnt_id,
          '',
          1,
          sha256(`${tnt_id}2${buyer_id}01`)
        ],
        [
          buyer_id,
          2,
          0,
          50,
          1,
          0,
          tnt_id,
          '',
          2,
          sha256(`${tnt_id}2${buyer_id}02`)
        ]
      ]
    ]
  };
}

export function getTestingCap(
  buyer_id: number,
  tnt_id: number
): QueryParamType {
  return {
    sql: 'insert into cap (seller_id, buyer_id, type, imp_cap, rev_cap, status, cap_status, op_id, sys_update_time, tnt_id) values ?',
    values: [[[0, buyer_id, 2, 500, 1, 1, 1, 0, '1970-01-01 00:00:01', tnt_id]]]
  };
}
