/*
 * @Author: chen<PERSON>dan
 * @Date: 2023-11-27 19:13:23
 * @LastEditors: chenmudan
 * @LastEditTime: 2023-12-28 15:20:48
 * @Description:
 */

import { escape } from '@/db/mysql';
import { trim, isNotEmptyStr } from '@/utils';

export const exactQueries = function (key: any, value: any, isNum: any, prefix?: string) {
  const pre = prefix || '';
  if (typeof value !== 'undefined' && !!trim(value)) {
    const str = escape(`${value}`);
    return isNum ? `${pre}${key} = ${value}` : `${pre}${key} = ${str}`;
  }
  return '';
};

export const fuzzyQueries = function (key: any, value: any, prefix?: string) {
  const pre = prefix || '';
  if (typeof value !== 'undefined' && !!trim(value)) {
    const str = escape(`${value}`);
    return `${pre}${key} ~* ${str}`;
  }
  return '';
};

export const fuzzyQueriesForMysql = function (key: any, value: any, prefix?: string) {
  const pre = prefix || '';
  if (typeof value !== 'undefined' && !!trim(value)) {
    let str = escape(`${value}`.toLowerCase());
    str = str.replace(/^\'|\'$/g, '');
    return `${pre}${key} like '%${str}%'`;
  }
  return '';
};

export const arrayQueries = function (key: any, value: any[], isNum: any, prefix?: string) {
  const pre = prefix || '';
  if (typeof value !== 'undefined' && Array.isArray(value) && value.length > 0) {
    const content = value
      .map((item) => {
        if (isNum) {
          return `${item}`;
        }
        const str = escape(`${item}`);
        return `${str}`;
      })
      .join(',');
    return `${pre}${key} in (${content})`;
  }
  return '';
};

export const joinQueries = function (keys: any, obj: any) {
  let exactQueriesStr = '';
  let fuzzyQueriesStr = '';
  let arrayQueriesStr = '';
  const queries = keys.extra ? [keys.extra] : [];
  const numberCols = keys.numberCols ? keys.numberCols : [];
  // 查询前缀，多表查询使用， 比如adx.admin cd.xxx
  const prefix = keys.prefix ? keys.prefix : '';
  if (keys.exactQueries) {
    exactQueriesStr = trim(
      keys.exactQueries
        .map((item: string | number) =>
          exactQueries(
            item,
            obj[item],
            numberCols.some((col: string | number) => col === item),
            prefix
          )
        )
        .filter(isNotEmptyStr)
        .join(' and ')
    );
  }
  if (keys.fuzzyQueries) {
    fuzzyQueriesStr = trim(
      keys.fuzzyQueries
        .map((item: string | number) => fuzzyQueries(item, obj[item], prefix))
        .filter(isNotEmptyStr)
        .join(' and ')
    );
  }
  if (keys.fuzzyQueriesForMysql) {
    fuzzyQueriesStr = trim(
      keys.fuzzyQueriesForMysql
        .map((item: string | number) => fuzzyQueriesForMysql(item, obj[item], prefix))
        .filter(isNotEmptyStr)
        .join(' and ')
    );
  }
  if (keys.arrayQueries) {
    arrayQueriesStr = trim(
      keys.arrayQueries
        .map((item: string | number) =>
          arrayQueries(
            item,
            obj[item],
            numberCols.some((col: string | number) => col === item),
            prefix
          )
        )
        .filter(isNotEmptyStr)
        .join(' and ')
    );
  }
  // eslint-disable-next-line no-unused-expressions
  !!exactQueriesStr && queries.push(exactQueriesStr);
  // eslint-disable-next-line no-unused-expressions
  !!fuzzyQueriesStr && queries.push(fuzzyQueriesStr);
  // eslint-disable-next-line no-unused-expressions
  !!arrayQueriesStr && queries.push(arrayQueriesStr);
  return trim(queries.join(' and '));
};

export const joinStr = function (strList: any[], connector: any) {
  return trim(strList.filter(isNotEmptyStr).join(connector));
};

export const getEscapeString = (value: string) => {
  let str = value || '';
  if (str) {
    str = escape(`${value}`);
    str = str.replace(/^\'|\'$/g, '');
  }
  return str;
};
