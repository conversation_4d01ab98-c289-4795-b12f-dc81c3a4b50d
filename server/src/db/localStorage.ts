/**
 * nodejs 异步上下文存储方案 异步包裹范围内都可读取写入 各请求数据隔离
 * 完整请求链路
 *
 * 暴露的逻辑
 * 1. resolveStorage 挂载异步上下文存储 白名单: 只对哪个api做处理 静态资源不加入
 * 2. 默认导出 storage 对象：
 *    getItem 获取数据
 *    setItem 设置数据
 */
import cls from 'cls-hooked';
import { Context, Next } from 'koa';
import { genEnCode, md5 } from '@/utils';

const CLS = cls.createNamespace('request-store');

// 保存数据
const setStorageByKey = (key: string, value: any) => {
  CLS.set(key, value);
};

// 读取数据
const getStorageByKey = (key: string) => CLS.get(key);

// 挂载异步上下文存储 白名单: 只对哪个api做处理 静态资源不加入
export const resolveStorage = (options: { whiteList: string[] }) => async (ctx: Context, next: Next) => {
  const start = Date.now();
  const arr = ctx.originalUrl.split('?').filter((e) => e.trim());
  const api = arr.length > 0 ? arr[0] : ctx.originalUrl;
  const user_id = ctx.session?.admin_id || 0;
  const account_name = ctx.session?.account_name || '';
  // 生成唯一的log_id，供日志使用，方便排查
  const log_id = md5(`${user_id}_${ctx.origin}_${start}_${genEnCode(8)}`);
  ctx.state.cur_log_id = log_id;

  const isAllow = options.whiteList.some((v) => api.startsWith(v));
  if (isAllow) {
    await CLS.runPromise(() => {
      CLS.set('user', `${account_name}(${user_id})`);
      CLS.set('log_id', log_id);
      CLS.set('api', api);
      return next();
    });
  } else {
    return await next();
  }
};

export default {
  getItem: getStorageByKey,
  setItem: setStorageByKey
};
