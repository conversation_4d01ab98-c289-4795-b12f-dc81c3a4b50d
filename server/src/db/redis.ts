/*
 * @Author: 袁跃钊 yuan<PERSON><PERSON><EMAIL>
 * @Date: 2023-03-27 16:20:19
 * @LastEditors: 袁跃钊 yuanyu<PERSON><EMAIL>
 * @LastEditTime: 2023-12-28 11:27:11
 * @LastEditTime: 2023-12-27 15:35:03
 * @Description:
 */

import Redis from 'ioredis';
import { getLogger } from '@/config/log4js';
/* eslint-disable implicit-arrow-linebreak */
/* eslint-disable indent */

import { getConfig, md5 } from '@/utils';

const { redisConfig } = getConfig();

const redis = redisConfig.is_cluster
  ? new Redis.Cluster(redisConfig.optArray, {
      // eslint-disable-next-line indent
      dnsLookup: (address: any, callback: (arg0: null, arg1: any) => any) =>
        // eslint-disable-next-line indent
        callback(null, address),
      scaleReads: 'slave'
    })
  : new Redis(redisConfig.opts);

redis.on('connect', () => {
  getLogger('redis').info('Connected to redis instance');
  console.log('Connected to redis instance');
});

redis.on('ready', () => {
  getLogger('redis').info('Redis instance is ready (data loaded from disk)');
});

// Handles redis connection temporarily going down without app crashing
// If an error is handled here, then redis will attempt to retry the request based on maxRetriesPerRequest
redis.on('error', (e: any) => {
  getLogger('error').error(`Error connecting to redis: "${e.message}"`);

  if (e.message === 'ERR invalid password') {
    getLogger('error').error(`Fatal error occurred "${e.message}". Stopping server.`);
    // throw e; // Fatal error, don't attempt to fix
  }
});

export const setRedisByKey = function (key: any, value: any, secs?: any) {
  getLogger('redis').info(`setRedisByKey key=${key}, value=[${value}], secs=[${secs}]`);
  return new Promise((resolve, reject) => {
    const val = JSON.stringify(value);
    if (secs && secs > 0 && redis) {
      redis.set(key, val, 'EX', secs, (err, res) => {
        if (err) {
          getLogger('error').error(
            `[Redis] set failed. key=[${key}], value=[${val}], seconds=[${secs}], err=[${err.message}]`
          );
          reject(err);
        } else {
          resolve(res);
        }
      });
    } else {
      redis.set(key, val, (err, res) => {
        if (err) {
          getLogger('error').error(`[Redis] set failed. key=[${key}], value=[${val}], err=[${err.message}]`);
          reject(err);
        } else {
          resolve(res);
        }
      });
    }
  });
};
export const getRedisByKey = function (key: any) {
  getLogger('redis').info(`getRedisByKey key=${key}`);
  return new Promise((resolve, reject) => {
    redis.get(key, (err, result: any) => {
      let res: any = result;
      if (err) {
        getLogger('error').error(`[Redis] get failed. key=[${key}], err=[${err.message}]`);
      } else {
        if (res !== 'null') {
          res = JSON.parse(result);
        } else {
          res = null;
        }
        resolve(res);
      }
    });
  });
};

export const delRedisByKey = function (key: any) {
  getLogger('redis').info(`delRedisByKey key=${key}`);
  if (!Array.isArray(key) || (Array.isArray(key) && key.length === 1)) {
    return new Promise((resolve, reject) => {
      redis.del(key, (err, res) => {
        if (err) {
          getLogger('error').error(`[Redis] del failed. key=[${key}], err=[${err.message}]`);
        } else {
          resolve(res);
        }
      });
    });
  }
  const dels = key.map((k) => ['del', k]);
  return new Promise((resolve, reject) => {
    redis.pipeline(dels).exec((err, res) => {
      if (err) {
        getLogger('error').error(`[Redis] del failed. key=[${key}], err=[${err.message}]`);
      } else {
        resolve(res);
      }
    });
  });
};

export async function delRedisByUserKey(u: any[]) {
  if (Array.isArray(u)) {
    u.forEach(async (user_id) => {
      const key = md5(`ALGORIX_ADMIN_AUTHORIZAION:${user_id}`);
      await delRedisByKey(key);
    });
  } else {
    const key = md5(`ALGORIX_ADMIN_AUTHORIZAION:${u}`);
    await delRedisByKey(key);
  }
}

export const setRedisSetCountByValue = (key: string, value: string | string[]) =>
  new Promise((resolve, reject) => {
    getLogger('redis').info(`setRedisSetCountByValue key=${key}, value=[${value}]`);
    if (Array.isArray(value)) {
      const values = value.filter((v) => v);
      redis.sadd(key, ...values, (err, res) => {
        if (err) {
          getLogger('error').error(`[Redis] sadd failed. key=[${key}], err=[${err.message}]`);
          reject(err);
        } else {
          getLogger('redis').info(`[Redis] sadd success. key=[${key}], value=[${values}], res=[${res}]`);

          resolve(res);
        }
      });
    } else {
      redis.sadd(key, value, (err, res) => {
        if (err) {
          getLogger('error').error(`[Redis] sadd failed. key=[${key}], err=[${err.message}]`);
          reject(err);
        } else {
          getLogger('redis').info(`[Redis] sadd success. key=[${key}], value=[${value}], res=[${res}]`);

          resolve(res);
        }
      });
    }
  });

export const getRedisSetCountByValue = (key: string, value: string) =>
  new Promise((resolve, reject) => {
    redis.sismember(key, value, (err, res) => {
      if (err) {
        getLogger('error').error(`[Redis] scard failed. key=[${key}], err=[${err.message}]`);
        reject(err);
      } else {
        getLogger('redis').info(`[Redis] sismember srem key=[${key}], value=[${value}], res=[${res}]`);
        resolve(res);
      }
    });
  });

export const delRedisSetCountByValue = (key: string, value: string) =>
  new Promise((resolve, reject) => {
    getLogger('redis').info(`delRedisSetCountByValue key=${key}, value=[${value}]`);
    redis.srem(key, value, (err, res) => {
      if (err) {
        getLogger('error').error(`[Redis] srem failed. key=[${key}], err=[${err.message}]`);
        reject(err);
      } else {
        resolve(res);
      }
    });
  });

export const isRedisKeyExists = (key: string) =>
  new Promise((resolve, reject) => {
    getLogger('redis').info(`isRedisKeyExists key=[${key}]`);
    redis.exists(key, (err, res) => {
      if (err) {
        getLogger('error').error(`[redis] exists key failed. key=[${key}], err=[${err.message}]`);
        reject(err);
      } else {
        getLogger('redis').info(`[redis] exists key=[${key}], res=[${res}]`);
        console.log('xxkey结果', res, key);
        // 直接返回包含的key
        const str = res === 1 ? key : '';
        resolve(str);
      }
    });
  });

export const isRedisMulKeyExists = (keys: string[]) => Promise.all(keys.map((key) => isRedisKeyExists(key)));
