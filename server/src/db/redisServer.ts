/**
 * 该文件 用于 获取服务端的 redis 数据
 */

import Redis from 'ioredis';
import { getConfig } from '@/utils';
import { getLogger } from '@/config/log4js';

const { reportRedisConfig } = getConfig();

const redis = reportRedisConfig.is_cluster
  ? new Redis.Cluster(reportRedisConfig.optArray, {
      // eslint-disable-next-line indent
      dnsLookup: (address: any, callback: (arg0: null, arg1: any) => any) =>
        // eslint-disable-next-line indent, implicit-arrow-linebreak
        callback(null, address),
      scaleReads: 'slave'
    })
  : new Redis(reportRedisConfig.opts);

redis.on('connect', () => {
  getLogger('app').info('Connected to redis instance');
  console.log('Connected to redis instance');
});

redis.on('ready', () => {
  getLogger('app').info('Redis instance is ready (data loaded from disk)');
});

// Handles redis connection temporarily going down without app crashing
// If an error is handled here, then redis will attempt to retry the request based on maxRetriesPerRequest
redis.on('error', (e: any) => {
  getLogger('error').error(`Error connecting to redis: "${e.message}"`);
  console.log('xxxerror', e);
  if (e.message === 'ERR invalid password') {
    getLogger('error').error(`Fatal error occurred "${e.message}". Stopping server.`);
    // throw e; // Fatal error, don't attempt to fix
  }
});

// redis 默认48小时过期
export const setRedisByKey = function (key: any, value: any, secs = reportRedisConfig.expire_time) {
  return new Promise((resolve, reject) => {
    const val = JSON.stringify(value);
    if (secs && secs > 0 && redis) {
      redis.set(key, val, 'EX', secs, (err, res) => {
        if (err) {
          getLogger('error').error(
            `[Redis] set failed. key=[${key}], value=[${val}], seconds=[${secs}], err=[${err.message}]`
          );
          reject(err);
        } else {
          getLogger('redis').info(`[Redis] set ex key=[${key}], value=[${val}], seconds=[${secs}], res=[${res}]`);
          resolve(res);
        }
      });
    } else {
      redis.set(key, val, (err, res) => {
        if (err) {
          getLogger('error').error(`[Redis] set failed. key=[${key}], value=[${val}], err=[${err.message}]`);
          reject(err);
        } else {
          getLogger('redis').info(`[Redis] set key=[${key}], value=[${val}], res=[${res}]`);
          resolve(res);
        }
      });
    }
  });
};
export const getRedisByKey = function (key: any) {
  return new Promise((resolve, reject) => {
    redis.get(key, (err, result: any) => {
      let res: any = result;
      if (err) {
        getLogger('error').error(`[Redis] get failed. key=[${key}], err=[${err.message}]`);
        reject(err);
      } else {
        getLogger('redis').info(`[Redis] get key=[${key}], res=[${res}]`);
        if (res !== 'null') {
          res = JSON.parse(result);
        } else {
          res = null;
        }
        resolve(res);
      }
    });
  });
};
// 删除单个的
export const delRedisByKey = (key: string) =>
  new Promise((resolve, reject) => {
    redis.del(key, (err, res) => {
      if (err) {
        getLogger('error').error(`[Redis] del failed. key=[${key}], err=[${err.message}]`);
        reject(err);
      } else {
        getLogger('redis').info(`[Redis] del key=[${key}], res=[${res}]`);
        resolve(res);
      }
    });
  });
// 批量删除
export const delRedisByMultipleKeys = (keys: string[]) => {
  if (keys.length) {
    const dels = keys.map((k) => ['del', k]);
    return new Promise((resolve, reject) => {
      redis.pipeline(dels).exec((err, res) => {
        if (err) {
          getLogger('error').error(`[Redis] del failed. key=[${JSON.stringify(keys)}], err=[${err.message}]`);
          reject(err);
        } else {
          getLogger('redis').info(`[Redis] del key=[${JSON.stringify(keys)}], res=[${res}]`);
          resolve(res);
        }
      });
    });
  }
};

// 批量获取
export const getRedisByMultipleKeys = (keys: string[]) => {
  if (keys.length) {
    const gets = keys.map((k) => ['get', k]);
    return new Promise((resolve, reject) => {
      redis.pipeline(gets).exec((err, res) => {
        if (err) {
          getLogger('error').error(`[Redis] del failed. key=[${JSON.stringify(keys)}], err=[${err.message}]`);
          reject(err);
        } else {
          const data = Array.isArray(res) && res.length ? res.flat(Infinity) : [];
          const tmp = data.filter((v) => v).map((v: any) => JSON.parse(v));
          getLogger('redis').info(
            `[Redis] getRedisByMultipleKeys key=[${JSON.stringify(keys)}], res=[${JSON.stringify(tmp)}]`
          );
          resolve(tmp);
        }
      });
    });
  }
};

export const getRedisSetCountByValue = (key: string, value: string) =>
  new Promise((resolve, reject) => {
    redis.sismember(key, value, (err, res) => {
      if (err) {
        getLogger('error').error(`[Redis] scard failed. key=[${key}], err=[${err.message}]`);
        reject(err);
      } else {
        getLogger('redis').info(`[Redis] sismember srem key=[${key}], value=[${value}], res=[${res}]`);
        resolve(res);
      }
    });
  });

export const delRedisSetCountByValue = (key: string, value: string) =>
  new Promise((resolve, reject) => {
    redis.srem(key, value, (err, res) => {
      if (err) {
        getLogger('error').error(`[Redis] scard srem key=[${key}], err=[${err.message}]`);
        reject(err);
      } else {
        getLogger('redis').info(`[Redis] scard srem key=[${key}], value=[${value}], res=[${res}]`);
        resolve(res);
      }
    });
  });

export const existRedisKey = (key: string) =>
  new Promise((resolve, reject) => {
    redis.exists(key, (err, res) => {
      if (err) {
        getLogger('error').error(`[Redis] exists failed. key=[${key}], err=[${err.message}]`);
        reject(err);
      } else {
        getLogger('redis').info(`[Redis] exists key=[${key}], res=[${res}]`);
        resolve(res);
      }
    });
  });

export const setRedisSetCountByValue = (key: string, value: string | string[]) =>
  new Promise((resolve, reject) => {
    if (Array.isArray(value)) {
      redis.sadd(key, ...value, (err, res) => {
        if (err) {
          getLogger('error').error(`[Redis] sadd failed. key=[${key}], err=[${err.message}]`);
          reject(err);
        } else {
          getLogger('redis').info(`[Redis] sadd keys=[${key}], value=[${value}], res=[${res}]`);
          resolve(res);
        }
      });
    } else {
      redis.sadd(key, value, (err, res) => {
        if (err) {
          getLogger('error').error(`[Redis] sadd failed. key=[${key}], err=[${err.message}]`);
          reject(err);
        } else {
          getLogger('redis').info(`[Redis] sadd key=[${key}], value=[${value}], res=[${res}]`);
          resolve(res);
        }
      });
    }
  });
