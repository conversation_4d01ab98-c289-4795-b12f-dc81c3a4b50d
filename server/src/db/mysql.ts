/* *
 * @Author: yuan<PERSON>@algorix.co
 * @file: 封装mysql https: //www.npmjs.com/package/mysql
 * @Date: 2019-03-29 16:40:34
 * @Last Modified by: ch<PERSON><PERSON><PERSON>@algorix.co
 * @Last Modified time: 2022-11-24 14:21:06
 */

import { getLogger } from '@/config/log4js';
import { getConfig } from '@/utils';
import mysql, { Connection, PoolConnection, escape } from 'mysql2';
import util from 'util';

// const Opt = {
//   database: 'MySQL'
// };
// const TntId = 'tnt_id';
// const parser = new Parser();

const { database } = getConfig();

const pool = mysql.createPool(database);

export type QueryParamType = string | mysql.QueryOptions;
export interface TransactionReturnType {
  connection: PoolConnection | null;
  commit: () => Promise<void>;
  query: (arg1: QueryParamType) => Promise<any>;
  beginTransaction: () => Promise<void>;
  rollback: () => Promise<void>;
  // query: (arg1: QueryParamType) => Promise<any>;
  queryStream?: (arg1: QueryParamType) => Promise<any>;
}

pool.on('connection', (conn: Connection) => {
  conn.query("SET time_zone='+00:00', group_concat_max_len=102400;", (error) => {
    if (error) {
      throw error;
    }
  });
});

pool.on('error', (err) => {
  getLogger('error').error(err.stack || err.message);
  console.log('mysql error', err);
});

const getConnection = util.promisify(pool.getConnection).bind(pool);

// 获取mysql的一些操作列
async function getConnectionParams(): Promise<TransactionReturnType> {
  const connection: PoolConnection | null = await getConnection();
  const beginTransaction = util.promisify(connection.beginTransaction).bind(connection);
  // eslint-disable-next-line no-shadow, no-underscore-dangle
  const _query = util.promisify(connection.query).bind(connection);
  const commit = util.promisify(connection.commit).bind(connection);
  const rollback = util.promisify(connection.rollback).bind(connection);
  // eslint-disable-next-line no-shadow
  const query = (arg: QueryParamType) => {
    const sql = typeof arg === 'string' ? arg : arg.sql;
    const values = typeof arg === 'string' ? undefined : arg?.values;
    let _sql = sql;
    if (values) {
      _sql = mysql.format(_sql, values);
    }
    getLogger('sql').info(`query sql: [${_sql}]`);
    return Promise.resolve(_query({ sql: _sql }));
  };

  const queryStream = (arg: QueryParamType) => {
    const sql = typeof arg === 'string' ? arg : arg.sql;
    const values = typeof arg === 'string' ? undefined : arg?.values;
    // eslint-disable-next-line no-underscore-dangle
    let _sql = sql;
    if (values) {
      _sql = mysql.format(_sql, values);
    }
    getLogger('sql').info(`queryStream sql: [${_sql}]`);
    return Promise.resolve(connection!.query(_sql));
  };
  return {
    connection,
    beginTransaction,
    query,
    commit,
    rollback,
    // query,
    queryStream
  };
}
// 执行sql
async function query(sql: string, values?: any) {
  try {
    const { query: queryFunc, connection } = await getConnectionParams();
    const result = await queryFunc({ sql, values });
    if (connection) {
      connection.release();
    }
    return Promise.resolve(result);
  } catch (error: any) {
    getLogger('error').error(sql);
    getLogger('error').error(`${error.message} ${error.sqlMessage}`);
    return Promise.reject(error);
  }
}

async function execWaterfallTransaction(sqls: QueryParamType[]) {
  getLogger('sql').info(`execWaterfallTransaction SQL: ${sqls}`);
  let conn: PoolConnection | null = null;
  try {
    const { query, connection, commit, beginTransaction } = await getConnectionParams();
    await beginTransaction();
    const result = sqls.map((sql) => query(sql));

    conn = connection;
    const data = await Promise.all(result);
    await commit();
    conn!.release();
    conn = null;
    return Promise.resolve(data);
  } catch (error: any) {
    getLogger('error').error(`execWaterfallTransaction error ${error.message}`);
    if (conn) {
      conn.release();
      conn = null;
    }
    return Promise.reject(error);
  }
}

/**
 * 执行事务，比 execWaterfallTransaction 更通用，可以执行任意sql
 * @param callback 回调函数，接收getConnectionParams返回的参数，执行业务逻辑
 * @returns 回调函数的返回值
 */
async function execTransaction<T>(
  callback: (params: TransactionReturnType) => Promise<T>
): Promise<T> {
  getLogger('sql').info('execTransaction: start transaction');
  let conn: PoolConnection | null = null;
  try {
    const connectionParams = await getConnectionParams();
    const { connection, beginTransaction, commit } = connectionParams;
    conn = connection;
    
    // 开始事务
    await beginTransaction();
    
    // 执行业务逻辑
    const result = await callback(connectionParams);
    
    // 提交事务
    await commit();
    
    // 释放连接
    conn!.release();
    conn = null;
    
    getLogger('sql').info('execTransaction: transaction success');
    return Promise.resolve(result);
  } catch (error: any) {
    getLogger('error').error(`execTransaction error: ${error.message}`);
    if (conn) {
      conn.rollback(() => {
        conn!.release();
        conn = null;
      });
    }
    return Promise.reject(error);
  }
}

export default {
  query,
  getConnectionParams,
  execWaterfallTransaction,
  execTransaction
  // query
};

export { PoolConnection, escape };
