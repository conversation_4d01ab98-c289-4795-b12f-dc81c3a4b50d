/* *
 * @Author: y<PERSON><PERSON>@algorix.co
 * @file: 查询aat 库
 * @Date: 2019-03-29 16:40:34
 * @Last Modified by: chen<PERSON><PERSON>@algorix.co
 * @Last Modified time: 2022-11-24 14:21:06
 */

import mysql, { Connection, PoolConnection } from 'mysql2';
import util from 'util';
import { getConfig } from '@/utils';
import { getLogger } from '@/config/log4js';

const { aat_database } = getConfig();

const pool = mysql.createPool(aat_database);

type QueryParamType = string | mysql.QueryOptions;
interface TransactionReturnType {
  connection: PoolConnection | null;
  commit: () => Promise<void>;
  query: (arg1: QueryParamType, is_write_log?: boolean) => Promise<any>;
  beginTransaction: () => Promise<void>;
  rollback: () => Promise<void>;
}

pool.on('connection', (conn: Connection) => {
  conn.query("SET time_zone='+00:00', group_concat_max_len=102400;", (error) => {
    if (error) {
      getLogger('error').error(`mysql connection error error=[${error.message}]`);
      throw error;
    }
  });
});

pool.on('error', (err) => {
  getLogger('error').error(err.stack || err.message);
  console.log('mysql error', err);
});

const getConnection = util.promisify(pool.getConnection).bind(pool);

// 获取mysql的一些操作列
async function getConnectionParams(): Promise<TransactionReturnType> {
  const connection: PoolConnection | null = await getConnection();
  const beginTransaction = util.promisify(connection.beginTransaction).bind(connection);
  // eslint-disable-next-line no-shadow, no-underscore-dangle
  const _query = util.promisify(connection.query).bind(connection);
  const commit = util.promisify(connection.commit).bind(connection);
  const rollback = util.promisify(connection.rollback).bind(connection);
  // eslint-disable-next-line no-shadow
  const query = (arg: QueryParamType, is_write_log = true) => {
    const sql = typeof arg === 'string' ? arg : arg.sql;
    const values = typeof arg === 'string' ? undefined : arg?.values;
    // eslint-disable-next-line no-underscore-dangle
    let _sql = sql;
    if (values) {
      _sql = mysql.format(_sql, values);
      console.log('sql', _sql);
    }
    if (is_write_log) {
      getLogger('sql').info(`getConnectionParams query sql=[${_sql}] values=[${values}]`);
    }
    return _query({ sql, values });
  };
  return {
    connection,
    beginTransaction,
    query,
    commit,
    rollback
  };
}
// 执行sql
async function query(sql: QueryParamType) {
  let conn: PoolConnection | null = null;
  try {
    const { query: queryFunc, connection } = await getConnectionParams();
    conn = connection;
    getLogger('sql').info(`query SQL: ${typeof sql === 'string' ? sql : JSON.stringify(sql)}`);
    const result = await queryFunc(sql, false);
    if (conn) {
      conn.release();
      conn = null;
    }
    return Promise.resolve(result);
  } catch (error: any) {
    console.log('xxerror', error);
    getLogger('error').error(`sql=[${JSON.stringify(sql)}], ${error.message} ${error.sqlMessage}`);
    if (conn) {
      conn.release();
      conn = null;
    }
    return Promise.reject(error);
  }
}

async function execWaterfallTransaction(sqls: QueryParamType[]) {
  getLogger('sql').info(`execWaterfallTransaction SQL: sqls=[${JSON.stringify(sqls)}]`);
  let conn: PoolConnection | null = null;
  try {
    const { query, connection, commit, beginTransaction } = await getConnectionParams();
    await beginTransaction();
    const result = sqls.map((sql) => query(sql, false));
    conn = connection;
    const data = await Promise.all(result);
    await commit();
    conn!.release();
    conn = null;
    return Promise.resolve(data);
  } catch (error: any) {
    console.log('xxerror', error);
    getLogger('error').error(
      `execWaterfallTransaction error, sqls=[${JSON.stringify(sqls)}], error=[${error.message}]`
    );
    if (conn) {
      conn.rollback(() => {
        conn!.release();
        conn = null;
      });
    }
    return Promise.reject(error);
  }
}

export default {
  query,
  getConnectionParams,
  execWaterfallTransaction
};
