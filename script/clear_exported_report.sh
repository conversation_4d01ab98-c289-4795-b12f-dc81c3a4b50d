#!/bin/bash

TARGET_DIR="/data/htdocs/saas.rix-admin/server/files/report"

DATE_7_DAYS_AGO=$(date -d "-7 days" +%Y%m%d)

for item in "$TARGET_DIR"/*
do
  # 如果是文件夹
  if [ -d "$item" ]
  then
    # 获取文件夹的修改时间
    ITEM_DATE=$(stat -c %y "$item" | cut -d' ' -f1 | tr -d '-')
    # 修改时间早于七天以前的日期
    if [ "$ITEM_DATE" -lt "$DATE_7_DAYS_AGO" ]
    then
      # 删除文件夹
      rm -rf "$item"
      echo "Deleted $item"
    fi
  fi
done