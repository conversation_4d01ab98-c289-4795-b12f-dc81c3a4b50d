#!/bin/bash

# ANSI color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# 检查是否已安装 Git
if ! command -v git &> /dev/null; then
    echo "${RED}Git is not installed. Please install Git and try again.${NC}"
    exit 1
fi

# 检查是否有未提交的更改
if [ -n "$(git status --porcelain)" ]; then
    echo -e "${RED}Error: There are uncommitted changes in your repository.${NC}"
    exit 1
fi

# 生成随机字符串
generate_random_string() {
    # 使用 `openssl rand` 生成随机字符串
    echo $(openssl rand -hex 4)
}

# 获取必填的分支名称
read -e -p "Enter the specific name for the new sprint branch (format: 'sprint-xxxYYYMMDD-(specific_name)'): " specific_name
if [ -z "$specific_name" ]; then
    echo "${RED}The sprint branch name cannot be empty.${NC}"
    exit 1
fi

# 获取当前日期
current_date=$(date +%Y%m%d)

# 创建分支名称
branch_name="sprint-$(generate_random_string)$current_date-$specific_name"

# 拉取最新的 master 分支
echo -e "${GREEN}Fetching latest changes from remote master...${NC}"
git fetch origin master

# 创建新的迭代分支
echo -e "${GREEN}Creating new sprint branch: $branch_name${NC}"
git checkout -b "$branch_name" origin/master

# 提示用户输入描述
read -e -p "Please add a description for this sprint (default: 'Start sprint: $branch_name'): " description
description=${description:-"Start sprint: $branch_name"}

# 提示用户输入描述后提交
echo -e "${GREEN}Description: $description${NC}"
git commit --allow-empty -m "$description"
echo -e "${GREEN}Sprint started and committed.${NC}"

# 如果需要推送至远程仓库，可以添加以下代码
# read -e -p "Push to remote (y/N)? " push_to_remote
# if [[ $push_to_remote =~ ^[Yy]$ ]]; then
#     git push -u origin "$branch_name"
#     echo -e "${GREEN}Branch pushed to remote.${NC}"
# fi
