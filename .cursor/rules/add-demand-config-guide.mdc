---
description: 
globs: 
alwaysApply: false
---
# Add Demand Configuration Guide

This guide explains how to add or modify demand configurations. The `burl_track_type` implementation is used as an example to demonstrate the process.

## Key File Locations

### Server Side
- Database Types: [server/src/types/demand.d.ts](mdc:server/src/types/demand.d.ts)
- Model Operations: [server/src/models/demand/demand.ts](mdc:server/src/models/demand/demand.ts)

### Client Side
- Type Definitions: [client/src/services/demand/typings.d.ts](mdc:client/src/services/demand/typings.d.ts)
- Constants & Enums: [client/src/constants/demand/demand-columns.tsx](mdc:client/src/constants/demand/demand-columns.tsx)
- Form Component: [client/src/pages/demand/components/AddDemandDrawer/index.tsx](mdc:client/src/pages/demand/components/AddDemandDrawer/index.tsx)
- Info Display: [client/src/constants/demand/info.tsx](mdc:client/src/constants/demand/info.tsx)

## Configuration Steps

### 1. Server-Side Configuration

#### Database Schema Updates
```sql
-- Template for adding new configuration
ALTER TABLE buyer 
ADD COLUMN {field_name} {data_type} DEFAULT {default_value} COMMENT '{description}';

-- Example (burl_track_type)
ALTER TABLE buyer 
ADD COLUMN burl_track_type int DEFAULT 1 COMMENT '1 for adm, 2 for server';
```

#### Server Code Updates
1. Add type definition in `server/src/types/demand.d.ts`:
   ```typescript
   // Location: DemandAPI.AddDemandParams
   type AddDemandParams = {
     // ... existing fields ...
     new_field_name: field_type;  // Add your new field here
   };
   ```

2. Update model in `server/src/models/demand/demand.ts`:
   ```typescript
   // Location: DemandModel.getDemandList
   // Add to select fields
   byr.new_field_name as new_field_name,

   // Location: DemandModel.updateDemand
   // Add to update fields list
   const updateFields = [
     // ... existing fields ...
     ['new_field_name', new_field_name],
   ];
   ```

### 2. Client-Side Configuration

#### Type Definitions
1. Update `client/src/services/demand/typings.d.ts`:
   ```typescript
   // Location: DemandAPI.DemandListItem
   type DemandListItem = {
     // ... existing fields ...
     new_field_name: field_type;
   };
   ```

#### Constants and Enums
1. Add in `client/src/constants/demand/demand-columns.tsx`:
   ```typescript
   // Location: Top-level exports
   // Add options for radio/select components
   export const NewConfigOptions = [
     { label: 'Option1', value: 1 },
     { label: 'Option2', value: 2 }
   ];

   // Add mapping for display
   export const NewConfigMap = {
     1: 'Option1',
     2: 'Option2'
   };

   // Add enum for usage in code
   export const NewConfig = {
     OPTION1: 1,
     OPTION2: 2
   };

   // Location: DemandColumns array
   // Add column definition
   {
     title: 'New Config',
     width: 150,
     dataIndex: 'new_field_name',
     render: (_: number) => <>{NewConfigMap[_]}</>,
   }
   ```

#### UI Components
1. Update `client/src/pages/demand/components/AddDemandDrawer/index.tsx`:
   ```typescript
   // Location: Import section
   import {
     NewConfigOptions,
     NewConfig,
   } from '@/constants/demand/demand-columns';

   // Location: DefaultFormData object
   const DefaultFormData = {
     // ... existing fields ...
     new_field_name: NewConfig.OPTION1,
   };

   // Location: Form section
   <Form.Item
     name="new_field_name"
     label="New Config:"
     rules={[{ required: true, message: 'Please select' }]}
     tooltip="Configuration description"
   >
     <NormalRadio options={NewConfigOptions} />
   </Form.Item>
   ```

2. Update info display in `client/src/constants/demand/info.tsx`:
   ```typescript
   // Location: Import section
   import { NewConfigMap } from './demand-columns';

   // Location: DemandInfoTabs array
   {
     title: 'New Config',
     dataIndex: 'new_field_name',
     render: (_) => <>{NewConfigMap[_]}</>,
   }
   ```

## Best Practices
1. Follow naming conventions:
   - Database columns: snake_case
   - TypeScript/JavaScript variables: camelCase
   - Types/Enums: PascalCase
2. Always provide:
   - Default values
   - Validation rules
   - Tooltips for complex options
3. Keep related code together:
   - Group constants and types in appropriate files
   - Follow existing patterns for similar configurations
4. Update all necessary files:
   - Database schema
   - Server types and models
   - Client types and constants
   - UI components and forms
   - Info display components

## Example Implementation
See the `burl_track_type` implementation in the codebase for a complete example:
- Database field: `burl_track_type int DEFAULT 1`
- Options: ADM (1), Server (2)
- UI: Radio button group with tooltip
- Display: Mapped values in table and info panel

## Example Configurations

### Banner Transfer Format
- **Paused (0)**: No banner format transformation
- **Banner2Video (1)**: Transform banner ads to video format
- **Banner2Native (2)**: Transform banner ads to native format

### BURL Track Type
- **ADM (1)**: Track through ADM
- **Server (2)**: Track through server

## Basic Configuration
The main configuration file for demand settings is located at [client/src/constants/demand/demand-columns.tsx](mdc:client/src/constants/demand/demand-columns.tsx).

## Banner Transfer Format
The Banner Transfer Format feature allows you to configure how banner ads are handled:

- **Paused**: No banner format transformation
- **Banner2Video**: Transform banner ads to video format
- **Banner2Native**: Transform banner ads to native format

This setting can be found in the demand configuration form at [client/src/pages/demand/components/AddDemandDrawer/index.tsx](mdc:client/src/pages/demand/components/AddDemandDrawer/index.tsx).

## Other Important Settings

### Format Settings
- **Multi Format**: Enable support for both banner and video ad formats within a single bid request
- **Banner Multi Size**: Add 300x250 size option for 320x50 banner requests
- **Native Format**: Choose between String or JsonObject format for native ads
- **Native Root Key**: Enable/disable native root key functionality

### Display Settings
- **Pass Display Manager**: Control whether to send display manager information to demand
- **SKOverlay**: Enable/disable SKOverlay for iOS traffic
- **Auto-Store**: Enable/disable auto-store functionality for iOS traffic
- **Block non-store apps**: Block requests from apps removed from the app store
- **Additional QPS**: Allocate additional QPS to demand sources with high ECPR when QPS is constrained
